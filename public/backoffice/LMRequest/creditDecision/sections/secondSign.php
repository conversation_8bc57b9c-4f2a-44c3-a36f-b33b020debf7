<?php

use models\constants\creditDecision;
use models\constants\gl\glDate;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\creditDecision as creditDecisionController;
use models\Controllers\loanForm;
use models\lendingwise\tblCreditDecisionForm;
use models\standard\Arrays;
use models\standard\Dates;
loanForm::pushSectionID('SS');

$LMRId = LMRequest:: $LMRId;

$secondSignReasonArray = creditDecision::secondSignReason();
$secondSignDecisionArray = creditDecision::decisionArray();
$boStaffArray = creditDecision::getBOEmployeeAssignedToFile('CD', $LMRId);

$creditDecisionFormData = creditDecisionController::$creditDecisionFormData;
if(!$creditDecisionFormData) {
    $creditDecisionFormData = [
        new tblCreditDecisionForm(),
    ];
}
?>
<div class="card card-custom secondSign">
    <div class="card-header card-header-tabs-line bg-gray-100">
        <div class="card-title">
            <h3 class="card-label">
                Second Sign
            </h3>
        </div>
        <div class="card-toolbar">
            <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer"
                  data-card-tool="toggle"
                  data-section="secondSign"
                  data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Section">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
        </div>
    </div>
    <div class="card-body secondSign_body">
        <div class="form-group row">
            <div class="col-md-6 <?php echo loanForm::showField('secondSignDate'); ?>">
                <div class="form-group row">
                    <label class="font-weight-bold col-md-5" for="secondSignDate">
                        <?php echo loanForm::label('secondSignDate'); ?>
                    </label>
                    <div class="col-md-7 input-group ">
                        <div class="input-group-prepend actionDate">
                            <span class="input-group-text">
                                <i class="fa fa-calendar text-primary icon-lg"></i>
                            </span>
                        </div>
                        <input
                            class="form-control input-sm dateNewClass <?php echo loanForm::isMandatory('secondSignDate') ? 'mandatory' : ''; ?>"
                            type="text"
                            name="secondSignDate"
                            id="secondSignDate"
                            value="<?php echo Dates::formatDateWithRE($creditDecisionFormData->secondSignDate, 'YMD H:i:s', 'm/d/Y');?>"
                            data-before-creation-date="true"
                            data-start-date="<?php echo glDate::getCreationDate(LMRequest::File()->recordDate); ?>"
                            placeholder="MM/DD/YYYY"
                            maxlength="10"
                            autocomplete="off" >
                    </div>
                </div>
            </div>
            <div class="col-md-6 <?php echo loanForm::showField('secondSignReason'); ?>">
                <div class="form-group row">
                    <label class="font-weight-bold col-md-5" for="secondSignReason">
                        <?php echo loanForm::label('secondSignReason'); ?>
                    </label>
                    <div class="col-md-7">
                        <select name="secondSignReason" id="secondSignReason" class="form-control 
                        <?php echo loanForm::isMandatory('secondSignReason') ? 'mandatory' : ''; ?>">
                            <option value="">Select</option>
                            <?php foreach ($secondSignReasonArray as $secondSignReasonKey => $secondSignReasonValue) { ?>
                                <option value="<?php echo $secondSignReasonKey;?>" 
                                <?php echo Arrays::isSelected($secondSignReasonKey, $creditDecisionFormData->secondSignReason);?>>
                                <?php echo $secondSignReasonValue;?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-md-6 <?php echo loanForm::showField('secondSignDecision'); ?>">
                <div class="form-group row">
                    <label class="font-weight-bold col-md-5" for="secondSignDecision">
                        <?php echo loanForm::label('secondSignDecision'); ?>
                    </label>
                    <div class="col-md-7">
                        <select name="secondSignDecision" id="secondSignDecision" class="form-control 
                        <?php echo loanForm::isMandatory('secondSignDecision') ? 'mandatory' : ''; ?>">
                            <option value="">Select</option>
                            <?php foreach ($secondSignDecisionArray as $secondSignDecisionKey => $secondSignDecisionValue) { ?>
                                <option value="<?php echo $secondSignDecisionKey;?>" 
                                <?php echo Arrays::isSelected($secondSignDecisionKey, $creditDecisionFormData->secondSignDecision);?>>
                                <?php echo $secondSignDecisionValue;?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-md-6 <?php echo loanForm::showField('secondSignDecisionDate'); ?>">
                <div class="form-group row">
                    <label class="font-weight-bold col-md-5" for="secondSignDecisionDate">
                        <?php echo loanForm::label('secondSignDecisionDate'); ?>
                    </label>
                    <div class="col-md-7 input-group">
                        <div class="input-group-prepend actionDate">
                            <span class="input-group-text">
                                <i class="fa fa-calendar text-primary icon-lg"></i>
                            </span>
                        </div>
                        <input
                            class="form-control input-sm dateNewClass <?php echo loanForm::isMandatory('secondSignDecisionDate') ? 'mandatory' : ''; ?>"
                            type="text"
                            name="secondSignDecisionDate"
                            id="secondSignDecisionDate"
                            value="<?php echo Dates::formatDateWithRE($creditDecisionFormData->secondSignDecisionDate, 'YMD H:i:s', 'm/d/Y');?>"
                            data-before-creation-date="true"
                            data-start-date="<?php echo glDate::getCreationDate(LMRequest::File()->recordDate); ?>"
                            placeholder="MM/DD/YYYY"
                            maxlength="10"
                            autocomplete="off" >
                    </div>
                </div>
            </div>
            <div class="col-md-6 <?php echo loanForm::showField('secondSignDecisionBy'); ?>">
                <div class="form-group row">
                    <label class="font-weight-bold col-md-5" for="secondSignDecisionBy">
                        <?php echo loanForm::label('secondSignDecisionBy'); ?>
                    </label>
                    <div class="col-md-7">
                        <select name="secondSignDecisionBy" id="secondSignDecisionBy" class="form-control 
                        <?php echo loanForm::isMandatory('secondSignDecisionBy') ? 'mandatory' : ''; ?>">
                            <option value="">Select</option>
                            <?php foreach ($boStaffArray as $boStaffArrayKey => $boStaffArrayValue) { ?>
                                <option value="<?php echo $boStaffArrayValue['AID']; ?>" 
                                <?php echo Arrays::isSelected($boStaffArrayValue['AID'], $creditDecisionFormData->secondSignDecisionBy);?> >
                                <?php echo $boStaffArrayValue['role'] . ' - ' . $boStaffArrayValue['processorName']; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-md-6 <?php echo loanForm::showField('secondSignDecisionNotes'); ?>">
                <div class="form-group row">
                    <label class="font-weight-bold col-md-5" for="secondSignDecisionNotes">
                        <?php echo loanForm::label('secondSignDecisionNotes'); ?>
                    </label>
                    <div class="col-md-7">
                        <textarea name="secondSignDecisionNotes" id="secondSignDecisionNotes" class="form-control validateMaxLength 
                        <?php echo loanForm::isMandatory('secondSignDecisionNotes') ? 'mandatory' : ''; ?>"
                        maxlength="<?php echo loanForm::getFieldLength('secondSignDecisionNotes','tblCreditDecisionForm'); ?>"
                        ><?php echo $creditDecisionFormData->secondSignDecisionNotes;?></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
