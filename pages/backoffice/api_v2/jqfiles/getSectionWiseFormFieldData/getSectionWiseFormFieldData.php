<?php

namespace pages\backoffice\api_v2\jqfiles\getSectionWiseFormFieldData;

use models\composite\oPC\getLoanPrograms;
use models\composite\oPC\getSectionwiseMasterFormFields;
use models\composite\oPC\getSectionwiseQuickAppCustomFormFields;
use models\constants\formFieldDisabledFAQA;
use models\constants\formFieldDisabledFields;
use models\constants\gl\cashFlowFormFields;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glFormFieldsVisibleToPC;
use models\constants\gl\glLoanSetupFormFields;
use models\constants\gl\glPCID;
use models\constants\gl\glPropertyDetailsFormFields;
use models\constants\gl\glRestrictCustomFields;
use models\constants\gl\glRestrictPCFields;
use models\constants\glDisableFAQAOptionInFF;
use models\constants\glMandatoryDisableFormFields;
use models\constants\pvFormFields;
use models\cypher;
use models\Database2;
use models\PageVariables;
use models\portals\BackofficePage;
use models\standard\HTTP;

class getSectionWiseFormFieldData extends BackofficePage
{
    public static function Get()
    {
        $glRestrictCustomFields = glRestrictCustomFields::$glRestrictCustomFields;
        $glRestrictPCFields = glRestrictPCFields::$glRestrictPCFields;
        $formFieldDisabledFields = formFieldDisabledFields::$formFieldDisabledFields;
        $formFieldDisabledFAQA = formFieldDisabledFAQA::$formFieldDisabledFAQA;
        $formFieldDisabledFieldsMandatory = formFieldDisabledFields::$formFieldDisabledFieldsMandatory;
        $cashFlowFormFields = cashFlowFormFields::$cashFlowFormFields;
        $pvFormFields = pvFormFields::$pvFormFields;
        $glFormFieldsVisibleToPC = glFormFieldsVisibleToPC::$glFormFieldsVisibleToPC;
        $formFieldsDisableFAQASections = formFieldDisabledFAQA::$formFieldsDisableFAQASections;

        $tabIndex = null;
        $pcid = 0;
        $sectionid = '';
        $fieldNameSearch = [];
        $fileTypeSearch = [];

        if (trim($_POST['sectionid']) != 'all') {
            if (isset($_POST['sectionid']))
                $sectionid = trim(cypher::myDecryption(trim($_POST['sectionid'])));
        } else {
            $sectionid = trim($_POST['sectionid']);
        }
        if (isset($_POST['pcid'])) $pcid = trim(cypher::myDecryption(trim($_POST['pcid'])));
        if (isset($_POST['fieldNameSearch'])) $fieldNameSearch = $_POST['fieldNameSearch'];
        if (isset($_POST['fileTypeSearch'])) $fileTypeSearch = $_POST['fileTypeSearch'];

        if ($pcid > 0) {
            $fieldsResultArray = getSectionwiseQuickAppCustomFormFields::getReport(
                $pcid,
                $sectionid,
                $fieldNameSearch,
                $fileTypeSearch
            ) ?? [];
        } else {
            $fieldsResultArray = getSectionwiseMasterFormFields::getReport($sectionid, [
                'fieldNameSearch' => $fieldNameSearch
            ]) ?? [];
        }
        $finalresult = [];
        if ($pcid > 0) {
            $assignedPCID = $pcid;
            $PCFieldsArray = $fieldsResultArray['PCquickAppFields'] ?? [];
            $modulesArray = $fieldsResultArray['PCModule'] ?? [];
            foreach ($fieldsResultArray['PCSectionHeadings'] ?? [] as $fieldsResult) {
                $sectionKey = $fieldsResult['sectionID'];
                $sectionHeadingForTitle = htmlentities($fieldsResult['sectionHeading']);
                $disableCheckboxFAQA = '';
                $hideResetToDefault = (in_array($pcid, [glPCID::PCID_PROD_CV3, glPCID::PCID_DEV_DAVE]) && PageVariables::$userRole != 'Super') ? 'd-none' : '';
                if ($sectionKey == 'FC' || $sectionKey == 'YRF4506' || $sectionKey == 'CRU' || $sectionKey == 'CD' 
                || $sectionKey == 'SS' || $sectionKey == 'CTC' || $sectionKey == 'DD' || $sectionKey == 'CDE' 
                || $sectionKey == 'AA' || $sectionKey == 'NOI' || $sectionKey == 'FI' || $sectionKey == 'CLTCL') $disableCheckboxFAQA = 'disabled';
                $disableCheckboxMandatory = '';
                if ($sectionKey == 'CRU') $disableCheckboxMandatory = 'disabled';
                $html = '   
    <div class="row noticeFF notice-danger text-left bg-gray-100 py-2 mb-2">
        <div class="col-sm-4 px-2">
            <label for="' . $sectionKey . 'FA">  <input type="checkbox" class="CheckAllFA" name="CheckAll" id="' . $sectionKey . 'FA" value="' . $sectionKey . '" ' . $disableCheckboxFAQA . '/> 
                Check All / Deselect All (Deselect All Will Hide This Section) - Full App
            </label>
        </div>
        <div class="col-sm-4 px-2">
            <label for="' . $sectionKey . 'FA">  <input type="checkbox" class="CheckAllFAM" name="CheckAll" id="' . $sectionKey . 'FAM" value="' . $sectionKey . '" ' . $disableCheckboxFAQA . '/>
                Check Mandatory All / Deselect Mandatory All - Full App
            </label>
        </div>
        <div class="col-sm-4 px-2">
            <label for="' . $sectionKey . 'ALP">  <input type="checkbox" class="CheckAllALP" name="CheckAll" id="' . $sectionKey . 'ALP" value="' . $sectionKey . '"/>
                Check All / Deselect All - All Loan Programs
            </label>
        </div>
    </div>

    <div class="row noticeFF notice-info text-left bg-gray-100 py-2 mb-2">
        <div class="col-sm-4 px-2">
            <label for="' . $sectionKey . '"> <input type="checkbox" class="CheckAllQA" name="CheckAll" id="' . $sectionKey . '" value="' . $sectionKey . '" ' . $disableCheckboxFAQA . '/>
                Check All / Deselect All (Deselect All Will Hide This Section) - Quick App
            </label>
        </div>
        <div class="col-sm-4 px-2">
            <label for="' . $sectionKey . '"><input type="checkbox" class="CheckAllQAM" name="CheckAll" id="' . $sectionKey . 'M" value="' . $sectionKey . '" ' . $disableCheckboxFAQA . '/> 
                Check Mandatory All / Deselect Mandatory All - Quick App
            </label>
        </div>
    </div>
    <div class="row noticeFF notice-danger text-left bg-gray-100 py-2 mb-2">
        <div class="col-sm-4 px-2">
            <label for="' . $sectionKey . 'BO"><input type="checkbox" class="CheckAllBO" name="CheckAll" id="' . $sectionKey . 'BO" value="' . $sectionKey . '"/>
                Check All / Deselect All (Deselect All Will Hide This Section) - Other Tabs/Back Office
            </label>
        </div>
        <div class="col-sm-4 px-2">
            <label for="' . $sectionKey . 'BO"><input type="checkbox" class="CheckAllBOM" name="CheckAll" id="' . $sectionKey . 'BOM" value="' . $sectionKey . '" ' . $disableCheckboxMandatory . '/> 
                Check Mandatory All / Deselect Mandatory All - Other Tabs/Back Office
            </label>
        </div>
    </div>
    
    <div class="clearfix"></div>
    
   <div class="row " >
   <div class="table-responsive">
<table id="' . cypher::myEncryption($sectionid) . '" class="table table-hover LWcustomTable table-bordered table-condensed table-sm table-vertical-center" width="100%">
<thead class="thead-light">
      <tr>
        <th width="20%" rowspan="2" colspan="1" class="align-left">Field Name</th>
        <th width="21%" rowspan="1" colspan="3" class="align-center">Display</th>
        <th  width="21%" rowspan="1" colspan="3" class="align-center">Mandatory</th>
        <th  width="20%" rowspan="2" colspan="1" class="align-center">File Type</th>
        <th  width="18%" rowspan="2" colspan="1" class="align-center">Loan Program</th>
      </tr>
      <tr>      
        <th class="align-center">Full App</th>
        <th class="align-center">Quick App</th>
        <th class="align-center">Other Tabs / Back Office</th>
        <th class="align-center">Full App</th>
        <th class="align-center">Quick App</th>
        <th class="align-center">Other Tabs / Back Office</th>
       </tr>
    </thead>
    <tbody>';
                $tempFieldsResultArray = $fieldsResultArray[$sectionKey] ?? [];
                $tempPCFieldsArray = [];
                foreach ($PCFieldsArray as $PCField) {
                    $fieldID = trim($PCField['fieldID']);
                    $tempPCFieldsArray[$fieldID] = $PCField;
                }
                $j = 0;
                foreach ($tempFieldsResultArray as $tempFieldsResult) {
                    if (!glCustomJobForProcessingCompany::showLoanInfoV2($pcid)
                        && in_array($tempFieldsResult['fieldName'], glCustomJobForProcessingCompany::LOANINFOV2_FORM_FIELDS)) {
                        continue;
                    }
                    $prFACls = $prBOCls = '';
                    $chkOpt = $chkManOpt = '';
                    $chkOptFA = $chkManOptFA = $chkManOptBO = $chkOptBO = '';
                    $chkEnableAllLPOpt = '';
                    $chkQAUniqueLPOpt = '';
                    $chkFAUniqueLPOpt = '';
                    $fldCls = '';
                    $fldIntent = '';
                    $prQACls = '';
                    $fileTypes = $loanPrg = '';
                    $fileTypeArr = $loanPrgArr = $fileloanPrgArr = [];
                    $fileQAloanPrgArr = [];
                    $fileFAloanPrgArr = [];
                    $resFields = [];
                    $fieldID = trim($tempFieldsResult['fieldID']);
                    $fieldLabel = htmlentities(trim($tempFieldsResult['fieldLabel']));
                    $fieldName = trim($tempFieldsResult['fieldName']);
                    $parentID = trim($tempFieldsResult['parentID']);
                    $toolTip = trim($tempFieldsResult['toolTip']);
                    $childLevel = trim($tempFieldsResult['childLevel']);
                    $isSubSection = trim($tempFieldsResult['isSubSection']);

                    // Story: https://app.clubhouse.io/lendingwise/story/1121/what-kind-of-loan-program-are-you-looking-for-inside-loan-programs-should-be-read-only
                    $isDisabled = (in_array($fieldName, $formFieldDisabledFields)) ? ' disabled ' : '';
                    $isDisabledFAQA = (in_array($fieldName, $formFieldDisabledFAQA) || in_array($sectionid, $formFieldsDisableFAQASections)) ? ' disabled ' : '';
                    $isDisabledMandatory = (in_array($fieldName, $formFieldDisabledFieldsMandatory)) ? ' disabled ' : '';
                    $cashFlowclass = (in_array($fieldName, $cashFlowFormFields)) ? ' disableorenableCashflowFields' : '';

                    $propInfoIsDisabled = (
                        in_array($fieldName, glPropertyDetailsFormFields::$glPropertyDetailsFormFields)
                        || in_array($fieldName, glLoanSetupFormFields::$glLoanSetupFormFields)
                    ) ? ' disabled ' : '';

                    $pvIsDisabled = (in_array($fieldName, $pvFormFields) && $sectionKey == 'PV') ? ' disabled ' : '';
                    $sectionFAQADisable = (in_array($sectionKey, glDisableFAQAOptionInFF::$glDisableFAQAOptionInFF)) ? ' d-none ' : '';
                    $mandatoryDisable = (in_array($fieldName, glMandatoryDisableFormFields::$glMandatoryDisableFormFields)) ? ' d-none ' : '';

                    if ($fieldName == 'entityType' && $sectionKey == 'BEN' && in_array($fieldName, glPropertyDetailsFormFields::$glPropertyDetailsFormFields)) {
                        $propInfoIsDisabled = '';
                    }
                    if ($fieldName == 'HOAFees1' && ($sectionKey == 'BME' || $sectionKey == '1LMS') && in_array($fieldName, glPropertyDetailsFormFields::$glPropertyDetailsFormFields)) {
                        $propInfoIsDisabled = '';
                    }

                    if ($sectionKey == 'PV') {
                        $propInfoIsDisabled = '';
                    }

                    if ($parentID > 0) {
                        $fldIntent = '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;';
                        $prFACls = 'PR_' . $parentID . '_FA';
                        $prQACls = 'PR_' . $parentID . '_QA';
                        $prBOCls = 'PR_' . $parentID . '_BO';
                    }
                    if ($childLevel == '1') {
                        $fldIntent = '&nbsp;&nbsp;&nbsp;';
                    }
                    if ($childLevel == '2') {
                        $fldIntent = '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp';
                    }

                    $chkQALPOpt = '';

                    if (isset($tempPCFieldsArray[$fieldID])) {
                        if (($tempPCFieldsArray[$fieldID]['FADisplay']) == 1) $chkOptFA = 'checked';
                        if (($tempPCFieldsArray[$fieldID]['FAMandatory']) == 1) $chkManOptFA = 'checked';
                        if (($tempPCFieldsArray[$fieldID]['BODisplay']) == 1) $chkOptBO = 'checked';
                        if (($tempPCFieldsArray[$fieldID]['BOMandatory']) == 1) $chkManOptBO = 'checked';
                        if (($tempPCFieldsArray[$fieldID]['QADisplay']) == 1) $chkOpt = 'checked';
                        if (($tempPCFieldsArray[$fieldID]['mandatory']) == 1) $chkManOpt = 'checked';
                        if (($tempPCFieldsArray[$fieldID]['enableAllLoanPrograms']) == 1) $chkEnableAllLPOpt = 'checked';
                        if (($tempPCFieldsArray[$fieldID]['enableQAUniqueLoanPrograms']) == 1) {
                            $chkQAUniqueLPOpt = ' checked ';
                        } else {
                            $chkQALPOpt = ' d-none ';
                        }
                        if (($tempPCFieldsArray[$fieldID]['enableFAUniqueLoanPrograms']) == 1) {
                            $chkFAUniqueLPOpt = ' checked ';
                            $chkFALPOpt = '';
                        } else {
                            $chkFALPOpt = ' d-none ';
                        }
                        $fieldLabel = htmlentities(trim($tempPCFieldsArray[$fieldID]['fieldLabel']));
                        $fieldToolTip = htmlentities(trim($tempPCFieldsArray[$fieldID]['toolTip']));
                        $fileTypes = trim($tempPCFieldsArray[$fieldID]['fileType']);
                        $loanPrg = trim($tempPCFieldsArray[$fieldID]['loanProgram']);
                        $FAloanPrg = trim($tempPCFieldsArray[$fieldID]['FALoanPrograms']);
                        $QAloanPrg = trim($tempPCFieldsArray[$fieldID]['QALoanPrograms']);
                        $fileTypeArr = explode(',', $fileTypes);
                        if ($loanPrg != '') {
                            $fileloanPrgArr = explode(',', $loanPrg);
                        }
                        if ($FAloanPrg != '') {
                            $fileFAloanPrgArr = explode(',', $FAloanPrg);
                        }
                        if ($QAloanPrg != '') {
                            $fileQAloanPrgArr = explode(',', $QAloanPrg);
                        }
                        $loanPrgArr = getLoanPrograms::getReport($fileTypeArr, $pcid);
                    }
                    $datapk = cypher::myEncryption($assignedPCID . '-' . $fieldID);
                    $dataSubSectionpk = cypher::myEncryption($assignedPCID . '-' . $fieldID . '-' . $isSubSection);
                    if (isset($glRestrictCustomFields[$assignedPCID])) { // https://www.pivotaltracker.com/story/show/161218497
                        $resFields = $glRestrictCustomFields[$assignedPCID];
                    }
                    /*story - 27792 display Do you have a business entity? only for desired PC*/

                    if ($pcid != glPCID::PCID_G1CommMort && $fieldName == 'borrowerUnderEntity') {
                        $BEClss = ' d-none ';
                    } else {
                        $BEClss = '';
                    }

                    $fieldDisplayVal = '';
                    $fileTypesArray = explode(',', $fileTypes);
                    if (count(array_intersect(array_column($modulesArray, 'moduleCode'), $fileTypesArray)) == 0 && $fileTypes != '') {
                        $fieldDisplayVal = ' d-none ';
                    }

                    $color = ($j % 2 == 0) ? 'even' : '';
                    if (!(in_array($fieldName, $glRestrictPCFields)) || in_array($fieldName, $resFields)) {
                        if (array_key_exists($fieldName, $glFormFieldsVisibleToPC) && !in_array($pcid, $glFormFieldsVisibleToPC[$fieldName])) {
                            continue;
                        }

                        if ($isSubSection == 1) {
                            $html .= '<tr 
                    title="' . $sectionHeadingForTitle . ' > ' . $fieldLabel . ' " 
                    id="FID_' . $fieldID . '" 
                    class="' . $color . '" style="background-color: #cee9f1;">
                    <td colspan="9">
                    <span
                    id="fieldLabel_' . $fieldID . '" 
                    style="color:#18719d;font-size:15px"
                     class="updateFieldLabel editable editable-click editable-open tip-bottom cursor-pointer"
                     data-input_name="' . $fieldName . '" 
                     data-type="text" 
                     data-pk="' . $datapk . '" 
                     data-value="' . $fieldLabel . '" 
                     data-title="SubSection"  
                      title="Click to change Subsection">' . $fieldLabel . '</span>
                      <span
                        id="subSectionTooltip_' . $fieldID . '"
                        data-type="textarea"
                        data-pk="' . htmlspecialchars($dataSubSectionpk) . '"
                        data-value="' . ($fieldToolTip) . '"
                        data-html="true"
                         style="border-bottom: none; display: inline-block;"
                        title="Click to edit Sub Section Tooltip"
                        class="updateSubSectionToolTip">
                        <i class="fa fa-info-circle text-primary popoverClass" 
                        title="Click to change Sub Section Tooltip"
                        aria-hidden="true" data-content="' . (nl2br($fieldToolTip)) . '"></i>
                        </span></td></tr>';
                        } else {
                            // https://www.pivotaltracker.com/story/show/161218497
                            $html .= '<tr class="' . $color . $BEClss . $fieldDisplayVal . '" id="FID_' . $fieldID . '">';

                            $html .= '<td class=" text-left ' . $fldCls . ' bbr">' . $fldIntent;
                            if ($toolTip != '') {
                                $html .= '<div class="left with-children-tip" style="margin-right: 5px;"><a class="fa fa-info-circle tip-bottom tooltipClass" style="text-decoration:none;" title="' . $toolTip . '"></a></div>';
                            }

                            $TacMandatoryAutoCheck = $TacBackOffice = '';
                            if ($sectionKey == 'TAC') {
                                if (in_array($fieldName, ['agreeTC', 'isEsignRequired'])) {
                                    $TacBackOffice = ' tacBackofficeDisabled ';
                                    $TacMandatoryAutoCheck = ' tacMandatoryAutoCheck ';
                                }
                            }

                            $html .= '
                    <span
                     id="fieldLabel_' . $fieldID . '"
                      class="updateFieldLabel editable editable-click editable-open tip-bottom cursor-pointer" 
                      data-type="text" 
                      data-pk="' . $datapk . '"
                       data-value="' . $fieldLabel . '"
                       data-input_name="' . $fieldName . '"
                        data-title="Field Label"   
                        title="Click to change Field Label">' . $fieldLabel . '</a>
                    </td>
                    <input type="hidden" name="fldSectionID" id="' . $fieldID . 'secID" value="' . $sectionKey . '">
                    <input type="hidden" name="SectionID" id="SectionID" value="' . $sectionKey . '">
                    <td  class="align-center">
                        <input type="checkbox" 
                            name="' . $fieldName . '" 
                            id="' . $fieldID . 'FA" 
                            class="checkFA' . $sectionKey . ' ' . $prFACls . $sectionFAQADisable . ' unCheckFA ' . $cashFlowclass . '_FA ' . $cashFlowclass . ' ' . $TacMandatoryAutoCheck . ' " ' . $chkOptFA . ' 
                            value="' . $fieldID . '" 
                            ' . $isDisabled . $isDisabledFAQA . ' data-is_disabled="' . $isDisabled . '"
                            ' . $propInfoIsDisabled . '  data-prop_info_is_disabled="' . $propInfoIsDisabled . '"
                            ' . $pvIsDisabled . ' data-pv_is_disabled="' . $pvIsDisabled . '"
                        >  
                     </td>
                    <td  class=" align-center">
                        <input type="checkbox"
                         name="' . $fieldName . '" 
                         id="' . $fieldID . 'QA"
                          class="check' . $sectionKey . ' ' . $prQACls . $sectionFAQADisable . ' unCheck ' . $cashFlowclass . '_QA ' . $cashFlowclass . ' ' . $TacMandatoryAutoCheck . ' " 
                          ' . $chkOpt . ' 
                          value="' . $fieldID . '" 
                            ' . $isDisabled . $isDisabledFAQA . ' data-is_disabled="' . $isDisabled . '"
                            ' . $propInfoIsDisabled . '  data-prop_info_is_disabled="' . $propInfoIsDisabled . '"
                            ' . $pvIsDisabled . ' data-pv_is_disabled="' . $pvIsDisabled . '"
                         >
                      </td>
                    <td class=" align-center">
                    <input type="checkbox"
                     name="BO_' . $fieldName . '" 
                     id="' . $fieldID . 'BO" 
                     class=" checkBO' . $sectionKey . ' ' . $prBOCls . ' dispBO ' . $cashFlowclass . '_BO ' . $cashFlowclass . ' ' . $TacBackOffice . ' " ' . $chkOptBO . ' 
                     value="' . $fieldID . '" 
                            ' . $isDisabled . ' data-is_disabled="' . $isDisabled . '"
                     ></td>
                    <td  class=" align-center">
                    ';
                            if ($fieldName != 'addNewBroker' && $fieldName != 'lien1Terms') {
                                $html .= '<input type="checkbox" name="' . $fieldName . '" id="' . $fieldID . 'FAMandatory" class="checkFA' . $sectionKey . 'Man Mandatory ' . $sectionFAQADisable . $mandatoryDisable . '" ' . $chkManOptFA . ' value="' . $fieldID . '" ';
                                if (in_array($sectionKey, ['ACF', 'PV', 'HOA', 'LEIN', 'IDI', 'LS'])) {
                                    $html .= ($pcid == glPCID::PCID_PROD_CV3 && $fieldName == 'CORTotalLoanAmt') ? '' : 'disabled';
                                }
                                $html .= ' ' . $isDisabled . $isDisabledFAQA . $isDisabledMandatory . ' >';
                            }
                            $html .= '</td>';
                            $html .= '<td   class=" align-center">';
                            if ($fieldName != 'addNewBroker' && $fieldName != 'lien1Terms') {
                                $html .= '<input type="checkbox" name="' . $fieldName . '" id="' . $fieldID . 'QAMandatory" class="check' . $sectionKey . 'Man Mandatory ' . $sectionFAQADisable . $mandatoryDisable . '" ' . $chkManOpt . ' value="' . $fieldID . '"';
                                if (in_array($sectionKey, ['ACF', 'PV', 'HOA', 'LEIN', 'IDI', 'LS'])) {
                                    $html .= ($pcid == glPCID::PCID_PROD_CV3 && $fieldName == 'CORTotalLoanAmt') ? '' : 'disabled';
                                }
                                $html .= ' ' . $isDisabled . $isDisabledFAQA . $isDisabledMandatory . '>';
                            }
                            $html .= '</td>';
                            $html .= '<td  class="align-center"><input type="checkbox" name="BO_' . $fieldName . '_M" id="' . $fieldID . 'BOMandatory" class="checkBOM' . $sectionKey . ' ' . $TacBackOffice . ' ' . 'Man Mandatory ' . $mandatoryDisable . ' " ' . $chkManOptBO . $sectionFAQADisable . ' value="' . $fieldID . '"';
                            if ($sectionKey == 'ACF') {
                                $html .= 'disabled';
                            }
                            $html .= '' . $isDisabled . $isDisabledMandatory . ' ></td>';
                            $html .= '<td  ><select class="chzn-select' . $sectionKey . ' odd" data-placeholder="Select Module Types" name="fileModuleTypes[]" onchange="getLoanProgramData(this.id,\'' . $pcid . '\',\'' . $fieldID . '\' ,\'' . $loanPrg . '\',\'' . $fileTypes . '\',\'fieldselect\')" id="fileModule_' . $fieldID . '" tabindex="' . ($tabIndex++) . '" multiple style="width:100%;" >';
                            $pcmoduleCodeInfo = [];
                            $seen = [];
                            foreach ($modulesArray as $module) {
                                $moduleCode = trim($module['moduleCode']);
                                $moduleName = trim($module['moduleName']);
                                if (in_array($moduleCode, $seen)) {
                                    continue;
                                }
                                $seen[] = $moduleCode;
                                $sel = '';
                                $pcmoduleCodeInfo[] = $moduleCode;
                                if (in_array($moduleCode, $fileTypeArr)) $sel = ' selected ';

                                $html .= '<option value="' . $moduleCode . '" ' . $sel . ' >' . trim($moduleName) . '</option>';
                            }
                            $html .= '	</select></td>';

                            $html .= '<td   class="text-left">
<div class="d-inline">
<label><input type="checkbox" name="' . $fieldName . '_LP" id="' . $fieldID . '_LP" class="checkALP' . $sectionKey . ' clearLP" ' . $chkEnableAllLPOpt . ' > Enable All Loan Programs </label>

 <label><input type="checkbox" name="' . $fieldName . '_selctAllLP" id="' . $fieldID . '_selctAllLP" class="selctAllLP" onclick="getLoanProgramData(\'fileModule_' . $fieldID . '\',\'' . $pcid . '\',\'' . $fieldID . '\',\'' . $loanPrg . '\',\'' . $fileTypes . '\',\'selectall\')" > Select All loanPrograms
 </label>
    <select class="fileLoanPrg chzn-select_' . $sectionKey . ' odd" data-placeholder="Select Loan Program"  name="fileLoanPrg[]"  id="fileLoanPrg_' . $fieldID . '" tabindex="' . $tabIndex++ . '" multiple style="width:100%;" >';
                            $selectedText = '';
                            foreach ($loanPrgArr as $loanKey => $loanVal) {
                                if (in_array($loanVal[0]['moduleCode'], $pcmoduleCodeInfo)) {
                                    $html .= ' <option class="optnGrp" disabled value="">' . $loanKey . '</option>';
                                    foreach ($loanVal as $loanVals) {
                                        $sel = '';
                                        if (count($fileloanPrgArr) > 0) {
                                            if (in_array(trim($loanVals['STCode']), $fileloanPrgArr))
                                                $sel = ' selected ';
                                        }

                                        if ($sel != '') {
                                            $selectedText .= trim($loanVals['serviceType']) . ',';
                                        }
                                        $csscolor = 'black';
                                        $loanProgram = trim($loanVals['serviceType']);
                                        $displayOption = '';
                                        if ($loanVals['STCode'] == 'TBD') {
                                            $displayOption = 'display:none;';
                                        }
                                        if (trim($loanVals['internalLoanProgram']) == 1) {
                                            $loanProgram = $loanProgram . ' *Internal';
                                            $csscolor = 'red';
                                        }
                                        $html .= '<option value="' . $loanVals['STCode'] . '" style="color:' . $csscolor . ';' . $displayOption . '" ' . $sel . ' >' . $loanProgram . '</option>';
                                    }
                                }
                            }
                            $html .= '</select>
<div id = "div_fileLoanPrg_' . $fieldID . '" title="' . $selectedText . '">
<span id="spanText_' . $fieldID . '">' . substr($selectedText, 0, 20) . '...</span>
<a href="javascript:void(0)" class="clickToShowLoanProgram" show-status="0" data-id="fileLoanPrg_' . $fieldID . '">Click To View LoanPrograms</a>
						</div>
						
						<label> <input type="checkbox" 
						class ="enableUniqueLoanPrograms enableQAUniqueLoanPrograms' . $sectionKey . '" 
						' . $chkQAUniqueLPOpt . '  
						name="' . $fieldName . '_enableQAUniqueLoanPrograms" 
						id="QA_' . $fieldID . '_enableQAUniqueLoanPrograms" > QA Unique Loan Programs </label>
						<select class="fileQALoanPrg chzn-select_QA_' . $sectionKey . $chkQALPOpt . ' odd" 
						data-placeholder="Select QA Unique Loan Program"  name="fileQALoanPrg[]"  
						id="fileQALoanPrg_' . $fieldID . '" tabindex="' . $tabIndex++ . '" multiple style="width:100%;"  ' . $isDisabled . '>';

                            foreach ($loanPrgArr as $loanKey => $loanVal) {
                                if (in_array($loanVal[0]['moduleCode'], $pcmoduleCodeInfo)) {
                                    $html .= ' <option class="optnGrp" disabled value="">' . $loanKey . '</option>';
                                    foreach ($loanVal as $loanVals) {
                                        $sel = '';
                                        if (count($fileQAloanPrgArr) > 0) {
                                            if (in_array(trim($loanVals['STCode']), $fileQAloanPrgArr))
                                                $sel = ' selected ';
                                        }

                                        $csscolor = 'black';
                                        $displayOption = '';
                                        if ($loanVals['STCode'] == 'TBD') {
                                            $displayOption = 'display:none;';
                                        }
                                        $loanProgram = trim($loanVals['serviceType']);
                                        if (trim($loanVals['internalLoanProgram']) == 1) {
                                            $loanProgram = $loanProgram . ' *Internal';
                                            $csscolor = 'red';
                                        }
                                        $html .= '<option value="' . $loanVals['STCode'] . '" 
                                style="color:' . $csscolor . ';' . $displayOption . '" ' . $sel . ' >' . $loanProgram . '</option>';
                                    }
                                }
                            }
                            $html .= '</select>

<label> <input type="checkbox" 
class ="enableUniqueLoanPrograms enableFAUniqueLoanPrograms"' . $sectionKey . ' ' . $chkFAUniqueLPOpt . ' 
name="' . $fieldName . '_enableFAUniqueLoanPrograms" id="FA_' . $fieldID . '_enableFAUniqueLoanPrograms" > FA Unique Loan Programs </label>
						<select class="fileFALoanPrg chzn-select_FA_' . $sectionKey . $chkFALPOpt . ' odd" 
						data-placeholder="Select FA Unique Loan Program"  name="fileFALoanPrg[]"  
						id="fileFALoanPrg_' . $fieldID . '" tabindex="' . $tabIndex++ . '" multiple style="width:100%;"  ' . $isDisabled . '>';

                            foreach ($loanPrgArr as $loanKey => $loanVal) {
                                if (in_array($loanVal[0]['moduleCode'], $pcmoduleCodeInfo)) {
                                    $html .= ' <option class="optnGrp" disabled value="">' . $loanKey . '</option>';
                                    foreach ($loanVal as $loanVals) {
                                        $sel = '';
                                        if (count($fileFAloanPrgArr) > 0) {
                                            if (in_array(trim($loanVals['STCode']), $fileFAloanPrgArr))
                                                $sel = ' selected ';
                                        }

                                        $csscolor = 'black';
                                        $displayOption = '';
                                        if ($loanVals['STCode'] == 'TBD') {
                                            $displayOption = 'display:none;';
                                        }
                                        $loanProgram = trim($loanVals['serviceType']);
                                        if (trim($loanVals['internalLoanProgram']) == 1) {
                                            $loanProgram = $loanProgram . ' *Internal';
                                            $csscolor = 'red';
                                        }
                                        $html .= '<option value="' . $loanVals['STCode'] . '" 
                                style="color:' . $csscolor . ';' . $displayOption . '" ' . $sel . ' >' . $loanProgram . '</option>';
                                    }
                                }
                            }
                            $html .= '</select>
						
						</div>
						</td>';

                            $html .= '</tr>';

                        }
                    }
                    $j++;
                }
                $html .= ' </tbody>
             </table>
             </div>
</div>';
                $html .= '</div>';

                $html .= '<div style="text-align: center;"><input type="button" disabled class= "btn btn-primary savecustomdata" value = "Save" id="save_' . $sectionKey . '" >  <input type="button" class= "btn btn-danger resetSection ' . $hideResetToDefault . '" value = "Reset to Default" id="' . $sectionKey . '_Reset" ></div>';
                $finalresult[$sectionKey] = $html;
            }
        } else {
            $modulesResultArray = $fieldsResultArray['moduleInfo'] ?? [];
            foreach ($fieldsResultArray['PCSectionHeadings'] ?? [] as $fieldsResult) {
                $sectionKey = $fieldsResult['sectionID'];
                $sectionHeadingForTitle = htmlentities($fieldsResult['sectionHeading']);
                $html = '<div class="row text-left bg-gray-100 px-2 py-2 mb-2">
					<label for="' . $sectionKey . 'FA">	<input type="checkbox" class="CheckAllFA" name="CheckAll" id="' . $sectionKey . 'FA" value="' . $sectionKey . '"/> Check All / Deselect All to Hide This Section - Full App</label>
					</div>
					<div class="row text-left bg-gray-100 px-2 py-2 mb-2">
							<label for="' . $sectionKey . 'FA">	<input type="checkbox" class="CheckAllQA" name="CheckAll" id="' . $sectionKey . '" value="' . $sectionKey . '"/> Check All / Deselect All to Hide This Section - Quick App</label>
					</div>
					<div class="row text-left bg-gray-100 px-2 py-2 mb-2">
						<label for="' . $sectionKey . 'BO"> <input type="checkbox" class="CheckAllBO" name="CheckAll" id="' . $sectionKey . 'BO" value="' . $sectionKey . '"/> Check All / Deselect All to Hide This Section - Back Office</label>
					</div>
                    <div class="row text-left bg-gray-100 px-2 py-2 mb-2">
						<label for="' . $sectionKey . 'ALP"> <input type="checkbox" class="CheckAllALP" name="CheckAll" id="' . $sectionKey . 'ALP" value="' . $sectionKey . '"/> Check All / Deselect All - All Loan Programs</label>
					</div>
        
        <div class="row " >
        <div class="table-responsive " >
<table  class="table table-hover LWcustomTable table-bordered table-condensed table-sm table-vertical-center" width="100%">
<thead class="thead-light">
      <tr class="align-center">
        <th width="20%" rowspan="2" colspan="1" class="align-left">Field Name</th>
        <th width="21%" rowspan="1" colspan="3" class="align-center">Display</th>';
                if ($sectionKey != 'ACF') {
                    $html .= '<th  width="21%" rowspan="1" colspan="3" class="align-center">Mandatory</th>';
                }
                $html .= '<th  width="20%" rowspan="2" colspan="1" class="align-center">File Type</th>
        <th  width="18%" rowspan="2" colspan="1" class="align-center">Loan Program</th>
      </tr>
      <tr>      
      <th class="align-center">FA</th>
      <th class="align-center">QA</th>
      <th class="align-center">BO</th>';
                if ($sectionKey != 'ACF') {
                    $html .= '<th class="align-center">FA</th>
      <th class="align-center">QA</th>
      <th class="align-center">BO</th>';
                }
                $html .= '</tr>
    </thead>
    <tbody>';
                if (count($fieldsResultArray) > 0) {
                    if (array_key_exists($sectionKey, $fieldsResultArray)) {
                        $tempFieldsResultArray = $fieldsResultArray[$sectionKey] ?? [];
                        $j = 0;
                        foreach ($tempFieldsResultArray as $tempFieldsResult) {
                            $prFACls = $prBOCls = '';
                            $chkOpt = $chkManOpt = '';
                            $chkOptFA = $chkManOptFA = $chkManOptBO = $chkOptBO = '';
                            $fldCls = '';
                            $fldIntent = '';
                            $prQACls = '';
                            $fileloanPrgArr = [];
                            $chkEnableAllLPOpt = '';
                            $chkQAUniqueLPOpt = '';
                            $chkFAUniqueLPOpt = '';
                            $fileQAloanPrgArr = [];
                            $fileFAloanPrgArr = [];

                            $fieldID = trim($tempFieldsResult['fieldID']);
                            $fieldLabel = htmlentities(trim($tempFieldsResult['fieldLabel']));
                            $fieldName = trim($tempFieldsResult['fieldName']);
                            $parentID = trim($tempFieldsResult['parentID']);
                            $toolTip = trim($tempFieldsResult['toolTip']);
                            $fileTypes = trim($tempFieldsResult['fileType']);
                            $loanPrg = trim($tempFieldsResult['loanProgram']);
                            $childLevel = trim($tempFieldsResult['childLevel']);
                            $isSubSection = trim($tempFieldsResult['isSubSection']);


                            // Story: https://app.clubhouse.io/lendingwise/story/1121/what-kind-of-loan-program-are-you-looking-for-inside-loan-programs-should-be-read-only
                            $isDisabled = (in_array($fieldName, $formFieldDisabledFields)) ? ' disabled ' : '';
                            $isDisabledFAQA = (in_array($fieldName, $formFieldDisabledFAQA) || in_array($sectionid, $formFieldsDisableFAQASections)) ? ' disabled ' : '';
                            $isDisabledMandatory = (in_array($fieldName, $formFieldDisabledFieldsMandatory)) ? ' disabled ' : '';
                            $cashFlowclass = (in_array($fieldName, $cashFlowFormFields)) ? ' disableorenableCashflowFields' : '';

                            $fileTypeArr = explode(',', $fileTypes);
                            if ($loanPrg != '') {
                                $fileloanPrgArr = explode(',', $loanPrg);
                            }

                            $loanPrgArr = getLoanPrograms::getReport($fileTypeArr, $pcid);  //echo '<pre>'; print_r($loanProgramsresult);

                            if ($parentID > 0) {
                                $fldIntent = '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;';
                                $prFACls = 'PR_' . $parentID . '_FA';
                                $prQACls = 'PR_' . $parentID . '_QA';
                                $prBOCls = 'PR_' . $parentID . '_BO';
                            }

                            if ($childLevel == '1') {
                                $fldIntent = '&nbsp;&nbsp;&nbsp;';
                            }
                            if ($childLevel == '2') {
                                $fldIntent = '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp';
                            }


                            if ($tempFieldsResult['QADefaultShow'] == 1) $chkOpt = 'checked';
                            if ($tempFieldsResult['FADefaultShow'] == 1) $chkOptFA = 'checked';
                            if ($tempFieldsResult['BODefaultShow'] == 1) $chkOptBO = 'checked';

                            if ($tempFieldsResult['FADefaultMandatory'] == 1) $chkManOptFA = 'checked';
                            if ($tempFieldsResult['QADefaultMandatory'] == 1) $chkManOpt = 'checked';
                            if ($tempFieldsResult['BODefaultMandatory'] == 1) $chkManOptBO = 'checked';
                            if (($tempFieldsResult['enableAllLoanPrograms']) == 1) $chkEnableAllLPOpt = 'checked';
                            if (($tempFieldsResult['enableQAUniqueLoanPrograms']) == 1) {
                                $chkQAUniqueLPOpt = ' checked ';
                                $chkQALPOpt = '';
                            } else {
                                $chkQALPOpt = ' d-none ';
                            }
                            if (($tempFieldsResult['enableFAUniqueLoanPrograms']) == 1) {
                                $chkFAUniqueLPOpt = ' checked ';
                                $chkFALPOpt = '';
                            } else {
                                $chkFALPOpt = ' d-none ';
                            }
                            $FAloanPrg = trim($tempFieldsResult['FALoanPrograms']);
                            $QAloanPrg = trim($tempFieldsResult['QALoanPrograms']);
                            if ($FAloanPrg != '') {
                                $fileFAloanPrgArr = explode(',', $FAloanPrg);
                            }
                            if ($QAloanPrg != '') {
                                $fileQAloanPrgArr = explode(',', $QAloanPrg);
                            }

                            $datapk = cypher::myEncryption('0-' . $fieldID);

                            $color = ($j % 2 == 0) ? 'even' : '';
                            if ($isSubSection == 1) {
                                $html .= '<tr 
                        title="' . $sectionHeadingForTitle . ' > ' . $fieldLabel . ' " 
                        id="FID_' . $fieldID . '" 
                        class="' . $color . '" 
                        style="background-color: #cee9f1;">
                        <td colspan="9">
                        <span  
                        id="fieldLabel_' . $fieldID . '" 
                        style="color:#18719d;font-size:15px" 
                        class="updateFieldLabel editable editable-click editable-open tip-bottom cursor-pointer" 
                        data-type="text"
                         data-pk="' . $datapk . '" 
                         data-value="' . $fieldLabel . '" 
                         data-title="SubSection"   
                         data-input_name="' . $fieldName . '"
                         title="Click to change Subsection">' . $fieldLabel . '</span></td></tr>';
                            } else {
                                $html .= '<tr id="FID_' . $fieldID . '" class="' . $color . '">';
                                $html .= '<td class="text-left ' . $fldCls . ' bbr">' . $fldIntent;
                                if ($toolTip != '') {
                                    $html .= '
<div class="left with-children-tip" style="margin-right: 5px;"><a class="fa fa-info-circle tip-bottom tooltipClass" style="text-decoration:none;" title="' . $toolTip . '"></a>
    </div>';
                                }

                                $html .= '<span id="fieldLabel_' . $fieldID . '">' . $fieldLabel . '</span></td>
                    <input type="hidden" name="fldSectionID" id="' . $fieldID . 'secID" value="' . $sectionKey . '">';
                                $html .= '<td class="align-center"><input type="checkbox" name="' . $fieldName . '" id="' . $fieldID . 'FA" class="checkFA' . $sectionKey . ' ' . $prFACls . ' unCheckFA ' . $cashFlowclass . '_FA ' . $cashFlowclass . ' " ' . $chkOptFA . ' value="' . $fieldID . '" ' . $isDisabled . $isDisabledFAQA . ' ></td>';// td-2 end
                                $html .= '<td class="align-center"><input type="checkbox" name="' . $fieldName . '" id="' . $fieldID . 'QA" class="check' . $sectionKey . ' ' . $prQACls . ' unCheck ' . $cashFlowclass . '_QA ' . $cashFlowclass . ' " ' . $chkOpt . ' value="' . $fieldID . '" ' . $isDisabled . $isDisabledFAQA . ' ></td>';// td-3 end
                                $html .= '<td class="align-center"><input type="checkbox" name="BO_' . $fieldName . '" id="' . $fieldID . 'BO" class="checkBO' . $sectionKey . ' ' . $prBOCls . ' dispBO ' . $cashFlowclass . '_BO ' . $cashFlowclass . ' " ' . $chkOptBO . ' value="' . $fieldID . '" ' . $isDisabled . ' ></td>';// td-4 end

                                if ($sectionKey != 'ACF') {
                                    $html .= '<td class="align-center">';
                                    if ($fieldName != 'addNewBroker' && $fieldName != 'lien1Terms') {
                                        $html .= '<input type="checkbox" name="' . $fieldName . '" id="' . $fieldID . 'FAMandatory" class="checkFA' . $sectionKey . 'Man Mandatory" ' . $chkManOptFA . ' value="' . $fieldID . '"' . $isDisabled . $isDisabledFAQA . $isDisabledMandatory . '>';
                                    }
                                    $html .= '</td>';
                                    $html .= '<td class="align-center">';
                                    if ($fieldName != 'addNewBroker' && $fieldName != 'lien1Terms') {
                                        $html .= '<input type="checkbox" name="' . $fieldName . '" id="' . $fieldID . 'QAMandatory" class="check' . $sectionKey . 'Man Mandatory"' . $chkManOpt . ' value="' . $fieldID . '"' . $isDisabled . $isDisabledFAQA . $isDisabledMandatory . '>';
                                    }

                                    $html .= '</td>';
                                    $html .= '<td class="align-center">
<input type="checkbox" name="BO_' . $fieldName . '_M" id="' . $fieldID . 'BOMandatory" class="checkBO' . $sectionKey . 'Man Mandatory" ' . $chkManOptBO . ' value="' . $fieldID . '" ' . $isDisabled . $isDisabledMandatory . '>
</td>';
                                }
                                $html .= '<td class="">
            <select class="chzn-select' . $sectionKey . ' odd" data-placeholder="Select Module Types" 
 onchange="getLoanProgramData(this.id,\'' . $pcid . '\',\'' . $fieldID . '\',\'' . $loanPrg . '\',\'' . $fileTypes . '\',\'fieldselect\' )" name="fileModuleTypes[]" id="fileModule_' . $fieldID . '" tabindex=" ' . $tabIndex++ . '" multiple style="width:100%;' . $isDisabled . '">';

                                $pcmoduleCodeInfo = [];
                                foreach ($modulesResultArray as $mocVal) {
                                    $html .= ' <optgroup label="' . $mocVal['moduleCode'] . '">';
                                    $sel = '';
                                    if (in_array(trim($mocVal['moduleCode']), $fileTypeArr)) $sel = ' selected ';
                                    $pcmoduleCodeInfo[] = $mocVal['moduleCode'];
                                    $html .= '<option value="' . $mocVal['moduleCode'] . '" ' . $sel . ' >' . trim($mocVal['moduleName']) . '</option>';
                                }
                                $html .= '</select>
</td>';
                                $html .= '<td class="text-left">
<div class="d-inline">
<label><input type="checkbox" name="' . $fieldName . '_LP" id="' . $fieldID . '_LP" class="checkALP' . $sectionKey . ' clearLP" ' . $chkEnableAllLPOpt . ' > Enable All Loan Programs </label>
 
<label><input type="checkbox" name="' . $fieldName . '_selctAllLP" id="' . $fieldID . '_selctAllLP" class="selctAllLP" onclick="getLoanProgramData(\'fileModule_' . $fieldID . '\',\'' . $pcid . '\',\'' . $fieldID . '\',\'' . $loanPrg . '\',\'' . $fileTypes . '\',\'selectall\')" > Select All loanPrograms</label>
<select class="fileLoanPrg chzn-select_' . $sectionKey . ' odd" data-placeholder="Select Loan Program"  name="fileLoanPrg[]"  id="fileLoanPrg_' . $fieldID . '" tabindex="' . $tabIndex++ . '" multiple style="width:100%;"  ' . $isDisabled . '>';

                                $selectedText = '';
                                foreach ($loanPrgArr as $loanKey => $loanVal) {
                                    if (in_array($loanVal[0]['moduleCode'], $pcmoduleCodeInfo)) {
                                        $html .= ' <option class="optnGrp" disabled value="">' . $loanKey . '</option>';
                                        foreach ($loanVal as $loanVals) {
                                            $sel = '';
                                            if (count($fileloanPrgArr) > 0) {
                                                if (in_array(trim($loanVals['STCode']), $fileloanPrgArr))
                                                    $sel = ' selected ';
                                            }

                                            if ($sel != '') {
                                                $selectedText .= trim($loanVals['serviceType']) . ',';
                                            }
                                            $csscolor = 'black';
                                            $displayOption = '';
                                            if ($loanVals['STCode'] == 'TBD') {
                                                $displayOption = 'display:none;';
                                            }
                                            $loanProgram = trim($loanVals['serviceType']);
                                            if (trim($loanVals['internalLoanProgram']) == 1) {
                                                $loanProgram = $loanProgram . ' *Internal';
                                                $csscolor = 'red';
                                            }
                                            $html .= '<option value="' . $loanVals['STCode'] . '" style="color:' . $csscolor . ';' . $displayOption . '" ' . $sel . ' >' . $loanProgram . '</option>';
                                        }
                                    }
                                }
                                $html .= '</select>
<div id = "div_fileLoanPrg_' . $fieldID . '" title="' . $selectedText . '"><span id="spanText_' . $fieldID . '">' . substr($selectedText, 0, 20) . '...</span><a href="javascript:void(0)" class="clickToShowLoanProgram" show-status="0" data-id="fileLoanPrg_' . $fieldID . '">Click To View LoanPrograms</a>
						</div>
						
						<label> <input type="checkbox" 
						class ="enableUniqueLoanPrograms enableQAUniqueLoanPrograms' . $sectionKey . '" 
						' . $chkQAUniqueLPOpt . '  
						name="' . $fieldName . '_enableQAUniqueLoanPrograms" 
						id="QA_' . $fieldID . '_enableQAUniqueLoanPrograms" > QA Unique Loan Programs </label>
						<select class="fileQALoanPrg chzn-select_QA_' . $sectionKey . $chkQALPOpt . ' odd" 
						data-placeholder="Select QA Unique Loan Program"  name="fileQALoanPrg[]"  
						id="fileQALoanPrg_' . $fieldID . '" tabindex="' . $tabIndex++ . '" multiple style="width:100%;"  ' . $isDisabled . '>';

                                foreach ($loanPrgArr as $loanKey => $loanVal) {
                                    if (in_array($loanVal[0]['moduleCode'], $pcmoduleCodeInfo)) {
                                        $html .= ' <option class="optnGrp" disabled value="">' . $loanKey . '</option>';
                                        foreach ($loanVal as $loanVals) {
                                            $sel = '';
                                            if (count($fileQAloanPrgArr) > 0) {
                                                if (in_array(trim($loanVals['STCode']), $fileQAloanPrgArr))
                                                    $sel = ' selected ';
                                            }

                                            $csscolor = 'black';
                                            $displayOption = '';
                                            if ($loanVals['STCode'] == 'TBD') {
                                                $displayOption = 'display:none;';
                                            }
                                            $loanProgram = trim($loanVals['serviceType']);
                                            if (trim($loanVals['internalLoanProgram']) == 1) {
                                                $loanProgram = $loanProgram . ' *Internal';
                                                $csscolor = 'red';
                                            }
                                            $html .= '<option value="' . $loanVals['STCode'] . '" 
                                style="color:' . $csscolor . ';' . $displayOption . '" ' . $sel . ' >' . $loanProgram . '</option>';
                                        }
                                    }
                                }
                                $html .= '</select>

<label> <input type="checkbox" 
class ="enableUniqueLoanPrograms enableFAUniqueLoanPrograms"' . $sectionKey . ' ' . $chkFAUniqueLPOpt . ' 
name="' . $fieldName . '_enableFAUniqueLoanPrograms" id="FA_' . $fieldID . '_enableFAUniqueLoanPrograms" > FA Unique Loan Programs </label>
						<select class="fileFALoanPrg chzn-select_FA_' . $sectionKey . $chkFALPOpt . ' odd" 
						data-placeholder="Select FA Unique Loan Program"  name="fileFALoanPrg[]"  
						id="fileFALoanPrg_' . $fieldID . '" tabindex="' . $tabIndex++ . '" multiple style="width:100%;"  ' . $isDisabled . '>';

                                foreach ($loanPrgArr as $loanKey => $loanVal) {
                                    if (in_array($loanVal[0]['moduleCode'], $pcmoduleCodeInfo)) {
                                        $html .= ' <option class="optnGrp" disabled value="">' . $loanKey . '</option>';
                                        foreach ($loanVal as $loanVals) {
                                            $sel = '';
                                            if (count($fileFAloanPrgArr) > 0) {
                                                if (in_array(trim($loanVals['STCode']), $fileFAloanPrgArr))
                                                    $sel = ' selected ';
                                            }

                                            $csscolor = 'black';
                                            $displayOption = '';
                                            if ($loanVals['STCode'] == 'TBD') {
                                                $displayOption = 'display:none;';
                                            }
                                            $loanProgram = trim($loanVals['serviceType']);
                                            if (trim($loanVals['internalLoanProgram']) == 1) {
                                                $loanProgram = $loanProgram . ' *Internal';
                                                $csscolor = 'red';
                                            }
                                            $html .= '<option value="' . $loanVals['STCode'] . '" 
                                style="color:' . $csscolor . ';' . $displayOption . '" ' . $sel . ' >' . $loanProgram . '</option>';
                                        }
                                    }
                                }
                                $html .= '</select>
						</div>
</td>';
                                $html .= '</tr>';
                            }
                            $j++;
                        }
                    }
                }
                $html .= '</tbody>';
                $html .= '</table>
</div>
</div>

';
                $html .= '<div style="text-align: center;"><input type="button" disabled class= "btn btn-primary savecustomdata" value = "Save" id="save_' . $sectionKey . '" ></div>';
                $finalresult[$sectionKey] = $html;
            }
        }
        Database2::saveLogQuery();
        HTTP::ExitJSON($finalresult);
    }

    public static function Post()
    {
        self::Get();
    }
}