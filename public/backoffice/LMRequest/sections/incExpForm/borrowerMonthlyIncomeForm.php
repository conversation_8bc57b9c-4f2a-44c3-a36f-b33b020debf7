<?php

use models\constants\employedInfo1Array;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\CustomField;
use models\lendingwise\tblFile;
use models\PageVariables;
use models\standard\Currency;
use models\standard\Strings;

global $isHMLO, $tabIndex, $isLO, $netDefaultText, $primTotalHouseHoldExpenses, $rental1, $grossDefaultText, $netRental1, $primTotalGrossIncome;
global $socialSecurity1, $borSocialSecurity, $netSocialSecurity1, $pensionOrRetirement1, $netPensionOrRetirement1, $disability1, $netDisability1;
global $unemployment1, $netUnemployment1, $allowToEdit, $unemploymentStDate1, $earnedInterest1, $netEarnedInterest1, $capitalGains1, $partnership1;
global $otherHouseHold1, $otherIncomeDescription1, $roomRental1, $netRoomRental1, $secondJobIncome1, $netSecondJobIncome1, $employedInfo1Array;
global $secondJobStDate1, $sonOrDaughter1, $parents1, $childSupportOrAlimony1, $foodStampWelfare1, $primTotalHouseHoldIncome, $totalGrossMonthlyHouseHoldIncome;
global $primTotalNetHouseHoldIncome, $totalHouseHoldIncome, $HOAFees1, $creditCards1, $creditCardsBalance1, $creditCardsBalance1, $autoLoan1;
global $autoLoanBalance1, $unsecuredLoans1, $unsecuredLoanBalance1, $otherMortgage1, $otherMortgageBalance1, $studentLoans1, $studentLoansBalance1;
global $childSupportOrAlimonyMonthly1, $careAmt1, $allInsurance1, $groceries1, $carExpenses1, $medicalBill1, $entertainment1, $other1;
global $donation1, $pets1, $monthlyParking1, $unionDues1, $personalLoan1, $lunchPurchased1, $rentalExp1, $cable1, $electricity1, $natural1;
global $primaryBorrowerPhone, $water1, $internet1, $dryCleaning1, $utilityOther1, $houseHoldExpensesNotes;

loanForm::pushSectionID('BMI');
?>
<!-- borrowerMonthlyIncomeForm.php -->
<div class="card card-custom borrowerMonthlyIncomeInfo">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <?php echo loanForm::toggleSectionV2('borrowerMonthlyIncomeInfo', true, true, '', 'Borrower Monthly Income'); ?>
    </div>
    <div class="card-body borrowerMonthlyIncomeInfo_body">
        <div class="row">
            <div class="col-md-6 form-group <?php echo loanForm::showField('grossIncome1'); ?>">
                <?php echo loanForm::label(
                    'grossIncome1',
                    null,
                    (!($isLO == 1 || $isHMLO == 1)) ? 'Gross Income is considered the TOTAL amount you make before taxes, insurance, and any other deductions.<br>For self-employed borrower, Gross Income should be your Adjusted Gross Income-after all major fixed/variable expenses are paid. <br>Please use the Income Notes section to clarify your gross income.' : '',
                    null,
                    null,
                    ($isLO == 1 || $isHMLO == 1) ? 'Base Employment Income' : 'Gross Monthly Income?'
                ); ?>

                <?php echo loanForm::currency(
                    'grossIncome1',
                    true,
                    $tabIndex++,
                    Strings::showField('grossIncome1', 'incomeInfo'),
                    null,
                    ($isHMLO == 1) ? 'calculateHMLOBorrowerNetHouseholdIncome(\'loanModForm\', \'primTotalNetHouseHoldIncome\');' : 'calculatePrimaryNewTotalNetMonthlyIncome(this.value)',
                        '',
                ); ?>
            </div>
            <div class="col-md-6 form-group <?php echo loanForm::showField('commissionOrBonus1'); ?>">
                <?php echo loanForm::label('commissionOrBonus1', null, null, null, null, 'Commission/Bonus'); ?>

                <?php echo loanForm::currency(
                    'commissionOrBonus1',
                    true,
                    $tabIndex++,
                    Strings::showField('commissionOrBonus1', 'incomeInfo'),
                    null,
                    ($isHMLO == 1) ? 'calculateHMLOBorrowerNetHouseholdIncome(\'loanModForm\', \'primTotalNetHouseHoldIncome\');' : 'calculatePrimaryNewTotalNetMonthlyIncome(this.value)',
                        '',
                ); ?>
            </div>
        </div>
        <div class=" row">
            <div class="col-md-6 form-group <?php echo loanForm::showField('militaryIncome1'); ?>">
                <?php echo loanForm::label('militaryIncome1', null, null, null, null, 'Military Entitlements'); ?>

                <?php echo loanForm::currency(
                    'militaryIncome1',
                    true,
                    $tabIndex++,
                    Strings::showField('militaryIncome1', 'incomeInfo'),
                    null,
                    ($isHMLO == 1) ? 'calculateHMLOBorrowerNetHouseholdIncome(\'loanModForm\', \'primTotalNetHouseHoldIncome\');' : 'calculatePrimaryNewTotalNetMonthlyIncome(this.value)',
                        '',
                ); ?>

            </div>
            <div class="col-md-6 form-group <?php echo loanForm::showField('overtime1'); ?>">
                <?php echo loanForm::label('overtime1', null, null, null, null, 'Overtime'); ?>
                <?php echo loanForm::currency(
                    'overtime1',
                    true,
                    $tabIndex++,
                    Strings::showField('overtime1', 'incomeInfo'),
                    null,
                    ($isHMLO == 1) ? 'calculateHMLOBorrowerNetHouseholdIncome(\'loanModForm\', \'primTotalNetHouseHoldIncome\');' : 'calculatePrimaryNewTotalNetMonthlyIncome(this.value)',
                        '',
                ); ?>

            </div>
        </div>

        <div class=" row">

            <?php if ($isLO == 1 || $isHMLO == 1) { ?>
                <div class="col-md-6 form-group <?php echo loanForm::showField('netRental1'); ?>">
                    <?php echo loanForm::label('netRental1', null, null, null, null, 'Rental'); ?>

                    <?php if (!($isHMLO == 1)) { ?>

                        <?php echo loanForm::currency(
                            'rental1',
                            true,
                            $tabIndex++,
                            Strings::showField('rental1', 'incomeInfo'),
                            null,
                            'calculateHMLOBorrowerNetHouseholdIncome(this.value); putMyMsg(\'loanModForm\',\'rental1\',\'' . $grossDefaultText . '\');',
                                '',
                            null,
                            'clearMyMsg(\'loanModForm\',\'rental1\',\'' . $grossDefaultText . '\');'
                        ); ?>

                    <?php } ?>

                    <?php echo loanForm::currency(
                        'netRental1',
                        true,
                        $tabIndex++,
                        Strings::showField('netRental1', 'incomeInfo'),
                        null,
                        ($isHMLO == 1) ? 'calculateHMLOBorrowerNetHouseholdIncome(\'loanModForm\', \'primTotalNetHouseHoldIncome\');' : 'calculatePrimaryNewTotalNetMonthlyIncome(this.value)',
                            '',
                        null,
                        'clearMyMsg(\'loanModForm\',\'netRental1\',\'' . $grossDefaultText . '\');'
                    ); ?>
                </div>
            <?php } ?>
            <?php if (!($isLO == 1 || $isHMLO == 1)) { ?>
                <div class="col-md-6 form-group <?php echo loanForm::showField('federalTaxFICA1'); ?> ">
                    <?php echo loanForm::label('federalTaxFICA1', null, null, null, null, 'Less: Federal and State Tax, FICA'); ?>

                    <?php echo loanForm::currency(
                        'federalTaxFICA1',
                        true,
                        $tabIndex++,
                        Strings::showField('federalTaxFICA1', 'incomeInfo'),
                        null,
                        'calculatePrimaryNewTotalNetMonthlyIncome(this.value);',
                            '',
                    ); ?>
                </div>
            <?php } ?>
        </div>

        <?php if (!($isLO == 1 || $isHMLO == 1)) { ?>

            <div class=" row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('tipsMiscIncome1'); ?>">
                    <?php echo loanForm::label('tipsMiscIncome1', null, null, null, null, 'Tips'); ?>

                    <?php echo loanForm::currency(
                        'tipsMiscIncome1',
                        true,
                        $tabIndex++,
                        Strings::showField('tipsMiscIncome1', 'incomeInfo'),
                        null,
                        'calculatePrimaryNewTotalNetMonthlyIncome(this.value);',
                            '',
                    ); ?>
                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('tipsMiscIncome1'); ?>">
                    <?php echo loanForm::label('tipsMiscIncome1', null, null, null, null, 'Less: Other Deductions (401K, etc.)'); ?>

                    <?php echo loanForm::currency(
                        'otherDeductions1',
                        true,
                        $tabIndex++,
                        Strings::showField('otherDeductions1', 'incomeInfo'),
                        null,
                        'calculatePrimaryNewTotalNetMonthlyIncome(this.value);',
                            '',
                    ); ?>

                </div>
            </div>
            <div class=" row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('primTotalGrossIncome'); ?>">
                    <?php echo loanForm::label('primTotalGrossIncome', null, null, null, null, 'Total Gross Income'); ?>

                    <h5>$
                        <span
                            id="primTotalGrossIncome"><?php echo Currency::formatDollarAmountWithDecimal($primTotalGrossIncome) ?></span>
                    </h5>
                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('netMonthlyIncome1'); ?>">
                    <?php echo loanForm::label2(
                        'netMonthlyIncome1',
                        null,
                        'Net Monthly Income is considered the amount you make after all deductions are made like taxes and insurance. For self-employed borrowers you can use the dollar amount you pay yourself via draws. Please provide a detailed explanation in the income notes section below to clarify your income to your lender.',
                        null,
                        null,
                        'Total Net Monthly Income'
                    ); ?>

                    <?php echo loanForm::currency(
                        'netMonthlyIncome1',
                        true,
                        $tabIndex++,
                        Strings::showField('netMonthlyIncome1', 'incomeInfo'),
                        null,
                        'calculatePrimaryNewTotalNetMonthlyIncome(this.value);',
                            '',
                    ); ?>
                </div>
            </div>

            <?php echo loanForm::sectionHeader('', 'Other Household Monthly Income'); ?>

            <?php
            if ($socialSecurity1 > 0) {
                $socialSecurity1 = number_format(Strings::replaceCommaValues($socialSecurity1), 2);
            }
            ?>
            <div class=" row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('socialSecurity'); ?>">
                    <?php echo loanForm::label2(
                        'socialSecurity',
                        null,
                        null,
                        null,
                        null,
                        'Social Security Income'
                    ); ?>

                    <div class="row">
                        <div class="col-md-6 form-group <?php echo loanForm::showField('socialSecurity1'); ?>">
                            <?php echo loanForm::label2(
                                'socialSecurity1',
                                null,
                                '"Grossed Up" value is 25% more than the amount entered in the "Gross" social security field and will be used in all calculations relation to DTI and HAMP calculations.',
                                null,
                                null,
                                'Grossed Up'
                            ); ?>

                            <?php echo loanForm::currency(
                                'socialSecurity1',
                                true,
                                $tabIndex++,
                                $borSocialSecurity,
                                null,
                                'calculatePrimaryTotalHouseHoldIncome(this.value); updateGrossSocialSecurityIncome(this.value, \'bor\');',
                                    '',
                            ); ?>

                        </div>
                        <div class="col-md-6 form-group <?php echo loanForm::showField('netSocialSecurity1'); ?>">

                            <?php echo loanForm::label2(
                                'netSocialSecurity1',
                                null,
                                'Net SSI values are used for calculating disposable income.',
                                null,
                                null,
                                'Net SSI'
                            ); ?>

                            <?php echo loanForm::currency(
                                'netSocialSecurity1',
                                true,
                                $tabIndex++,
                                $netSocialSecurity1,
                                null,
                                'calculatePrimaryTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-12 <?php echo loanForm::showField('grossedUp'); ?>">

                            <?php echo loanForm::label2(
                                'grossedUp',
                                null,
                                null,
                                null,
                                null,
                                'Grossed Up: $<span id="grossedUp">' . $socialSecurity1 . '</span>'
                            ); ?>

                            <?php echo loanForm::checkbox(
                                'addGrossedUp1',
                                true,
                                $tabIndex++,
                                1,
                                Strings::showField('addGrossedUp1', 'incomeInfo'),
                                null,
                                'calculatePrimaryTotalHouseHoldIncome(document.loanModForm.socialSecurity1.value); updateGrossSocialSecurityIncome(document.loanModForm.socialSecurity1.value, \'bor\');',
                                false,
                                'Grossed Up the gross SSI value <i
                                            class="fa fa-info-circle text-primary tooltipClass"
                                            title="If you choose to Gross up the Gross SSI  value, it will be used in current + proposed DTI calculations and populate on the RMA, 710 form, and other lender packages."></i>'
                            ); ?>

                        </div>
                    </div>
                </div>
                <div class="col-md-6 form-group">
                    <?php echo loanForm::label2(
                        'pensionOrRetirement',
                        null,
                        null,
                        null,
                        null,
                        'Pension/Retirement Income'
                    ); ?>

                    <div class="row">
                        <div class="col-md-6 form-group <?php echo loanForm::showField('pensionOrRetirement1'); ?>">
                            <?php echo loanForm::label2(
                                'pensionOrRetirement1',
                                null,
                                null,
                                null,
                                null,
                                'Gross'
                            ); ?>

                            <?php echo loanForm::currency(
                                'pensionOrRetirement1',
                                true,
                                $tabIndex++,
                                $pensionOrRetirement1,
                                $pensionOrRetirement1 == $grossDefaultText ? 'fontGrey' : 'fontNormal',
                                'calculatePrimaryTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                        <div class="col-md-6 form-group <?php echo loanForm::showField('netPensionOrRetirement1'); ?>">
                            <?php echo loanForm::label2(
                                'netPensionOrRetirement1',
                                null,
                                null,
                                null,
                                null,
                                'Net'
                            ); ?>

                            <?php echo loanForm::currency(
                                'netPensionOrRetirement1',
                                true,
                                $tabIndex++,
                                $netPensionOrRetirement1,
                                null,
                                'calculatePrimaryTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                    </div>

                </div>
            </div>

            <div class=" row">
                <div class="col-md-12">

                    <?php echo loanForm::label2(
                        'netDisability1',
                        null,
                        null,
                        null,
                        null,
                        'Disability Income'
                    ); ?>

                    <div class="row">
                        <div class="col-md-6 form-group <?php echo loanForm::showField('disability1'); ?>">
                            <?php echo loanForm::label2(
                                'disability1',
                                null,
                                null,
                                null,
                                null,
                                'Gross'
                            ); ?>

                            <?php echo loanForm::currency(
                                'disability1',
                                true,
                                $tabIndex++,
                                $disability1,
                                null,
                                'calculatePrimaryTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                        <div class="col-md-6 form-group <?php echo loanForm::showField('netDisability1'); ?>">
                            <?php echo loanForm::label2(
                                'netDisability1',
                                null,
                                null,
                                null,
                                null,
                                'Net'
                            ); ?>

                            <?php echo loanForm::currency(
                                'netDisability1',
                                true,
                                $tabIndex++,
                                $netDisability1,
                                null,
                                'calculatePrimaryTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                    </div>
                </div>
            </div>

            <div class=" row">
                <div class="col-md-12">

                    <?php echo loanForm::label2(
                        'unemployment',
                        null,
                        'Unemployment Income will not be factored in the RMA form and also in the Proposed Housing DTI calculation.',
                        null,
                        null,
                        'Unemployment'
                    ); ?>

                    <label for="netUnemployment1"></label>
                    <div class="row">
                        <div class="col-md-4 <?php echo loanForm::showField('unemployment1'); ?>">

                            <?php echo loanForm::label2(
                                'unemployment1',
                                null,
                                null,
                                null,
                                null,
                                'Gross'
                            ); ?>

                            <?php echo loanForm::currency(
                                'unemployment1',
                                true,
                                $tabIndex++,
                                $unemployment1,
                                null,
                                'calculatePrimaryTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                        <div class="col-md-4 <?php echo loanForm::showField('netUnemployment1'); ?>">

                            <?php echo loanForm::label2(
                                'netUnemployment1',
                                null,
                                null,
                                null,
                                null,
                                'Net'
                            ); ?>

                            <?php echo loanForm::currency(
                                'netUnemployment1',
                                true,
                                $tabIndex++,
                                $netUnemployment1,
                                null,
                                'calculatePrimaryTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>

                        <div class="col-md-4 <?php echo loanForm::showField('unemploymentStDate1'); ?>">
                            <?php echo loanForm::label2(
                                'unemploymentStDate1',
                                null,
                                null,
                                null,
                                null,
                                'Start Date'
                            ); ?>

                            <?php echo loanForm::date(
                                'unemploymentStDate1',
                                true,
                                $tabIndex++,
                                $unemploymentStDate1,
                                $allowToEdit ? 'unemploymentStDate1 dateNewClass' : ''
                            ); ?>

                        </div>
                    </div>
                </div>
            </div>
        <?php } ?>

        <?php if (!($isLO == 1 || $isHMLO == 1)) { ?>
            <div class="row ">
                <div class="col-md-12">
                    <?php echo loanForm::label2(
                        'rental',
                        null,
                        null,
                        null,
                        null,
                        'Rental Income'
                    ); ?>

                    <div class="row">
                        <div class="col-md-4 <?php echo loanForm::showField('rental1'); ?>">

                            <?php echo loanForm::label2(
                                'rental1',
                                null,
                                'The monthly net income or loss on a rental property is calculated as 75 percent of the monthly gross rental income, reduced by the monthly principal and interest payment, plus 1/12th of annual real property taxes, annual property insurance premiums and annual homeowner\'s associations dues, if applicable (i.e., PITIA). Section 5.1.8, Chapter II of the Handbook was amended to exclude from the passive income guidance rental income from a rental property that secures the loan being evaluated for a HAMP Tier 2. If the monthly net income of a rental property securing the mortgage loan being evaluated for modification under HAMP Tier 2 is equal to or greater than the pre-modification PITIA of that property, the servicer must verify and document the cause of the borrower\'s hardship as delinquency alone is not considered a hardship.',
                                null,
                                null,
                                'Gross'
                            ); ?>

                            <?php echo loanForm::currency(
                                'rental1',
                                true,
                                $tabIndex++,
                                $rental1,
                                null,
                                'calculatePrimaryTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                        <div class="col-md-4 <?php echo loanForm::showField('netRental1'); ?>">

                            <?php echo loanForm::label2(
                                'netRental1',
                                null,
                                'In regards to modifying a Rental Property: Include Rental Income received from all properties you own EXCEPT a property for which you are seeking mortgage assistance.',
                                null,
                                null,
                                'Net'
                            ); ?>

                            <?php echo loanForm::currency(
                                'netRental1',
                                true,
                                $tabIndex++,
                                $netRental1,
                                null,
                                'calculatePrimaryTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>

                    </div>
                </div>
            </div>
        <?php } ?>

        <div class="row">

            <?php if (!($isHMLO == 1)) { ?>
                <div class="col-md-6 form-group <?php echo loanForm::showField('earnedInterest1') ?>">
                    <?php echo loanForm::label2(
                        'earnedInterest1',
                        null,
                        null,
                        null,
                        null,
                        ($isLO == 1 || $isHMLO == 1) ? 'Dividends / Interest' : 'Earned Interest'
                    ); ?>

                    <?php echo loanForm::currency(
                        'earnedInterest1',
                        true,
                        $tabIndex++,
                        $earnedInterest1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>
                </div>
            <?php } ?>

            <div class="col-md-6 form-group <?php echo loanForm::showField('netEarnedInterest1') ?>">
                <?php echo loanForm::label2(
                    'netEarnedInterest1',
                    null,
                    null,
                    null,
                    null,
                    ($isLO == 1 || $isHMLO == 1) ? 'Dividends / Interest' : 'Net Earned Interest'
                ); ?>

                <?php echo loanForm::currency(
                    'netEarnedInterest1',
                    true,
                    $tabIndex++,
                    $netEarnedInterest1,
                    null,
                    ($isHMLO == 1) ? 'calculateHMLOBorrowerNetHouseholdIncome(\'loanModForm\',  \'primTotalNetHouseHoldIncome\');' : 'calculatePrimaryTotalHouseHoldIncome(this.value);',
                        '',
                ); ?>
            </div>

        </div>

        <div class="row">


        <?php if ($isLO == 1 || $isHMLO == 1) { ?>
                <div class="col-md-6 form-group <?php echo loanForm::showField('capitalGains1') ?>">

                    <?php echo loanForm::label2(
                        'capitalGains1',
                        null,
                        null,
                        null,
                        null,
                        'Capital Gains / (Losses)'
                    ); ?>


                    <?php echo loanForm::currency(
                        'capitalGains1',
                        true,
                        $tabIndex++,
                        $capitalGains1,
                        null,
                        ($isHMLO == 1) ? 'calculateHMLOBorrowerNetHouseholdIncome(\'loanModForm\',  \'primTotalNetHouseHoldIncome\');' : 'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            <?php } ?>

        </div>

        <div class=" row">
            <div class="col-md-6 form-group <?php echo loanForm::showField('partnership1'); ?>">
                <?php echo loanForm::label2(
                    'partnership1',
                    null,
                    null,
                    null,
                    null,
                    'Partnership / Subchapter S Income'
                ); ?>

                <?php echo loanForm::currency(
                    'partnership1',
                    true,
                    $tabIndex++,
                    $partnership1,
                    null,
                    ($isHMLO == 1) ? 'calculateHMLOBorrowerNetHouseholdIncome(\'loanModForm\',  \'primTotalNetHouseHoldIncome\');' : 'calculatePrimaryTotalHouseHoldIncome(this.value);',
                        '',
                ); ?>

            </div>
            <?php if ($isLO == 1 || $isHMLO == 1) { ?>
                <div class="col-md-6 form-group <?php echo loanForm::showField('otherHouseHold1') ?>">

                    <?php echo loanForm::label2(
                        'otherHouseHold1',
                        null,
                        null,
                        null,
                        null,
                        'Other Income'
                    ); ?>

                    <?php echo loanForm::currency(
                        'otherHouseHold1',
                        true,
                        $tabIndex++,
                        $otherHouseHold1,
                        null,
                        ($isHMLO == 1) ? 'calculateHMLOBorrowerNetHouseholdIncome(\'loanModForm\',  \'primTotalNetHouseHoldIncome\');' : 'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            <?php } ?>
        </div>

        <div class="row">
            <div class="col-md-12 form-group <?php echo loanForm::showField('otherIncomeDescription1') ?>">

                <?php echo loanForm::label2(
                    'otherIncomeDescription1',
                    null,
                    null,
                    null,
                    null,
                    'Other Income Description'
                ); ?>

                <?php echo loanForm::textarea(
                    'otherIncomeDescription1',
                    true,
                    $tabIndex++,
                    $otherIncomeDescription1
                ); ?>
            </div>
        </div>


        <?php if (!($isLO == 1 || $isHMLO == 1)) { ?>
            <div class="row">
                <div class="col-md-6 form-group">

                    <?php echo loanForm::label2(
                        'roomRental',
                        null,
                        null,
                        null,
                        null,
                        'Room Rental'
                    ); ?>

                    <div class="row">
                        <div class="col-md-6 form-group <?php echo loanForm::showField('roomRental1'); ?>">

                            <?php echo loanForm::label2(
                                'roomRental1',
                                null,
                                null,
                                null,
                                null,
                                'Gross'
                            ); ?>

                            <?php echo loanForm::currency(
                                'roomRental1',
                                true,
                                $tabIndex++,
                                $roomRental1,
                                null,
                                'calculatePrimaryTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>
                        </div>
                        <div class="col-md-6 form-group <?php echo loanForm::showField('netRoomRental1'); ?>">

                            <?php echo loanForm::label2(
                                'netRoomRental1',
                                null,
                                null,
                                null,
                                null,
                                'Net'
                            ); ?>


                            <?php echo loanForm::currency(
                                'netRoomRental1',
                                true,
                                $tabIndex++,
                                $netRoomRental1,
                                null,
                                'calculatePrimaryTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                    </div>
                </div>
                <div class="col-md-6 form-group">

                    <?php echo loanForm::label2(
                        'secondJobIncome1',
                        null,
                        null,
                        null,
                        null,
                        'Monthly Income (2nd Job)'
                    ); ?>

                    <div class="row">
                        <div class="col-md-6 form-group <?php echo loanForm::showField('secondJobIncome1'); ?>">

                            <?php echo loanForm::label2(
                                'secondJobIncome1',
                                null,
                                null,
                                null,
                                null,
                                'Gross'
                            ); ?>

                            <?php echo loanForm::currency(
                                'secondJobIncome1',
                                true,
                                $tabIndex++,
                                $secondJobIncome1,
                                null,
                                'calculatePrimaryTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                        <div class="col-md-6 form-group <?php echo loanForm::showField('netSecondJobIncome1'); ?>">
                            <?php echo loanForm::label2(
                                'netSecondJobIncome1',
                                null,
                                null,
                                null,
                                null,
                                'Net'
                            ); ?>

                            <?php echo loanForm::currency(
                                'netSecondJobIncome1',
                                true,
                                $tabIndex++,
                                $netSecondJobIncome1,
                                null,
                                'calculatePrimaryTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 form-group <?php echo loanForm::showField('secondJobEmpType1'); ?>">
                            <?php echo loanForm::label2(
                                'secondJobEmpType1',
                                null,
                                null,
                                null,
                                null,
                                'Type'
                            ); ?>

                            <?php echo loanForm::select(
                                'secondJobEmpType1',
                                true,
                                $tabIndex++,
                                Strings::showField('secondJobEmpType1', 'incomeInfo'),
                                employedInfo1Array::$options,
                                null,
                                null,
                                '- Select One -'
                            ); ?>

                        </div>
                        <div class="col-md-6 form-group <?php echo loanForm::showField('secondJobStDate1'); ?>">
                            <div class="input-group">

                                <?php echo loanForm::label2(
                                    'secondJobStDate1',
                                    $allowToEdit ? 'secondJobStDate1' : '',
                                    null,
                                    null,
                                    null,
                                    'Start Date'
                                ); ?>

                                <?php echo loanForm::date(
                                    'secondJobStDate1',
                                    true,
                                    $tabIndex++,
                                    $secondJobStDate1,
                                    'dateNewClass'
                                ); ?>

                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('sonOrDaughter'); ?>">

                    <?php echo loanForm::label2(
                        'sonOrDaughter',
                        null,
                        null,
                        null,
                        null,
                        'Spouse/Son/Daughter'
                    ); ?>

                    <?php echo loanForm::currency(
                        'sonOrDaughter1',
                        true,
                        $tabIndex++,
                        $sonOrDaughter1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('parents1'); ?>">
                    <?php echo loanForm::label2(
                        'parents1',
                        null,
                        null,
                        null,
                        null,
                        'Parents'
                    ); ?>

                    <?php echo loanForm::currency(
                        'parents1',
                        true,
                        $tabIndex++,
                        $parents1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            </div>
            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('childSupportOrAlimony1'); ?>">
                    <?php echo loanForm::label2(
                        'childSupportOrAlimony1',
                        null,
                        null,
                        null,
                        null,
                        'Child Support/Alimony'
                    ); ?>

                    <?php echo loanForm::currency(
                        'childSupportOrAlimony1',
                        true,
                        $tabIndex++,
                        $childSupportOrAlimony1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('otherHouseHold1'); ?>">
                    <?php echo loanForm::label2(
                        'otherHouseHold1',
                        null,
                        null,
                        null,
                        null,
                        'Other'
                    ); ?>

                    <?php echo loanForm::currency(
                        'otherHouseHold1',
                        true,
                        $tabIndex++,
                        $otherHouseHold1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>
                </div>
            </div>
        <?php } ?>



        <?php if (!($isLO == 1 || $isHMLO == 1)) { ?>
            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('foodStampWelfare1'); ?>">

                    <?php echo loanForm::label2(
                        'foodStampWelfare1',
                        null,
                        null,
                        null,
                        null,
                        'Food Stamps/Welfare'
                    ); ?>

                    <?php echo loanForm::currency(
                        'foodStampWelfare1',
                        true,
                        $tabIndex++,
                        $foodStampWelfare1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('incomeNotes'); ?>">
                    <?php echo loanForm::label2(
                        'incomeNotes',
                        null,
                        'Please provide any explanation you feel may be necessary to validate the income listed above.',
                        null,
                        null,
                        'Income notes / Letter of Explanation'
                    ); ?>

                    <?php echo loanForm::textarea(
                        'incomeNotes',
                        true,
                        $tabIndex++,
                        Strings::showField('incomeNotes', 'incomeInfo')
                    ); ?>


                </div>
            </div>
        <?php } ?>

        <?php if (!($isHMLO == 1)) { ?>
            <div class="row">
                <div class="col-md-6 form-group">
                    <label class="font-weight-bold">Total Gross Household Income</label>
                </div>
                <div class="col-md-6 form-group">
                            <span class="h6">$
                                <span
                                    id="primTotalHouseHoldIncome"><?php echo Currency::formatDollarAmountWithDecimal($primTotalHouseHoldIncome) ?></span>
                            </span>(Borrower)

                    <?php
                    if (Strings::showField('isCoBorrower', 'LMRInfo') == 0) {
                        ?>
                        <div class="left" style="display:none">
                            <h3>
                                <span
                                    id="subTotalHouseHoldIncome"><?php echo Currency::formatDollarAmountWithDecimal($totalGrossMonthlyHouseHoldIncome) ?></span>
                            </h3>
                        </div>
                        <?php
                    } ?>
                </div>
            </div>
        <?php } ?>


        <div class="row <?php echo loanForm::showField('primTotalNetHouseHoldIncome'); ?>">
            <div class="col-md-6 form-group">
                <label class="font-weight-bold">Total Income</label>
            </div>
            <div class="col-md-6 form-group">
                        <span class="h6">$
                            <span
                                id="primTotalNetHouseHoldIncome"><?php echo Currency::formatDollarAmountWithDecimal($primTotalNetHouseHoldIncome) ?></span>
                        </span>(Borrower)

                <?php
                if (Strings::showField('isCoBorrower', 'LMRInfo') == 0) {
                    ?>
                    <div class="left" style="display:none">
                        <h3>
                            <span
                                id="subTotalNetHouseHoldIncome"><?php echo Currency::formatDollarAmountWithDecimal($totalHouseHoldIncome) ?></span>
                        </h3>
                    </div>
                    <?php
                } ?>

            </div>
        </div>
        <?php if (!($isLO == 1 || $isHMLO == 1)) { ?>

            <?php echo loanForm::sectionHeader('propdetailstitleco', 'Monthly Expenses'); ?>

            <div class="row">
                <div class="col-md-6 form-group">
                    <label class="font-weight-bold">1st Mortgage Payment $</label>
                    <h5><?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('lien1Payment', 'LMRInfo')) ?></h5>
                </div>
                <div class="col-md-6 form-group">
                    <label class="font-weight-bold">H.O.A Fees $ <?php if ($allowToEdit) { ?>
                            <i class="fa fa-info-circle text-primary tooltipClass"
                               title="HOA Fees or associations fees are paid be homeowners who live in a condominium, town house, or housing development. This amount is usually paid separate from a mortgage payment, but is considered in the housing payment."></i>
                        <?php } ?></label>
                    <h5><?php echo Currency::formatDollarAmountWithDecimal($HOAFees1) ?></h5>
                    <input type="hidden" name="HOAFees1" id="HOAFees1"
                           value="<?php echo $HOAFees1 ?>"/>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <?php if ((trim(Strings::showField('servicer2', 'LMRInfo')) != '') || (Strings::replaceCommaValues(Strings::showField('lien2Payment', 'LMRInfo')) > 0)) { ?>
                        <label class="font-weight-bold"> 2nd Mortgage Payment $</label>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('lien2Payment', 'LMRInfo')) ?></h5>
                        <?php
                    }
                    ?>
                </div>
            </div>


            <div class="row">
                <div class="col-md-6 form-group"><label class="font-weight-bold">Taxes (if not escrowed) $</label>
                    <h5><?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('taxes1', 'incomeInfo')) ?></h5>
                    <input type="hidden" name="taxes1" id="taxes1"
                           value="<?php echo Strings::showField('taxes1', 'incomeInfo') ?>"/></div>
                <div class="col-md-6 form-group">
                    <label class="font-weight-bold"> Insurance (if not escrowed) $</label>
                    <h5><?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('insurance1', 'incomeInfo')) ?></h5>
                    <input type="hidden" name="insurance1" id="insurance1"
                           value="<?php echo Strings::showField('insurance1', 'incomeInfo') ?>"/>
                </div>
            </div>


            <div class="row">
                <div class="col-md-6 form-group"><label class="font-weight-bold">Mortgage Insurance $</label>
                    <h5><?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('mortgageInsurance1', 'incomeInfo')) ?></h5>
                    <input type="hidden" name="mortgageInsurance1" id="mortgageInsurance1"
                           value="<?php echo Strings::showField('mortgageInsurance1', 'incomeInfo') ?>"/>
                </div>
                <div class="col-md-6 form-group"><label class="font-weight-bold">Flood Insurance $</label>

                    <h5><?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('floodInsurance1', 'incomeInfo')) ?></h5>
                    <input type="hidden" name="floodInsurance1" id="floodInsurance1"
                           value="<?php echo Strings::showField('floodInsurance1', 'incomeInfo') ?>"/>
                </div>
            </div>


            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('creditCards1'); ?>">

                    <?php
                    echo loanForm::label2(
                        'creditCards1',
                        null,
                        'Add the total monthly minimum payments from all credit cards.',
                        null,
                        null,
                        'Credit Cards'
                    ); ?>

                    <?php echo loanForm::currency(
                        'creditCards1',
                        true,
                        $tabIndex++,
                        $creditCards1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('creditCardsBalance1'); ?>">

                    <?php
                    echo loanForm::label2(
                        'creditCardsBalance1',
                        null,
                        null,
                        null,
                        null,
                        'Total Credit Card <span style="color:red"><b>Balance(s)</b></span>'
                    ); ?>

                    <?php echo loanForm::currency(
                        'creditCardsBalance1',
                        true,
                        $tabIndex++,
                        $creditCardsBalance1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>
                </div>
            </div>


            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('autoLoan1'); ?>">

                    <?php echo loanForm::label2(
                        'autoLoan1',
                        null,
                        null,
                        null,
                        null,
                        'Auto/Car Loan(s)'
                    ); ?>

                    <?php echo loanForm::currency(
                        'autoLoan1',
                        true,
                        $tabIndex++,
                        $autoLoan1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('autoLoanBalance1'); ?>">

                    <?php echo loanForm::label2(
                        'autoLoanBalance1',
                        null,
                        null,
                        null,
                        null,
                        'Total Auto Loan <span style="color:red"><b>Balance(s)</b></span>'
                    ); ?>

                    <?php echo loanForm::currency(
                        'autoLoanBalance1',
                        true,
                        $tabIndex++,
                        $autoLoanBalance1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('unsecuredLoans1'); ?>">

                    <?php echo loanForm::label2(
                        'unsecuredLoans1',
                        null,
                        null,
                        null,
                        null,
                        'Unsecured Loan(s)'
                    ); ?>

                    <?php echo loanForm::currency(
                        'unsecuredLoans1',
                        true,
                        $tabIndex++,
                        $unsecuredLoans1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('unsecuredLoanBalance1'); ?>">

                    <?php echo loanForm::label2(
                        'unsecuredLoanBalance1',
                        null,
                        null,
                        null,
                        null,
                        'Total Unsecured Loan <span style="color:red"><b>Balance(s)</b></span>'
                    ); ?>

                    <?php echo loanForm::currency(
                        'unsecuredLoanBalance1',
                        true,
                        $tabIndex++,
                        $unsecuredLoanBalance1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('otherMortgage1'); ?>">

                    <?php echo loanForm::label2(
                        'otherMortgage1',
                        null,
                        'In regards to modifying a Loan for a Rental Property: Include Mortgage Payments on all properties you own EXCEPT your principal residence and the property for which you are seeking mortgage assistance."',
                        null,
                        null,
                        'Other Mortgages'
                    ); ?>

                    <?php echo loanForm::currency(
                        'otherMortgage1',
                        true,
                        $tabIndex++,
                        $otherMortgage1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('otherMortgageBalance1'); ?>">

                    <?php echo loanForm::label2(
                        'otherMortgageBalance1',
                        null,
                        null,
                        null,
                        null,
                        'Mortgage <span style="color:red"><b>Balance(s)</b></span>'
                    ); ?>

                    <?php echo loanForm::currency(
                        'otherMortgageBalance1',
                        true,
                        $tabIndex++,
                        $otherMortgageBalance1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            </div>


            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('studentLoans1'); ?>">

                    <?php echo loanForm::label2(
                        'studentLoans1',
                        null,
                        null,
                        null,
                        null,
                        'Student Loans/Tuition'
                    ); ?>

                    <?php echo loanForm::currency(
                        'studentLoans1',
                        true,
                        $tabIndex++,
                        $studentLoans1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>

                <div class="col-md-6 form-group <?php echo loanForm::showField('aaa'); ?>">

                    <?php echo loanForm::label2(
                        'studentLoansBalance1',
                        null,
                        null,
                        null,
                        null,
                        'Total Student Loan <span style="color:red"><b>Balance(s)</b></span>'
                    ); ?>


                    <?php echo loanForm::currency(
                        'studentLoansBalance1',
                        true,
                        $tabIndex++,
                        $studentLoansBalance1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('childSupportOrAlimonyMonthly1'); ?>">

                    <?php echo loanForm::label2(
                        'childSupportOrAlimonyMonthly1',
                        null,
                        null,
                        null,
                        null,
                        'Alimony/Child Support'
                    ); ?>

                    <?php echo loanForm::currency(
                        'childSupportOrAlimonyMonthly1',
                        true,
                        $tabIndex++,
                        $childSupportOrAlimonyMonthly1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('careAmt1'); ?>">

                    <?php echo loanForm::label2(
                        'careAmt1',
                        null,
                        null,
                        null,
                        null,
                        'Child/Dependent/Elderly Care'
                    ); ?>

                    <?php echo loanForm::currency(
                        'careAmt1',
                        true,
                        $tabIndex++,
                        $careAmt1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('allInsurance1'); ?>">

                    <?php echo loanForm::label2(
                        'allInsurance1',
                        null,
                        null,
                        null,
                        null,
                        'Insurance (Health, Life)'
                    ); ?>

                    <?php echo loanForm::currency(
                        'allInsurance1',
                        true,
                        $tabIndex++,
                        $allInsurance1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('groceries1'); ?>">

                    <?php echo loanForm::label2(
                        'groceries1',
                        null,
                        null,
                        null,
                        null,
                        'Groceries'
                    ); ?>

                    <?php echo loanForm::currency(
                        'groceries1',
                        true,
                        $tabIndex++,
                        $groceries1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('carExpenses1'); ?>">

                    <?php echo loanForm::label2(
                        'carExpenses1',
                        null,
                        null,
                        null,
                        null,
                        'Car Expenses (Ins.,Gas, Maint.)'
                    ); ?>

                    <?php echo loanForm::currency(
                        'carExpenses1',
                        true,
                        $tabIndex++,
                        $carExpenses1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>

                <div class="col-md-6 form-group <?php echo loanForm::showField('medicalBill1'); ?>">

                    <?php echo loanForm::label2(
                        'medicalBill1',
                        null,
                        null,
                        null,
                        null,
                        'Doctor/Medical Bills'
                    ); ?>

                    <?php echo loanForm::currency(
                        'medicalBill1',
                        true,
                        $tabIndex++,
                        $medicalBill1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>

            </div>
            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('entertainment1'); ?>">

                    <?php echo loanForm::label2(
                        'entertainment1',
                        null,
                        null,
                        null,
                        null,
                        'Entertainment'
                    ); ?>

                    <?php echo loanForm::currency(
                        'entertainment1',
                        true,
                        $tabIndex++,
                        $entertainment1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('other1'); ?>">

                    <?php echo loanForm::label2(
                        'other1',
                        null,
                        null,
                        null,
                        null,
                        'Other'
                    ); ?>

                    <?php echo loanForm::currency(
                        'other1',
                        true,
                        $tabIndex++,
                        $other1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('donation1'); ?>">

                    <?php echo loanForm::label2(
                        'donation1',
                        null,
                        null,
                        null,
                        null,
                        'Church/Club Donations'
                    ); ?>

                    <?php echo loanForm::currency(
                        'donation1',
                        true,
                        $tabIndex++,
                        $donation1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('pets1'); ?>">

                    <?php echo loanForm::label2(
                        'pets1',
                        null,
                        null,
                        null,
                        null,
                        'Pets'
                    ); ?>

                    <?php echo loanForm::currency(
                        'pets1',
                        true,
                        $tabIndex++,
                        $pets1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>

            </div>
            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('monthlyParking1'); ?>">

                    <?php echo loanForm::label2(
                        'monthlyParking1',
                        null,
                        null,
                        null,
                        null,
                        'Monthly Parking'
                    ); ?>

                    <?php echo loanForm::currency(
                        'monthlyParking1',
                        true,
                        $tabIndex++,
                        $monthlyParking1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('unionDues1'); ?>">

                    <?php echo loanForm::label2(
                        'unionDues1',
                        null,
                        null,
                        null,
                        null,
                        'Union Dues'
                    ); ?>

                    <?php echo loanForm::currency(
                        'unionDues1',
                        true,
                        $tabIndex++,
                        $unionDues1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            </div>


            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('personalLoan1'); ?>">

                    <?php echo loanForm::label2(
                        'personalLoan1',
                        null,
                        null,
                        null,
                        null,
                        'Personal Loan'
                    ); ?>

                    <?php echo loanForm::currency(
                        'personalLoan1',
                        true,
                        $tabIndex++,
                        $personalLoan1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('lunchPurchased1'); ?>">

                    <?php echo loanForm::label2(
                        'lunchPurchased1',
                        null,
                        null,
                        null,
                        null,
                        'School / Work Lunches Purchased'
                    ); ?>

                    <?php echo loanForm::currency(
                        'lunchPurchased1',
                        true,
                        $tabIndex++,
                        $lunchPurchased1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            </div>
            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('rentalExp1'); ?>">

                    <?php echo loanForm::label2(
                        'rentalExp1',
                        null,
                        null,
                        null,
                        null,
                        'Rental'
                    ); ?>

                    <?php echo loanForm::currency(
                        'rentalExp1',
                        true,
                        $tabIndex++,
                        $rentalExp1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            </div>


            <?php echo loanForm::sectionHeader('monthlyExpensed', 'Utilities'); ?>


            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('cable1'); ?>">

                    <?php echo loanForm::label2(
                        'cable1',
                        null,
                        null,
                        null,
                        null,
                        'Cable TV/Satellite'
                    ); ?>

                    <?php echo loanForm::currency(
                        'cable1',
                        true,
                        $tabIndex++,
                        $cable1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('electricity1'); ?>">

                    <?php echo loanForm::label2(
                        'electricity1',
                        null,
                        null,
                        null,
                        null,
                        'Electricity'
                    ); ?>

                    <?php echo loanForm::currency(
                        'electricity1',
                        true,
                        $tabIndex++,
                        $electricity1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('natural1'); ?>">

                    <?php echo loanForm::label2(
                        'natural1',
                        null,
                        null,
                        null,
                        null,
                        'Natural Gas/Oil'
                    ); ?>

                    <?php echo loanForm::currency(
                        'natural1',
                        true,
                        $tabIndex++,
                        $natural1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('primaryBorrowerPhone'); ?>">

                    <?php echo loanForm::label2(
                        'primaryBorrowerPhone',
                        null,
                        null,
                        null,
                        null,
                        'Telephone/Cell'
                    ); ?>

                    <?php echo loanForm::currency(
                        'primaryBorrowerPhone',
                        true,
                        $tabIndex++,
                        $primaryBorrowerPhone,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('water1'); ?>">

                    <?php echo loanForm::label2(
                        'water1',
                        null,
                        null,
                        null,
                        null,
                        'Water/Sewer'
                    ); ?>

                    <?php echo loanForm::currency(
                        'water1',
                        true,
                        $tabIndex++,
                        $water1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('internet1'); ?>">

                    <?php echo loanForm::label2(
                        'internet1',
                        null,
                        null,
                        null,
                        null,
                        'Internet'
                    ); ?>

                    <?php echo loanForm::currency(
                        'internet1',
                        true,
                        $tabIndex++,
                        $internet1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            </div>
            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('dryCleaning1'); ?>">

                    <?php echo loanForm::label2(
                        'dryCleaning1',
                        null,
                        null,
                        null,
                        null,
                        'Dry Cleaning/Clothing'
                    ); ?>

                    <?php echo loanForm::currency(
                        'dryCleaning1',
                        true,
                        $tabIndex++,
                        $dryCleaning1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('utilityOther1'); ?>">

                    <?php echo loanForm::label2(
                        'utilityOther1',
                        null,
                        null,
                        null,
                        null,
                        'Other'
                    ); ?>

                    <?php echo loanForm::currency(
                        'utilityOther1',
                        true,
                        $tabIndex++,
                        $utilityOther1,
                        null,
                        'calculatePrimaryTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            </div>
            <div class="row">
                <div class="col-md-6 form-group <?php echo loanForm::showField('houseHoldExpensesNotes'); ?>">

                    <?php echo loanForm::label2(
                        'houseHoldExpensesNotes',
                        null,
                        'Please enter any explanation or notes for any of the above listed expenses.<br>For example, if someone else is paying an auto loan in your name, explain that, and make sure to include their payment in &quot;other income&quot;',
                        null,
                        null,
                        'Expense Notes / Letter of Explanation'
                    ); ?>

                    <?php echo loanForm::textarea(
                        'houseHoldExpensesNotes',
                        true,
                        $tabIndex++,
                        $houseHoldExpensesNotes
                    ); ?>

                </div>

                <div class="col-md-6 form-group <?php echo loanForm::showField('includeNotesToPDF'); ?>">

                    <?php echo loanForm::checkbox(
                        'includeNotesToPDF',
                        true,
                        $tabIndex++,
                        1,
                        Strings::showField('includeNotesToPDF', 'incomeInfo'),
                        null,
                        null,
                        false,
                        'Include Notes/Letter of Explanation with Income/Expense Summary page?'
                    ); ?>

                </div>
            </div>


            <div class="row">
                <div class="col-md-6 form-group"><label class="font-weight-bold">Total Household Expenses</label></div>
                <div class="col-md-6 form-group"><h3>$
                        <span
                            id="primTotalHouseHoldExpenses"><?php echo Currency::formatDollarAmountWithDecimal($primTotalHouseHoldExpenses) ?></span>
                    </h3>(Borrower)
                </div>
            </div>


        <?php } ?>

        <?php echo CustomField::RenderForTabSection(
            PageVariables::$PCID,
            tblFile::class,
            LMRequest::$LMRId,
            'BMI',
            $fileTab,
            $activeTab,
            LMRequest::myFileInfo()->getFileTypes(),
            LMRequest::myFileInfo()->getLoanPrograms()
        ); ?>

    </div>
</div>

<!-- borrowerMonthlyIncomeForm.php -->
<?php

loanForm::popSectionID('BMI');

