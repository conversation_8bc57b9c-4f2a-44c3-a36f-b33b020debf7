<?php

global $assignedPCID;

use models\composite\oPC\getPCModules;
use models\composite\oPC\getPCServiceType;
use models\composite\oWorkflow\getPCWFServiceType;
use models\composite\oWorkflow\getPCWFSteps;
use models\composite\oWorkflow\getPCWorkflow;
use models\cypher;
use models\standard\Strings;

$PCWorkflowArray = [];
$PCWorkflowKeyArray = [];
$WFIds = '';
$rowCnt = 0;
$PCLMRClientTypeInfoArray = [];
$PCClientTypeInfoArray = [];
$PCWFStepsArray = [];
$t = 0;
$temp1 = 0;
$PCWFServiceTypeArray = [];
$serviceType = [];

$inArray['PCID'] = $assignedPCID;


$inArray['getInternalLoanPrograms'] = 1;

//if ($assignedPCID > 0)
$PCLMRClientTypeInfoArray = getPCServiceType::getReport($inArray);

if (count($PCLMRClientTypeInfoArray ?? []) > 0) {
    if (array_key_exists($assignedPCID, $PCLMRClientTypeInfoArray)) {
        $PCClientTypeInfoArray = $PCLMRClientTypeInfoArray[$assignedPCID];
    }

    $PCSelectedHMLOClientType = [];
    for ($m = 0; $m < count($PCClientTypeInfoArray ?? []); $m++) {
        if ($PCClientTypeInfoArray[$m]['PCServiceModuleCode'] == 'HMLO') {
            $PCSelectedHMLOClientType[] = $PCClientTypeInfoArray[$m]['LMRClientType'];
        }
    }
}
/**
 *
 * Description    : Display the Work flow PC selected Modules
 * Date        : Feb 23, 2015
 * Developer    : Viji, venkatesh Raju
 **/

$PCSelectedServiceTypeArray = [];
$PCSelectedServiceType = '';
$PCWFServiceTypeArrayKeys = [];

if (count($PCClientTypeInfoArray ?? []) > 0) {
    for ($i = 0; $i < count($PCClientTypeInfoArray ?? []); $i++) {
        $LMRClientType = $PCClientTypeInfoArray[$i]['LMRClientType'];
        $PCSelectedServiceTypeArray[] = $LMRClientType;
    }
}
/**
 *
 * Description    : Get the PC selected the modules
 * Date        : April 07, 2017
 * Developer    : Viji, venkatesh Raju
 **/

$PCModulesArray = [];
$isHMLO = 0;

//if ($assignedPCID > 0) {
$ip['PCID'] = $assignedPCID;
$ip['keyNeeded'] = 'n';

$modulesInfoArray = getPCModules::getReport($ip);
//}

for ($i = 0; $i < count($modulesInfoArray ?? []); $i++) {
    $PCModulesArray[] = $modulesInfoArray[$i]['moduleCode'];
}
if (in_array('HMLO', $PCModulesArray)) $isHMLO = 1;

$PCSelectedServiceType = implode("','", $PCSelectedServiceTypeArray);
$inArray['WFServiceType'] = $PCSelectedServiceType;

$PCWFServiceTypeArray = getPCWFServiceType::getReport($inArray);

$PCWFServiceTypeArrayKeys = array_keys($PCWFServiceTypeArray);

for ($temp = 0; $temp < count($PCWFServiceTypeArrayKeys ?? []); $temp++) {
    if ($temp > 0) $WFIds .= ', ';
    $WFIds .= $PCWFServiceTypeArrayKeys[$temp];
}
if ($temp > 0) {
    $inArray = ['WFId' => $WFIds, 'PCID' => $assignedPCID,'opt'=>'PCWF'];

    $PCWFStepsArray = getPCWFSteps::getReport($inArray);
    $PCWorkflowArray = getPCWorkflow::getReport($inArray);
}

?>

<div class="row bg-gray-100 pt-10 pl-4 mb-2">
    <div class="col-md-6 form-group row">
        <h7>To change the display order of your workflow steps please drag them to the
            desired order, then click "save display order" button.
        </h7>
    </div>
    <div class="col-md-2 form-group row pr-2">
        <a data-id="PCID=<?php echo cypher::myEncryption($assignedPCID) ?>"
           id="button_addworkflow"
           data-href="<?php echo CONST_URL_POPS; ?>addPCWorkflow.php"
           data-name="Add / Edit Workflow" data-toggle='modal' data-target='#exampleModal1' data-wsize='modal-xl'
           title="Click to add workflow" style="text-decoration:none"
           class="btn btn-success tooltipClass">+ Add Workflow</a>
    </div>
    <div class="col-md-2 form-group row">
        <a target="_blank" class="btn btn-primary" href="https://youtu.be/PweBvH60xNs?si=Z2n3dVp73uA1CAS1"><i
                    class="fas fa-video img-circle btn-primary"></i>How
            to video</a>
    </div>

    <div class="col-md-2 form-group row">
        <div class="py-4"><a target="_blank"
                             href="https://docs.google.com/document/d/1xyaKYPN4nAHRDGQ1oSB1H6i6sm6gDeTM6XetBXW4-bg/edit"
                             class=" tip-bottom"><b>Example Workflows</b></a></div>
    </div>
</div>
<div class="row mb-2 saveDispOrder" style="display: none; text-align: center;">
    <div class="btn btn-primary btn-sm  dispButton tooltipClass" title="Click to save new display order">Save Display
        Order
    </div>
    <span class="form-text text-muted ">(Note: Click the Save button to save your re-ordering)</span>
</div>
<input type="hidden" name="selectedWFId" id="selectedWFId" value="">
<div class="row">
    <?php
    $ws1 = 0;
    for ($p = 0; $p < count($PCWorkflowArray ?? []); $p++) {
        $WFId = 0;
        $PCWFServiceType = '';
        $tempPCWFServiceTypeArray = [];
        $serviceType = [];
        $isSLMOnly = 0;
        $isHMLOClientType = 0;
        $WFId = trim($PCWorkflowArray[$p]['WFID']);

        if (count($PCWFServiceTypeArray ?? []) > 0) {
            if (array_key_exists($WFId, $PCWFServiceTypeArray)) {
                $tempPCWFServiceTypeArray = $PCWFServiceTypeArray[$WFId];
                $PCWFServiceType = Strings::implode2dServiceTypeArray(', ', $tempPCWFServiceTypeArray, 'WFServiceType');

                for ($i = 0; $i < count($tempPCWFServiceTypeArray ?? []); $i++) {
                    if (in_array($tempPCWFServiceTypeArray[$i]['WFServiceType'], $PCSelectedHMLOClientType ?? [])) {  //Check the HMLO PC selected Service Type on Dec 26, 2017 By Venky
                        $isHMLOClientType = 1;
                    }
                    $serviceType[] = trim($tempPCWFServiceTypeArray[$i]['WFServiceType']);
                }

                if (in_array('SLM', $serviceType) && count($serviceType ?? []) == 1) {
                    /** Show ONLY Student loan mod tabs and its respective tabs **/
                    $isSLMOnly = 1;
                }
            }
        }
        ?>
        <div class="col-md-6 <?php if(!array_key_exists($WFId, $PCWFStepsArray) && $assignedPCID){ echo  'd-none'; }?>">
            <div class="card card-custom" id="WF_<?php echo $p ?>">
                <div class="card-header card-header-tabs-line bg-gray-100">
                    <div class="card-title">
                        <h3 class="card-label"><?php echo $PCWorkflowArray[$p]['WFName']; ?></h3>
                    </div>
                    <div class="card-toolbar ">
                        <a data-id="PCID=<?php echo cypher::myEncryption($assignedPCID) ?>&WFId=<?php echo cypher::myEncryption($WFId) ?>&isSLMOnly=<?php echo cypher::myEncryption($isSLMOnly) ?>&isHMLO=<?php echo cypher::myEncryption($isHMLOClientType) ?>&showSaveBtn=1"
                           id="button_<?php echo Strings::removeSpaceWithSpecialChars($PCWorkflowArray[$p]['WFName']); ?>_addsteps"
                           data-toggle='modal' data-target='#exampleModal1' data-wsize='modal-xl'
                           data-href="<?php echo CONST_URL_POPS; ?>addWorkflowSteps.php"
                           title="Click to add workflow steps"
                           class="btn btn-sm btn-primary tip-bottom"
                           style="text-decoration:none"
                           data-name="Add workflow steps for: &quot;<?php echo $PCWorkflowArray[$p]['WFName']; ?>&quot;">Add
                            Steps</a>
                        <a class="tooltipClass btn btn-sm btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2"
                           id="button_edit_<?php echo Strings::removeSpaceWithSpecialChars($PCWorkflowArray[$p]['WFName']); ?>"
                           style="text-decoration:none;"
                           data-id="PCID=<?php echo cypher::myEncryption($assignedPCID) ?>&WFId=<?php echo cypher::myEncryption($WFId) ?>"
                           data-name="Add / Edit Workflow" data-toggle='modal' data-target='#exampleModal1'
                           data-wsize='modal-xl'
                           data-href="<?php echo CONST_URL_POPS; ?>addPCWorkflow.php"><i class="tooltipClass far fa-edit"
                                                                                  title="Click to edit"></i></a>

                        <a class="tooltipClass btn btn-sm btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2"
                           style="text-decoration:none;"
                           href="javascript:deleteWorkflow('<?php echo cypher::myEncryption($WFId) ?>','<?php echo cypher::myEncryption($assignedPCID) ?>');">
                            <i class="flaticon2-trash" title="Click to delete"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row bg-gray-100 py-4 px-2 mb-2" id="WFST_<?php echo $p ?>">
                        <?php if ($isHMLO == 1) { ?> Loan Programs: <?php } else { ?> Service Type: <?php } ?><?php echo $PCWFServiceType ?>
                    </div>

                    <?php
                    if (count($PCWFStepsArray ?? []) > 0) {
                        if (array_key_exists($WFId, $PCWFStepsArray)) {
                            $tempPCWFSArray = [];
                            $tempPCWFSArray = $PCWFStepsArray[$WFId];
                        }
                    }
                    ?>
                    <div class="row">
                        <div class="table-responsive">
                            <table class="table table-hover LWcustomTable table-bordered table-condensed table-sm table-vertical-center">
                                <thead class="thead-light">
                                <tr>
                                    <th title="Display Order" style="text-align: center;">#</th>
                                    <th>Step Name</th>
                                    <th>Actions</th>
                                </tr>
                                </thead>
                                <tbody id="tabledivbody2_<?php echo $WFId; ?>" class="ui-sortable-helper">
                                <?php
                                if (count($PCWFStepsArray ?? []) > 0) {
                                    if (array_key_exists($WFId, $PCWFStepsArray)) {
                                        if (count($tempPCWFSArray ?? []) > 0) {
                                            for ($ws = 0; $ws < count($tempPCWFSArray ?? []); $ws++) {
                                                //Show the No of automated events for each WF Step
                                                $wfsid = '';
                                                $wfsid = $tempPCWFSArray[$ws]['WFSID'];
                                                $wftrId = cypher::myEncryption($wfsid) . '_tr_' . cypher::myEncryption($WFId);

                                                ?>
                                                <tr id="WFSID_<?php echo $tempPCWFSArray[$ws]['WFSID'] ?>"
                                                    class="<?php echo $wftrId; ?>">
                                                    <td title="Display Order"
                                                        class="workfolwDisOrder_<?php echo $WFId; ?>"
                                                        style="text-align: center;"><?php echo $tempPCWFSArray[$ws]['dispOrder'] ?></td>
                                                    <td class="tooltipClass"><span
                                                                title="<?php echo $tempPCWFSArray[$ws]['description'] ?>"><?php echo $tempPCWFSArray[$ws]['steps'] ?></span>
                                                    </td>
                                                    <td>
                                                        <a class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon btn-sm m-2"
                                                           id="button_<?php echo Strings::removeSpaceWithSpecialChars($PCWorkflowArray[$p]['WFName']) . '_' . Strings::removeSpaceWithSpecialChars($tempPCWFSArray[$ws]['steps'])?>_walkthru"
                                                           style="text-decoration:none;"
                                                           data-id="PCID=<?php echo cypher::myEncryption($assignedPCID) ?>&WFId=<?php echo cypher::myEncryption($WFId) ?>&WFSID=<?php echo cypher::myEncryption($tempPCWFSArray[$ws]['WFSID']) ?>&isSLMOnly=<?php echo cypher::myEncryption($isSLMOnly) ?>&isHMLO=<?php echo cypher::myEncryption($isHMLOClientType) ?>&showSaveBtn=1"
                                                           data-href="<?php echo CONST_URL_POPS; ?>addWorkflowSteps.php"
                                                           data-toggle='modal' data-target='#exampleModal1'
                                                           data-wsize='modal-xl'
                                                           data-name="Edit workflow steps for: &quot;<?php echo $PCWorkflowArray[$p]['WFName']; ?>&quot;">
                                                            <i class="tooltipClass far fa-edit"
                                                               title="Click here to edit steps"></i></a>

                                                        <a class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon btn-sm"
                                                           style="text-decoration:none;"
                                                           href="javascript:deleteWFSteps('<?php echo cypher::myEncryption($assignedPCID) ?>','<?php echo cypher::myEncryption($tempPCWFSArray[$ws]['WFSID']) ?>', '<?php echo cypher::myEncryption($WFId) ?>');"><i
                                                                    class="flaticon2-trash"
                                                                    title="Click to delete steps"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                                <?php
                                            }
                                        }
                                    }
                                }
                                $ws1++;
                                ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    ?>
</div>
<div class="row m-5 saveDispOrder" style="display: none; text-align: center;">
    <div class="btn btn-primary btn-sm  dispButton tooltipClass" title="Click to save new display order">Save Display
        Order
    </div>
    <span class="form-text text-muted ">(Note: Click the Save button to save your re-ordering)</span>
</div>
<script type='text/javascript'>
    $(document).ready(function () {

        <?php
        for($j = 0;$j < count($PCWorkflowArray ?? []);$j++) {
        $WFID = '';
        $WFID = trim($PCWorkflowArray[$j]['WFID']);
        ?>
        $("#tabledivbody2_<?php echo $WFID;?>").sortable({
            items: "tr",
            cursor: 'move',
            opacity: 0.6,
            placeholder: "ui-state-highlight",
            axis: 'y',
            containment: "parent",
            update: function () {
                var serNo = 1;
                $(".workfolwDisOrder_<?php echo $WFID;?>").each(function () {
                    $(this).html(serNo);
                    serNo++;
                });
                $('#selectedWFId').val('<?php echo $WFID;?>');
                $('.saveDispOrder').css("display", "block");
                //$('.with-children-tip > *').hideTip();
            }
        });
        // $( "#tabledivbody" ).disableSelection();
        <?php
        }
        ?>
        $('.dispButton').click(function (event) {
            var selectedWFID = $('#selectedWFId').val();
            var order = $("#tabledivbody2_" + selectedWFID).sortable("serialize");
            var PCID = "<?php echo $assignedPCID; ?>";
            if (order != '') {
                order = order + '&PCID=' + PCID
            }
            $('#PCUpdMsgBottom').html('<img src="<?php echo IMG_PROGRESS_BAR; ?>">');
            $.post("updatePCWorkflowOrder.php", order, function (theResponse) {
                $("#sessMsg").html('<h4>' + theResponse + '<h4>');
                $('.saveDispOrder').css("display", "none");
                $('#PCUpdMsgBottom').html('');
                // $('.with-tip, .with-children-tip > *').tip();
            });

            event.preventDefault();
        });
    });
</script>
<style>
    .trHover tr:hover {
        background-color: #E6E6FA;
    }
</style>
