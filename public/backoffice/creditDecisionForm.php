<?php

use models\constants\gl\glDate;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\lendingwise\tblCreditDecisionForm;
use models\standard\Dates;
use models\standard\Strings;
use models\Controllers\LMRequest\creditDecision as creditDecisionController;

require_once 'LMRequest/creditDecision/sections/creditDecision.php';
require_once 'LMRequest/creditDecision/sections/secondSign.php';
require_once 'LMRequest/creditDecision/sections/clearToClose.php';
require_once 'LMRequest/creditDecision/sections/dealDesk.php';
require_once 'LMRequest/creditDecision/sections/exception.php';


$creditDecisionFormData = creditDecisionController::$creditDecisionFormData;
if(!$creditDecisionFormData) {
    $creditDecisionFormData = [
        new tblCreditDecisionForm(),
    ];
}
?>
<input type="hidden" name="creditDecisionFormId" id="creditDecisionFormId" value="<?php echo $creditDecisionFormData->id;?>">
<div class="clearfix"></div>
<div class="col-md-12" id="buttons">
    <div class="col-md-12 text-center mt-5 mb-5">
        <input type="submit" class="btn btn-primary btnSave" name="btnSave" id="saveBtn" value="Save">
        <input type="submit" class="btn btn-primary btnSave ml-1" name="btnSave" id="saveNextBtn"
               value="Save & Next"
               onclick="if(this.disabled===false) {return true;} else {return false;}">
    </div>
</div>
<?php
Strings::includeMyScript(['/backoffice/LMRequest/creditDecision/js/creditDecision.js']);
?>
