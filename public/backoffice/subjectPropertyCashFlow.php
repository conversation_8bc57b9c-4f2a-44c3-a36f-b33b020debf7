<?php
global $fileTab, $publicUser, $LMRId, $typeOfHMLOLoanRequesting, $subjectPropertySectionDispOpt, $allowToEdit,
       $tabIndex, $vacancy, $vacancyFactor, $effectiveGrossIncome, $taxes1, $spcf_hoafees,
       $lessActualExpenses, $netOperatingIncome, $capRateToolTip, $capRate, $reserves, $reserveFactor,
       $reserveValue, $reserveFactoron, $serviceDebt, $grossAnnualRentLargestTenant,
       $actualRentsInPlaceCommercial, $vacancyFactorCommercial, $vacancyCommercial, $waterSewer, $electricity,
       $gas, $repairsMaintenance, $legal, $payroll, $misc, $tenantReimursements, $managementExpense,
       $actualRentsInPlaceCheckbox, $actualRentsInPlaceCommercialCheckbox;
global $managementExpensePercentage, $activeTab, $netMonthlyPayment;
global $actualRentsInPlace, $fileMC;
global $tenantContribution, $otherIncome;
global $vacancyOtherIncome, $otherIncomeVacancyRate;
global $vacancyTenantContribution, $tenantContributionVacancyRate;
global $commonAreaUtilities, $elevatorMaintenance, $replacementReserves, $other;
global $PCID;


use models\composite\debtServiceRatioPITIA;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glFileTabs;
use models\constants\reserveFactoron;
use models\constants\show1003FieldsArray;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\cashFlowWebform;
use models\Controllers\LMRequest\Property;
use models\Controllers\loanForm;
use models\CustomField;
use models\HMLOLoanTermsCalculation;
use models\lendingwise\tblFile;
use models\PageVariables;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Strings;
use models\tabIndex;

$taxes1 = LMRequest::File()->LMRId ? LMRequest::File()->getTblIncomeInfo_by_LMRId()->taxes1 :  Strings::showField('taxes1', 'incomeInfo');
$spcf_hoafees = LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->spcf_hoafees;

$show1003FieldsArray = show1003FieldsArray::$show1003FieldsArray;

require 'cashFlowVars.php';

$secArr = BaseHTML::sectionAccess2(['sId' => 'SPCF', 'opt' => $fileTab, 'activeTab' => $activeTab]);
loanForm::pushSectionID('SPCF');

$ischk = count($secArr) > 0 ? 'checked' : '';
if ($publicUser == 1) {
    if ($LMRId > 0 && $ischk != '') {
        $ischk = 'checked';
    } else {
        $ischk = '';
    }
}
$show1003FieldsArr = [];
$show1003FieldsArr['SPCF'] = array_diff(array_keys($secArr), $show1003FieldsArray['SPCF']);
if ($activeTab == '1003') {  /* story-31529 hide some fields based on the requirement mentioned in story*/
    foreach ($show1003FieldsArr['SPCF'] as $showFieldkey => $hideFieldValue) {
        if (is_array($secArr[$hideFieldValue])) {
            $secArr[$hideFieldValue]['fieldDisplay'] = 0;
            loanForm::hideField('SPCF', $hideFieldValue);
        }
    }
}
$tabIndex = (!$tabIndex) ? tabIndex::next() : $tabIndex;
//calculate debtServiceRatioPITIA
$debtServiceRatioPITIA = 0;
$fieldEnable = loanForm::isVisible('debtServiceRatioPITIA');
if ($fieldEnable) {
    $annualInsurancePolicyPremium = Strings::showField('annualPremium', 'fileHMLOPropertyInfo');
    $params = [
            'effectiveGrossIncome' => $effectiveGrossIncome,
            'netMonthlyPayment'    => $netMonthlyPayment,
            'annualHoaFee'         => $spcf_hoafees,
    ];
    $debtServiceRatioPITIA = debtServiceRatioPITIA::getReport($params);
}

if (in_array($PCID, glCustomJobForProcessingCompany::$glCustomHideCashFlowSection) &&
        in_array($activeTab, [glFileTabs::HMLI, glFileTabs::PI]) &&
        !$publicUser
) {
    $subjectPropertySectionDispOpt = 'display:none';
}

$showEffectiveGrossIncome = loanForm::showField('reserveFactor');

if (loanForm::isVisible('actualRentsInPlace') || loanForm::isVisible('actualRentsInPlaceCommercial')) {
    $showEffectiveGrossIncome = 'secShow';
}


$showExpenseInfo = 'secHide';
$expenseInfoFields = [
        'spcf_taxes1',
        'spcf_annualPremium',
        'spcf_hoafees',
        'lessActualExpenses',
        'waterSewer',
        'electricity',
        'gas',
        'repairsMaintenance',
        'legal',
        'payroll',
        'misc',
        'commonAreaUtilities',
        'elevatorMaintenance',
        'replacementReserves',
        'other',
        'tenantReimursements',
        'managementExpense',
        'reserveFactor',
        'capRate',
        'reserveFactor',
        'reserveFactor',
        'debtServiceRatio',
        'serviceDebt',
        'debtServiceRatioPITIA',
        'grossAnnualRentLargestTenant',
];
foreach ($expenseInfoFields as $field) {
    if (loanForm::isVisible($field)) {
        $showExpenseInfo = 'secShow';
        break;
    }
}
?>

<!-- subjectPropertyCashFlow.php -->
<div class="card card-custom HMLOLoanInfoSections subjectPropertySection SPCF SPCFCard <?php
if (trim($ischk) == 'checked') {
    echo 'secShow';
} else {
    echo 'secHide';
}

?> <?php echo BaseHTML::parentFieldAccess(['fNm' => 'typeOfHMLOLoanRequesting', 'sArr' => $secArr, 'pv' => $typeOfHMLOLoanRequesting, 'mv' => 'Commercial Purchase,Commercial Rate / Term Refinance,Commercial Cash Out Refinance']); ?>"
     style=" <?php echo $subjectPropertySectionDispOpt; ?> <?php echo $HMLOLoanInfoSectionsDisp; ?>">

    <div class="card-header card-header-tabs-line bg-gray-100">
        <?php echo loanForm::toggleSectionV2(
                'SPCF',
                true,
                true
        ); ?>
    </div>

    <div class="card-body SPCFCard_body">

        <?php if (cashFlowWebform::$isCashFlowWebform) { ?>
            <input type="hidden" name="addRentableSqFt" id="addRentableSqFt"
                   value="<?php echo Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyRentableSqFt; ?>">
            <input type="hidden" name="noUnitsOccupied" id="noUnitsOccupied"
                   value="<?php echo Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfUnits; ?>">
        <?php } ?>

        <div class="form-group row col-lg-12 m-0 mb-4 px-0" id="incomeInfoTitle">
            <label class="bg-secondary py-2  col-lg-12"><b>Income Info</b></label>
        </div>
        <div class="row incomeInfoTitle">
            <div class="form-group col-md-6 col-sm-12 <?php echo loanForm::showField('actualRentsInPlace'); ?>">
                <div class="row">
                    <?php echo loanForm::label('actualRentsInPlace', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                            'actualRentsInPlace',
                            $allowToEdit,
                            $tabIndex++,
                            $actualRentsInPlace,
                            'input-sm',
                            'calculateCashFlow();',
                                '',
                            intval($actualRentsInPlaceCheckbox) == 1
                        ); ?>
                    </div>
                </div>
            </div>

            <div
                    class="form-group col-md-6 actualRentsInPlaceCheckboxCls <?php echo loanForm::showField('actualRentsInPlace'); ?> <?php if (cashFlowWebform::$isCashFlowWebform) {
                        echo ' d-none ';
                    } ?>">
                <div class="row">
                    <?php echo loanForm::label('actualRentsInPlace', 'col-md-5',
                            'This calculates the Monthly Rent from the Rent Roll Entry. The current 
                        calculation sums all Residential unit type entries x 12.',
                            'Residential Calculate from Rent Roll?'); ?>
                    <div class="col-md-2 checkbox-list ">
                        <?php echo loanForm::simpleCheckbox(
                                'actualRentsInPlaceCheckbox',
                                'newCheck',
                                'checkbox font-weight-bold',
                                'calculateResidentialMonthlyAmounts();',
                                $actualRentsInPlaceCheckbox == '1',
                                '1',
                                '',
                                'actualRentsInPlaceCheckbox'
                        ); ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="row incomeInfoTitle">
            <div class="form-group col-md-6 <?php echo loanForm::showField('vacancyFactor'); ?>">
                <div class="row">
                    <?php echo loanForm::label('vacancyFactor', 'col-md-5 ', '', '', '', 'Vacancy'); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'vacancy',
                                $allowToEdit,
                                $tabIndex++,
                                $vacancy,
                                'input-sm',
                                "calculateVacancy('vacancy')",
                                '',
                        ); ?>
                    </div>
                </div>
            </div>

            <div class="form-group col-md-6 col-sm-12 <?php echo loanForm::showField('vacancyFactor'); ?>">
                <div class="row">
                    <?php echo loanForm::label('vacancyFactor', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::percentage(
                                'vacancyFactor',
                                $allowToEdit,
                                $tabIndex++,
                                $vacancyFactor,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                                6
                        ); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="row incomeInfoTitle">
            <div
                    class="form-group col-md-6 col-sm-12 <?php echo loanForm::showField('actualRentsInPlaceCommercial'); ?>">
                <div class="row">
                    <?php echo loanForm::label('actualRentsInPlaceCommercial', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'actualRentsInPlaceCommercial',
                                $allowToEdit,
                                $tabIndex++,
                                $actualRentsInPlaceCommercial,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                                intval($actualRentsInPlaceCommercialCheckbox) == 1
                        ); ?>
                    </div>
                </div>
            </div>

            <div
                    class="form-group col-md-6 actualRentsInPlaceCommercialCheckboxCls <?php echo loanForm::showField('actualRentsInPlaceCommercial'); ?> <?php if (cashFlowWebform::$isCashFlowWebform) {
                        echo ' d-none ';
                    } ?>">
                <div class="row">
                    <?php echo loanForm::label('actualRentsInPlaceCommercial', 'col-md-5',
                            'This calculates the Monthly Rent from the Rent Roll Entry. The current calculation sums all Commercial unit type entries x 12.', 'Commercial Calculate from Rent Roll?'); ?>
                    <div class="col-md-2 checkbox-list ">
                        <?php echo loanForm::simpleCheckbox(
                                'actualRentsInPlaceCommercialCheckbox',
                                'newCheck',
                                'checkbox font-weight-bold',
                                'calculateCommercialMonthlyAmounts();',
                                $actualRentsInPlaceCommercialCheckbox == '1',
                                1,
                                '',
                                'actualRentsInPlaceCommercialCheckbox'
                        ); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="row incomeInfoTitle">
            <div class="form-group col-md-6 <?php echo loanForm::showField('reserveFactorCommercial'); ?>">
                <div class="row">
                    <?php echo loanForm::label('reserveFactorCommercial', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text" class="form-control input-sm"
                                   value="<?php echo Currency::formatDollarAmountWithDecimalZeros($vacancyCommercial); ?>"
                                   name="vacancyCommercial" id="vacancyCommercial"
                                   onblur="currencyConverter(this, this.value);calculateVacancy('vacancyCommercial')"
                                   placeholder="0.00"
                                   autocomplete="off">
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group col-md-6 col-sm-12 <?php echo loanForm::showField('vacancyFactorCommercial'); ?>">
                <div class="row">
                    <?php echo loanForm::label('vacancyFactorCommercial', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::percentage(
                                'vacancyFactorCommercial',
                                $allowToEdit,
                                $tabIndex++,
                                $vacancyFactorCommercial,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                                6
                        ); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="row incomeInfoTitle">
            <div class="form-group col-md-6 col-sm-12 <?php echo loanForm::showField('otherIncome'); ?>">
                <div class="row">
                    <?php echo loanForm::label('otherIncome', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'otherIncome',
                                $allowToEdit,
                                $tabIndex++,
                                $otherIncome,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                        ); ?>
                    </div>
                </div>
            </div>

        </div>
        <div class="row incomeInfoTitle">
            <div class="form-group col-md-6 <?php echo loanForm::showField('vacancyOtherIncome'); ?>">
                <div class="row">
                    <?php echo loanForm::label('vacancyOtherIncome', 'col-md-5', '', ''); ?>
                    <div class="col-md-7">
                        <div class="input-group">
                            <?php echo loanForm::currency(
                                    'vacancyOtherIncome',
                                    $allowToEdit,
                                    $tabIndex++,
                                    $vacancyOtherIncome,
                                    'input-sm',
                                    "calculateVacancy('vacancyOtherIncome');",
                                    '',
                            ); ?>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group col-md-6 col-sm-12 <?php echo loanForm::showField('otherIncomeVacancyRate'); ?>">
                <div class="row">
                    <?php echo loanForm::label('otherIncomeVacancyRate', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::percentage(
                                'otherIncomeVacancyRate',
                                $allowToEdit,
                                $tabIndex++,
                                $otherIncomeVacancyRate,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                                6
                        ); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="row incomeInfoTitle">
            <div class="form-group col-md-6 col-sm-12 <?php echo loanForm::showField('tenantContribution'); ?>">
                <div class="row">
                    <?php echo loanForm::label('tenantContribution', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'tenantContribution',
                                $allowToEdit,
                                $tabIndex++,
                                $tenantContribution,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                        ); ?>
                    </div>
                </div>
            </div>

        </div>
        <div class="row incomeInfoTitle">
            <div class="form-group col-md-6 <?php echo loanForm::showField('vacancyTenantContribution'); ?>">
                <div class="row">
                    <?php echo loanForm::label('vacancyTenantContribution', 'col-md-5', '', ''); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'vacancyTenantContribution',
                                $allowToEdit,
                                $tabIndex++,
                                $vacancyTenantContribution,
                                'input-sm',
                                "calculateVacancy('vacancyTenantContribution');",
                                '',
                        ); ?>
                    </div>
                </div>
            </div>
            <div
                    class="form-group col-md-6 col-sm-12 <?php echo loanForm::showField('tenantContributionVacancyRate'); ?>">
                <div class="row">
                    <?php echo loanForm::label('tenantContributionVacancyRate', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::percentage(
                                'tenantContributionVacancyRate',
                                $allowToEdit,
                                $tabIndex++,
                                $tenantContributionVacancyRate,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                                6
                        ); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="row incomeInfoTitle">
            <div
                    class="form-group col-md-6 <?php echo loanForm::showField('effectiveGrossIncome'); ?> <?php echo $showEffectiveGrossIncome; ?>">
                <div class="row">
                    <?php echo loanForm::label2(
                            'effectiveGrossIncome',
                            'col-md-5',
                            '(Residential Gross Potential Income - Vacancy + Commercial Gross Potential Income - Vacancy Commercial + Other Income - Vacancy Other Income + Tenant Contributions)',
                            '',
                            '',
                            'Effective Gross Income'
                    );
                    ?>
                    <div class="col-md-7">
                        <div class="input-group">
                            <h5>$ <span id="effectiveGrossIncome">
                                <?php echo Currency::formatDollarAmountWithDecimal($effectiveGrossIncome); ?>
                            </span>
                            </h5>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group row col-lg-12 m-0 mb-4 px-0 <?php echo $showExpenseInfo; ?>" id="expenseInfoTitle">
            <label class="bg-secondary  py-2  col-lg-12"><b>Expense Info</b></label>
        </div>
        <div class="row">
            <div class="form-group col-md-6 <?php echo loanForm::showField('spcf_taxes1'); ?>">
                <div class="row">
                    <?php echo loanForm::label('spcf_taxes1', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'spcf_taxes1',
                                $allowToEdit,
                                $tabIndex++,
                                $taxes1,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                        ); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="form-group col-md-6 <?php echo loanForm::showField('spcf_annualPremium'); ?>">
                <div class="row">
                    <?php echo loanForm::label('spcf_annualPremium', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'spcf_annualPremium',
                                $allowToEdit,
                                $tabIndex++,
                                Strings::showField('annualPremium', 'fileHMLOPropertyInfo'),
                                'input-sm',
                                "calculateCashFlow(); mirrorField.mirrorFieldValues('spcf_annualPremium','annualPremium');",
                                '',
                        ); ?>
                    </div>
                </div>
            </div>
            <div class="form-group col-md-6 <?php echo loanForm::showField('spcf_hoafees'); ?>">
                <div class="row">
                    <?php echo loanForm::label('spcf_hoafees', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'spcf_hoafees',
                                $allowToEdit,
                                $tabIndex++,
                                $spcf_hoafees,
                                'input-sm',
                                'calculateCashFlow();calculateHMLONetMonthlyPayment();',
                                '',
                        ); ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="form-group col-md-6 <?php echo loanForm::showField('lessActualExpenses'); ?>">
                <div class="row">
                    <?php echo loanForm::label('lessActualExpenses', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'lessActualExpenses',
                                $allowToEdit,
                                $tabIndex++,
                                $lessActualExpenses,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                        ); ?>
                    </div>
                </div>
            </div>

            <div class="form-group col-md-6 <?php echo loanForm::showField('waterSewer'); ?>">
                <div class="row">
                    <?php echo loanForm::label('waterSewer', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'waterSewer',
                                $allowToEdit,
                                $tabIndex++,
                                $waterSewer,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                        ); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="form-group col-md-6 <?php echo loanForm::showField('electricity'); ?>">
                <div class="row">
                    <?php echo loanForm::label('electricity', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'electricity',
                                $allowToEdit,
                                $tabIndex++,
                                $electricity,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                        ); ?>
                    </div>
                </div>
            </div>

            <div class="form-group col-md-6 <?php echo loanForm::showField('gas'); ?>">
                <div class="row">
                    <?php echo loanForm::label('gas', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'gas',
                                $allowToEdit,
                                $tabIndex++,
                                $gas,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                        ); ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">

            <div class="form-group col-md-6 <?php echo loanForm::showField('repairsMaintenance'); ?>">
                <div class="row">
                    <?php echo loanForm::label('repairsMaintenance', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'repairsMaintenance',
                                $allowToEdit,
                                $tabIndex++,
                                $repairsMaintenance,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                        ); ?>
                    </div>
                </div>
            </div>

            <div class="form-group col-md-6 <?php echo loanForm::showField('legal'); ?>">
                <div class="row">
                    <?php echo loanForm::label('legal', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'legal',
                                $allowToEdit,
                                $tabIndex++,
                                $legal,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                        ); ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">

            <div class="form-group col-md-6 <?php echo loanForm::showField('payroll'); ?>">
                <div class="row">
                    <?php echo loanForm::label('payroll', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'payroll',
                                $allowToEdit,
                                $tabIndex++,
                                $payroll,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                        ); ?>
                    </div>
                </div>
            </div>

            <div class="form-group col-md-6 <?php echo loanForm::showField('misc'); ?>">
                <div class="row">
                    <?php echo loanForm::label('misc', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'misc',
                                $allowToEdit,
                                $tabIndex++,
                                $misc,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                        ); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="form-group col-md-6 <?php echo loanForm::showField('commonAreaUtilities'); ?>">
                <div class="row">
                    <?php echo loanForm::label('commonAreaUtilities', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'commonAreaUtilities',
                                $allowToEdit,
                                $tabIndex++,
                                $commonAreaUtilities,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                        ); ?>
                    </div>
                </div>
            </div>
            <div class="form-group col-md-6 <?php echo loanForm::showField('elevatorMaintenance'); ?>">
                <div class="row">
                    <?php echo loanForm::label('elevatorMaintenance', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'elevatorMaintenance',
                                $allowToEdit,
                                $tabIndex++,
                                $elevatorMaintenance,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                        ); ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="form-group col-md-6 <?php echo loanForm::showField('replacementReserves'); ?>">
                <div class="row">
                    <?php echo loanForm::label('replacementReserves', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'replacementReserves',
                                $allowToEdit,
                                $tabIndex++,
                                $replacementReserves,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                        ); ?>
                    </div>
                </div>
            </div>
            <div class="form-group col-md-6 <?php echo loanForm::showField('other'); ?>">
                <div class="row">
                    <?php //echo loanForm::label('other', 'col-md-5 '); ?>
                    <label class="col-md-5 font-weight-bold"
                           for="other"><?php echo BaseHTML::fieldAccess(['fNm' => 'other', 'sArr' => $secArr, 'opt' => 'L']); ?></label>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'other',
                                $allowToEdit,
                                $tabIndex++,
                                $other,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                        ); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">

            <div class="form-group col-md-6 <?php echo loanForm::showField('tenantReimursements'); ?>">
                <div class="row">
                    <?php echo loanForm::label('tenantReimursements', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'tenantReimursements',
                                $allowToEdit,
                                $tabIndex++,
                                $tenantReimursements,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                        ); ?>
                    </div>
                </div>
            </div>

            <div class="form-group col-md-6 <?php echo loanForm::showField('managementExpense'); ?>">
                <div class="row">
                    <?php echo loanForm::label('managementExpense', 'col-md-5 '); ?>
                    <div class="col-md-4">
                        <?php echo loanForm::currency(
                                'managementExpense',
                                $allowToEdit,
                                $tabIndex++,
                                $managementExpense,
                                'input-sm',
                                'calculateManagementExpenseReverse();',
                                '',
                        ); ?>
                    </div>
                    <div class="col-md-3 <?php echo loanForm::showField('managementExpensePercentage'); ?>">
                        <?php echo loanForm::percentage(
                                'managementExpensePercentage',
                                $allowToEdit,
                                $tabIndex++,
                                $managementExpensePercentage,
                                'input-sm',
                                'calculateManagementExpense();'
                        ); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="form-group row col-md-6 <?php echo loanForm::showField('netOperatingIncome'); ?>">
                <?php echo loanForm::label('netOperatingIncome', 'col-md-5', ''); ?>
                <div class="col-md-7">
                    <div class="input-group">
                        <h5>$ <span id="totalNetOperatingIncome">
                                <?php echo Currency::formatDollarAmountWithDecimal($netOperatingIncome) ?>
                            </span>
                        </h5>
                    </div>
                </div>
            </div>

            <div
                    class="form-group row col-md-6 <?php echo loanForm::showField('capRate'); ?> <?php if (cashFlowWebform::$isCashFlowWebform) {
                        echo ' d-none ';
                    } ?> ">
                <?php echo loanForm::label('capRate', 'col-md-5', $capRateToolTip, ''); ?>
                <div class="col-md-7">
                    <div class="input-group">
                        <h5><span id="capRate">
                                <?php echo Currency::formatDollarAmountWithDecimal($capRate) ?>
                            </span>%
                        </h5>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">

            <div class="form-group row col-md-6 <?php echo loanForm::showField('reserveFactor'); ?>">
                <?php echo loanForm::label('reserves', 'col-md-5 '); ?>
                <div class="col-md-7">
                    <div class="input-group">
                        <h5>$ <span
                                    id="reserves"><?php echo Currency::formatDollarAmountWithDecimalZeros($reserves) ?></span>
                        </h5>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="form-group col-md-6 <?php echo loanForm::showField('reserveFactor'); ?>">
                <div class="row">
                    <div class="form-group font-weight-bold col-md-6 no-padding-right">
                        <div class="row">
                            <?php echo loanForm::label('reserveFactor', 'col-md-5 '); ?>
                            <div class="col-md-7">
                                <?php echo loanForm::currency(
                                        'reserveFactor',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $reserveFactor,
                                        'input-sm',
                                        'calculateCashFlow();',
                                        '',
                                ); ?>
                            </div>
                        </div>
                    </div>

                    <div class="form-group col-md-3">
                        <?php echo loanForm::number(
                                'reserveValue',
                                $allowToEdit,
                                $tabIndex++,
                                $reserveValue,
                                'mirrorRentAndunitValues()',
                                'input-sm'
                        ); ?>
                    </div>

                    <div class="form-group col-md-3">
                        <?php echo loanForm::select(
                                'reserveFactoron',
                                $allowToEdit,
                                $tabIndex++,
                                intval($reserveFactoron),
                                reserveFactoron::$options,
                                'getReserveData()',
                                'input-sm',
                        ); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">

            <div
                    class="form-group col-md-6 <?php echo loanForm::showField('debtServiceRatio'); ?> <?php if (cashFlowWebform::$isCashFlowWebform) {
                        echo ' d-none ';
                    } ?> ">
                <div class="row">
                    <?php echo loanForm::label('debtServiceRatio', 'col-md-5', 'Debit Service Ratio = [Net Operating Income/ (monthly payment *12)]'); ?>
                    <div class="col-md-7">
                        <div class="input-group">
                            <h5>
                                <span data-show="<?php echo intval(loanForm::isVisible('debtServiceRatio')); ?>"
                                      id="debtServiceRatio"><?php echo Currency::formatDollarAmountWithDecimalZeros(HMLOLoanTermsCalculation::$debtServiceRatio) ?></span>
                            </h5>
                        </div>
                    </div>
                </div>
            </div>


            <div
                    class="form-group col-md-6 <?php echo loanForm::showField('serviceDebt'); ?> <?php if (cashFlowWebform::$isCashFlowWebform) {
                        echo ' d-none ';
                    } ?> ">
                <div class="row">
                    <?php echo loanForm::label('serviceDebt', 'col-md-5', 'Effective Gross Income - Total Operating Expenses'); ?>
                    <div class="col-md-7">
                        <div class="input-group">
                            <h5>$<span id="serviceDebt"
                                       class="ml-2"><?php echo Currency::formatDollarAmountWithDecimalZeros($serviceDebt) ?></span>
                            </h5>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div
                    class="form-group col-md-6 <?php echo loanForm::showField('debtServiceRatioPITIA'); ?> <?php if (cashFlowWebform::$isCashFlowWebform) {
                        echo ' d-none ';
                    } ?> ">
                <div class="row">
                    <?php echo loanForm::label2(
                            'debtServiceRatioPITIA',
                            'col-md-5',
                            '
[(Effective Gross Income) / (PITIA * 12) ] <div class="separator separator-dashed separator-dark"></div>
<br>[($' . Currency::formatDollarAmountWithDecimalZeros($effectiveGrossIncome) . ') / ($' . Currency::formatDollarAmountWithDecimalZeros($netMonthlyPayment) . ' * 12)]
<div class="separator separator-dashed separator-dark"></div>
<br> A monthly payment and an income is required for this calculation to run
'
                    ); ?>
                    <div class="col-md-7">
                        <div class="input-group">
                            <h5><span id="debtServiceRatioPITIA"
                                      class="ml-2"><?php echo Currency::formatDollarAmountWithDecimalZeros($debtServiceRatioPITIA); ?></span>
                            </h5>
                        </div>
                    </div>
                </div>
            </div>


            <div class="form-group col-md-6 <?php echo loanForm::showField('grossAnnualRentLargestTenant'); ?>">
                <div class="row">
                    <?php echo loanForm::label('grossAnnualRentLargestTenant', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php echo loanForm::currency(
                                'grossAnnualRentLargestTenant',
                                $allowToEdit,
                                $tabIndex++,
                                $grossAnnualRentLargestTenant,
                                'input-sm',
                                'calculateCashFlow();',
                                '',
                        ); ?>
                    </div>
                </div>
            </div>
        </div>

        <?php echo CustomField::RenderForTabSection(
                intval($PCID),
                tblFile::class,
                LMRequest::$LMRId,
                'SPCF',
                $fileTab,
                $activeTab,
                LMRequest::myFileInfo()->getFileTypes(),
                LMRequest::myFileInfo()->getLoanPrograms()
        ); ?>
    </div>
</div>
<!-- subjectPropertyCashFlow.php -->
<?php loanForm::popSectionID();?>
<script src="<?php echo CONST_SITE_URL; ?>assets/js/cashFlow.js?<?php echo CONST_JS_VERSION; ?>"></script>
