<?php

use models\composite\oFileUpdate\estimatedProjectCostSave;
use models\composite\oFileUpdate\saveFileInternalLoanPrograms;
use models\composite\oFileUpdate\saveFileServices;
use models\composite\oHMLOInfo\saveHMLOPropertyInfo;
use models\composite\oLockFile\lockFileOperation;
use models\composite\oPC\isPCAllowedAutomation;
use models\constants\typeOfHMLOLoanRequesting;
use models\Controllers\backoffice\HMLONewLoanInfoSave;
use models\Controllers\LMRequest\HUDCalculation;
use models\Controllers\LMRequest\loanSetting;
use models\Controllers\LMRequest\partnerShips;
use models\Controllers\LMRequest\refinanceMortgage;
use models\CustomField;
use models\cypher;
use models\Database2;
use models\lendingwise\tblAutomatedRuleRequestV2;
use models\lendingwise\tblFile;
use models\lendingwise_log\ChangeLog;
use models\PageVariables;
use models\Request;
use models\standard\Strings;
use models\standard\UserAccess;

session_start();
require '../includes/util.php';

require 'initPageVariables.php';
require 'getPageVariables.php';
UserAccess::checkReferrerPgs(['url' => 'LMRequest.php']);
UserAccess::CheckAdminUse();
global $userRole, $userName, $userFName, $userLName, $userGroup, $PCID;
$cnt = 0;


HMLONewLoanInfoSave::Init();

$executiveId = HMLONewLoanInfoSave::$executiveId;
$brokerNumber = HMLONewLoanInfoSave::$brokerNumber;
$LMRId = HMLONewLoanInfoSave::$LMRId;
$responseId = HMLONewLoanInfoSave::$responseId;
$clientId = HMLONewLoanInfoSave::$clientId;
$goToTab = HMLONewLoanInfoSave::$goToTab;
$activeTab = HMLONewLoanInfoSave::$activeTab;
$btnValue = HMLONewLoanInfoSave::$btnValue;
$isHMLOOpt = HMLONewLoanInfoSave::$isHMLOOpt;
$isEFOOpt = HMLONewLoanInfoSave::$isEFOOpt;
$LMRClientType = HMLONewLoanInfoSave::$LMRClientType;
$op = HMLONewLoanInfoSave::$op;
$isSave = HMLONewLoanInfoSave::$isSave;

$fileTypesTxtArray = HMLONewLoanInfoSave::$fileTypesTxtArray;


if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    ChangeLog::LogChanges(
        tblFile::class,
        $LMRId,
        basename(__FILE__, '.php'),
        $_REQUEST,
        PageVariables::$userNumber
    );
}

/**
 ** Description    : HMLO Modules Loan info Tab New section Field save functionality
 ** Developer    : Viji, Venkatesh
 ** Author        : AwataSoftsys
 ** Date            : May 10, 2017
 **/

$allowRepeat = Request::GetClean('allowRepeat') ?? '';
$triggerRule = Request::GetClean('triggerRule') ?? 'No';


if ($LMRId > 0 && ($isHMLOOpt == 1 || $isEFOOpt == 1)) {
    //Loan Settings
    $LSCount = $_POST['LSCount'] ?? 0;
    if ($LSCount) {
        $postVars = [
            'lsId'                 => $_REQUEST['lsId'],
            'maxLTVPercent'        => $_REQUEST['maxLTVPercent'],
            'minDSCRRatio'         => $_REQUEST['minDSCRRatio'],
            'minActBal'            => $_REQUEST['minActBal'],
            'floorRate'            => $_REQUEST['floorRate'],
            'prepayLockOut'        => $_REQUEST['prepayLockOut'],
            'isAdjustable'         => $_REQUEST['isAdjustable'],
            'isPreStabilized'      => $_REQUEST['isPreStabilized'],
            'initialTermYears'     => $_REQUEST['initialTermYears'],
            'initialRateMargin'    => $_REQUEST['initialRateMargin'],
            'initialRateIndex'     => $_REQUEST['initialRateIndex'],
            'initialRateFloor'     => $_REQUEST['initialRateFloor'],
            'initialAmor'          => $_REQUEST['initialAmor'],
            'initialTermAdjust'    => $_REQUEST['initialTermAdjust'],
            'indicatedRateDate'    => $_REQUEST['indicatedRateDate'],
            'indicatedRatePercent' => $_REQUEST['indicatedRatePercent'],

            'stabilizedrateMargin' => $_REQUEST['stabilizedrateMargin'],
            'stabilizedRateIndex'  => $_REQUEST['stabilizedRateIndex'],
            'stabilizedRateFloor'  => $_REQUEST['stabilizedRateFloor'],
            'stabilizedAmor'       => $_REQUEST['stabilizedAmor'],

            'loanSettingTermFields' => $_REQUEST['loanSettingTermFields'],

        ];
        loanSetting::saveData($postVars, $LMRId);
    }

    if (isset($_REQUEST['fileLock'])) {

        $lockTabOpt = '';
        $lockSectionOpt = '';
        $fileLockedStatus = 0;

        if (isset($_REQUEST['lockTabOpt'])) {
            $lockTabOpt = $_REQUEST['lockTabOpt'];
        }
        if (isset($_REQUEST['lockSectionOpt'])) {
            $lockSectionOpt = $_REQUEST['lockSectionOpt'];
        }
        $fileLockedStatus = $_REQUEST['fileLock'];

        // if($_REQUEST['fileLock'] == 1){
        $a = [
            'LMRId'            => $LMRId,
            'lockedUID'        => PageVariables::$userNumber,
            'lockedUserRole'   => $userRole,
            'lockedBy'         => $userName,
            'lockTabOpt'       => $lockTabOpt,
            'lockSectionOpt'   => $lockSectionOpt,
            'fileLockedStatus' => $fileLockedStatus,
        ];
        $res = lockFileOperation::getReport($a); // refactored
    }

    if ($LMRClientType) {
        saveFileServices::getReport([
            'LMRId'         => $LMRId,
            'LMRClientType' => $LMRClientType,
        ]); // refactored
    } //File Service Type Save and Update Added july 24, 2017 SK...

    $HMLOArray = [
        'p'         => $_POST,
        'LMRId'     => $LMRId,
        'saveTab'   => 'HMLI',
        'clientId'  => $clientId,
        'userName'  => $userFName . ' ' . $userLName,
        'userGroup' => $userGroup,
        'PCID'      => $PCID,
    ];
    saveHMLOPropertyInfo::getReport($HMLOArray); // refactored
    /* estimatedProjectCostSave */
    estimatedProjectCostSave::getReport($HMLOArray); // refactored
    /* estimatedProjectCostSave */
    //Insert or Update only for Refinance fields
    $typeOfHMLOLoanRequesting = HMLONewLoanInfoSave::$typeOfHMLOLoanRequesting;
    if (in_array($typeOfHMLOLoanRequesting, [
        typeOfHMLOLoanRequesting::COMMERCIAL_CASH_OUT_REFINANCE
        , typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
        , typeOfHMLOLoanRequesting::DELAYED_PURCHASE
        , typeOfHMLOLoanRequesting::COMMERCIAL_RATE_TERM_REFINANCE
        , typeOfHMLOLoanRequesting::REFINANCE
        , typeOfHMLOLoanRequesting::RATE_TERM_REFINANCE
    ])) {
        refinanceMortgage::saveData($_REQUEST['rcmFields'], $LMRId); // refactored
    }
    partnerShips::saveData($_REQUEST['partnerShipFields'], $LMRId); // refactored

}

if (isset($_REQUEST['LMRInternalLoanProgram']) || isset($_REQUEST['LMRInternalLoanProgramShadow'])) {
    saveFileInternalLoanPrograms::getReport([
        'LMRId' => $LMRId,
        'LMRInternalLoanProgram' => $_REQUEST['LMRInternalLoanProgram'],
    ]);
}

//check if the PC is enabled for Automation
$allowAutomation = 0;
$PCID = intval(Strings::GetSess('PCID')) ?? null;
//allow automation
$allowAutomation = isPCAllowedAutomation::getReport($PCID);
if ($allowAutomation) { //$allowAutomation set in getPageVariables.php
    include('automatedRulesActionController.php');
}

//re-calculate HUD
HUDCalculation::process((int)$LMRId);

tblAutomatedRuleRequestV2::Trigger($LMRId, $PCID); // last
CustomField::Save(
    tblFile::class,
    $LMRId,
    $PCID,
    PageVariables::$userNumber,
    PageVariables::$userRole,
    PageVariables::$userGroup
);
Database2::saveLogQuery();

if (!Strings::GetSess('msg')) {
    Strings::SetSess('msg', 'Updated Successfully');
}


if ($userRole == 'Branch') $redirect = CONST_URL_BRSSL;
else if ($userRole == 'Agent') $redirect = CONST_URL_AG_SSL;
else    $redirect = CONST_URL_BOSSL;

$redirect .= 'LMRequest.php?eId=' . cypher::myEncryption($executiveId) . '&lId=' . cypher::myEncryption($LMRId) . '&rId=' . cypher::myEncryption($responseId) . '&op=' . trim($op) . '&tabOpt=';
if ($btnValue == 'Save' || $isSave == 1) $redirect .= $activeTab;
else $redirect .= $goToTab;
header('Location: ' . $redirect);

exit();

