<?php

use models\composite\oCFPBAudit\getCFPBAuditFileCnt;
use models\composite\oFile\getFileData;
use models\composite\oFile\getFileData_new;
use models\composite\oFile\getFileIDs_new;
use models\composite\oFile\getPipelinePage;
use models\composite\oHomeReport\calculateHOMEFilePaymentDetails;
use models\composite\oLender\getAllLenderList;
use models\composite\oLoanAudit\getLoanAuditFileCnt;
use models\composite\oModules\getLibModules;
use models\composite\oPLOPlan\getPLOPlansForClient;
use models\composite\oServiceType\getLoanProgram;
use models\constants\accessSecondaryWFPC;
use models\constants\agentException;
use models\constants\agentPCException;
use models\constants\branchException;
use models\constants\CFPBAuditingUsersHeader;
use models\constants\CFPBAuditStatusArray;
use models\constants\gl\glNotesTypeArray;
use models\constants\gl\glPrimaryLoanModStatusArray;
use models\constants\gl\glSalesRepresentativeArray;
use models\constants\gl\glStaleFileArray;
use models\constants\loanAuditStatusArray;
use models\constants\PCException;
use models\constants\priorityLevelArray;
use models\constants\salesRepArray;
use models\Controllers\backoffice\myPipeline;
use models\Controllers\backoffice\myPipeline\myPipelineSearch;
use models\Controllers\backoffice\myPipelineColumns;
use models\Database2;
use models\cypher;
use models\PageVariables;
use models\Request;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Strings;
use models\standard\UserAccess;

session_start();
require '../includes/util.php';
require '../includes/router.php';

//global variables
global $allowToCFPBSubmitForPC, $allowPCUserToSubmitCFPB, $allowToViewCFPBPipeline, $allowCFPBAuditing;
global $permissionToREST, $subscribeToREST, $subscribePCToHOME, $subscribeToHOME;
global $agentBranchArray, $allowToViewAllFiles;
global $userName, $viewPrivateNotes, $userSeeBilling;
global $viewPublicNotes, $externalBroker, $secondaryBrokerNumber, $WFServiceType;

global $isPCHMLO, $PCStatusInfoForTab;

UserAccess::CheckAdminUse();
require 'initPageVariables.php';
require 'getPageVariables.php';

$PCException = PCException::$PCException;
$accessSecondaryWFPC = accessSecondaryWFPC::$accessSecondaryWFPC;
$agentPCException = agentPCException::$agentPCException;
$branchException = branchException::$branchException;
$agentException = agentException::$agentException;
$salesRepArray = salesRepArray::$salesRepArray;
$glPrimaryLoanModStatusArray = glPrimaryLoanModStatusArray::$glPrimaryLoanModStatusArray;
$CFPBAuditingUsersHeader = CFPBAuditingUsersHeader::$CFPBAuditingUsersHeader;

if (PageVariables::$userRole == 'Client') {
    $redirectUrl = CONST_URL_CL . 'index.php';
    header('Location: ' . $redirectUrl);
    exit();
}

$noOfRecords = null;
$fileType = '';
$msg = '';
$executiveId = '';
$brokerNumb = '';
$employeeId = '';
$statusOpt = '';
$statusOptArray = [];
$tempExecutiveId = 0;
$exSelected = 0;
$subStatus = '';
$lenderName = '';
$endDate = '';
$startDate = '';
$leadSource = '';
$tabVal = 0;
$reportId = 'All';
$primaryStatus = '';
$pageNumber = 1;
$sortOpt = '';
$orderBy = 'desc';
$primaryStatusArray = [];
$stateArray = [];
$selectedBranchInfoArray = [];
$searchFieldArray = [];

$RESTExecutiveName = '';
$RESTBranchId = 0;
$RESTPCName = '';
$RESTProcId = 0;
$PCName = '';
$branchName = '';
$clientID = '';
$searchTerm = '';
$selExecName = '';
$brokerName = '';
$employeeName = '';
$myStaleDay = '';
$propertyState = '';
$LMRClientType = '';
$LMRClientTypeArray = [];
$LMRInternalClientType = [];
$priorityLevel = '';
$newPg = 0;
$billingDueDate = '';
$noOfRecordsPerPage = 25;
$paymentStatus = '';
$notesType = '';
$LMRClientTypes = '';
$resArray = [];
$PCPLMSFilterArray = [];
$PCPLMSFilterKeyArray = [];
$PCPLMSInactiveArray = [];
$activeFile = 1;
$PCPLMSArray = [];
$fileIdArray = [];
$cntInfoArray = [];
$allCount = 0;
$regonlyCount = 0;
$qryClient = '';
$qryLen = '';
$fileInfoArray = [];
$fileInfo = [];
$LMRDataArray = [];
$LMRDataKeyArray = [];
$LMRDataCnt = 0;
$substatusInfoArray = [];
$fileClientTypeArray = [];
$WFListArray = [];
$LMRWFArray = [];
$PCWFArray = [];
$FileWorkFlowStepsArray = [];
$cklistNotRequiredArray = [];
$LMRChecklistArray = [];
$PCChecklistArray = [];
$empChkInfoArray = [];
$tempPCChecklistArray = [];
$LenderInfo1Array = [];
$LenderInfo2Array = [];
$QAInfoArray = [];
$proposalInfoArray = [];
$uploadedDocArray = [];
$eSignedDocArray = [];
$incomeInfoArray = [];
$empWFInfoArray = [];
$branchWFInfoArray = [];
$agentWFInfoArray = [];
$notesInfoArray = [];
$billingInfo = [];
$scheduledEmailArray = [];
$assignedStaff = [];
$taskListArray = [];
$taskEmpInfo = [];
$caseId = '';
$RESTPCID = 0;
$RESTTotalOwed = '';
$RESTUploadInfoArray = [];
$PCInfo = [];
$RESTReportArray = [];
$tempRESTReportArray = [];
$tempExeIDArray = [];
$RESTFeeArray = [];
$qry = '';
$RESTPaymentDetailsArray = [];
$salesRep = '';
$propertyStateArray = [];
$fileBranchInfo = [];
$fileAgentInfo = [];
$fileLoanOfficerInfo = [];
$op = '';
$searchField = '';
$filterInfoArray = [];
$PCServiceTypeArray = [];
$PCClientTypeInfoArray = [];
$PCStatusInfo = [];
$PCSubStatusInfo = [];
$PCStatusInfoArray = [];
$fileResultArray = [];
$agentPCIDArray = [];
$exceptionPCIDArray = [];
$planInfoArray = [];
$clientInfo = [];
$mySalesRep = '';
$subEndDate = '';
$subStDate = '';
$exportIp = [];
$CaseFileIdInfoArray = [];
$clientPaymentInfo = [];
$glLMRClientTypeArray = [];
$HRHistoryInfoArray = [];
$HOMEPaymentDetailsArray = [];
$HOMETotalOwed = 0;
$HOMETotalPaid = 0;
$HOMEBalance = 0;
$massStatusCnt = '';
$massStatusValue = '';
$opn = '';
$modifiedStartDate = '';
$modifiedEndDate = '';
$closingStartDate = '';
$closingEndDate = '';

$maturityStartDate = '';
$maturityEndDate = '';
$targetClosingStartDate = '';
$targetClosingEndDate = '';

$appraisalStartDate = '';
$appraisalEndDate = '';

$receivedStartDate = '';
$receivedEndDate = '';

$disclosureSentDateStart = '';
$disclosureSentDateEnd = '';

$fileStatusInfo = [];
$listingRealtorInfo2 = [];
$fileStatusArray = [];
$file2Info = [];
$favFiles = [];
$PCPrimaryStatusTabArray = [];

$newStatusId = 0;
$PLMStatusId = 0;
$pipelineUserPreferencesKeyArray = [];
$pipelineHeaderKeyArray = [];
$listingRealtorInfo = [];
$titleContactInfo = [];
$fileHMLOEntityInfo = [];
$insuranceCompContactInfo = [];
$fileHMLOListOfRepairs = [];
$filePropInfo = [];
$assetsInfoArray = [];
$AssetsInfo = [];
$incomeArray = [];
$fileWFNotesInfo = [];
$fileChecklistNotesInfo = [];
$proposalInfo = [];
$assignedBoStaff = [];
$loanAuditInfoArray = [];
$loanAuditProductInfo = [];
$loanAuditStatus = 'All';
$CFPBAuditStatus = 'All';
$workflowInfoArray = [];
$workflowInfo = [];
$WFID = [];
$workflowSetps = [];
$workflowSetpsInfo = [];
$WFSID = [];
$WFSNotCompletedId = [];
$WFStatus = 1;
$workFlowStepStatus = 1;
$pipelineInfoArray = [];
$permissionsToEdit = [];
$fileStatus = '';
$contactId = 0;
$lenderNameArray = [];
$fileContactsInfo = [];
$fileVelocityInfo = [];
$QAInfo = [];
$loanOriginationInfo = [];
$clientCFPBInfo = [];
$fileStatusHistory = [];
$CFPBAuditInfoArray = [];
$CFPBAuditFileCnt = [];
$empCFPBInfo = [];
$branchCFPBInfo = [];
$agentCFPBInfo = [];
$lockCFPBFileInfoArray = [];
$assignedCFPBAuditorInfo = [];
$loggedInUserPCID = 0;
$multiplePrimaryStatus = '';
$fileMFLoanTerms = [];
$isHMLO = 0;
$fileModuleTypeArray = [];
$PCModuleInfo = [];

$fileHMLOPropertyInfo = [];
$fileHMLONewLoanInfo = [];

$fileLOAssetsInfo = [];
$fileHMLOInfo = [];
$fileHMLOExperienceInfo = [];
$fileHMLOBackGroundInfo = [];
$lienPosition = '';
$openHouseDate = '';
$allFileResponseIDs = '';
$allFileResponseIDsCnt = 0;
$AllPCInfo = [];

$loggedInUserPCID = PageVariables::isSuper() ? 0 : PageVariables::$PCID;


$loanOfficerSearch = '';
$secondaryBrokerName = '';
$selectedLoanOfficerArray = [];

if (isset($_REQUEST['secondaryBrokerName'])) $secondaryBrokerName = trim($_REQUEST['secondaryBrokerName']);

if (isset($_REQUEST['loanOfficerSearch'])) {
    if (is_array($_REQUEST['loanOfficerSearch'])) {
        foreach ($_REQUEST['loanOfficerSearch'] as $eachLoanOfficerID) {
            $selectedLoanOfficerArray[] = (trim($eachLoanOfficerID));
        }
        $loanOfficerSearch = implode(',', $selectedLoanOfficerArray);
    } else {
        $loanOfficerSearch = trim($_REQUEST['loanOfficerSearch']);
    }
}
myPipelineSearch::$loanOfficerList = $loanOfficerSearch;


if (isset($_POST['fileType'])) $fileType = trim($_POST['fileType']);
else if (isset($_REQUEST['fileType'])) $fileType = trim(cypher::myDecryption(trim($_REQUEST['fileType'])));
if (isset($_SESSION['msg'])) $msg = trim($_SESSION['msg']);

/* Check permission to see CFPB Pipeline Access */
if ($fileType == 'CFPB') {
    if (PageVariables::$userRole == 'Auditor Manager'
        || PageVariables::$userRole == 'CFPB Auditor'
        || ($allowToCFPBSubmitForPC == 1 && $allowPCUserToSubmitCFPB == 1 && $allowToViewCFPBPipeline == 1)
        || ($allowCFPBAuditing == 1)) {
        doNothing();
    } else {
        header('Location:' . CONST_SITE_URL . 'unauthorizedPage.php');
        exit();
    }
}

/* Check permission to see CFPB Pipeline Access */
if (
    ($permissionToREST == 1 && $subscribeToREST == 1) ||
    ($subscribePCToHOME == 1 && $subscribeToHOME == 1) ||
    (PageVariables::isSuper()) ||
    (PageVariables::$userRole == 'Sales') ||
    (PageVariables::$userRole == 'Auditor') ||
    (PageVariables::$userRole == 'CFPB Auditor') ||
    (PageVariables::$userRole == 'Auditor Manager')

) {
    /** Check permission to see REST Pipeline, home report pipeline for the user other than Super & Sales **/
    doNothing();
} elseif ($fileType == 2 || $fileType == 4) {
    header('Location:' . CONST_SITE_URL . 'unauthorizedPage.php');
    exit();
}


if (PageVariables::isSuper() || PageVariables::$userRole == 'REST') {
    $userType = PageVariables::$userRole;
} else {
    $userType = PageVariables::$userGroup;
}

$remarks = 'LI';
if (isset($_REQUEST['opn'])) $opn = trim($_REQUEST['opn']);
if (isset($_REQUEST['op'])) $op = trim($_REQUEST['op']);
if ($op == 'open') {
    $remarks = 'Legacy Pipeline';
}

$searchPCID = 0;

if (isset($_REQUEST['sortBy'])) $sortOpt = trim($_REQUEST['sortBy']);
if (isset($_REQUEST['orderBy'])) $orderBy = trim($_REQUEST['orderBy']);
if (isset($_REQUEST['pageNumber'])) $pageNumber = trim($_REQUEST['pageNumber']);
if (isset($_REQUEST['newPg'])) $newPg = trim($_REQUEST['newPg']);
if (isset($_REQUEST['searchPCID'])) {
    $searchPCID = trim($_REQUEST['searchPCID']);
    if ($searchPCID != PageVariables::$PCID && !PageVariables::isSuper()) {
        header('Location:' . CONST_SITE_URL . 'unauthorizedPage.php');
        exit();
    }
}
$executiveIds = '';
if (isset($_REQUEST['PCName'])) {
    $PCName = trim($_REQUEST['PCName']);
}

$selectedBranchIDs = [];

if (isset($_REQUEST['eId'])) {
    if (is_array($_REQUEST['eId'])) {
        foreach ($_REQUEST['eId'] as $eachEid) {
            $selectedBranchIDs[] = trim(cypher::myDecryption(trim($eachEid)));
        }
        $executiveId = implode(',', $selectedBranchIDs);
    } else {
        $executiveId = trim(cypher::myDecryption(trim($_REQUEST['eId'])));
    }
}
myPipelineSearch::$branchList = $executiveId;

if (isset($_REQUEST['branchName'])) $branchName = trim($_REQUEST['branchName']);
if ($opn == 'UB') {
    if (isset($_REQUEST['newBId'])) $executiveId = trim(cypher::myDecryption(trim($_REQUEST['newBId'])));
    if (isset($_REQUEST['newBranchName'])) $branchName = trim($_REQUEST['newBranchName']);
}
if (isset($_REQUEST['brokerName'])) $brokerName = trim($_REQUEST['brokerName']);

$selectedBrokerArray = [];

if (isset($_REQUEST['broker'])) {
    if (is_array($_REQUEST['broker'])) {
        foreach ($_REQUEST['broker'] as $eachBrokerID) {
            $selectedBrokerArray[] = trim($eachBrokerID);
        }
        $brokerNumb = implode(',', $selectedBrokerArray);
    } else {
        $brokerNumb = trim($_REQUEST['broker']);
    }
}
myPipelineSearch::$brokerList = $brokerNumb;

$selectedEmployeeArray = [];
if (isset($_POST['employeeId'])) {
    if (is_array($_POST['employeeId'])) {
        foreach ($_POST['employeeId'] as $eachEmployeeID) {
            $selectedEmployeeArray[] = (trim($eachEmployeeID));
        }
        $employeeId = implode(',', $selectedEmployeeArray);
    } else {
        $employeeId = trim($_POST['employeeId']);
    }
} else if (isset($_REQUEST['empId'])) {
    $employeeId = cypher::myDecryption(trim($_REQUEST['empId']));
}

myPipelineSearch::$employeeList = $employeeId;

if (isset($_REQUEST['employeeName'])) $employeeName = trim($_REQUEST['employeeName']);
if (isset($_REQUEST['staleDay'])) $myStaleDay = trim($_REQUEST['staleDay']);
if (isset($_REQUEST['priorityLevel'])) $priorityLevel = trim($_REQUEST['priorityLevel']);
if (isset($_REQUEST['stDate'])) $startDate = trim($_REQUEST['stDate']);
if (isset($_REQUEST['endDate'])) $endDate = trim($_REQUEST['endDate']);
if (isset($_REQUEST['subStDate'])) $subStDate = trim($_REQUEST['subStDate']);
if (isset($_REQUEST['subEndDate'])) $subEndDate = trim($_REQUEST['subEndDate']);
//	if (isset($_REQUEST["lenderName"]))     	 $lenderName 		 = trim($_REQUEST["lenderName"]);
if (isset($_REQUEST['leadSource'])) $leadSource = trim($_REQUEST['leadSource']);
if (isset($_REQUEST['activeFile'])) $activeFile = trim($_REQUEST['activeFile']);
if (isset($_REQUEST['noOfRecordsPerPage'])) $noOfRecordsPerPage = trim($_REQUEST['noOfRecordsPerPage']);
if (isset($_POST['LMRClientType'])) $LMRClientTypeArray = $_POST['LMRClientType'];
else if (isset($_REQUEST['LMRClientType'])) $LMRClientType = trim($_REQUEST['LMRClientType']);

if (isset($_REQUEST['LMRInternalClientType'])) $LMRInternalClientType = ($_REQUEST['LMRInternalClientType']);
if (isset($_REQUEST['billingDueDate'])) $billingDueDate = trim($_REQUEST['billingDueDate']);
if (isset($_REQUEST['notesType'])) $notesType = trim($_REQUEST['notesType']);
if (isset($_REQUEST['searchTerm'])) $searchTerm = trim($_REQUEST['searchTerm']);
if (isset($_REQUEST['clientID'])) $clientID = trim($_REQUEST['clientID']);
if ($clientID) {
    $searchTerm = str_replace(' ', '+', $searchTerm);
}
if (isset($_POST['searchField'])) {
    if (is_array($_POST['searchField'])) $searchFieldArray = $_POST['searchField'];
    else                                     $searchField = trim($_POST['searchField']);
} else if (isset($_REQUEST['searchField'])) $searchField = trim($_REQUEST['searchField']);

if (isset($_POST['propertyState'])) {
    if (is_array($_POST['propertyState'])) $propertyStateArray = $_POST['propertyState'];
    else                                     $propertyState = trim($_POST['propertyState']);
} else if (isset($_REQUEST['propertyState'])) $propertyState = trim($_REQUEST['propertyState']);

if (isset($_REQUEST['primaryStatus'])) $primaryStatus = trim($_REQUEST['primaryStatus']);
if (isset($_REQUEST['loanAuditStatus'])) $loanAuditStatus = trim($_REQUEST['loanAuditStatus']);
if (isset($_REQUEST['CFPBAuditStatus'])) $CFPBAuditStatus = trim($_REQUEST['CFPBAuditStatus']);
if (isset($_POST['WFID'])) {
    $WFID = array_filter($_POST['WFID']);
}
if (isset($_GET['WFID'])) {
    if ($_GET['WFID']) {
        $WFID = explode(',', trim($_GET['WFID']));
    }
}

if (isset($_POST['WFSId'])) {
    $WFSID = array_filter($_POST['WFSId']);
}
if (isset($_GET['WFSId'])) {
    if ($_GET['WFSId']) {
        $WFSID = explode(',', $_GET['WFSId']);
    }
}
if (isset($_REQUEST['WFSNotCompletedId'])) {
    $WFSNotCompletedId = array_values(array_filter($_REQUEST['WFSNotCompletedId']));
}
if (isset($_REQUEST['WFStatus'])) $WFStatus = trim($_REQUEST['WFStatus']);
if (isset($_REQUEST['workFlowStepStatus'])) $workFlowStepStatus = trim($_REQUEST['workFlowStepStatus']);
if (isset($_REQUEST['modifiedStartDate'])) $modifiedStartDate = trim($_REQUEST['modifiedStartDate']);
if (isset($_REQUEST['modifiedEndDate'])) $modifiedEndDate = trim($_REQUEST['modifiedEndDate']);
if (isset($_REQUEST['closingStartDate'])) $closingStartDate = trim($_REQUEST['closingStartDate']);
if (isset($_REQUEST['closingEndDate'])) $closingEndDate = trim($_REQUEST['closingEndDate']);

if (isset($_REQUEST['maturityStartDate'])) $maturityStartDate = trim($_REQUEST['maturityStartDate']);
if (isset($_REQUEST['maturityEndDate'])) $maturityEndDate = trim($_REQUEST['maturityEndDate']);

if (Request::isset('targetClosingStartDate')) $targetClosingStartDate = Request::GetClean('targetClosingStartDate');
if (Request::isset('targetClosingEndDate')) $targetClosingEndDate = Request::GetClean('targetClosingEndDate');

if (isset($_REQUEST['appraisalStartDate'])) $appraisalStartDate = trim($_REQUEST['appraisalStartDate']);
if (isset($_REQUEST['appraisalEndDate'])) $appraisalEndDate = trim($_REQUEST['appraisalEndDate']);

if (isset($_REQUEST['receivedStartDate'])) $receivedStartDate = trim($_REQUEST['receivedStartDate']);
if (isset($_REQUEST['receivedEndDate'])) $receivedEndDate = trim($_REQUEST['receivedEndDate']);

if (isset($_REQUEST['disclosureSentDateStart'])) $disclosureSentDateStart = trim($_REQUEST['disclosureSentDateStart']);
if (isset($_REQUEST['disclosureSentDateEnd'])) $disclosureSentDateEnd = trim($_REQUEST['disclosureSentDateEnd']);

$paymentBased = Request::GetClean('paymentBased') ?? '';

if (isset($_REQUEST['contactSearchId'])) {
    if (is_array($_REQUEST['contactSearchId'])) {
        foreach ($_REQUEST['contactSearchId'] as $eachContactSearchIdID) {
            $selectedContactsArray[] = trim($eachContactSearchIdID);
        }
        $contactsNumbers = implode(',', $selectedContactsArray);
    } else {
        $contactsNumbers = trim($_REQUEST['contactSearchId']);
    }
}
myPipelineSearch::$contactsList = $contactsNumbers;
$referringParty = null;
if (isset($_REQUEST['referringParty'])) $referringParty = trim($_REQUEST['referringParty']);

if (isset($_REQUEST['contactId'])) $contactId = trim(cypher::myDecryption($_REQUEST['contactId']));
if (isset($_REQUEST['lienPosition'])) $lienPosition = trim($_REQUEST['lienPosition']);
if (isset($_POST['openHouseDate'])) $openHouseDate = trim($_POST['openHouseDate']);
if (isset($_POST['lenderName'])) {
    if (is_array($_POST['lenderName'])) {
        $lenderNameArray = $_POST['lenderName'];
    } else {
        $lenderName = trim($_POST['lenderName']);
    }
} elseif (isset($_REQUEST['lenderName'])) {
    $lenderName = trim($_REQUEST['lenderName']);
}

if ($LMRClientType) {
    $LMRClientTypeArray[] = $LMRClientType;
}
$WFStepIDsArray = [];
$WFStepIDs = '';

if ($WFID == 'Array') {
    $WFID = [];
}

if ($WFSID == 'Array') {
    $WFSID = [];
}
$employeeReport = $_REQUEST['employeeReport'] ?? '';
if (in_array($employeeReport, ['filesUniqueUpdated', 'filesUniqueGENotes'])) {
    $modifiedStartDate = $modifiedStartDate ? date('m/d/Y', strtotime($modifiedStartDate)) : '';
    $modifiedEndDate = $modifiedEndDate ? date('m/d/Y', strtotime($modifiedEndDate)) : '';
}

$userSearchFieldsPreference = [];

$PCException = PCException::$PCException;
$accessSecondaryWFPC = accessSecondaryWFPC::$accessSecondaryWFPC;
$agentPCException = agentPCException::$agentPCException;
$glNotesTypeArray = glNotesTypeArray::$glNotesTypeArray;
$priorityLevelArray = priorityLevelArray::$priorityLevelArray;
$glStaleFileArray = glStaleFileArray::$glStaleFileArray;
$glSalesRepresentativeArray = glSalesRepresentativeArray::$glSalesRepresentativeArray;
$CFPBAuditStatusArray = CFPBAuditStatusArray::$CFPBAuditStatusArray;
$loanAuditStatusArray = loanAuditStatusArray::$loanAuditStatusArray;

myPipeline::setVars(
    $loggedInUserPCID,
    PageVariables::$userGroup,
    PageVariables::$userNumber,
    PageVariables::isSuper() ? $searchPCID : PageVariables::$PCID,
    PageVariables::$userRole,
    $executiveId,
    $brokerNumb,
    $reportId,
    $externalBroker
);

myPipeline::Init();

myPipelineColumns::Init(
    PageVariables::$userNumber,
    PageVariables::isSuper() ? $searchPCID : PageVariables::$PCID,
    PageVariables::$userGroup,
    PageVariables::$userRole,
    $fileType,
    $PCModuleInfo,
    $loggedInUserPCID,
    $isPCHMLO,
    $allowToCFPBSubmitForPC
);

myPipelineColumns::initColumns(
    $fileType,
    $allowCFPBAuditing,
    $CFPBAuditingUsersHeader
);

$AllPCInfo = myPipeline::$AllPCInfo;
$PCID = myPipeline::$PCID;
$exResultArray = myPipeline::$exResultArray;

/*** Search contact list ***/
if ($contactId) {
    myPipeline::$reportId = 'All';
}

if (isset($_REQUEST['reportId'])) {
    myPipeline::$reportId = trim($_REQUEST['reportId']);
}

if (in_array($PCID, $accessSecondaryWFPC)) {
    if (isset($_POST['WFStepID'])) {
        if (is_array($_POST['WFStepID'])) {
            $WFStepIDsArray = $_POST['WFStepID'];
        } else {
            $WFStepIDs = trim($_POST['WFStepID']);
        }
    } elseif (isset($_REQUEST['WFStepID'])) {
        $WFStepIDs = trim($_REQUEST['WFStepID']);
    }
}
if (isset($_REQUEST['MPS'])) {
    $multiplePrimaryStatus = trim(cypher::myDecryption($_REQUEST['MPS']));

    if (trim($multiplePrimaryStatus)) {
        $fileStatusArray = preg_split('/[, ]+/', $multiplePrimaryStatus);
        myPipeline::$reportId = $multiplePrimaryStatus;
    }
}


if ($primaryStatus && isset($_POST['but_submit'])) {
    myPipeline::$reportId = $primaryStatus;
} else if (isset($_POST['but_submit'])) {
    myPipeline::$reportId = 'All';
} else {
    $primaryStatus = '';
}

$leadStatusIDArray = [];
$leadStatusID = 0;


/*** Search with primary loan mod status ***/
if (PageVariables::$userRole == 'Branch') {
    if ($executiveId == '') {
        $executiveId = trim(PageVariables::$userNumber);
        if (array_key_exists($PCID, $branchException)) {
            $executiveId .= ',' . implode(',', $branchException[$PCID]);
        } else {
            $executiveId = trim(PageVariables::$userNumber);
        }
    }
} elseif (PageVariables::$userRole == 'Agent') {
    /** Get agent assigned PCIDs **/
    $agentBranchKeys = [];
    $BRIDArray = [];
    $assignedBRIDArray = [];
    $leadsBRIDArray = [];
    $assignedBRID = '';
    $leadsBRID = '';
    $BRID = '';
    $AID = 0;

    $agentBranchKeys = array_keys($agentBranchArray);
    $processingCompanyArray = [];
    for ($i = 0; $i < count($agentBranchKeys); $i++) {
        $tempBRID = 0;
        $tempArray = [];
        $processingCompanyId = 0;
        $tempBRID = trim($agentBranchKeys[$i]);
        $tempArray = $agentBranchArray[$tempBRID];

        if (count($tempArray) > 0) {
            $processingCompanyArray[] = $tempArray['processingCompanyId'];
        }
        if (trim($tempArray['agentFileAccess']) == 3 && $externalBroker == 1) {
            $BRIDArray[] = $tempBRID;
        }
        if (trim($tempArray['agentFileAccess']) == 2 && $externalBroker == 1) {
            $leadsBRIDArray[] = $tempBRID;
            if (trim($tempArray['defaultPrimaryStatusForFA']) > 0) {
                $leadStatusIDArray[] = trim($tempArray['defaultPrimaryStatusForFA']);
            }
            if (trim($tempArray['defaultPrimaryStatus']) > 0) {
                $leadStatusIDArray[] = trim($tempArray['defaultPrimaryStatus']);
            }
        }
    }

    if (in_array($PCID, $PCException) || in_array($PCID, $agentPCException)) {
        /** Allow Common branch's+agent's files to be shared with all their other agents for pickup. Example: (1) FA mitigators agents to see all lead files. (2) Apex Agents to assign mailer data branch files to themselves.  **/

        if ($brokerNumb == '') {
            $brokerNumb = trim(PageVariables::$userNumber);
            if (array_key_exists($PCID, $agentException)) {
                $brokerNumb .= ',' . implode(',', $agentException[$PCID]);
            }
        }
    } elseif (count($BRIDArray) > 0 || count($leadsBRIDArray) > 0) {

        if (count($BRIDArray) > 0) {
            $BRID = implode(', ', $BRIDArray);
            $AID = trim(PageVariables::$userNumber);
            $ip['AID'] = $AID;
            $ip['BRID'] = $BRID;
        }

        if (count($leadsBRIDArray) > 0) {
            $leadsBRID = implode(', ', $leadsBRIDArray);
            $AID = trim(PageVariables::$userNumber);
            $ip['AID'] = $AID;
            $ip['leadsBRID'] = $leadsBRID;
        }

        if (count($processingCompanyArray) > 0) {
            PCException::$PCException = array_merge($PCException, array_unique($processingCompanyArray));
        }

    } else {
        if ($externalBroker == 0) {
            $brokerNumb = trim(PageVariables::$userNumber);
        }
    }
    if ($externalBroker > 0) {
        $ip['externalBroker'] = $externalBroker;
        if ($secondaryBrokerNumber > 0) {
            $ip['secondaryBrokerNumber'] = $secondaryBrokerNumber;
        }
    }

}

//if (isset($_REQUEST['secondaryBrokerNumber'])) {
//    if ($_REQUEST['secondaryBrokerNumber'] > 0) {
//        $ip['secondaryBrokerNumber'] = $_REQUEST['secondaryBrokerNumber'];
//    }
//}
if (!empty($_REQUEST['loanOfficerSearch'])) {
    myPipelineSearch::$loanOfficerList = $loanOfficerSearch;
    $ip['secondaryBrokerNumber'] = ($loanOfficerSearch) ? Arrays::explodeIntVals($loanOfficerSearch) : [];
}


if (isset($_POST['multiplePrimaryStatus'])) {
    if (is_array($_POST['multiplePrimaryStatus'])) {
        $fileStatusArray = $_POST['multiplePrimaryStatus'];
        $multiplePrimaryStatus = implode(', ', $fileStatusArray);
    } else {
        $multiplePrimaryStatus = trim($_POST['multiplePrimaryStatus']);
    }
} elseif (isset($_REQUEST['multiplePrimaryStatus'])) {
    $multiplePrimaryStatus = trim($_REQUEST['multiplePrimaryStatus']);
    if (trim($multiplePrimaryStatus)) $fileStatusArray = preg_split('/[, ]+/', $multiplePrimaryStatus);
}

if (isset($_POST['statusOpt'])) {
    if (is_array($_POST['statusOpt'])) {
        $statusOptArray = $_POST['statusOpt'];
        $statusOpt = implode(',', $statusOptArray);
    } else {
        $statusOpt = trim($_POST['statusOpt']);
    }
} elseif (isset($_REQUEST['statusOpt'])) {
    $statusOpt = trim($_REQUEST['statusOpt']);
}

$servicingStatusCodeArray = [];
$servicingStatusCode = '';
if (isset($_POST['servicingStatusCode'])) {
    if (is_array($_POST['servicingStatusCode'])) {
        $servicingStatusCodeArray = $_POST['servicingStatusCode'];
        $servicingStatusCode = implode(',', $servicingStatusCodeArray);
    } else {
        $servicingStatusCode = trim($_POST['servicingStatusCode']);
    }
} elseif (isset($_REQUEST['servicingStatusCode'])) {
    $servicingStatusCode = trim($_REQUEST['servicingStatusCode']);
    if (trim($servicingStatusCode)) $servicingStatusCodeArray = preg_split('/[, ]+/', $servicingStatusCode);
}


if (trim($propertyState)) $propertyStateArray = explode(',', $propertyState);
if (count($propertyStateArray) > 0) $propertyState = implode(',', $propertyStateArray);

if (trim($searchField)) $searchFieldArray = explode(',', $searchField);
if (count($searchFieldArray) > 0) $searchField = implode(',', $searchFieldArray);

if (trim($lenderName)) $lenderNameArray = explode(',', $lenderName);
if (count($lenderNameArray) > 0) $lenderName = implode(',', $lenderNameArray);

if (in_array($PCID, $accessSecondaryWFPC)) {
    if (trim($WFStepIDs)) $WFStepIDsArray = explode(',', $WFStepIDs);
    if (count($WFStepIDsArray) > 0) $WFStepIDs = implode(',', $WFStepIDsArray);

    $ip['WFStepIDs'] = $WFStepIDs;
}

// If NOT super or Manager, get emp ID from session proc number. ONLY if user is allowed to view all files. //
if (PageVariables::isSuper() || PageVariables::$userRole == 'Manager' || $allowToViewAllFiles == 1 || $employeeId != '') {
    doNothing();
} else {
    $employeeId = trim(PageVariables::$userNumber);
}

if (PageVariables::$userRole == 'REST' || ($fileType == 2 && (PageVariables::isSuper() || PageVariables::$userRole == 'Sales'))) {

    if (isset($_REQUEST['caseId'])) {
        $caseId = trim($_REQUEST['caseId']);
    }
    if (isset($_REQUEST['RESTBranchId'])) {
        $RESTBranchId = $executiveId = trim(cypher::myDecryption(trim($_REQUEST['RESTBranchId'])));
    }
    if (isset($_REQUEST['paymentStatus'])) $paymentStatus = trim($_REQUEST['paymentStatus']);
    if (isset($_REQUEST['mySalesRep'])) $mySalesRep = trim($_REQUEST['mySalesRep']);
    if (isset($_REQUEST['RESTExecutiveName'])) $RESTExecutiveName = trim($_REQUEST['RESTExecutiveName']);
    if (isset($_REQUEST['RESTProcId'])) $RESTProcId = trim($_REQUEST['RESTProcId']);
    if (isset($_POST['RESTPCName'])) $RESTPCName = trim($_POST['RESTPCName']);

    $ip['caseId'] = $caseId;
    $ip['PCID'] = $RESTProcId;
    $ip['paymentStatus'] = $paymentStatus;

}
if ($fileType == 'LA' && PageVariables::isSuper()) {
    if (isset($_REQUEST['paymentStatus'])) {
        $paymentStatus = trim($_REQUEST['paymentStatus']);
    }
    $ip['paymentStatus'] = $paymentStatus;
} elseif ($fileType == 4 && PageVariables::isSuper()) {
    if (isset($_POST['RESTPCName'])) $RESTPCName = trim($_POST['RESTPCName']);
    if (isset($_REQUEST['RESTExecutiveName'])) $RESTExecutiveName = trim($_REQUEST['RESTExecutiveName']);
    if (isset($_REQUEST['RESTProcId'])) $RESTProcId = $PCID = trim($_REQUEST['RESTProcId']);
    if (isset($_REQUEST['paymentStatus'])) $paymentStatus = trim($_REQUEST['paymentStatus']);
    if (isset($_REQUEST['RESTBranchId'])) {
        $RESTBranchId = $executiveId = trim(cypher::myDecryption(trim($_REQUEST['RESTBranchId'])));
    }

    $ip['PCID'] = $RESTProcId;
    $ip['paymentStatus'] = $paymentStatus;
}
$lenderListArray = [];

$lenderListArray = getAllLenderList::getReport(['approvedStatus' => 1]);

if (PageVariables::$userRole == 'Branch') {
    $redirectUrl = CONST_URL_BR;
} elseif (PageVariables::$userRole == 'Agent') {
    $redirectUrl = CONST_URL_AG;
} else {
    $redirectUrl = CONST_BO_URL;
}

/* 
 * Allow CFPB auditing team to search by PC
 */
$searchPCID = 0;
$searchPCID = $PCID;
if (PageVariables::$userRole == 'Auditor Manager'
    || PageVariables::$userRole == 'CFPB Auditor'
    || ($fileType == 'CFPB' && $allowCFPBAuditing == 1)
) {
    if (!isset($_REQUEST['searchPCID'])) {
        $searchPCID = 0;
    }
}

$myUrl = $redirectUrl . 'myPipeline.php?' . http_build_query([
        'searchTerm'            => $searchTerm,
        'statusOpt'             => $statusOpt,
        'sortBy'                => $sortOpt,
        'orderBy'               => $orderBy,
        'pageNumber'            => $pageNumber,
        'reportId'              => myPipeline::$reportId,
        'primaryStatus'         => $primaryStatus,
        'tabVal'                => $tabVal,
        'stDate'                => $startDate,
        'endDate'               => $endDate,
        'eId'                   => cypher::myEncryption($executiveId),
        'broker'                => $brokerNumb,
        'loanOfficerSearch'     => $loanOfficerSearch,
        'empId'                 => cypher::myEncryption($employeeId),
        'priorityLevel'         => $priorityLevel,
        'LMRClientType'         => $LMRClientType,
        'lenderName'            => $lenderName,
        'leadSource'            => $leadSource,
        'propertyState'         => $propertyState,
        'noOfRecordsPerPage'    => $noOfRecordsPerPage,
        'staleDay'              => $myStaleDay,
        'paymentBased'          => $paymentBased,
        'billingDueDate'        => $billingDueDate,
        'notesType'             => $notesType,
        'searchField'           => $searchField,
        'searchPCID'            => $searchPCID,
        'PCName'                => $PCName,
        'subStDate'             => $subStDate,
        'subEndDate'            => $subEndDate,
        'activeFile'            => $activeFile,
        'branchName'            => $branchName,
        'brokerName'            => $brokerName,
        'employeeName'          => $employeeName,
        'loanAuditStatus'       => $loanAuditStatus,
        'fileType'              => cypher::myEncryption($fileType),
        'RESTPCName'            => $RESTPCName,
        'RESTProcId'            => $RESTProcId,
        'RESTExecutiveName'     => $RESTExecutiveName,
        'RESTBranchId'          => cypher::myEncryption($RESTBranchId),
        'paymentStatus'         => $paymentStatus,
        'WFID'                  => implode(',', $WFID),
        'WFSID'                 => implode(',', $WFSID),
        'WFStatus'              => $WFStatus,
        'modifiedStartDate'     => $modifiedStartDate,
        'modifiedEndDate'       => $modifiedEndDate,
        'closingStartDate'      => $closingStartDate,
        'closingEndDate'        => $closingEndDate,
        'multiplePrimaryStatus' => $multiplePrimaryStatus,
        'CFPBAuditStatus'       => $CFPBAuditStatus,
        'WFStepID'              => $WFStepIDs,
        'multipleModuleCode'    => myPipeline::$multipleModuleCode,
        'servicingStatusCode'   => $servicingStatusCode,
        'lienPosition'          => $lienPosition,
        'openHouseDate'         => $openHouseDate,
        'clientID'              => $clientID,
        'contactSearchId'       => $contactsNumbers
    ]);

Strings::SetSess('pipelineUrl', '');
Strings::SetSess('pipelineUrl', $myUrl);

if (count($LMRClientTypeArray) > 0) {
    $LMRClientTypes = "'" . implode("','", $LMRClientTypeArray) . "'";
}
if (count($LMRInternalClientType) > 0) {
    $LMRInternalClientTypeText = "'" . implode("','", $LMRInternalClientType) . "'";
}
for ($glPc = 0; $glPc < count($glPrimaryLoanModStatusArray); $glPc++) {
    ${strtolower(str_replace(' ', '', $glPrimaryLoanModStatusArray[$glPc])) . 'Count'} = 0;
    ${strtolower(str_replace(' ', '', $glPrimaryLoanModStatusArray[$glPc])) . 'Tip'} = '';
}

$ip['searchTerm'] = $searchTerm;
$ip['clientID'] = $clientID;
$ip['staleDay'] = $myStaleDay;
$ip['paymentBased'] = $paymentBased;
$ip['contactsSearch'] = $contactsNumbers ?  Arrays::explodeIntVals($contactsNumbers):[];
$ip['billingDueDate'] = $billingDueDate;
$ip['statusOpt'] = $statusOpt;
$ip['employeeId'] = ($employeeId) ? Arrays::explodeIntVals($employeeId) : [];
$ip['LMRClientType'] = $LMRClientTypes;
$ip['LMRInternalClientType'] = $LMRInternalClientType;
$ip['orderBy'] = $orderBy;
//    $ip['lenderName'] 				= 	$lenderName;
$ip['brokerNumber'] = ($brokerNumb) ? Arrays::explodeIntVals($brokerNumb) : [];
$ip['branchId'] = ($executiveId) ? Arrays::explodeIntVals($executiveId) : [];
$ip['searchTerm'] = $searchTerm;
$ip['searchField'] = $searchFieldArray;
$ip['loanAuditStatus'] = $loanAuditStatus;
$ip['WFID'] = $WFID;
$ip['WFSID'] = $WFSID;
$ip['WFSNotCompletedId'] = $WFSNotCompletedId;
$ip['WFStatus'] = $WFStatus;
$ip['workFlowStepStatus'] = $workFlowStepStatus;
$ip['userGroup'] = PageVariables::$userGroup;
$ip['UID'] = PageVariables::$userNumber;
$ip['multipleStatus'] = $multiplePrimaryStatus;
$ip['CFPBAuditStatus'] = $CFPBAuditStatus;
$ip['multipleModuleCode'] = myPipeline::$multipleModuleCode;
$ip['servicingStatusCode'] = $servicingStatusCode;

$lenderNames = [];
for ($i = 0; $i < count($lenderNameArray); $i++) {
    $lenderNames[] = Strings::escapeQuote(trim($lenderNameArray[$i]));
}
if (count($propertyStateArray) > 0) $ip['propertyState'] = "'" . implode("','", $propertyStateArray) . "'";
if (count($lenderNameArray) > 0) $ip['lenderName'] = "'" . implode("','", $lenderNameArray) . "'";

if (PageVariables::isSuper()
    || PageVariables::$userRole == 'Manager'
    || PageVariables::$userRole == 'Processor'
    || PageVariables::$userRole == 'Negotiator'
    || PageVariables::$userRole == 'REST'
) {
    $ip['LMRExecutiveStatus'] = 'all';
}

if (PageVariables::isSuper() || PageVariables::$userRole == 'REST') {
    doNothing();
} else {
    $ip['PCID'] = $PCID;
}

if ($activeFile > 0) {
    $ip['activeStatus'] = $activeFile;
} else {
    $ip['activeStatus'] = '0';
}

if ($fileType == 4 || $fileType == 2 || PageVariables::$userRole == 'REST') {
    $ip['subStDate'] = $subStDate;
    $ip['subEndDate'] = $subEndDate;
}


$ip['remarks'] = $remarks;
$ip['noOfRecordsPerPage'] = $noOfRecordsPerPage;
$ip['pageNumber'] = ($pageNumber - 1) * $noOfRecordsPerPage;
$ip['userRole'] = PageVariables::$userRole;
$ip['priorityLevel'] = $priorityLevel;
$ip['leadSource'] = $leadSource;
$ip['restFile'] = $fileType;

if (PageVariables::$userRole == 'REST'
    || ($fileType == 2 && (PageVariables::isSuper() || PageVariables::$userRole == 'Sales'))
    || ($fileType == 4 && (PageVariables::isSuper() || PageVariables::$userRole == 'Sales'))
) {
    doNothing();
} else {
    $ip['PCID'] = $PCID;
}


$pipelineInfoArray = getPipelinePage::getReport(
    $PCID,
    implode(',', $WFID),
    $WFServiceType,
    myPipeline::$multipleModuleCode,
    PageVariables::$userNumber,
    PageVariables::$userGroup,
    $remarks
);

myPipelineColumns::$PCModuleInfo = $pipelineInfoArray['PCModuleInfo'] ?? [];
myPipelineColumns::InitColumnSettings();

if (count($pipelineInfoArray) > 0) {
    if (array_key_exists('states', $pipelineInfoArray)) {
        $stateArray = $pipelineInfoArray['states'];
        /*** Lib states ***/
    }
    if (array_key_exists('serviceTypes', $pipelineInfoArray)) {
        $glLMRClientTypeArray = $pipelineInfoArray['serviceTypes'];
        /*** Lib service types ***/
    }
    if (array_key_exists('permissionsToEdit', $pipelineInfoArray)) {
        $permissionsToEdit = $pipelineInfoArray['permissionsToEdit'];
        /*** Allowed PC status ***/
    }
}
$loanProgramInfo = [];
if (PageVariables::isSuper()) {

    $loanProgramInfo = getLoanProgram::getReport([
        'filetype' => Arrays::getArrayValue('multipleModuleCode', $_REQUEST),
        'pcid'     => $PCID,
    ]);
}

if (PageVariables::$userRole == 'REST'
    || ($fileType == 2 && (PageVariables::isSuper() || PageVariables::$userRole == 'Sales'))
    || ($fileType == 4 && (PageVariables::isSuper() || PageVariables::$userRole == 'Sales'))) {
    doNothing();
} else {
    $ip['PCID'] = $PCID;
    if ($op == 'open') {
        $planInfoArray = getPLOPlansForClient::getReport(
            'PLO',
            0,
            $PCID
        );
    }


    if (count($pipelineInfoArray) > 0) {
        $PCServiceTypeArray = $pipelineInfoArray['PCServiceType'];
        $PCStatusInfo = $pipelineInfoArray['PCStatusInfo'];
        $PCSubStatusInfo = $pipelineInfoArray['PCSubStatusInfo'];
        $workflowInfo = $pipelineInfoArray['fileWorkflow'];
        $workflowSetps = $pipelineInfoArray['PCWFSteps'];
        foreach ($workflowSetps as $workflowId => $eachworkFlowStepInfo) {
            if (in_array($workflowId, $WFID ?? [])) {
                $workflowSetpsInfo[$workflowId] = $eachworkFlowStepInfo;
            }
        }
        if (array_key_exists('PCModuleInfo', $pipelineInfoArray)) {
            $PCModuleInfo = $pipelineInfoArray['PCModuleInfo'];
        }

        if ($newPg == '1') { /* Hide to Display the all Primary Status for the Superuser on March 23, 2016 */
            doNothing();
        } elseif (array_key_exists('PCStatusInfoForTab', $pipelineInfoArray ?? [])) {
            myPipeline::$PCStatusInfoForTab = $pipelineInfoArray['PCStatusInfoForTab'];
        }

    }
    if ($PCID > 0) {
        if (array_key_exists($PCID, $PCStatusInfo)) {
            $PCStatusInfoArray = $PCStatusInfo[$PCID];
        }
        if (array_key_exists($PCID, myPipeline::$PCStatusInfoForTab)) {
            myPipeline::$PCStatusInfoForTab = myPipeline::$PCStatusInfoForTab[$PCID];
        }
    }
    /**
     * Get master file types.
     */
    if (PageVariables::isSuper()) {

        $PCModuleInfo = getLibModules::getReport(['activeStatus' => 1]);
    }

    if (PageVariables::$userGroup == 'Agent') {
        for ($j = 0; $j < count($PCStatusInfoArray); $j++) {
            if (trim($PCStatusInfoArray[$j]['primaryStatus']) == 'Lead') $leadStatusIDArray[] = trim($PCStatusInfoArray[$j]['PSID']);
        }
        if (count($leadStatusIDArray) > 0) {
            $leadStatusID = implode(',', $leadStatusIDArray);
        }
        $ip['leadStatusID'] = $leadStatusID;
    }

    if (array_key_exists($PCID, $PCSubStatusInfo)) $PCSubStatusInfo = $PCSubStatusInfo[$PCID];
    if (array_key_exists($PCID, $PCServiceTypeArray)) $PCClientTypeInfoArray = $PCServiceTypeArray[$PCID];
}

$isPCSLM = 0;
for ($i = 0; $i < count($PCClientTypeInfoArray); $i++) {
    if (trim($PCClientTypeInfoArray[$i]['LMRClientType']) == 'SLM' && (count($PCClientTypeInfoArray) == 1)) {
        $isPCSLM = 1;
        break;
    }
}
/* PC has Hard Money Module Data Export March 6, 2017 */
$isPCHMLO = 0;
$moduleArray = [];
$tempCode = 0;
for ($i = 0; $i < count($PCClientTypeInfoArray); $i++) {
    $moduleArray[] = trim($PCClientTypeInfoArray[$i]['moduleCode']);
}

if ($moduleArray > 0) {
    for ($i = 0; $i < count($moduleArray); $i++) {
        if ($moduleArray[$i] == 'HMLO') {
            //$tempCode ++;
            $isPCHMLO = 1;
            break;
        }
    }
}

if (PageVariables::$userRole == 'Auditor Manager'
    || PageVariables::$userRole == 'CFPB Auditor'
    || ($fileType == 'CFPB' && $allowCFPBAuditing == 1)
) {
    if (!isset($_REQUEST['searchPCID'])) {
        unset($ip['PCID']);
        $PCID = 0;
    }
}

if ($newPg != '1' && $PCID) {
    $ip['primeStatusId'] = myPipeline::$reportId;
    $ip['startDate'] = $startDate;
    $ip['endDate'] = $endDate;
    $ip['PCStatusInfo'] = $PCStatusInfoArray;
    $ip['notesType'] = $notesType;
    $ip['sortOpt'] = $sortOpt;
    $ip['orderBy'] = $orderBy;
    $ip['modifiedStartDate'] = $modifiedStartDate;
    $ip['modifiedEndDate'] = $modifiedEndDate;
    $ip['closingStartDate'] = $closingStartDate;
    $ip['closingEndDate'] = $closingEndDate;

    $ip['maturityStartDate'] = $maturityStartDate;
    $ip['maturityEndDate'] = $maturityEndDate;

    $ip['targetClosingStartDate'] = $targetClosingStartDate;
    $ip['targetClosingEndDate'] = $targetClosingEndDate;

    $ip['appraisalStartDate'] = $appraisalStartDate;
    $ip['appraisalEndDate'] = $appraisalEndDate;

    $ip['receivedStartDate'] = $receivedStartDate;
    $ip['receivedEndDate'] = $receivedEndDate;
    $ip['disclosureSentDateStart'] = $disclosureSentDateStart;
    $ip['disclosureSentDateEnd'] = $disclosureSentDateEnd;

    $ip['referringParty'] = $referringParty;

    $ip['contactId'] = $contactId;
    $ip['openHouseDate'] = $openHouseDate;
    $ip['lienPosition'] = $lienPosition;


    if (PageVariables::$userRole == 'Sales') {
        if (array_key_exists($userName, $salesRepArray)) $mySalesRep = $salesRepArray[$userName];
    }

    if (PageVariables::$userRole == 'REST'
        || ($fileType == 2 && (PageVariables::isSuper() || PageVariables::$userRole == 'Sales'))
    ) {
        $ip['salesRep'] = $mySalesRep;
    }
    //The main function to get the file ids
    $ip['employeeReport'] = $employeeReport;
    $fileResultArray = getFileIDs_new::getReport($ip);

    if (count($fileResultArray) > 0) {
        $fileIdArray = $fileResultArray['fileID'];
        $cntInfoArray = $fileResultArray['cntInfo'];
        $qry = $fileResultArray['qry'];
        $allFileResponseIDs = $fileResultArray['allFileResponseIDs'];
        $allFileResponseIDsCnt = count(explode(',', $allFileResponseIDs));
    }
}
$exportIp = $ip; /* used for export files please dont delete */

myPipelineColumns::setPCStatusInfoArray();

foreach (myPipeline::$PCStatusInfoForTab as $pc => $item) {

    $PLMStatusValue = '';
    $totNewCount = 0;
    $PLMStatusId = 0;
    $PLMStatusDesc = '';

    $PLMStatusId = trim($item['PSID']);
    $PLMStatusValue = trim($item['primaryStatus']);
    $PLMStatusDesc = trim($item['statusDesc']);

    if ($PLMStatusValue == 'New') {
        $newStatusId = $PLMStatusId;
    }

    $PCPrimaryStatusTabArray['Default Tab On Pipeline Open'][$PLMStatusId] = $PLMStatusValue; // Active Tab Primary Status Fields...

    $key = strtolower(str_replace(' ', '', $PLMStatusId));

    myPipeline::$tabCount[$key] = 0;
    myPipeline::$tabTip[$key] = '';
    if (count($cntInfoArray) > 0) {

        if (array_key_exists($key . 'Count', $cntInfoArray)) {
            myPipeline::$tabCount[$key] = $cntInfoArray[$key . 'Count'];
        }
        myPipeline::$tabTip[$key] = $PLMStatusValue . ' (' . myPipeline::$tabCount[$key] . ')';
        if (trim($PLMStatusDesc)) {
            myPipeline::$tabTip[$key] .= '<br>' . $PLMStatusDesc;
        }
        if (array_key_exists('allCount', $cntInfoArray)) {
            $allCount = $cntInfoArray['allCount'];
        }
        if (array_key_exists('clientCount', $cntInfoArray)) {
            $regonlyCount = $cntInfoArray['clientCount'];
        }

    }
    if (strtolower(myPipeline::$reportId) == strtolower($PLMStatusId)) {
        $noOfRecords = myPipeline::$tabCount[$key];
        $massStatusCnt = $noOfRecords;
        $massStatusValue = $PLMStatusValue;
    } elseif (strtolower(myPipeline::$reportId) == 'all') {
        $noOfRecords = $allCount;
    }
    $allTip = 'All (' . $allCount . ')<br>This tab is for ALL client files in the pipeline.';
}


if (
    (strtolower(myPipeline::$reportId) == 'all'
        && (
            PageVariables::$userRole == 'Auditor'
            || PageVariables::isSuper()
            || PageVariables::$userRole == 'Sales'
            || PageVariables::$userRole == 'CFPB Auditor'
            || PageVariables::$userRole == 'Auditor Manager'
        ) && count($cntInfoArray) > 0
    ) || PageVariables::$userRole == 'REST'
) {
    if (array_key_exists('allCount', $cntInfoArray)) {
        $allCount = $cntInfoArray['allCount'];
        $noOfRecords = $allCount;
    }
    $allTip = 'All (' . $allCount . ')<br>This tab is for ALL client files in the pipeline.';
}
$allECount = 0;
if (myPipeline::$reportId || $PLMStatusId) {
    $allECount = $noOfRecords;
} else {
    $allECount = $allCount;
}

$loanAuditFileCnt = [];
if (PageVariables::$userGroup == 'Auditor' || $fileType == 'LA') {
    if (myPipeline::$reportId && $fileType == 'LA') {
        $loanAuditStatus = myPipeline::$reportId;
    }
    ${strtolower(str_replace(' ', '', $loanAuditStatus))} = 0;

    $loanAuditFileCnt = getLoanAuditFileCnt::getReport(['userGroup' => $userGroup, 'activeStatus' => $activeFile, 'qry' => $qry]);
    if (array_key_exists('allCount', $loanAuditFileCnt)) {
        $allCount = $loanAuditFileCnt['allCount'];
        $noOfRecords = $allECount = $allCount;
    }
    if (strtolower(myPipeline::$reportId) == strtolower($loanAuditStatus) && strtolower(myPipeline::$reportId) != 'all') {
        $noOfRecords = $allECount = $loanAuditFileCnt[strtolower(str_replace(' ', '', $loanAuditStatus))];
    } elseif (strtolower(myPipeline::$reportId) == 'all') {
        $noOfRecords = $allECount = $allCount;
    }
    $allTip = 'All (' . $allCount . ')<br>This tab is for ALL client files in the pipeline.';
} elseif (PageVariables::$userGroup == 'CFPB Auditor' || PageVariables::$userGroup == 'Auditor Manager' || $fileType == 'CFPB') {

    ${strtolower(str_replace(' ', '', $CFPBAuditStatus))} = 0;
    $CFPBAuditFileCnt = getCFPBAuditFileCnt::getReport([
        'userGroup'    => PageVariables::$userGroup,
        'activeStatus' => $activeFile,
        'qry'          => $qry,
        'fileType'     => $fileType,
    ]);

    if (array_key_exists('allCount', $CFPBAuditFileCnt)) {
        $allCount = $CFPBAuditFileCnt['allCount'];
        $noOfRecords = $allECount = $allCount;
    }
    if (strtolower(myPipeline::$reportId) == strtolower($CFPBAuditStatus) && strtolower(myPipeline::$reportId) != 'all') {
        $noOfRecords = $allECount = $CFPBAuditFileCnt[strtolower(str_replace(' ', '', $CFPBAuditStatus))];
    } else if (strtolower(myPipeline::$reportId) == 'all') {
        $noOfRecords = $allECount = $allCount;
    }
    $allTip = 'All (' . $allCount . ')<br>This tab is for ALL client files in the pipeline.';
}
if ($noOfRecords > $noOfRecordsPerPage) {
    $recNumbStart = ($pageNumber - 1) * $noOfRecordsPerPage - 1;
    $temp = $recNumbStart + 1;
}
$recNumbStart = ($pageNumber - 1) * $noOfRecordsPerPage + 1;
$recNumbEnd = $recNumbStart + $noOfRecordsPerPage - 1;
if ($recNumbEnd > $noOfRecords) {
    $recNumbEnd = $noOfRecords;
}
$recNumbStart = $recNumbStart - 1;

$ip['viewPrivateNotes'] = $viewPrivateNotes;
$ip['viewPublicNotes'] = $viewPublicNotes;

if ($newPg == '1') {
    doNothing();
} elseif (count($fileIdArray) > 0) {
    $ip['LMRID'] = $fileIdArray;
    $ip['op'] = $op;
    $ip['sortOpt'] = $sortOpt;
    $ip['orderBy'] = $orderBy;

    if ($op == 'open') {
        $fileInfo = $fileInfoArray = getFileData::getReport($ip);
    } else {
        $fileInfo = $fileInfoArray = getFileData_new::getReport($ip);
    }
}

if (count($fileInfoArray) > 0) {

    if (array_key_exists('LMRData', $fileInfoArray)) {
        $LMRDataArray = $fileInfoArray['LMRData'];
    }

    if (count($LMRDataArray) > 0) {
        $LMRDataKeyArray = array_keys($LMRDataArray);
        $LMRDataCnt = count($LMRDataKeyArray);
    }

    if (array_key_exists('fileBranchInfo', $fileInfoArray)) $fileBranchInfo = $fileInfoArray['fileBranchInfo'];
    if (array_key_exists('file2Info', $fileInfoArray)) $file2Info = $fileInfoArray['file2Info'];
    if (array_key_exists('favFiles', $fileInfoArray)) $favFiles = $fileInfoArray['favFiles'];

    if ($fileType == 4) {

        $HOMEPaymentDetailsArray = calculateHOMEFilePaymentDetails::getReport([
            'PCID'          => $PCID,
            'subStDate'     => $subStDate,
            'qry'           => $qry,
            'subEndDate'    => $subEndDate,
            'searchField'   => $searchFieldArray,
            'searchTerm'    => $searchTerm,
            'paymentStatus' => $paymentStatus,
            'branchId'      => $executiveId,
        ]);
    }
}

if (count($fileInfoArray) > 0) {
    if ($op == 'open') {
        $taskBranchInfo = [];
        $taskAgentInfo = [];
        $scheduledEmailInfo = [];
        $taskClientInfo = [];
        if (array_key_exists('WFListArray', $fileInfoArray)) $WFListArray = $fileInfoArray['WFListArray'];
        if (array_key_exists('LMRWorkflow', $fileInfoArray)) $LMRWFArray = $fileInfoArray['LMRWorkflow'];
        if (array_key_exists('PCWFArray', $fileInfoArray)) $PCWFArray = $fileInfoArray['PCWFArray'];
        if (array_key_exists('cklistNotRequired', $fileInfoArray)) $cklistNotRequiredArray = $fileInfoArray['cklistNotRequired'];
        if (array_key_exists('LMRChecklist', $fileInfoArray)) $LMRChecklistArray = $fileInfoArray['LMRChecklist'];
        if (array_key_exists('PCCheckList', $fileInfoArray)) $tempPCChecklistArray = $fileInfoArray['PCCheckList'];
        if (array_key_exists('empChkInfo', $fileInfoArray)) $empChkInfoArray = $fileInfoArray['empChkInfo'];
        if (array_key_exists('LenderInfo2', $fileInfoArray)) $LenderInfo2Array = $fileInfoArray['LenderInfo2'];
        if (array_key_exists('docArray', $fileInfoArray)) $uploadedDocArray = $fileInfoArray['docArray'];
        if (array_key_exists('eSignedDoc', $fileInfoArray)) $eSignedDocArray = $fileInfoArray['eSignedDoc'];
        if (array_key_exists('empWFInfo', $fileInfoArray)) $empWFInfoArray = $fileInfoArray['empWFInfo'];
        if (array_key_exists('branchWFInfo', $fileInfoArray)) $branchWFInfoArray = $fileInfoArray['branchWFInfo'];
        if (array_key_exists('agentWFInfo', $fileInfoArray)) $agentWFInfoArray = $fileInfoArray['agentWFInfo'];
        if (array_key_exists('billingPayment', $fileInfoArray)) $billingInfo = $fileInfoArray['billingPayment'];
        if (array_key_exists('scheduledEmail', $fileInfoArray)) $scheduledEmailInfo = $fileInfoArray['scheduledEmail'];
        if (array_key_exists('assignedStaff', $fileInfoArray)) $assignedStaff = $fileInfoArray['assignedStaff'];
        if (array_key_exists('RESTInfo', $fileInfoArray)) $loanOriginationInfo = $fileInfoArray['RESTInfo'];
    } else {
        if (array_key_exists('loanOriginationInfo', $fileInfoArray)) $loanOriginationInfo = $fileInfoArray['loanOriginationInfo'];
    }
    if (array_key_exists('QAInfo', $fileInfoArray)) $QAInfo = $fileInfoArray['QAInfo'];
    if (array_key_exists('loanAuditProductInfo', $fileInfoArray)) $loanAuditProductInfo = $fileInfoArray['loanAuditProductInfo'];
    if (array_key_exists('loanAuditInfo', $fileInfoArray)) $loanAuditInfoArray = $fileInfoArray['loanAuditInfo'];
    if (array_key_exists('proposalInfo', $fileInfoArray)) $proposalInfo = $fileInfoArray['proposalInfo'];
    if (array_key_exists('LenderInfo1', $fileInfoArray)) $LenderInfo1Array = $fileInfoArray['LenderInfo1'];
    if (array_key_exists('substatusInfo', $fileInfoArray)) $substatusInfoArray = $fileInfoArray['substatusInfo'];
    if (array_key_exists('LMRClientType', $fileInfoArray)) $fileClientTypeArray = $fileInfoArray['LMRClientType'];
    if (array_key_exists('LMRInternalLoanProgramInfo', $fileInfoArray)) $LMRInternalLoanProgramInfoArray = $fileInfoArray['LMRInternalLoanProgramInfo'];
    if (array_key_exists('LMRadditionalLoanprogramsInfo', $fileInfoArray)) $LMRadditionalLoanprogramsInfoArray = $fileInfoArray['LMRadditionalLoanprogramsInfo'];


    if (array_key_exists('fileModuleInfo', $fileInfoArray)) $fileModuleTypeArray = $fileInfoArray['fileModuleInfo'];


    if (array_key_exists('AssetsInfo', $fileInfoArray)) $AssetsInfo = $fileInfoArray['AssetsInfo'];
    if (array_key_exists('income', $fileInfoArray)) $incomeInfoArray = $fileInfoArray['income'];
    if (array_key_exists('filePropInfo', $fileInfoArray)) $filePropInfo = $fileInfoArray['filePropInfo'];
    if (array_key_exists('listingRealtorInfo', $fileInfoArray)) $listingRealtorInfo = $fileInfoArray['listingRealtorInfo'];
    if (array_key_exists('isPLOArray', $fileInfoArray)) $PCInfo = $fileInfoArray['isPLOArray'];
    if (array_key_exists('notesInfo', $fileInfoArray)) $notesInfoArray = $fileInfoArray['notesInfo'];
    if (array_key_exists('fileAgentInfo', $fileInfoArray)) $fileAgentInfo = $fileInfoArray['fileAgentInfo'];
    if (array_key_exists('fileLoanOfficerInfo', $fileInfoArray)) $fileLoanOfficerInfo = $fileInfoArray['fileLoanOfficerInfo'];
    if (array_key_exists('clientPaymentInfo', $fileInfoArray)) $clientPaymentInfo = $fileInfoArray['clientPaymentInfo'];
    if (array_key_exists('taskInfo', $fileInfoArray)) $taskListArray = $fileInfoArray['taskInfo'];
    if (array_key_exists('taskEmployee', $fileInfoArray)) $taskEmpInfo = $fileInfoArray['taskEmployee'];
    if (array_key_exists('taskBranch', $fileInfoArray)) $taskBranchInfo = $fileInfoArray['taskBranch'];
    if (array_key_exists('taskAgent', $fileInfoArray)) $taskAgentInfo = $fileInfoArray['taskAgent'];
    if (array_key_exists('taskClient', $fileInfoArray)) $taskClientInfo = $fileInfoArray['taskClient'];
    if (array_key_exists('assignedStaff', $fileInfoArray)) $assignedBoStaff = $fileInfoArray['assignedStaff'];
    if (array_key_exists('fileStatusInfo', $fileInfoArray)) $fileStatusInfo = $fileInfoArray['fileStatusInfo'];
    if (array_key_exists('fileVelocityInfo', $fileInfoArray)) $fileVelocityInfo = $fileInfoArray['fileVelocityInfo'];
    if (array_key_exists('fileStatusHistory', $fileInfoArray)) $fileStatusHistory = $fileInfoArray['fileStatusHistory'];
    if (array_key_exists('clientInfo', $fileInfoArray)) $clientInfo = $fileInfoArray['clientInfo'];

    if (array_key_exists('fileContactsInfo', $fileInfoArray)) $fileContactsInfo = $fileInfoArray['fileContactsInfo'];
    if ($fileType == 4) {
        if (array_key_exists('HRHistoryInfo', $fileInfoArray)) $HRHistoryInfoArray = $fileInfoArray['HRHistoryInfo'];

    }
    if (array_key_exists('CFPBAuditInfo', $fileInfoArray)) $CFPBAuditInfoArray = $fileInfoArray['CFPBAuditInfo'];

    if (PageVariables::$userGroup == 'CFPB Auditor' || PageVariables::$userGroup == 'Auditor Manager' || $fileType == 'CFPB') {
        if (array_key_exists('lockCFPBFileInfo', $fileInfoArray)) $lockCFPBFileInfoArray = $fileInfoArray['lockCFPBFileInfo'];
        if (array_key_exists('empCFPBInfo', $fileInfoArray)) $empCFPBInfo = $fileInfoArray['empCFPBInfo'];
        if (array_key_exists('branchCFPBInfo', $fileInfoArray)) $branchCFPBInfo = $fileInfoArray['branchCFPBInfo'];
        if (array_key_exists('agentCFPBInfo', $fileInfoArray)) $agentCFPBInfo = $fileInfoArray['agentCFPBInfo'];
        if (array_key_exists('clientCFPBInfo', $fileInfoArray)) $clientCFPBInfo = $fileInfoArray['clientCFPBInfo'];

        if (array_key_exists('assignedCFPBAuditorInfo', $fileInfoArray)) $assignedCFPBAuditorInfo = $fileInfoArray['assignedCFPBAuditorInfo'];
    }
    if (array_key_exists('fileHMLOPropertyInfo', $fileInfoArray)) $fileHMLOPropertyInfo = $fileInfoArray['fileHMLOPropertyInfo'];
    if (array_key_exists('titleContactInfo', $fileInfoArray)) $titleContactInfo = $fileInfoArray['titleContactInfo'];
    if (array_key_exists('fileHMLOEntityInfo', $fileInfoArray)) $fileHMLOEntityInfo = $fileInfoArray['fileHMLOEntityInfo'];
    if (array_key_exists('insuranceCompContactInfo', $fileInfoArray)) $insuranceCompContactInfo = $fileInfoArray['insuranceCompContactInfo'];
    if (array_key_exists('fileHMLOListOfRepairs', $fileInfoArray)) $fileHMLOListOfRepairs = $fileInfoArray['fileHMLOListOfRepairs'];
    if (array_key_exists('fileHMLONewLoanInfo', $fileInfoArray)) $fileHMLONewLoanInfo = $fileInfoArray['fileHMLONewLoanInfo'];
    if (array_key_exists('fileMFLoanTerms', $fileInfoArray)) $fileMFLoanTerms = $fileInfoArray['fileMFLoanTerms'];
    if (array_key_exists('fileLOAssetsInfo', $fileInfoArray)) $fileLOAssetsInfo = $fileInfoArray['fileLOAssetsInfo'];
    if (array_key_exists('fileHMLOInfo', $fileInfoArray)) $fileHMLOInfo = $fileInfoArray['fileHMLOInfo'];
    if (array_key_exists('fileHMLOExperienceInfo', $fileInfoArray)) $fileHMLOExperienceInfo = $fileInfoArray['fileHMLOExperienceInfo'];
    if (array_key_exists('fileHMLOBackGroundInfo', $fileInfoArray)) $fileHMLOBackGroundInfo = $fileInfoArray['fileHMLOBackGroundInfo'];
    if (array_key_exists('WFListArray', $fileInfoArray)) $WFListArray = $fileInfoArray['WFListArray'];
    if (array_key_exists('PCWFArray', $fileInfoArray)) $PCWFArray = $fileInfoArray['PCWFArray'];
    if (array_key_exists('FileWorkFlowStepsArray', $fileInfoArray)) myPipeline::$FileWorkFlowStepsArray = $fileInfoArray['FileWorkFlowStepsArray'];
    if (array_key_exists('LMRWFArray', $fileInfoArray)) $LMRWFArray = $fileInfoArray['LMRWFArray'];
    if (array_key_exists('listingRealtorInfo2', $fileInfoArray)) $listingRealtorInfo2 = $fileInfoArray['listingRealtorInfo2'];
    if (array_key_exists('insExpiryDatesArray', $fileInfoArray)) $insExpiryDatesArray = $fileInfoArray['insExpiryDatesArray'];
    if (array_key_exists('budgetAndDrawsInfo', $fileInfoArray)) $budgetAndDrawsInfo = $fileInfoArray['budgetAndDrawsInfo'];
    if (array_key_exists('lenderFileContactInfo', $fileInfoArray)) $lenderFileContactInfo = $fileInfoArray['lenderFileContactInfo'];
    if (array_key_exists('trusteeFileContactInfo', $fileInfoArray)) $trusteeFileContactInfo = $fileInfoArray['trusteeFileContactInfo'];
    if (array_key_exists('servicerFileContactInfo', $fileInfoArray)) $servicerFileContactInfo = $fileInfoArray['servicerFileContactInfo'];
    if (array_key_exists('investorFileContactInfo', $fileInfoArray)) $investorFileContactInfo = $fileInfoArray['investorFileContactInfo'];
}

myPipeline::initSearchFilter(
    $userSeeBilling,
    $fileType,
    $allowCFPBAuditing,
    $PCModuleInfo,
    $workflowInfo
);


echo BaseHTML::openPage('My Pipeline - ' . CONST_DOMAIN);

$customCSSArray = [];
$customCSSArray = [
    '/assets/js/3rdParty/datatables-1.10.22/datatables.bundle.css',
    '/assets/styles/images.css',
];
Strings::includeMyCSS($customCSSArray);

/** maximum number of records allowed to export by user For the PC -  FIX MY PAYMENT - Division Of AMS
 ** Overwriting the global variables - 6000
 ** 652 = FIX MY PAYMENT - Division of AMS Alliance, Inc
 **/
if ($loggedInUserPCID == '652') {
    $maxN4Export = 6500;
}

/* Dynamic $Breadcrumb */
$Breadcrumb = [];

$Breadcrumb['icon'] = 'fas fa-file icon-md';
$Breadcrumb['breadcrumbList'] = '';//array(array('href' => '#', 'title' => 'Sample home'), array('href' => '#', 'title' => 'Sample Child'));
$Breadcrumb['toolbarHTML'] = '';
/* End of Dynamic $Breadcrumb */
if ($fileType == 2 || PageVariables::$userRole == 'REST') {
    $Breadcrumb['title'] = 'File submitted to REST';
} else if ($fileType == 3) {
    $Breadcrumb['title'] = 'E-sign doc Pipeline';
} else if ($fileType == 4) {
    $Breadcrumb['title'] = 'HOME Report Pipeline';
} else if ($fileType == 'CFPB') {
    $Breadcrumb['title'] = 'CFPB File Pipeline';
} else {
    $Breadcrumb['title'] = 'Client File Pipeline';
}

$subHeaderHTML = '';
//$bodyVariables = 'subheader-enabled subheader-fixed';
require('../includesNew/_page-body-loader.php');
require('../includesNew/_layoutOpen.php');

?>
<input type="hidden" name="myPipelineUrl" id="myPipelineUrl" value="<?php echo $myUrl;?>">
<div class="d-none" id="divLoader">
    <img src="<?php echo IMG_PROGRESS_BAR; ?>" alt="Please wait."
         title="Please wait">
</div>
<?php
if ((PageVariables::$userRole == 'REST' || $fileType == 2) || $fileType == 4) {
    ?>
    <div style="float:right; display:none" id="showRESTOwed"></div>
<?php } ?>

<?php require __DIR__ . '/myPipeline/form.php'; ?>

<?php
if (PageVariables::isSuper()) {
    ?>
    <a href="javascript:convertIntoXLS()" id='data=<?php echo urlencode(serialize($exportIp)) ?>'
       title="Click to generate as csv">
        <div class="right" style="width:20px;height:20px;"></div>
    </a>
<?php } ?>

<?php
require('../includesNew/_layoutClose.php');

require 'adminFooter.php';
echo BaseHTML::closePage();

$scriptArray = [
    '/assets/js/models/Dates.js',
    '/assets/js/models/formValue.js',
    '/assets/js/fileCommon.js',
    '/backoffice/myPipeline/js/myPipelineFunctions.js',
    '/assets/js/LMRequest.js',
    '/assets/js/REST.js',
    '/assets/js/clientCreate.js',
    '/assets/js/3rdParty/jquery-autocomplete/jquery.autocomplete_min.js',
    '/assets/js/accordion.js',
    '/assets/js/pops/addNotesTemplate.js',
    '/assets/js/CWForm.js',
    '/assets/js/3rdParty/datatables-1.10.22/datatables.bundle.js',
    '/assets/js/3rdParty/datatables-1.10.24/dataTables.bootstrap4.min.js',
];
Strings::includeMyScript($scriptArray);
echo BaseHTML::thirdPartyFileManager();
require('../includesNew/_customPage.php');
?>

<div class="d-none" id="taskMsgDiv">
    <?php echo Strings::DisplayMessage(Strings::GetSess('msg'));
    Strings::SetSess('msg', ''); ?>
</div>
<div id="warning"></div>

<?php require 'suggestedDDAndToolTip.php'; ?>
<?php
include_once CONST_BO_PATH . 'automation/sections/previewAutomatedEventPopup.php';
?>

<?php require __DIR__ . '/DocsForm/modals/AddEditNote.php'; ?>

<script>
    $(function () {
        <?php if(!myPipeline::$PCID) { ?>
        toastrNotification('A PC must must be selected to view the pipeline report', 'error');
        <?php } ?>
    });
</script>
<?php Database2::saveLogQuery(); ?>
<?php if ($_REQUEST['debug'] ?? null) { ?>
    <?php Debug(myPipelineColumns::dataCheck()); ?>
<?php } ?>
