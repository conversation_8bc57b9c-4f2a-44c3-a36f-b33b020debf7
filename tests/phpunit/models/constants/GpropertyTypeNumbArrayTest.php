<?php

namespace tests\models\constants;

use models\constants\GpropertyTypeNumbArray;
use PHPUnit\Framework\TestCase;

/**
 * Tests the GpropertyTypeNumbArray class.
 *
 * Auto-generated docblock. Please refine descriptions as needed.
 *
 * @covers \models\constants\GpropertyTypeNumbArray::constants
 * @covers \models\constants\GpropertyTypeNumbArray::gpropertyTypeNumbArray
 * @covers \models\constants\GpropertyTypeNumbArray::gpropertyTypeNumbArray2
 * @covers \models\constants\GpropertyTypeNumbArray::globalPropertyTypeGroup
 * @covers \models\constants\GpropertyTypeNumbArray::init
 */
class GpropertyTypeNumbArrayTest extends TestCase
{
  /**
   * Tests the constants method of the GpropertyTypeNumbArray class.
   *
   * Auto-generated docblock. Please update with test specifics.
   */
  public function testConstants()
  {
    $this->assertEquals(48, GpropertyTypeNumbArray::CONDO);
    $this->assertEquals(5, GpropertyTypeNumbArray::CONDO_HIGH_RISE);
    $this->assertEquals(4, GpropertyTypeNumbArray::CONDO_LOW_RISE);
  }

  /**
   * Tests the gpropertyTypeNumbArray method of the GpropertyTypeNumbArray class.
   *
   * Auto-generated docblock. Please update with test specifics.
   */
  public function testGpropertyTypeNumbArray()
  {
    $this->assertIsArray(GpropertyTypeNumbArray::$GpropertyTypeNumbArray);
    $this->assertArrayHasKey(48, GpropertyTypeNumbArray::$GpropertyTypeNumbArray);
    $this->assertEquals('Condo', GpropertyTypeNumbArray::$GpropertyTypeNumbArray[48]);
  }
}
