<?php


use models\composite\oFile\getFileInfo;
use models\constants\creditDecision;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glDate;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\creditDecision as creditDecisionController;
use models\Controllers\loanForm;
use models\lendingwise\tblCreditDecision;
use models\lendingwise\tblCreditDecisionForm;
use models\myFileInfo;
use models\standard\Arrays;
use models\standard\Dates;
use models\standard\Strings;
global $myFileInfo;
loanForm::pushSectionID('CD');

$LMRId = LMRequest:: $LMRId;
$creditDecisionArray = creditDecision::decisionArray();
$boStaffArray = creditDecision::getBOEmployeeAssignedToFile('CD', $LMRId);

$creditDecisionData = creditDecisionController::$creditDecisionData;
if (!sizeof($creditDecisionData ?? [])) {
    $creditDecisionData = [
        new tblCreditDecision(),
    ];
}
$myFileInfoObject = new myFileInfo();
$myFileInfoObject->LMRId = $LMRId;
$fileHMLOExperienceInfo = $myFileInfoObject->getFileHMLOExperience();
$fileAssignedLoanProgram = getFileInfo::$fileLoanPrograms[0] ?? '';
$borNoOfFlippingExperience = is_null($myFileInfo['fileHMLOExperienceInfo']['borNoOfFlippingExperience']) ? '' : intval($fileHMLOExperienceInfo->borNoOfFlippingExperience);

$creditDecisionFormData = creditDecisionController::$creditDecisionFormData;
if(!$creditDecisionFormData) {
    $creditDecisionFormData = [
        new tblCreditDecisionForm(),
    ];
}
?>
<div class="card card-custom creditDecision">
    <div class="card-header card-header-tabs-line bg-gray-100">
        <div class="card-title">
            <h3 class="card-label">
                Credit Decision
            </h3>
        </div>
        <div class="card-toolbar">
            <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer"
                  data-card-tool="toggle"
                  data-section="creditDecision"
                  data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Section">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
        </div>
    </div>
    <div class="card-body creditDecision_body">
        <div class="form-group row">
            <div class="col-md-6 <?php echo loanForm::showField('propUsedExpValidated'); ?>">
                <div class="form-group row">
                    <label class="font-weight-bold col-md-5" for="borNoOfFlippingExperience_mirror">
                        <?php echo loanForm::label('propUsedExpValidated'); ?>
                    </label>
                    <div class="col-md-7">
                        <input type="text" class="form-control 
                        <?php echo loanForm::isMandatory('propUsedExpValidated') ? 'mandatory' : ''; ?>"
                         name="borNoOfFlippingExperience_mirror" id="borNoOfFlippingExperience_mirror"
                        value="<?php echo $borNoOfFlippingExperience; ?>">
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 <?php echo glCustomJobForProcessingCompany::showHideUpdateTier($fileAssignedLoanProgram); ?>">
                <!-- html code for greater than or equal to is &#8805; -->
                <div class="form-group row">
                    <label class="font-weight-bold col-md-5">Updated Tier Pricing <br>(Bridge = Yes, Rental 1 for 12 months = Yes, Rental &#8805; 3 = No)</label>
                    <div class="col-md-7 radio-inline">
                        <label class="radio radio-solid font-weight-bold" for="fullTimeRealEstateInvestorYes">
                            <input type="radio" name="fullTimeRealEstateInvestor"
                                class=""
                                id="fullTimeRealEstateInvestorYes" value="Yes"
                                <?php echo Strings::isChecked('Yes', $fileHMLOExperienceInfo->fullTimeRealEstateInvestor); ?>
                            ><span></span>Yes
                        </label>
                        <label class="radio radio-solid font-weight-bold" for="fullTimeRealEstateInvestorNo">
                            <input type="radio" name="fullTimeRealEstateInvestor"
                                class=""
                                id="fullTimeRealEstateInvestorNo" value="No"
                                <?php echo Strings::isChecked('No', $fileHMLOExperienceInfo->fullTimeRealEstateInvestor); ?>
                            ><span></span>No
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <?php
        $cd_i = 1;
        foreach ($creditDecisionData as $cdData) {
            $class = $cd_i == '1' ? 'hide' : 'show';
            ?>
            <div class="creditDecisionFields" id="creditDecisionFields_<?php echo $cd_i; ?>">
                <input type="hidden" name="creditDecisionFields[<?php echo $cd_i; ?>][creditDecisionId]"
                       id="creditDecisionId_<?php echo $cd_i; ?>" value="<?php echo $cdData->id; ?>">
                <div class="creditDecisionFieldsRemove text-right <?php echo $class; ?>">
                <span class="btn btn-sm btn-danger btn-icon ml-2 cursor-pointer"
                      data-toggle="popover"
                      data-content="Click to remove"
                      onclick="creditDecisionJs.removeFields(this);"
                      data-clone-section="creditDecisionFields"
                      data-inc-id="<?php echo $cd_i; ?>"
                      data-row-id="<?php echo $cdData->id; ?>"
                      data-LMRId="<?php echo $LMRId; ?>"
                >
                    <i class="icon-md fas fa-minus"></i>
                </span>
                </div>
                <div class="form-group row">
                    <div class="col-md-6 <?php echo loanForm::showField('creditDecision'); ?>">
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="creditDecision_<?php echo $cd_i; ?>">
                                <?php echo loanForm::label('creditDecision'); ?>
                            </label>
                            <div class="col-md-7">
                                <select name="creditDecisionFields[<?php echo $cd_i; ?>][creditDecision]"
                                        id="creditDecision_<?php echo $cd_i; ?>" class="form-control 
                                        <?php echo loanForm::isMandatory('creditDecision') ? 'mandatory' : ''; ?>">
                                    <option value="">Select</option>
                                    <?php foreach ($creditDecisionArray as $creditDecisionKey => $creditDecisionValue) { ?>
                                        <option value="<?php echo $creditDecisionKey; ?>" 
                                        <?php echo Arrays::isSelected($creditDecisionKey, $cdData->creditDecision); ?> 
                                        ><?php echo $creditDecisionValue; ?></option>
                                    <?php } ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 <?php echo loanForm::showField('creditDecisionDate'); ?>">
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="decisionDate_<?php echo $cd_i; ?>">
                                <?php echo loanForm::label('creditDecisionDate'); ?>
                            </label>
                            <div class="col-md-7 input-group">
                                <div class="input-group-prepend actionDate">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary icon-lg"></i>
                                    </span>
                                </div>
                                <input
                                    class="form-control input-sm dateNewClass decisionDate <?php echo loanForm::isMandatory('creditDecisionDate') ? 'mandatory' : ''; ?>"
                                    type="text"
                                    name="creditDecisionFields[<?php echo $cd_i; ?>][creditDecisionDate]"
                                    id="decisionDate_<?php echo $cd_i; ?>"
                                    data-before-creation-date="true"
                                    data-start-date="<?php echo glDate::getCreationDate(LMRequest::File()->recordDate); ?>"
                                    value="<?php echo Dates::formatDateWithRE($cdData->creditDecisionDate, 'YMD H:i:s', 'm/d/Y'); ?>"
                                    placeholder="MM/DD/YYYY"
                                    maxlength="10"
                                    autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 <?php echo loanForm::showField('guidelineVersion'); ?>">
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="guidelineVersion_<?php echo $cd_i; ?>">
                                <?php echo loanForm::label('guidelineVersion'); ?> #
                            </label>
                            <div class="col-md-7">
                                <input type="text"
                                name="creditDecisionFields[<?php echo $cd_i; ?>][guidelineVersion]"
                                id="guidelineVersion_<?php echo $cd_i; ?>"
                                value="<?php echo $cdData->guidelineVersion; ?>"
                                maxlength="64"
                                class="form-control <?php echo loanForm::isMandatory('guidelineVersion') ? 'mandatory' : ''; ?>">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 <?php echo loanForm::showField('creditDecisionBy'); ?>">
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="decisionBy_<?php echo $cd_i; ?>">
                                <?php echo loanForm::label('creditDecisionBy'); ?> #
                            </label>
                            <div class="col-md-7">
                                <select name="creditDecisionFields[<?php echo $cd_i; ?>][creditDecisionBy]"
                                id="decisionBy_<?php echo $cd_i; ?>" class="form-control 
                                <?php echo loanForm::isMandatory('creditDecisionBy') ? 'mandatory' : ''; ?>">
                                    <option value="">Select</option>
                                    <?php foreach ($boStaffArray as $boStaffArrayKey => $boStaffArrayValue) { ?>
                                        <option value="<?php echo $boStaffArrayValue['AID']; ?>" 
                                        <?php echo Arrays::isSelected($boStaffArrayValue['AID'], $cdData->creditDecisionBy); ?> 
                                        ><?php echo $boStaffArrayValue['role'] . ' - ' . $boStaffArrayValue['processorName']; ?>
                                        </option>
                                    <?php } ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 <?php echo loanForm::showField('creditDecisionNotes'); ?>">
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="creditDecisionNotes_<?php echo $cd_i; ?>">
                                <?php echo loanForm::label('creditDecisionNotes'); ?> #
                            </label>
                            <div class="col-md-7">
                                <textarea name="creditDecisionFields[<?php echo $cd_i; ?>][creditDecisionNotes]"
                                  id="creditDecisionNotes_<?php echo $cd_i; ?>"
                                  maxlength="<?php echo loanForm::getFieldLength('creditDecisionNotes','tblCreditDecision'); ?>"
                                  class="form-control validateMaxLength 
                                  <?php echo loanForm::isMandatory('creditDecisionNotes') ? 'mandatory' : ''; ?>"
                                ><?php echo $cdData->creditDecisionNotes; ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php $cd_i++;
        } ?>
        <div class="row">
            <div class="col-lg-3">
                <label class="font-weight-bold">Add Decision</label>
                <span class="btn btn-sm btn-success btn-icon ml-2 cursor-pointer"
                      data-toggle="popover"
                      data-content="Click to add Decision (Max 5)"
                      onclick="creditDecisionJs.addMoreFields('creditDecisionFields');">
                    <i class=" icon-md fas fa-plus "></i>
                </span>
            </div>
        </div>
        <hr>
        <div class="form-group row">
            <div class="col-md-2 <?php echo loanForm::showField('doNotSell'); ?>">
                <div class="checkbox-inline">
                    <label class="checkbox font-weight-bold">
                        <input type="checkbox"
                            name="doNotSell"
                            value="Yes" <?php echo Strings::isChecked('Yes', $creditDecisionFormData->doNotSell);?> >
                        <span></span> &nbsp;&nbsp;&nbsp; <?php echo loanForm::label('doNotSell'); ?></label>
                </div>
            </div>
        </div>
        <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('doNotSellDate'); ?>">
                    <div class="form-group row">
                    <label class="font-weight-bold col-md-5" for="doNotSellDate"><?php echo loanForm::label('doNotSellDate'); ?></label>
                    <div class="col-md-7 input-group">
                        <div class="input-group-prepend actionDate">
                            <span class="input-group-text">
                                <i class="fa fa-calendar text-primary icon-lg"></i>
                            </span>
                        </div>
                        <input
                            class="form-control input-sm dateNewClass <?php echo loanForm::isMandatory('doNotSellDate') ? 'mandatory' : ''; ?>"
                            type="text"
                            name="doNotSellDate"
                            id="doNotSellDate"
                            value="<?php echo Dates::formatDateWithRE($creditDecisionFormData->doNotSellDate, 'YMD H:i:s', 'm/d/Y');?>"
                            data-before-creation-date="true"
                            data-start-date="<?php echo glDate::getCreationDate(LMRequest::File()->recordDate); ?>"
                            placeholder="MM/DD/YYYY"
                            maxlength="10"
                            autocomplete="off" >
                    </div>
                    </div>
                </div>
                <div class="col-md-6 <?php echo loanForm::showField('doNotSellReason'); ?>">
                    <div class="form-group row">
                    <label class="font-weight-bold col-md-5" for="doNotSellReason"><?php echo loanForm::label('doNotSellReason'); ?></label>
                    <div class="col-md-7">
                    <textarea name="doNotSellReason" id="doNotSellReason" class="form-control validateMaxLength 
                        <?php echo loanForm::isMandatory('doNotSellReason') ? 'mandatory' : ''; ?>"
                        maxlength="<?php echo loanForm::getFieldLength('doNotSellReason','tblCreditDecisionForm'); ?>"
                        ><?php echo $creditDecisionFormData->doNotSellReason;?></textarea>
                    </div>
                    </div>
                </div>
            
        </div>
    </div>
</div>
