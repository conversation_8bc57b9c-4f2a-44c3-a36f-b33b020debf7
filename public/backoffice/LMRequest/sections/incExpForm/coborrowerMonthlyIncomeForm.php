<?php

use models\constants\employedInfo1Array;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\CustomField;
use models\lendingwise\tblFile;
use models\PageVariables;
use models\standard\Currency;
use models\standard\Strings;

global $isHMLO, $isLO, $tabIndex, $rental2, $netRental2, $coTotalGrossIncome, $socialSecurity2, $coBSocialSecurity;
global $netSocialSecurity2, $pensionOrRetirement2, $netPensionOrRetirement2, $disability2, $netDisability2;
global $unemployment2, $netUnemployment2, $allowToEdit, $unemploymentStDate2, $earnedInterest2, $earnedInterest2;
global $netEarnedInterest2, $capitalGains2, $partnership2, $otherHouseHold2, $otherIncomeDescription2, $roomRental2;
global $netRoomRental2, $secondJobIncome2, $netSecondJobIncome2, $employedInfo1Array, $secondJobStDate2;
global $sonOrDaughter2, $parents2, $childSupportOrAlimony2, $foodStampWelfare2, $coTotalHouseHoldIncome;
global $totalGrossMonthlyHouseHoldIncome, $coTotalNetHouseHoldIncome, $totalHouseHoldIncome;
global $creditCards2, $creditCardsBalance2, $autoLoan2, $autoLoanBalance2, $unsecuredLoans2;
global $unsecuredLoanBalance2, $otherMortgage2, $otherMortgageBalance2, $studentLoans2, $studentLoansBalance2;
global $childSupportOrAlimonyMonthly2, $careAmt2, $allInsurance2, $groceries2, $carExpenses2;
global $medicalBill2, $entertainment2, $other2, $donation2, $pets2, $monthlyParking2, $unionDues2;
global $personalLoan2, $lunchPurchased2, $rentalExp2, $cable2, $electricity2, $natural2, $coBorrowerPhone;
global $water2, $internet2, $dryCleaning2, $utilityOther2, $coTotalHouseHoldExpenses, $totalHouseHoldExpenses;

loanForm::pushSectionID('CBME');
?>
<!-- coborrowerMonthlyIncomeForm.php -->
<div class="card card-custom coborrowerMonthlyIncomeInfo">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <?php echo loanForm::toggleSectionV2('coborrowerMonthlyIncomeInfo', true, true, '', 'Co-Borrower Monthly Income'); ?>
    </div>

    <div class="card-body coborrowerMonthlyIncomeInfo_body">
        <div class="form-group row">

            <div class="col-md-6 <?php echo loanForm::showField('grossIncome2'); ?>">

                <?php echo loanForm::label2(
                    'grossIncome2',
                    null,
                    (!($isLO == 1 || $isHMLO == 1)) ? 'Gross Income is considered the TOTAL amount you make before taxes, insurance, and any other deductions.<br>For self-employed borrower, Gross Income should be your Adjusted Gross Income-after all major fixed/variable expenses are paid. <br>Please use the Income Notes section to clarify your gross income.' : '',
                    null,
                    null,
                    ($isLO == 1 || $isHMLO == 1) ? 'Base Employment Income' : 'Gross Monthly Income?'
                ); ?>

                <?php echo loanForm::currency(
                    'grossIncome2',
                    true,
                    $tabIndex++,
                    Strings::showField('grossIncome2', 'incomeInfo'),
                    null,
                    ($isHMLO == 1) ? 'calculateHMLOCoBorrowerNetHouseholdIncome(\'loanModForm\', \'coTotalNetHouseHoldIncome\');' : 'calculateCoBorrowerNewTotalNetMonthlyIncome(this.value)',
                        '',
                ); ?>

            </div>
            <div class="col-md-6 <?php echo loanForm::showField('commissionOrBonus2'); ?>">

                <?php echo loanForm::label2(
                    'commissionOrBonus2',
                    null,
                    null,
                    null,
                    null,
                    'Commission/Bonus'
                ); ?>

                <?php echo loanForm::currency(
                    'commissionOrBonus2',
                    true,
                    $tabIndex++,
                    Strings::showField('commissionOrBonus2', 'incomeInfo'),
                    null,
                    ($isHMLO == 1) ? 'calculateHMLOCoBorrowerNetHouseholdIncome(\'loanModForm\', \'coTotalNetHouseHoldIncome\');' : 'calculateCoBorrowerNewTotalNetMonthlyIncome(this.value)',
                        '',
                ); ?>

            </div>
        </div>


        <div class="form-group row">
            <div class="col-md-6 <?php echo loanForm::showField('overtime2'); ?>">

                <?php echo loanForm::label2(
                    'overtime2',
                    null,
                    null,
                    null,
                    null,
                    'Overtime'
                ); ?>

                <?php echo loanForm::currency(
                    'overtime2',
                    true,
                    $tabIndex++,
                    Strings::showField('overtime2', 'incomeInfo'),
                    null,
                    ($isHMLO == 1) ? 'calculateHMLOCoBorrowerNetHouseholdIncome(\'loanModForm\', \'coTotalNetHouseHoldIncome\');' : 'calculateCoBorrowerNewTotalNetMonthlyIncome(this.value)',
                        '',
                ); ?>

            </div>
        </div>

        <div class="form-group row">
            <?php if ($isLO == 1 || $isHMLO == 1) { ?>

                <?php if (!($isHMLO == 1)) { ?>

                    <div class="col-md-6 <?php echo loanForm::showField('rental2'); ?>">
                        <?php echo loanForm::label('rental2', null, null, null, null, 'Rental'); ?>

                        <?php echo loanForm::currency(
                            'rental2',
                            true,
                            $tabIndex++,
                            Strings::showField('rental2', 'incomeInfo'),
                            null,
                            'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                                '',
                            null
                        ); ?>
                    </div>

                <?php } ?>
                <div class="col-md-6 <?php echo loanForm::showField('netRental2'); ?>">

                    <?php echo loanForm::label('netRental2', null, null, null, null, 'Net Rental'); ?>

                    <?php echo loanForm::currency(
                        'netRental2',
                        true,
                        $tabIndex++,
                        Strings::showField('netRental2', 'incomeInfo'),
                        null,
                        ($isHMLO == 1) ? 'calculateHMLOCoBorrowerNetHouseholdIncome(\'loanModForm\', \'coTotalNetHouseHoldIncome\');' : 'calculateCoBorrowerTotalHouseHoldIncome(this.value)',
                            '',
                        null
                    ); ?>
                </div>
            <?php } ?>
        </div>
        <div class="form-group row">
            <?php if (!($isLO == 1 || $isHMLO == 1)) { ?>

                <div class="col-md-6 <?php echo loanForm::showField('federalTaxFICA1'); ?>">
                    <?php echo loanForm::label('federalTaxFICA1', null, null, null, null, 'Less: Federal and State Tax, FICA'); ?>

                    <?php echo loanForm::currency(
                        'federalTaxFICA2',
                        true,
                        $tabIndex++,
                        Strings::showField('federalTaxFICA2', 'incomeInfo'),
                        null,
                        'calculateCoBorrowerNewTotalNetMonthlyIncome(this.value);',
                            '',
                    ); ?>
                </div>
            <?php } ?>

        </div>


        <?php if (!($isLO == 1 || $isHMLO == 1)) { ?>

            <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('tipsMiscIncome2'); ?>">

                    <?php echo loanForm::label('tipsMiscIncome2', null, null, null, null, 'Tips'); ?>

                    <?php echo loanForm::currency(
                        'tipsMiscIncome2',
                        true,
                        $tabIndex++,
                        Strings::showField('tipsMiscIncome2', 'incomeInfo'),
                        null,
                        'calculateCoBorrowerNewTotalNetMonthlyIncome(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 <?php echo loanForm::showField('otherDeductions2'); ?>">

                    <?php echo loanForm::label('otherDeductions2', null, null, null, null, 'Less: Other Deductions (401K, etc.)'); ?>

                    <?php echo loanForm::currency(
                        'otherDeductions2',
                        true,
                        $tabIndex++,
                        Strings::showField('otherDeductions2', 'incomeInfo'),
                        null,
                        'calculateCoBorrowerNewTotalNetMonthlyIncome(this.value);',
                            '',
                    ); ?>

                </div>

            </div>

            <div class="form-group row">
                <div class="col-md-6">
                    <?php echo loanForm::label('coTotalGrossIncome', null, null, null, null, 'Total Gross Income'); ?>

                    <h5>$
                        <span
                            id="coTotalGrossIncome"><?php echo Currency::formatDollarAmountWithDecimal($coTotalGrossIncome); ?></span>
                    </h5>
                </div>
                <div class="col-md-6 <?php echo loanForm::showField('netMonthlyIncome2'); ?>">

                    <?php echo loanForm::label2(
                        'netMonthlyIncome2',
                        null,
                        'Net Monthly Income is considered the amount you make after all deductions are made like taxes and insurance. For self-employed borrowers you can use the dollar amount you pay yourself via draws. Please provide a detailed explanation in the income notes section below to clarify your income to your lender.',
                        null,
                        null,
                        'Total Net Monthly Income'
                    ); ?>

                    <?php echo loanForm::currency(
                        'netMonthlyIncome2',
                        true,
                        $tabIndex++,
                        Strings::showField('netMonthlyIncome2', 'incomeInfo'),
                        null,
                        'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <?php echo loanForm::sectionHeader('propdetailstitle2', 'Other Household Monthly Income'); ?>

            <?php
            if ($socialSecurity2 > 0) {
                $socialSecurity2 = number_format(Strings::replaceCommaValues($socialSecurity2), 2);
            }
            ?>
            <div class="form-group row">
                <div class="col-md-6">

                    <?php echo loanForm::label2(
                        'socialSecurity',
                        null,
                        null,
                        null,
                        null,
                        'Social Security Income'
                    ); ?>

                    <div class="row">
                        <div class="col-md-6 <?php echo loanForm::showField('socialSecurity2'); ?>">

                            <?php echo loanForm::label2(
                                'socialSecurity2',
                                null,
                                '"Grossed Up" value is 25% more than the amount entered in the "Gross" social security field and will be used in all calculations relation to DTI and HAMP calculations.',
                                null,
                                null,
                                'Grossed Up'
                            ); ?>

                            <?php echo loanForm::currency(
                                'socialSecurity2',
                                true,
                                $tabIndex++,
                                $coBSocialSecurity,
                                null,
                                'calculateCoBorrowerTotalHouseHoldIncome(this.value); updateGrossSocialSecurityIncome(this.value, \'coBor\');',
                                    '',
                            ); ?>


                        </div>
                        <div class="col-md-6 <?php echo loanForm::showField('netSocialSecurity2'); ?>">

                            <?php echo loanForm::label2(
                                'netSocialSecurity2',
                                null,
                                'Net SSI values are used for calculating disposable income.',
                                null,
                                null,
                                'Net SSI'
                            ); ?>

                            <?php echo loanForm::currency(
                                'netSocialSecurity2',
                                true,
                                $tabIndex++,
                                $netSocialSecurity2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-12 <?php echo loanForm::showField('grossedUp'); ?>">

                            <?php echo loanForm::label2(
                                'grossedUp',
                                null,
                                null,
                                null,
                                null,
                                'Grossed Up: $<span id="grossedUp2">' . $socialSecurity2 . '</span>'
                            ); ?>

                            <?php echo loanForm::checkbox(
                                'addGrossedUp2',
                                true,
                                $tabIndex++,
                                1,
                                Strings::showField('addGrossedUp2', 'incomeInfo'),
                                null,
                                'calculateCoBorrowerTotalHouseHoldIncome(document.loanModForm.socialSecurity2.value); updateGrossSocialSecurityIncome(document.loanModForm.socialSecurity2.value, \'coBor\');',
                                false,
                                'Grossed Up the gross SSI value <i
                                            class="fa fa-info-circle text-primary tooltipClass"
                                            title="If you choose to Gross up the Gross SSI  value, it will be used in current + proposed DTI calculations and populate on the RMA, 710 form, and other lender packages."></i>'
                            ); ?>

                        </div>
                    </div>
                </div>
                <div class="col-md-6">

                    <?php echo loanForm::label2(
                        'pensionOrRetirement2',
                        null,
                        null,
                        null,
                        null,
                        'Pension/Retirement Income'
                    ); ?>

                    <div class="row">
                        <div class="col-md-6 <?php echo loanForm::showField('pensionOrRetirement2'); ?>">

                            <?php echo loanForm::label2(
                                'pensionOrRetirement2',
                                null,
                                null,
                                null,
                                null,
                                'Gross'
                            ); ?>

                            <?php echo loanForm::currency(
                                'pensionOrRetirement2',
                                true,
                                $tabIndex++,
                                $pensionOrRetirement2,
                                '',
                                'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                        <div class="col-md-6 <?php echo loanForm::showField('netPensionOrRetirement2'); ?>">

                            <?php echo loanForm::label2(
                                'netPensionOrRetirement2',
                                null,
                                null,
                                null,
                                null,
                                'Net'
                            ); ?>

                            <?php echo loanForm::currency(
                                'netPensionOrRetirement2',
                                true,
                                $tabIndex++,
                                $netPensionOrRetirement2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <div class="col-md-12">

                    <?php echo loanForm::label2(
                        'disability2',
                        null,
                        null,
                        null,
                        null,
                        'Disability Income'
                    ); ?>


                    <div class="row">


                        <div class="col-md-6 <?php echo loanForm::showField('disability2'); ?>">

                            <?php echo loanForm::label2(
                                'disability2',
                                null,
                                null,
                                null,
                                null,
                                'Gross'
                            ); ?>

                            <?php echo loanForm::currency(
                                'disability2',
                                true,
                                $tabIndex++,
                                $disability2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                        <div class="col-md-6 <?php echo loanForm::showField('netDisability2'); ?>">

                            <?php echo loanForm::label2(
                                'netDisability2',
                                null,
                                null,
                                null,
                                null,
                                'Net'
                            ); ?>

                            <?php echo loanForm::currency(
                                'netDisability2',
                                true,
                                $tabIndex++,
                                $netDisability2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-md-12">

                    <?php echo loanForm::label2(
                        'unemployment2',
                        null,
                        'Unemployment Income will not be factored in the RMA form and also in the Proposed Housing DTI calculation.',
                        null,
                        null,
                        'Unemployment'
                    ); ?>

                    <div class="row">


                        <div class="col-md-4 <?php echo loanForm::showField('unemployment2'); ?>">

                            <?php echo loanForm::label2(
                                'unemployment2',
                                null,
                                null,
                                null,
                                null,
                                'Gross'
                            ); ?>

                            <?php echo loanForm::currency(
                                'unemployment2',
                                true,
                                $tabIndex++,
                                $unemployment2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                        <div class="col-md-4 <?php echo loanForm::showField('netUnemployment2'); ?>">

                            <?php echo loanForm::label2(
                                'netUnemployment2',
                                null,
                                null,
                                null,
                                null,
                                'Net'
                            ); ?>

                            <?php echo loanForm::currency(
                                'netUnemployment2',
                                true,
                                $tabIndex++,
                                $netUnemployment2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                        <div class="col-md-4 <?php echo loanForm::showField('unemploymentStDate2'); ?>">

                            <?php echo loanForm::label2(
                                'unemploymentStDate2',
                                null,
                                null,
                                null,
                                null,
                                'Start Date'
                            ); ?>

                            <?php echo loanForm::date(
                                'unemploymentStDate2',
                                true,
                                $tabIndex++,
                                $unemploymentStDate2,
                                $allowToEdit ? 'unemploymentStDate2 dateNewClass ' : ''
                            ); ?>

                        </div>

                    </div>
                </div>
            </div>


            <div class="form-group row">
                <div class="col-md-12">

                    <?php echo loanForm::label2(
                        'rental',
                        null,
                        null,
                        null,
                        null,
                        'Rental Income'
                    ); ?>


                    <div class="row">
                        <div class="col-md-4 <?php echo loanForm::showField('rental2'); ?>">

                            <?php echo loanForm::label2(
                                'rental2',
                                null,
                                'The monthly net income or loss on a rental property is calculated as 75 percent of the monthly gross rental income, reduced by the monthly principal and interest payment, plus 1/12th of annual real property taxes, annual property insurance premiums and annual homeowner\'s associations dues, if applicable (i.e., PITIA). Section 5.1.8, Chapter II of the Handbook was amended to exclude from the passive income guidance rental income from a rental property that secures the loan being evaluated for a HAMP Tier 2. If the monthly net income of a rental property securing the mortgage loan being evaluated for modification under HAMP Tier 2 is equal to or greater than the pre-modification PITIA of that property, the servicer must verify and document the cause of the borrower\'s hardship as delinquency alone is not considered a hardship.',
                                null,
                                null,
                                'Gross'
                            ); ?>

                            <?php echo loanForm::currency(
                                'rental2',
                                true,
                                $tabIndex++,
                                $rental2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>


                        </div>
                        <div class="col-md-4 <?php echo loanForm::showField('netRental2'); ?>">

                            <?php echo loanForm::label2(
                                'netRental2',
                                null,
                                'In regards to modifying a Rental Property: Include Rental Income received from all properties you own EXCEPT a property for which you are seeking mortgage assistance.',
                                null,
                                null,
                                'Net'
                            ); ?>

                            <?php echo loanForm::currency(
                                'netRental2',
                                true,
                                $tabIndex++,
                                $netRental2,
                                null,
                                $isHMLO == 1 ? 'calculateHMLOCoBorrowerNetHouseholdIncome(\'loanModForm\',  \'coTotalNetHouseHoldIncome\');' : 'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                        <div class="col-md-4">
                        </div>
                    </div>
                </div>
            </div>
        <?php } ?>

        <div class="form-group row">
            <div class="col-md-6">

                <div class="row">
                    <?php if (!($isHMLO == 1)) { ?>
                        <div class="col-md-6 <?php echo loanForm::showField('earnedInterest2'); ?>">

                            <?php echo loanForm::label2(
                                'earnedInterest2',
                                null,
                                null,
                                null,
                                null,
                                'Earned Interest'
                            ); ?>

                            <?php echo loanForm::currency(
                                'earnedInterest2',
                                true,
                                $tabIndex++,
                                $earnedInterest2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>
                        </div>

                    <?php } ?>
                    <div class="col-md-6 <?php echo loanForm::showField('netEarnedInterest2'); ?>">

                        <?php echo loanForm::label2(
                            'netEarnedInterest2',
                            null,
                            null,
                            null,
                            null,
                            ($isLO == 1 || $isHMLO == 1) ? 'Dividends / Interest' : 'Earned Interest'
                        ); ?>


                        <?php echo loanForm::currency(
                            'netEarnedInterest2',
                            true,
                            $tabIndex++,
                            $netEarnedInterest2,
                            null,
                            ($isHMLO == 1) ? 'calculateHMLOCoBorrowerNetHouseholdIncome(\'loanModForm\',  \'coTotalNetHouseHoldIncome\');' : 'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                                '',
                        ); ?>

                    </div>
                </div>
            </div>
            <?php if ($isLO == 1 || $isHMLO == 1) { ?>
                <div class="col-md-6 <?php echo loanForm::showField('capitalGains2'); ?>">

                    <?php echo loanForm::label2(
                        'capitalGains2',
                        null,
                        null,
                        null,
                        null,
                        'Capital Gains / (Losses)'
                    ); ?>


                    <?php echo loanForm::currency(
                        'capitalGains2',
                        true,
                        $tabIndex++,
                        $capitalGains2,
                        null,
                        ($isHMLO == 1) ? 'calculateHMLOCoBorrowerNetHouseholdIncome(\'loanModForm\',  \'coTotalNetHouseHoldIncome\');' : 'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            <?php } ?>
        </div>

        <div class="form-group row">
            <div class="col-md-6 <?php echo loanForm::showField('partnership2'); ?>">

                <?php echo loanForm::label2(
                    'partnership2',
                    null,
                    null,
                    null,
                    null,
                    'Partnership / Subchapter S Income'
                ); ?>

                <?php echo loanForm::currency(
                    'partnership2',
                    true,
                    $tabIndex++,
                    $partnership2,
                    null,
                    ($isHMLO == 1) ? 'calculateHMLOCoBorrowerNetHouseholdIncome(\'loanModForm\',  \'coTotalNetHouseHoldIncome\');' : 'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                        '',
                ); ?>

            </div>

            <?php if ($isLO == 1 || $isHMLO == 1) { ?>
                <div class="col-md-6 <?php echo loanForm::showField('otherHouseHold2'); ?>">

                    <?php echo loanForm::label2(
                        'otherHouseHold2',
                        null,
                        null,
                        null,
                        null,
                        'Other Income'
                    ); ?>

                    <?php echo loanForm::currency(
                        'otherHouseHold2',
                        true,
                        $tabIndex++,
                        $otherHouseHold2,
                        null,
                        ($isHMLO == 1) ? 'calculateHMLOCoBorrowerNetHouseholdIncome(\'loanModForm\',  \'coTotalNetHouseHoldIncome\');' : 'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            <?php } ?>
        </div>

        <?php if ($isLO == 1 || $isHMLO == 1) { ?>
            <div class="form-group row">
                <div class="col-md-12 <?php echo loanForm::showField('otherIncomeDescription2'); ?>">

                    <?php echo loanForm::label2(
                        'otherIncomeDescription2',
                        null,
                        null,
                        null,
                        null,
                        'Other Income Description'
                    ); ?>

                    <?php echo loanForm::textarea(
                        'otherIncomeDescription2',
                        true,
                        $tabIndex++,
                        $otherIncomeDescription2
                    ); ?>

                </div>
            </div>
        <?php } ?>

        <?php if (!($isLO == 1 || $isHMLO == 1)) { ?>
            <div class="form-group row">
                <div class="col-md-6">

                    <?php echo loanForm::label2(
                        'netRoomRental2',
                        null,
                        null,
                        null,
                        null,
                        'Room Rental'
                    ); ?>

                    <div class="row">
                        <div class="col-md-6 <?php echo loanForm::showField('roomRental2'); ?>">

                            <?php echo loanForm::label2(
                                'roomRental2',
                                null,
                                null,
                                null,
                                null,
                                'Gross'
                            ); ?>

                            <?php echo loanForm::currency(
                                'roomRental2',
                                true,
                                $tabIndex++,
                                $roomRental2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                        <div class="col-md-6 <?php echo loanForm::showField('netRoomRental2'); ?>">

                            <?php echo loanForm::label2(
                                'netRoomRental2',
                                null,
                                null,
                                null,
                                null,
                                'Net'
                            ); ?>


                            <?php echo loanForm::currency(
                                'netRoomRental2',
                                true,
                                $tabIndex++,
                                $netRoomRental2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                    </div>
                </div>
                <div class="col-md-6">

                    <?php echo loanForm::label2(
                        'secondJobIncome2',
                        null,
                        null,
                        null,
                        null,
                        'Monthly Income (2nd Job)'
                    ); ?>

                    <div class="row">
                        <div class="col-md-6 <?php echo loanForm::showField('secondJobIncome2'); ?>">

                            <?php echo loanForm::label2(
                                'secondJobIncome2',
                                null,
                                null,
                                null,
                                null,
                                'Gross'
                            ); ?>

                            <?php echo loanForm::currency(
                                'secondJobIncome2',
                                true,
                                $tabIndex++,
                                $secondJobIncome2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                        <div class="col-md-6 <?php echo loanForm::showField('netSecondJobIncome2'); ?>">

                            <?php echo loanForm::label2(
                                'netSecondJobIncome2',
                                null,
                                null,
                                null,
                                null,
                                'Net'
                            ); ?>

                            <?php echo loanForm::currency(
                                'netSecondJobIncome2',
                                true,
                                $tabIndex++,
                                $netSecondJobIncome2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                                    '',
                            ); ?>

                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 <?php echo loanForm::showField('secondJobEmpType2'); ?>">

                            <?php echo loanForm::label2(
                                'secondJobEmpType2',
                                null,
                                null,
                                null,
                                null,
                                'Type'
                            ); ?>

                            <?php echo loanForm::select(
                                'secondJobEmpType2',
                                true,
                                $tabIndex++,
                                Strings::showField('secondJobEmpType2', 'incomeInfo'),
                                employedInfo1Array::$options,
                                null,
                                null,
                                '- Select One -'
                            ); ?>

                        </div>
                        <div class="col-md-6 <?php echo loanForm::showField('secondJobStDate2'); ?>">

                            <?php echo loanForm::label2(
                                'secondJobStDate2',
                                $allowToEdit ? 'secondJobStDate2' : '',
                                null,
                                null,
                                null,
                                'Start Date'
                            ); ?>

                            <?php echo loanForm::date(
                                'secondJobStDate2',
                                true,
                                $tabIndex++,
                                $secondJobStDate2,
                                ' dateNewClass '
                            ); ?>

                        </div>
                    </div>

                </div>
            </div>
            <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('sonOrDaughter2'); ?>">

                    <?php echo loanForm::label2(
                        'sonOrDaughter2',
                        null,
                        null,
                        null,
                        null,
                        'Spouse/Son/Daughter'
                    ); ?>

                    <?php echo loanForm::currency(
                        'sonOrDaughter2',
                        true,
                        $tabIndex++,
                        $sonOrDaughter2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 <?php echo loanForm::showField('parents2'); ?>">

                    <?php echo loanForm::label2(
                        'parents2',
                        null,
                        null,
                        null,
                        null,
                        'Parents'
                    ); ?>

                    <?php echo loanForm::currency(
                        'parents2',
                        true,
                        $tabIndex++,
                        $parents2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('childSupportOrAlimony2'); ?>">

                    <?php echo loanForm::label2(
                        'childSupportOrAlimony2',
                        null,
                        null,
                        null,
                        null,
                        'Child Support/Alimony'
                    ); ?>

                    <?php echo loanForm::currency(
                        'childSupportOrAlimony2',
                        true,
                        $tabIndex++,
                        $childSupportOrAlimony2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>

                <div class="col-md-6 <?php echo loanForm::showField('otherHouseHold2'); ?>">

                    <?php echo loanForm::label2(
                        'otherHouseHold2',
                        null,
                        null,
                        null,
                        null,
                        'Other'
                    ); ?>

                    <?php echo loanForm::currency(
                        'otherHouseHold2',
                        true,
                        $tabIndex++,
                        $otherHouseHold2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('foodStampWelfare2'); ?>">

                    <?php echo loanForm::label2(
                        'foodStampWelfare2',
                        null,
                        'Food Stamps/Welfare Income will not be factored in the Proposed Housing DTI calculation.',
                        null,
                        null,
                        'Food Stamps/Welfare'
                    ); ?>

                    <?php echo loanForm::currency(
                        'foodStampWelfare2',
                        true,
                        $tabIndex++,
                        $foodStampWelfare2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldIncome(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6"></div>
            </div>
        <?php } ?>

        <?php if (!($isHMLO == 1)) { ?>
            <div class="form-group row">
                <div class="col-md-6"><h3>$
                        <span
                            id="coTotalHouseHoldIncome"><?php echo Currency::formatDollarAmountWithDecimal($coTotalHouseHoldIncome) ?></span>
                    </h3>(Co-Borrower)
                </div>
                <div class="col-md-6"><h3>$
                        <span
                            id="subTotalHouseHoldIncome"><?php echo Currency::formatDollarAmountWithDecimal($totalGrossMonthlyHouseHoldIncome) ?></span>
                    </h3>(Borr + Cobor)
                </div>
            </div>
        <?php } ?>

        <div class="form-group row">
            <div class="col-md-6"><h3>$
                    <span
                        id="coTotalNetHouseHoldIncome"><?php echo Currency::formatDollarAmountWithDecimal($coTotalNetHouseHoldIncome) ?></span>
                </h3>(Co-Borrower)
            </div>
            <div class="col-md-6"><h3>$
                    <span
                        id="subTotalNetHouseHoldIncome"><?php echo Currency::formatDollarAmountWithDecimal($totalHouseHoldIncome) ?></span>
                </h3>(Borr + Cobor)
            </div>
        </div>

        <?php if (!($isLO == 1 || $isHMLO == 1)) { ?>

            <?php echo loanForm::sectionHeader('propdetailstitleco', 'Monthly Expenses'); ?>

            <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('creditCards2'); ?>">

                    <?php echo loanForm::label2(
                        'creditCards2',
                        null,
                        'Add the total monthly minimum payments from all credit cards.',
                        null,
                        null,
                        'Credit Cards'
                    ); ?>

                    <?php echo loanForm::currency(
                        'creditCards2',
                        true,
                        $tabIndex++,
                        $creditCards2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>

                <div class="col-md-6 <?php echo loanForm::showField('creditCardsBalance2'); ?>">

                    <?php echo loanForm::label2(
                        'creditCardsBalance2',
                        null,
                        null,
                        null,
                        null,
                        'Total Credit Card <span style="color:red"><b>Balance(s)</b></span>'
                    ); ?>

                    <?php echo loanForm::currency(
                        'creditCardsBalance2',
                        true,
                        $tabIndex++,
                        $creditCardsBalance2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
            </div>
            <div class="form-group row">


                <div class="col-md-6 <?php echo loanForm::showField('autoLoan2'); ?>">

                    <?php echo loanForm::label2(
                        'autoLoan2',
                        null,
                        null,
                        null,
                        null,
                        'Auto/Car Loan(s)'
                    ); ?>

                    <?php echo loanForm::currency(
                        'autoLoan2',
                        true,
                        $tabIndex++,
                        $autoLoan2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 <?php echo loanForm::showField('autoLoanBalance2'); ?>">

                    <?php echo loanForm::label2(
                        'autoLoanBalance2',
                        null,
                        null,
                        null,
                        null,
                        'Total Auto Loan <span style="color:red"><b>Balance(s)</b></span>'
                    ); ?>

                    <?php echo loanForm::currency(
                        'autoLoanBalance2',
                        true,
                        $tabIndex++,
                        $autoLoanBalance2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
            </div>
            <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('unsecuredLoans2'); ?>">

                    <?php echo loanForm::label2(
                        'unsecuredLoans2',
                        null,
                        null,
                        null,
                        null,
                        'Unsecured Loan(s)'
                    ); ?>

                    <?php echo loanForm::currency(
                        'unsecuredLoans2',
                        true,
                        $tabIndex++,
                        $unsecuredLoans2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 <?php echo loanForm::showField('unsecuredLoanBalance2'); ?>">

                    <?php echo loanForm::label2(
                        'unsecuredLoanBalance2',
                        null,
                        null,
                        null,
                        null,
                        'Total Unsecured Loan <span style="color:red"><b>Balance(s)</b></span>'
                    ); ?>

                    <?php echo loanForm::currency(
                        'unsecuredLoanBalance2',
                        true,
                        $tabIndex++,
                        $unsecuredLoanBalance2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
            </div>
            <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('otherMortgage2'); ?>">

                    <?php echo loanForm::label2(
                        'otherMortgage2',
                        null,
                        'In regards to modifying a Loan for a Rental Property: Include Mortgage Payments on all properties you own EXCEPT your principal residence and the property for which you are seeking mortgage assistance."',
                        null,
                        null,
                        'Other Mortgages'
                    ); ?>

                    <?php echo loanForm::currency(
                        'otherMortgage2',
                        true,
                        $tabIndex++,
                        $otherMortgage2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 <?php echo loanForm::showField('otherMortgageBalance2'); ?>">

                    <?php echo loanForm::label2(
                        'otherMortgageBalance2',
                        null,
                        null,
                        null,
                        null,
                        'Mortgage <span style="color:red"><b>Balance(s)</b></span>'
                    ); ?>

                    <?php echo loanForm::currency(
                        'otherMortgageBalance2',
                        true,
                        $tabIndex++,
                        $otherMortgageBalance2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('studentLoans2'); ?>">

                    <?php echo loanForm::label2(
                        'studentLoans2',
                        null,
                        null,
                        null,
                        null,
                        'Student Loans/Tuition'
                    ); ?>

                    <?php echo loanForm::currency(
                        'studentLoans2',
                        true,
                        $tabIndex++,
                        $studentLoans2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 <?php echo loanForm::showField('studentLoansBalance2'); ?>">

                    <?php echo loanForm::label2(
                        'studentLoansBalance2',
                        null,
                        null,
                        null,
                        null,
                        'Total Student Loan <span style="color:red"><b>Balance(s)</b></span>'
                    ); ?>


                    <?php echo loanForm::currency(
                        'studentLoansBalance2',
                        true,
                        $tabIndex++,
                        $studentLoansBalance2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('childSupportOrAlimonyMonthly2'); ?>">

                    <?php echo loanForm::label2(
                        'childSupportOrAlimonyMonthly2',
                        null,
                        null,
                        null,
                        null,
                        'Alimony/Child Support'
                    ); ?>

                    <?php echo loanForm::currency(
                        'childSupportOrAlimonyMonthly2',
                        true,
                        $tabIndex++,
                        $childSupportOrAlimonyMonthly2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>

                <div class="col-md-6 <?php echo loanForm::showField('careAmt2'); ?>">

                    <?php echo loanForm::label2(
                        'careAmt2',
                        null,
                        null,
                        null,
                        null,
                        'Child/Dependent/Elderly Care'
                    ); ?>

                    <?php echo loanForm::currency(
                        'careAmt2',
                        true,
                        $tabIndex++,
                        $careAmt2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('allInsurance2'); ?>">

                    <?php echo loanForm::label2(
                        'allInsurance2',
                        null,
                        null,
                        null,
                        null,
                        'Insurance (Health, Life)'
                    ); ?>

                    <?php echo loanForm::currency(
                        'allInsurance2',
                        true,
                        $tabIndex++,
                        $allInsurance2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 <?php echo loanForm::showField('groceries2'); ?>">

                    <?php echo loanForm::label2(
                        'groceries2',
                        null,
                        null,
                        null,
                        null,
                        'Groceries'
                    ); ?>

                    <?php echo loanForm::currency(
                        'groceries2',
                        true,
                        $tabIndex++,
                        $groceries2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('carExpenses2'); ?>">

                    <?php echo loanForm::label2(
                        'carExpenses2',
                        null,
                        null,
                        null,
                        null,
                        'Car Expenses (Ins.,Gas, Maint.)'
                    ); ?>

                    <?php echo loanForm::currency(
                        'carExpenses2',
                        true,
                        $tabIndex++,
                        $carExpenses2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 <?php echo loanForm::showField('medicalBill2'); ?>">

                    <?php echo loanForm::label2(
                        'medicalBill2',
                        null,
                        null,
                        null,
                        null,
                        'Doctor/Medical Bills'
                    ); ?>

                    <?php echo loanForm::currency(
                        'medicalBill2',
                        true,
                        $tabIndex++,
                        $medicalBill2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('entertainment2'); ?>">

                    <?php echo loanForm::label2(
                        'entertainment2',
                        null,
                        null,
                        null,
                        null,
                        'Entertainment'
                    ); ?>

                    <?php echo loanForm::currency(
                        'entertainment2',
                        true,
                        $tabIndex++,
                        $entertainment2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>

                <div class="col-md-6 <?php echo loanForm::showField('other2'); ?>">

                    <?php echo loanForm::label2(
                        'other2',
                        null,
                        null,
                        null,
                        null,
                        'Other'
                    ); ?>

                    <?php echo loanForm::currency(
                        'other2',
                        true,
                        $tabIndex++,
                        $other2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('donation2'); ?>">

                    <?php echo loanForm::label2(
                        'donation2',
                        null,
                        null,
                        null,
                        null,
                        'Church/Club Donations'
                    ); ?>

                    <?php echo loanForm::currency(
                        'donation2',
                        true,
                        $tabIndex++,
                        $donation2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 <?php echo loanForm::showField('pets2'); ?>">

                    <?php echo loanForm::label2(
                        'pets2',
                        null,
                        null,
                        null,
                        null,
                        'Pets'
                    ); ?>

                    <?php echo loanForm::currency(
                        'pets2',
                        true,
                        $tabIndex++,
                        $pets2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('monthlyParking2'); ?>">

                    <?php echo loanForm::label2(
                        'monthlyParking2',
                        null,
                        null,
                        null,
                        null,
                        'Monthly Parking'
                    ); ?>

                    <?php echo loanForm::currency(
                        'monthlyParking2',
                        true,
                        $tabIndex++,
                        $monthlyParking2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 <?php echo loanForm::showField('unionDues2'); ?>">

                    <?php echo loanForm::label2(
                        'unionDues2',
                        null,
                        null,
                        null,
                        null,
                        'Union Dues'
                    ); ?>

                    <?php echo loanForm::currency(
                        'unionDues2',
                        true,
                        $tabIndex++,
                        $unionDues2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
            </div>


            <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('personalLoan2'); ?>">

                    <?php echo loanForm::label2(
                        'personalLoan2',
                        null,
                        null,
                        null,
                        null,
                        'Personal Loan'
                    ); ?>

                    <?php echo loanForm::currency(
                        'personalLoan2',
                        true,
                        $tabIndex++,
                        $personalLoan2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 <?php echo loanForm::showField('lunchPurchased2'); ?>">

                    <?php echo loanForm::label2(
                        'lunchPurchased2',
                        null,
                        null,
                        null,
                        null,
                        'School / Work Lunches Purchased'
                    ); ?>

                    <?php echo loanForm::currency(
                        'lunchPurchased2',
                        true,
                        $tabIndex++,
                        $lunchPurchased2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>
                </div>

            </div>

            <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('rentalExp2'); ?>">

                    <?php echo loanForm::label2(
                        'rentalExp2',
                        null,
                        null,
                        null,
                        null,
                        'Rental'
                    ); ?>

                    <?php echo loanForm::currency(
                        'rentalExp2',
                        true,
                        $tabIndex++,
                        $rentalExp2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6"></div>
            </div>

            <?php echo loanForm::sectionHeader('propdetailstitleco', 'Utilities'); ?>

            <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('cable2'); ?>">

                    <?php echo loanForm::label2(
                        'cable2',
                        null,
                        null,
                        null,
                        null,
                        'Cable TV/Satellite'
                    ); ?>

                    <?php echo loanForm::currency(
                        'cable2',
                        true,
                        $tabIndex++,
                        $cable2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 <?php echo loanForm::showField('electricity2'); ?>">

                    <?php echo loanForm::label2(
                        'electricity2',
                        null,
                        null,
                        null,
                        null,
                        'Electricity'
                    ); ?>

                    <?php echo loanForm::currency(
                        'electricity2',
                        true,
                        $tabIndex++,
                        $electricity2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('natural2'); ?>">

                    <?php echo loanForm::label2(
                        'natural2',
                        null,
                        null,
                        null,
                        null,
                        'Natural Gas/Oil'
                    ); ?>

                    <?php echo loanForm::currency(
                        'natural2',
                        true,
                        $tabIndex++,
                        $natural2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 <?php echo loanForm::showField('coBorrowerPhone'); ?>">

                    <?php echo loanForm::label2(
                        'coBorrowerPhone',
                        null,
                        null,
                        null,
                        null,
                        'Telephone/Cell'
                    ); ?>

                    <?php echo loanForm::currency(
                        'coBorrowerPhone',
                        true,
                        $tabIndex++,
                        $coBorrowerPhone,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('water2'); ?>">

                    <?php echo loanForm::label2(
                        'water2',
                        null,
                        null,
                        null,
                        null,
                        'Water/Sewer'
                    ); ?>

                    <?php echo loanForm::currency(
                        'water2',
                        true,
                        $tabIndex++,
                        $water2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 <?php echo loanForm::showField('internet2'); ?>">

                    <?php echo loanForm::label2(
                        'internet2',
                        null,
                        null,
                        null,
                        null,
                        'Internet'
                    ); ?>

                    <?php echo loanForm::currency(
                        'internet2',
                        true,
                        $tabIndex++,
                        $internet2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
            </div>

            <div class="form-group row">
                <div class="col-md-6 <?php echo loanForm::showField('dryCleaning2'); ?>">

                    <?php echo loanForm::label2(
                        'dryCleaning2',
                        null,
                        null,
                        null,
                        null,
                        'Dry Cleaning/Clothing'
                    ); ?>

                    <?php echo loanForm::currency(
                        'dryCleaning2',
                        true,
                        $tabIndex++,
                        $dryCleaning2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
                <div class="col-md-6 <?php echo loanForm::showField('utilityOther2'); ?>">

                    <?php echo loanForm::label2(
                        'utilityOther2',
                        null,
                        null,
                        null,
                        null,
                        'Other'
                    ); ?>

                    <?php echo loanForm::currency(
                        'utilityOther2',
                        true,
                        $tabIndex++,
                        $utilityOther2,
                        null,
                        'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                            '',
                    ); ?>

                </div>
            </div>


            <div class="form-group row">
                <div class="col-md-6"><h3>$
                        <span
                            id="coTotalHouseHoldExpenses"><?php echo Currency::formatDollarAmountWithDecimal($coTotalHouseHoldExpenses) ?></span>
                    </h3>(Co-Borrower)
                </div>
                <div class="col-md-6"><h3>$
                        <span
                            id="subTotalHouseHoldExpenses"><?php echo Currency::formatDollarAmountWithDecimal($totalHouseHoldExpenses) ?></span>
                    </h3>(Borr + Cobor)
                </div>
            </div>
        <?php } ?>

        <?php echo CustomField::RenderForTabSection(
            PageVariables::$PCID,
            tblFile::class,
            LMRequest::$LMRId,
            'CBMI',
            $fileTab,
            $activeTab,
            LMRequest::myFileInfo()->getFileTypes(),
            LMRequest::myFileInfo()->getLoanPrograms()
        ); ?>
    </div>

</div>
<!-- coborrowerMonthlyIncomeForm.php -->

<?php

loanForm::popSectionID();
