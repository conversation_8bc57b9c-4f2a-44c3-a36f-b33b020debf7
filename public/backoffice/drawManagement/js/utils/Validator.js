class Validator {
    constructor() {
        this.errors = [];
    }

    validateCategory(category, context = {}) {
        this.clearErrors();

        if (!category.categoryName || category.categoryName.trim() === '') {
            this.addError('Category name is required');
        }

        if (category.categoryName && category.categoryName.length > 255) {
            this.addError('Category name must not exceed 255 characters');
        }

        if (category.description && category.description.length > 1000) {
            this.addError('Category description must not exceed 1000 characters');
        }

        if (!category.order || category.order <= 0) {
            this.addError('Category order must be a positive number');
        }

        if (context.maxCategories && category.order > context.maxCategories) {
            this.addError(`Category order cannot exceed ${context.maxCategories}`);
        }

        if (category.lineItems && Array.isArray(category.lineItems)) {
            category.lineItems.forEach((lineItem, index) => {
                if (!this.validateLineItem(lineItem, context)) {
                    this.addError(`Line item ${index + 1}: ${this.getFirstError()}`);
                }
            });
        }

        return this.isValid();
    }

    validateLineItem(lineItem, context = {}) {
        this.clearErrors();
        if (!lineItem.name || lineItem.name.trim() === '') {
            this.addError('Line item name is required');
        }

        if (lineItem.name && lineItem.name.length > 255) {
            this.addError('Line item name must not exceed 255 characters');
        }

        if (lineItem.description && lineItem.description.length > 1000) {
            this.addError('Line item description must not exceed 1000 characters');
        }

        if (!lineItem.order || lineItem.order <= 0) {
            this.addError('Line item order must be a positive number');
        }

        if (context.userType === 'borrower') {

            if (lineItem.completedAmount < 0) {
                this.addError('Completed amount cannot be negative');
            }

            if (lineItem.completedAmount > lineItem.cost) {
                this.addError('Completed amount cannot exceed total cost');
            }

            if (lineItem.completedPercent < 0 || lineItem.completedPercent > 100) {
                this.addError('Completed percent must be between 0 and 100');
            }

            if (lineItem.requestedAmount < 0) {
                this.addError('Requested amount cannot be negative');
            }

            const maxRequestable = lineItem.cost - lineItem.completedAmount;
            if (lineItem.requestedAmount > maxRequestable) {
                this.addError('Requested amount cannot exceed remaining cost');
            }

            if (lineItem.requestedPercent < 0) {
                this.addError('Requested percent cannot be negative');
            }

            const maxRequestablePercent = 100 - lineItem.completedPercent;
            if (lineItem.requestedPercent > maxRequestablePercent) {
                this.addError('Requested percent cannot exceed remaining percent');
            }

            if (lineItem.cost > 0) {
                const calculatedAmount = (lineItem.requestedPercent / 100) * lineItem.cost;
                const tolerance = 0.01;

                if (Math.abs(lineItem.requestedAmount - calculatedAmount) > tolerance) {
                    this.addError('Requested amount and percent are inconsistent');
                }
            }

            if (lineItem.notes && lineItem.notes.length > 2000) {
                this.addError('Notes must not exceed 2000 characters');
            }

            if (lineItem.lenderNotes && lineItem.lenderNotes.length > 2000) {
                this.addError('Lender notes must not exceed 2000 characters');
            }

        }

        return this.isValid();
    }

    validateDrawRequest(drawRequest, context = {}) {
        this.clearErrors();

        if (!drawRequest.LMRId) {
            this.addError('Loan file ID is required');
        }

        if (!drawRequest.status) {
            this.addError('Status is required');
        }

        const validStatuses = ['new', 'pending', 'approved', 'rejected'];
        if (drawRequest.status && !validStatuses.includes(drawRequest.status)) {
            this.addError('Invalid status value');
        }

        if (drawRequest.amountRequested < 0) {
            this.addError('Requested amount cannot be negative');
        }

        if (drawRequest.amountApproved && drawRequest.amountApproved < 0) {
            this.addError('Approved amount cannot be negative');
        }

        if (drawRequest.amountApproved > drawRequest.amountRequested) {
            this.addError('Approved amount cannot exceed requested amount');
        }

        if (drawRequest.lenderNotes && drawRequest.lenderNotes.length > 2000) {
            this.addError('Lender notes must not exceed 2000 characters');
        }

        if (drawRequest.categories) {
            drawRequest.categories.forEach((category, index) => {
                if (!this.validateCategory(category, context)) {
                    this.addError(`Category ${index + 1}: ${this.getFirstError()}`);
                }
            });
        }

        return this.isValid();
    }

    validateForm(formData, formType, context = {}) {
        this.clearErrors();

        switch (formType) {
            case 'categories':
                return this.validateCategoriesForm(formData, context);
            case 'lineItems':
                return this.validateLineItemsForm(formData, context);
            case 'drawRequest':
                return this.validateDrawRequest(formData, context);
            default:
                this.addError('Unknown form type');
                return false;
        }
    }

    validateCategoriesForm(formData, context) {
        if (!formData.categories || !Array.isArray(formData.categories)) {
            this.addError('Categories data is required');
            return false;
        }

        const orders = formData.categories.map(cat => cat.order);
        const duplicateOrders = orders.filter((order, index) => orders.indexOf(order) !== index);
        if (duplicateOrders.length > 0) {
            this.addError('Duplicate category orders found');
        }

        formData.categories.forEach((category, index) => {
            if (!this.validateCategory(category, context)) {
                this.addError(`Category ${index + 1}: ${this.getFirstError()}`);
            }
        });

        return this.isValid();
    }

    validateLineItemsForm(formData, context) {
        if (!formData.lineItems || typeof formData.lineItems !== 'object') {
            this.addError('Line items data is required');
            return false;
        }

        let hasRequestedAmounts = true;
        let isDrawRequest = context.isDrawRequest !== undefined ? context.isDrawRequest : false;

        Object.keys(formData.lineItems).forEach(categoryId => {
            let categoryLineItems = formData.lineItems;

            if(isDrawRequest) {
                hasRequestedAmounts = this.validateDrawRequestLineItemsData(categoryLineItems);
            } else {
                this.validateSoWLineItemsData(categoryLineItems, context);
            }
        });

        if (!context.isDraft && !hasRequestedAmounts) {
            this.addError('At least one line item must have a requested amount');
        }

        return this.isValid();
    }

    validateSoWLineItemsData(lineItemsData, context) {
        Object.keys(lineItemsData).forEach(categoryId => {
            let categoryLineItems = lineItemsData[categoryId];

            if (Array.isArray(categoryLineItems) && categoryLineItems.length > 0) {
                categoryLineItems.forEach((lineItem, index) => {
                    if (!this.validateLineItem(lineItem, context)) {
                        this.addError(`Category ${categoryId}, Line item ${index + 1}: ${this.getFirstError()}`);
                    }
                });
            }
        })
    }

    validateDrawRequestLineItemsData(lineItemsData) {
        Object.keys(lineItemsData).forEach(categoryId => {
            let categoryLineItems = lineItemsData;

            if(typeof categoryLineItems === 'object') {
                categoryLineItems = Object.values(categoryLineItems);
            }

            if (Array.isArray(categoryLineItems) && categoryLineItems.length > 0) {
                categoryLineItems.forEach((lineItem, index) => {
                    if (!lineItem.requestedAmount > 0) {
                        return false;
                    }
                });
            }
        })
        return true;
    }

    addError(error) {
        this.errors.push(error);
    }


    getErrors() {
        return this.errors;
    }

    getFirstError() {
        return this.errors.length > 0 ? this.errors[0] : null;
    }

    isValid() {
        return this.errors.length === 0;
    }

    clearErrors() {
        this.errors = [];
    }

    displayErrors(containerSelector = '.validation-errors') {
        const $container = $(containerSelector);

        if (this.errors.length === 0) {
            $container.hide();
            return;
        }

        const errorHtml = this.errors.map(error => `<li>${error}</li>`).join('');
        $container.html(`<ul class="list-unstyled mb-0">${errorHtml}</ul>`).show();
    }
}

window.Validator = Validator;
