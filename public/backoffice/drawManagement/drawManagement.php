<?php

use models\PageVariables;
use models\cypher;
use models\standard\Strings;
use models\Request;

$PCID = isset(PageVariables::$PCID) ? cypher::myEncryption(PageVariables::$PCID) : '';
if (empty($PCID) && $_REQUEST['pcid']) $PCID = REQUEST::GetClean('pcid');
Strings::includeMyScript(
    [
    '/backoffice/drawManagement/js/utils/ApiClient.js',
    '/backoffice/drawManagement/js/utils/DataMapper.js',
    '/backoffice/drawManagement/js/utils/Validator.js',
    '/backoffice/drawManagement/js/common.js',
    '/backoffice/drawManagement/js/lender.js',
    '/assets/js/3rdParty/sortable/Sortable.min.js',
    ]
);
Strings::includeMyCSS(['/backoffice/drawManagement/css/drawManagement.css']);

$showDrawManagementTab = PageVariables::PC()->drawManagement && PageVariables::PC()->enableDrawManagementV2;
if (!$showDrawManagementTab) {
    return;
}
$userType= 'lender';
?>

<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card card-custom">
            <div class="card-header card-header-tabs-line bg-gray-100">
                <div class="card-title">
                    <h3 class="card-label">
                        Scope of Work Template
                    </h3>
                </div>
                <div class="card-toolbar">
                    <a href="javascript:void(0);" class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass" data-card-tool="toggle" data-section="workCategoriesCard" data-toggle="tooltip" data-placement="top" title="Toggle Card" data-original-title="Toggle Card">
                        <i class="ki icon-nm ki-arrow-down"></i>
                    </a>
                </div>
            </div>

            <?php require __DIR__ . '/partials/_draw-management-card.php'; ?>
        </div>
    </div>
    <div class="col-md-12 mb-4">
        <div class="card card-custom">
            <div class="card-header card-header-tabs-line bg-gray-100">
                <div class="card-title">
                    <h3 class="card-label">
                        Settings
                    </h3>
                </div>
                <div class="card-toolbar">
                    <a href="javascript:void(0);" class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass collapsed" data-card-tool="toggle" data-section="drawTemplateSettings" data-toggle="tooltip" data-placement="top" title="Toggle Card" data-original-title="Toggle Card">
                        <i class="ki icon-nm ki-arrow-down"></i>
                    </a>
                </div>
            </div>

            <?php require __DIR__ . '/partials/_draw-template-settings-card.php'; ?>
        </div>
    </div>
</div>

<div id="drawManagement">
    <input type="hidden" id="pcid" value="<?php echo $PCID; ?>">
</div>

<script>
    $(document).ready(function() {
        DrawManagement.init(`<?= $userType ?>`);
    });
</script>
