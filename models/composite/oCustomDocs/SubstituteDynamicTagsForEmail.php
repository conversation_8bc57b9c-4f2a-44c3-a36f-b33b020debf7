<?php

namespace models\composite\oCustomDocs;

use models\types\strongType;

/**
 * Class SubstituteDynamicTagsForEmail
 */
class SubstituteDynamicTagsForEmail extends strongType
{
    private const UNBOLD_TAG = '##Unbold##';

    /**
     * @param array $input
     * @param bool $debug
     * @return array|string|string[]
     */
    public static function getReport(array $input = [], bool $debug = false)
    {
        // Trim and prepare initial document content
        $docContent = trim($input['DOCCONTENT']);
        $tags = self::fetchDynamicMergeTags($input);
        $lmrData = self::processLmrId($input);

        if (!empty($tags)) {
            $tags = array_change_key_case($tags, CASE_LOWER); // Normalize case
            $searchKeys = array_keys($tags);

            foreach ($searchKeys as $key) {
                $searchStr = trim($key);
                $replaceStr = trim($tags[$searchStr] ?? '');
                if (isset($lmrData[$replaceStr]) && is_array($lmrData[$replaceStr])) {
                    if(!empty($lmrData[$replaceStr])) {
                        $replaceVal = implode(', ', array_map('trim', array_unique($lmrData[$replaceStr])));
                    }
                } else {
                    $replaceVal = trim($lmrData[$replaceStr] ?? '');
                }
                $docContent = self::replaceInContent($docContent, $searchStr, $replaceVal);
            }
        }

        $docContent = self::removeUnboldTag($docContent);
        return str_replace('../bootstrapeditor', CONST_SITE_URL . 'bootstrapeditor', $docContent);
    }

    /**
     * @param array $input
     * @return array
     */
    private static function fetchDynamicMergeTags(array $input): array
    {
        return $input['dynamicMergeTags'] ?? getAllDynamicFieldTags::getReport();
    }

    /**
     * @param array $input
     * @return array
     */
    private static function processLmrId(array $input): array
    {
        $lmrId = $input['LMRID'] ?? 0;
        return $input[$lmrId] ?? $input;
    }

    /**
     * Replaces all occurrences of a string in the content while escaping it for safety.
     *
     * @param string $content
     * @param string $search
     * @param string $replace
     * @return string
     */
    private static function replaceInContent(string $content, string $search, string $replace): string
    {
        $escapedSearch = preg_quote($search, '/');
        $replace = str_replace('$', '\\$', $replace);
        return preg_replace('/' . $escapedSearch . '/i', $replace, $content);
    }

    /**
     * Removes the unbold tag from the content.
     *
     * @param string $content
     * @return string
     */
    private static function removeUnboldTag(string $content): string
    {
        return str_replace(self::UNBOLD_TAG, '', $content);
    }
}
