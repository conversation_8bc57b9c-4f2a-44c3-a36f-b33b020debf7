<?php


use models\constants\creditDecision;
use models\constants\gl\glDate;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\creditDecision as creditDecisionController;
use models\Controllers\loanForm;
use models\lendingwise\tblCreditDecisionDealDesk;
use models\standard\Arrays;
use models\standard\Dates;

$LMRId = LMRequest::$LMRId;

$dealDeskReasonArray = creditDecision::dealDeskReason();
$boStaffArray = creditDecision::getBOEmployeeAssignedToFile('CD', $LMRId);
$dealDeskData = creditDecisionController::$dealDeskData;
if(!sizeof($dealDeskData ?? [])) {
    $dealDeskData = [
        new tblCreditDecisionDealDesk(),
    ];

}

loanForm::pushSectionID('DD');
?>
<div class="card card-custom dealDesk">
    <div class="card-header card-header-tabs-line bg-gray-100">
        <div class="card-title">
            <h3 class="card-label">
                Deal Desk
            </h3>
        </div>
        <div class="card-toolbar">
            <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer"
                  data-card-tool="toggle"
                  data-section="dealDesk"
                  data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Section">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
        </div>
    </div>
    <div class="card-body dealDesk_body">
        <?php
        $dd_i = 1;
        foreach ($dealDeskData as $dd_field) {
            $classShowHide = $dd_field->dealDeskReason == '5' ? 'show' : 'hide';
            $classRemoveIcon = $dd_i == '1' ? 'hide' : 'show';
            ?>
            <div class="dealDeskFields" id="dealDeskFields_<?php echo $dd_i;?>">
                <input type="hidden" name="dealDeskFields[<?php echo $dd_i;?>][creditDecisionDealDeskId]" value="<?php echo $dd_field->id;?>">
                <div class="dealDeskFieldsRemove text-right <?php echo $classRemoveIcon;?>">
                <span class="btn btn-sm btn-danger btn-icon ml-2 cursor-pointer"
                      data-toggle="popover"
                      data-content="Click to remove"
                      onclick="creditDecisionJs.removeFields(this)"
                      data-clone-section="dealDeskFields"
                      data-inc-id="<?php echo $dd_i; ?>"
                      data-row-id="<?php echo $dd_field->id;?>"
                      data-LMRId="<?php echo $LMRId; ?>"
                >
                    <i class="icon-md fas fa-minus"></i>
                </span>
                </div>
                <div class="form-group row">
                    <div class="col-md-6 <?php echo loanForm::showField('dealDeskReason'); ?>">
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="dealDeskReason_<?php echo $dd_i;?>">
                                <?php echo loanForm::label('dealDeskReason'); ?>
                            </label>
                            <div class="col-md-7">
                                <select name="dealDeskFields[<?php echo $dd_i;?>][dealDeskReason]" id="dealDeskReason_<?php echo $dd_i;?>" 
                                class="form-control <?php echo loanForm::isMandatory('dealDeskReason') ? 'mandatory' : ''; ?>" onchange="creditDecisionJs.dealReason(this)">
                                    <option value="">Select</option>
                                    <?php foreach ($dealDeskReasonArray as $dealDeskReasonKey => $dealDeskReasonValue) { ?>
                                        <option value="<?php echo $dealDeskReasonKey; ?>" <?php echo Arrays::isSelected($dealDeskReasonKey, $dd_field->dealDeskReason);  ?> >
                                        <?php echo $dealDeskReasonValue; ?></option>
                                    <?php }?>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 <?php echo loanForm::showField('dateActionTaken'); ?>">
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="dateActionTaken_<?php echo $dd_i;?>">
                                <?php echo loanForm::label('dateActionTaken'); ?>
                            </label>
                            <div class="col-md-7 input-group">
                                <div class="input-group-prepend actionDate">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary icon-lg"></i>
                                    </span>
                                </div>
                                <input
                                class="form-control input-sm  dateActionTaken dateNewClass <?php echo loanForm::isMandatory('dateActionTaken') ? 'mandatory' : ''; ?>"
                                type="text"
                                name="dealDeskFields[<?php echo $dd_i;?>][dateActionTaken]"
                                id="dateActionTaken_<?php echo $dd_i;?>"
                                value="<?php echo Dates::formatDateWithRE($dd_field->dateActionTaken, 'YMD H:i:s', 'm/d/Y');?>"
                                data-before-creation-date="true"
                                data-start-date="<?php echo glDate::getCreationDate(LMRequest::File()->recordDate); ?>"
                                placeholder="MM/DD/YYYY"
                                maxlength="10"
                                autocomplete="off" >
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 <?php echo loanForm::showField('actionTakenBy'); ?>">
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="actionTakenBy_<?php echo $dd_i;?>">
                                <?php echo loanForm::label('actionTakenBy'); ?>
                            </label>
                            <div class="col-md-7">
                                <select name="dealDeskFields[<?php echo $dd_i;?>][actionTakenBy]" id="actionTakenBy_<?php echo $dd_i;?>" 
                                class="form-control <?php echo loanForm::isMandatory('actionTakenBy') ? 'mandatory' : ''; ?>">
                                    <option value="">Select</option>
                                    <?php foreach ($boStaffArray as $boStaffArrayKey => $boStaffArrayValue) { ?>
                                        <option value="<?php echo $boStaffArrayValue['AID']; ?>" 
                                        <?php echo Arrays::isSelected($boStaffArrayValue['AID'], $dd_field->actionTakenBy);?> >
                                        <?php echo $boStaffArrayValue['role'] . ' - ' . $boStaffArrayValue['processorName']; ?></option>
                                    <?php } ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 <?php echo loanForm::showField('actionTaken'); ?>">
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="actionTaken_<?php echo $dd_i;?>">
                                <?php echo loanForm::label('actionTaken'); ?>
                            </label>
                            <div class="col-md-7">
                                <textarea name="dealDeskFields[<?php echo $dd_i;?>][actionTaken]" id="actionTaken_<?php echo $dd_i;?>" 
                                class="form-control validateMaxLength <?php echo loanForm::isMandatory('actionTaken') ? 'mandatory' : ''; ?>"
                                  maxlength="<?php echo loanForm::getFieldLength('actionTaken','tblCreditDecisionDealDesk'); ?>"
                                  ><?php echo $dd_field->actionTaken;?></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 <?php echo $classShowHide;?>" id="dealDeskReasonOtherDiv_<?php echo $dd_i;?>">
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="dealDeskReasonOther_<?php echo $dd_i;?>">
                                If Other, Please Explain
                            </label>
                            <div class="col-md-7">
                                <textarea name="dealDeskFields[<?php echo $dd_i;?>][dealDeskReasonOther]" id="dealDeskReasonOther_<?php echo $dd_i;?>" 
                                class="form-control validateMaxLength" maxlength="<?php echo loanForm::getFieldLength('dealDeskReasonOther','tblCreditDecisionDealDesk'); ?>"
                                ><?php echo $dd_field->dealDeskReasonOther;?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php $dd_i++;} ?>
        <div class="row">
            <div class="col-lg-3">
                <label class="font-weight-bold">Add Action</label>
                <span class="btn btn-sm btn-success btn-icon ml-2 cursor-pointer"
                      data-toggle="popover"
                      data-content="Click to add Action (Max 5)"
                      onclick="creditDecisionJs.addMoreFields('dealDeskFields');" >
                    <i class=" icon-md fas fa-plus "></i>
                </span>
            </div>
        </div>
    </div>
</div>
