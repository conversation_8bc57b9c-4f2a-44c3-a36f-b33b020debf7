<?php

namespace models\constants\gl;

use models\types\strongType;

class glLoanServicer extends strongType
{
    public static array $loanServicer_CV3 = [
        'ServEase' => 'ServEase',
        'FCI'      => 'FCI',
    ];

    public static array $loanServicer = [
        'ServEase' => 'ServEase',
        'FCI'      => 'FCI',
        'BSI'      => 'BSI',
        'SLS'      => 'SLS',
        'In House' => 'In House',
    ];

    public static array $loanServicerStates = [
        'AK',
        'GA',
        'NE',
        'NV',
        'NJ',
        'NC',
        'SC',
        'OR',
        'WV'
    ];


    public static array $loanServicerStates_FCI = [
        'AK', // Alaska
        'DE', // Delaware
        'NE', // Nebraska
        'NJ', // New Jersey
        'NV', // Nevada
        'WV'  // West Virginia
    ];

    public static array $loanServicerStates_ServEase = [
        'AL', // Alabama
        'AZ', // Arizona
        'AR', // Arkansas
        'CA', // California
        'CO', // Colorado
        'CT', // Connecticut
        'FL', // Florida
        'GA', // Georgia
        'HI', // Hawaii
        'ID', // Idaho
        'IL', // Illinois
        'IN', // Indiana
        'IA', // Iowa
        'KS', // Kansas
        'KY', // Kentucky
        'LA', // Louisiana
        'ME', // Maine
        'MD', // Maryland
        'MA', // Massachusetts
        'MI', // Michigan
        'MN', // Minnesota
        'MS', // Mississippi
        'MO', // Missouri
        'MT', // Montana
        'NH', // New Hampshire
        'NM', // New Mexico
        'NY', // New York
        'NC', // North Carolina
        'ND', // North Dakota
        'OH', // Ohio
        'OK', // Oklahoma
        'OR', // Oregon
        'PA', // Pennsylvania
        'RI', // Rhode Island
        'SC', // South Carolina
        'SD', // South Dakota
        'TN', // Tennessee
        'TX', // Texas
        'UT', // Utah
        'VT', // Vermont
        'VA', // Virginia
        'WA', // Washington
        'WI', // Wisconsin
        'WY', // Wyoming
        'DC'  // District of Columbia
    ];
}