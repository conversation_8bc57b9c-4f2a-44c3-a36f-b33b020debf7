<?php
global $myFileInfo, $incomeInfo, $LMRId, $isHMLO, $totalHouseHoldIncome, $totalHouseHoldExpenses, $totalGrossMonthlyHouseHoldIncome,
       $totalAssetsOwed, $netSocialSecurity1, $socialSecurity1, $netPensionOrRetirement1, $pensionOrRetirement1, $netUnemployment1,
       $unemployment1, $netRental1, $roomRental1, $rental1, $netDisability1, $disability1, $netEarnedInterest1, $earnedInterest1,
       $netRoomRental1, $netSecondJobIncome1, $secondJobIncome1, $netSocialSecurity2, $socialSecurity2, $netPensionOrRetirement2,
       $pensionOrRetirement2, $netDisability2, $disability2, $netUnemployment2,
       $unemployment2, $netRental2, $rental2, $netEarnedInterest2, $earnedInterest2, $netRoomRental2, $roomRental2,
       $netSecondJobIncome2, $secondJobIncome2, $isLO, $employedInfo1Array, $allowToEdit, $stateArray, $primTotalGrossIncome,
       $capitalGains1, $partnership1, $otherHouseHold1, $otherIncomeDescription1, $sonOrDaughter1, $parents1, $autoLoan1, $autoLoanBalance1,
       $unsecuredLoanBalance1, $otherMortgage1, $otherMortgageBalance1, $childSupportOrAlimony1, $foodStampWelfare1, $primTotalHouseHoldIncome,
       $primTotalNetHouseHoldIncome, $HOAFees1, $creditCards1, $creditCardsBalance1, $unsecuredLoans1, $studentLoans1, $studentLoansBalance1,
       $childSupportOrAlimonyMonthly1, $careAmt1, $allInsurance1, $groceries1, $careAmt2, $medicalBill1, $entertainment1, $other1, $donation1,
       $pets1, $monthlyParking1, $carExpenses1, $unionDues1, $personalLoan1, $lunchPurchased1, $rentalExp1, $cable1, $electricity1, $natural1,
       $primaryBorrowerPhone, $water1, $internet1, $dryCleaning1, $utilityOther1, $primTotalHouseHoldExpenses, $coTotalGrossIncome, $capitalGains2,
       $partnership2, $otherHouseHold2, $otherIncomeDescription2, $sonOrDaughter2, $parents2, $childSupportOrAlimony2, $foodStampWelfare2,
       $coTotalHouseHoldIncome, $coTotalNetHouseHoldIncome, $creditCards2, $creditCardsBalance2, $autoLoan2, $autoLoanBalance2, $unsecuredLoans2,
       $unsecuredLoanBalance2, $otherMortgageBalance2, $studentLoansBalance2, $childSupportOrAlimonyMonthly2, $otherMortgage2, $studentLoans2,
       $allInsurance2, $groceries2, $carExpenses2, $medicalBill2, $entertainment2, $other2, $donation2, $pets2, $monthlyParking2, $unionDues2,
       $personalLoan2, $lunchPurchased2, $rentalExp2, $cable2, $electricity2, $natural2, $coBorrowerPhone, $water2, $internet2, $dryCleaning2,
       $utilityOther2, $coTotalHouseHoldExpenses, $isTaxReturnDisp, $tabIndexNo, $totalDisposableIncome, $HMLORealEstateTaxes, $totalInsurance,
       $childSupportOrAlimonyMonthlyBalance1, $childSupportOrAlimonyMonthlyBalance2, $otherBalance1, $otherBalance2, $expFedTax, $expFedTaxOwed,
       $expStateTax, $expStateTaxOwed, $expRentalPay, $expRentalPayOwed, $expMortgPayResi, $expMortgPayResiOwed, $expMortgPayInvest, $expMortgPayInvestOwed,
       $expPropTaxResi, $expPropTaxResiOwed, $expPropTaxInvest, $expPropTaxInvestOwed, $expLoanPayments, $expLoanPaymentsOwed,
       $expIns, $expInsOwed, $expInvestments, $expInvestmentsOwed, $expTuition, $expTuitionOwed, $expOtherLiving, $expOtherLivingOwed,
       $expMedical, $expMedicalOwed, $fileMC;


//echo 'expFedTax '.$expFedTax;
//exit;
global $op, $fileTab;

use models\composite\proposalFormula;
use models\constants\employedInfo1Array;
use models\constants\gl\glCountryArray;
use models\Controllers\loanForm;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;


$debugCoBorrower = $_REQUEST['DEBUG_COBORROWER'] ?? null;
$isLO = $_REQUEST['DEBUG_IS_LO'] ?? $isLO;
$isHMLO = $_REQUEST['DEBUG_IS_HMLO'] ?? $isLO;


$employedInfo1Array = employedInfo1Array::$employedInfo1Array;
$glCountryArray = glCountryArray::$glCountryArray;

$netDefaultText = '- Net -';
$grossDefaultText = '- Gross -';
$lien1Payment = '';
$lien2Payment = '';
$paidOften = '';
$paidOften1 = '';
$taxes1 = 0;
$isTaxesInsEscrowed = '';
$mortgageDelinquencyAmount = '';
$QAInfo = [];
$borEmploymentInfo = [];
$coBEmploymentInfo = [];
$fileLOExpensesInfo = [];
$YRF4506TDate1 = '';
$YRF4506TDate2 = '';
$YRF4506TDate3 = '';
$YRF4506TDate4 = '';

$boremptypeshare = '';
$borempmonthlyincome = '';

$coboremptypeshare = '';
$coborempmonthlyincome = '';
$employedByOtherParty = 0;
$ownerOrSelfEmpoyed = 0;
$tabIndex = 1;
if (count($myFileInfo) > 0) {
    $lien1Payment = Strings::showField('lien1Payment', 'LMRInfo');
    $lien2Payment = Strings::showField('lien2Payment', 'LMRInfo');
    $paidOften = Strings::showField('paidOften', 'incomeInfo');
    $paidOften1 = Strings::showField('paidOften1', 'incomeInfo');
    $floodInsurance1 = Strings::showField('floodInsurance1', 'incomeInfo');
    $taxes1 = Strings::showField('taxes1', 'incomeInfo');
    //$isTaxesInsEscrowed		= Strings::showField('isTaxesInsEscrowed', 'fileHMLONewLoanInfo');

    $employedByOtherParty = Strings::showField('employedByOtherParty', 'incomeInfo');
    $ownerOrSelfEmpoyed = Strings::showField('ownerOrSelfEmpoyed', 'incomeInfo');

    if (array_key_exists('QAInfo', $myFileInfo)) $QAInfo = $myFileInfo['QAInfo'];
    /** Fetch Q+A info **/

    if (array_key_exists('borEmploymentInfo', $myFileInfo)) $borEmploymentInfo = $myFileInfo['borEmploymentInfo'];

    if (array_key_exists('coBEmploymentInfo', $myFileInfo)) $coBEmploymentInfo = $myFileInfo['coBEmploymentInfo'];

    if (array_key_exists('fileLOExpensesInfo', $myFileInfo)) $fileLOExpensesInfo = $myFileInfo['fileLOExpensesInfo'];
}

require 'incExpCalculation.php';
/** Income & expenses calculation for a file **/

if (count($myFileInfo) > 0) {
    $creditorInfo = $myFileInfo['creditorInfo'];
    $assetsInfo = $myFileInfo['AssetsInfo'];
}
$borrowerHireDate = '';
if (count($incomeInfo) > 0) {
    $houseHoldExpensesNotes = trim($incomeInfo['houseHoldExpensesNotes']);
    $borrowerHireDate = trim($incomeInfo['borrowerHireDate']);
    $coBorrowerHireDate = trim($incomeInfo['coBorrowerHireDate']);
    $unemploymentStDate1 = trim($incomeInfo['unemploymentStDate1']);
    $secondJobStDate1 = trim($incomeInfo['secondJobStDate1']);
    $unemploymentStDate2 = trim($incomeInfo['unemploymentStDate2']);
    $secondJobStDate2 = trim($incomeInfo['secondJobStDate2']);

    $borSocialSecurity = trim($incomeInfo['borSocialSecurity']);
    $coBSocialSecurity = trim($incomeInfo['coBSocialSecurity']);

    $boremptypeshare = trim($incomeInfo['emptypeshare1']);
    $borempmonthlyincome = trim($incomeInfo['empmonthlyincome1']);
    $coboremptypeshare = trim($incomeInfo['emptypeshare2']);
    $coborempmonthlyincome = trim($incomeInfo['empmonthlyincome2']);

    if (Dates::IsEmpty($borrowerHireDate)) {
        $borrowerHireDate = '';
    } else {
        $borrowerHireDate = Dates::formatDateWithRE($borrowerHireDate, 'YMD', 'm/d/Y');
    }
    if (Dates::IsEmpty($coBorrowerHireDate)) {
        $coBorrowerHireDate = '';
    } else {
        $coBorrowerHireDate = Dates::formatDateWithRE($coBorrowerHireDate, 'YMD', 'm/d/Y');
    }
    if (Dates::IsEmpty($unemploymentStDate1)) {
        $unemploymentStDate1 = '';
    } else {
        $unemploymentStDate1 = Dates::formatDateWithRE($unemploymentStDate1, 'YMD', 'm/d/Y');
    }
    if (Dates::IsEmpty($secondJobStDate1)) {
        $secondJobStDate1 = '';
    } else {
        $secondJobStDate1 = Dates::formatDateWithRE($secondJobStDate1, 'YMD', 'm/d/Y');
    }

    if (Dates::IsEmpty($unemploymentStDate2)) {
        $unemploymentStDate2 = '';
    } else {
        $unemploymentStDate2 = Dates::formatDateWithRE($unemploymentStDate2, 'YMD', 'm/d/Y');
    }
    if (Dates::IsEmpty($secondJobStDate2)) {
        $secondJobStDate2 = '';
    } else {
        $secondJobStDate2 = Dates::formatDateWithRE($secondJobStDate2, 'YMD', 'm/d/Y');
    }

    $employer1Phone = Strings::showField('employer1Phone', 'incomeInfo');
    $employer2Phone = Strings::showField('employer2Phone', 'incomeInfo');
    $employer1PhoneArray = Strings::splitPhoneNumber($employer1Phone);
    $employer2PhoneArray = Strings::splitPhoneNumber($employer2Phone);
    if (count($employer1PhoneArray) > 0) {
        $employer1Phone1 = trim($employer1PhoneArray['No1']);
        $employer1Phone2 = trim($employer1PhoneArray['No2']);
        $employer1Phone3 = trim($employer1PhoneArray['No3']);
        $employer1PhoneExt = trim($employer1PhoneArray['Ext']);
    }
    if (count($employer2PhoneArray) > 0) {
        $employer2Phone1 = trim($employer2PhoneArray['No1']);
        $employer2Phone2 = trim($employer2PhoneArray['No2']);
        $employer2Phone3 = trim($employer2PhoneArray['No3']);
        $employer2PhoneExt = trim($employer2PhoneArray['Ext']);
    }
}


if ($LMRId > 0) {

    $lien1DTI = '';
    $lien2DTI = '';
    $mortgageOwner1 = '';
    $mortgageOwner1 = Strings::showField('mortgageOwner1', 'LMRInfo');

    /*** Remove  the mortgage insurance from calculating DTI - on Sep 03, 2015 ***/
    if ($mortgageOwner1 == '3') {
        /*** Include the mortgage insurance while calculating DTI if the mortgage type/owner is FHA - on May 04, 2016 ***/
        $tempLien1PaymentPITIA = proposalFormula::calculatePaymentPI(Strings::showField('lien1Payment', 'LMRInfo'), Strings::showField('taxes1', 'incomeInfo'), Strings::showField('insurance1', 'incomeInfo'), Strings::showField('HOAFees1', 'incomeInfo'), Strings::showField('mortgageInsurance1', 'incomeInfo'), Strings::showField('floodInsurance1', 'incomeInfo'));
    } else {
        $tempLien1PaymentPITIA = proposalFormula::calculatePaymentPI(Strings::showField('lien1Payment', 'LMRInfo'), Strings::showField('taxes1', 'incomeInfo'), Strings::showField('insurance1', 'incomeInfo'), Strings::showField('HOAFees1', 'incomeInfo'), 0, Strings::showField('floodInsurance1', 'incomeInfo'));
    }
    if ($isHMLO == 1) { /* Calculate separate DTI for HMLO */
        $lien1DTI = proposalFormula::calculateDTIForHMLO($totalHouseHoldIncome, $totalHouseHoldExpenses);
    } else {
        $lien1DTI = proposalFormula::calculateDTI($tempLien1PaymentPITIA, 0, $totalGrossMonthlyHouseHoldIncome);
    }
}

$presentRent = '';
$proposedRent = '';
$presentFirstMortgage = '';
$proposedFirstMortgage = '';
$presentOtherFinancing = '';
$proposedOtherFinancing = '';
$presentHazardInsurance = '';
$proposedHazardInsurance = '';
$presentRealEstateTaxes = '';
$proposedRealEstateTaxes = '';
$presentMortgageInsurance = '';
$proposedMortgageInsurance = '';
$presentHomeownerAssnDues = '';
$proposedHomeownerAssnDues = '';
$presentOther = '';
$proposedOther = '';

if (count($fileLOExpensesInfo) > 0) {
    $presentRent = trim($fileLOExpensesInfo['presentRent']);
    $proposedRent = trim($fileLOExpensesInfo['proposedRent']);
    $presentFirstMortgage = trim($fileLOExpensesInfo['presentFirstMortgage']);
    $proposedFirstMortgage = trim($fileLOExpensesInfo['proposedFirstMortgage']);
    $presentOtherFinancing = trim($fileLOExpensesInfo['presentOtherFinancing']);
    $proposedOtherFinancing = trim($fileLOExpensesInfo['proposedOtherFinancing']);

    $presentHazardInsurance = trim($fileLOExpensesInfo['presentHazardInsurance']);
    $proposedHazardInsurance = trim($fileLOExpensesInfo['proposedHazardInsurance']);
    $presentRealEstateTaxes = trim($fileLOExpensesInfo['presentRealEstateTaxes']);
    $proposedRealEstateTaxes = trim($fileLOExpensesInfo['proposedRealEstateTaxes']);
    $presentMortgageInsurance = trim($fileLOExpensesInfo['presentMortgageInsurance']);
    $proposedMortgageInsurance = trim($fileLOExpensesInfo['proposedMortgageInsurance']);
    $presentHomeownerAssnDues = trim($fileLOExpensesInfo['presentHomeownerAssnDues']);
    $proposedHomeownerAssnDues = trim($fileLOExpensesInfo['proposedHomeownerAssnDues']);
    $presentOther = trim($fileLOExpensesInfo['presentOther']);
    $proposedOther = trim($fileLOExpensesInfo['proposedOther']);
}

if (count($assetsInfo) > 0) {
    $assetId = trim($assetsInfo['assetID']);
    $assetCheckingAccounts = trim($assetsInfo['assetCheckingAccounts']);
    $assetSavingMoneyMarket = trim($assetsInfo['assetSavingMoneyMarket']);
    $assetStocks = trim($assetsInfo['assetStocks']);
    $assetIRAAccounts = trim($assetsInfo['assetIRAAccounts']);
    $assetESPOAccounts = trim($assetsInfo['assetESPOAccounts']);
    $assetHome = trim($assetsInfo['assetHome']);
    $assetORE = trim($assetsInfo['assetORE']);
    $assetCars = trim($assetsInfo['assetCars']);
    $assetLifeInsurance = trim($assetsInfo['assetLifeInsurance']);
    $assetOther = trim($assetsInfo['assetOther']);
    $assetCash = trim($assetsInfo['assetCash']);
    $assetHomeOwed = trim($assetsInfo['assetHomeOwed']);
    $assetOREOwed = trim($assetsInfo['assetOREOwed']);
    $assetCarsOwed = trim($assetsInfo['assetCarsOwed']);
}
if (count($QAInfo) > 0) {
    $mortgageDelinquencyAmount = Strings::showField('mortgageDelinquencyAmount', 'QAInfo');
    $YRF4506TDate1 = Strings::showField('YRF4506TDate1', 'QAInfo'); // YRF - Year requested For 4506T
    $YRF4506TDate2 = Strings::showField('YRF4506TDate2', 'QAInfo');
    $YRF4506TDate3 = Strings::showField('YRF4506TDate3', 'QAInfo');
    $YRF4506TDate4 = Strings::showField('YRF4506TDate4', 'QAInfo');

    if (Dates::IsEmpty($YRF4506TDate1)) {
        $YRF4506TDate1 = '';
    } else {
        $YRF4506TDate1 = Dates::formatDateWithRE($YRF4506TDate1, 'YMD', 'm/d/Y');
    }
    if (Dates::IsEmpty($YRF4506TDate2)) {
        $YRF4506TDate2 = '';
    } else {
        $YRF4506TDate2 = Dates::formatDateWithRE($YRF4506TDate2, 'YMD', 'm/d/Y');
    }
    if (Dates::IsEmpty($YRF4506TDate3)) {
        $YRF4506TDate3 = '';
    } else {
        $YRF4506TDate3 = Dates::formatDateWithRE($YRF4506TDate3, 'YMD', 'm/d/Y');
    }
    if (Dates::IsEmpty($YRF4506TDate4)) {
        $YRF4506TDate4 = '';
    } else {
        $YRF4506TDate4 = Dates::formatDateWithRE($YRF4506TDate4, 'YMD', 'm/d/Y');
    }
}

$totalAssets = Strings::replaceCommaValues($assetCheckingAccounts) + Strings::replaceCommaValues($assetSavingMoneyMarket);
$totalAssets += Strings::replaceCommaValues($assetStocks) + Strings::replaceCommaValues($assetIRAAccounts);
$totalAssets += Strings::replaceCommaValues($assetESPOAccounts) + Strings::replaceCommaValues($assetHome);
$totalAssets += Strings::replaceCommaValues($assetORE) + Strings::replaceCommaValues($assetCars);
$totalAssets += Strings::replaceCommaValues($assetLifeInsurance) + Strings::replaceCommaValues($assetOther);
$totalAssets += Strings::replaceCommaValues($assetCash);
$totalAssets = round($totalAssets, 2);
$totalAssetsOwed += Strings::replaceCommaValues($assetHomeOwed);
$totalAssetsOwed += Strings::replaceCommaValues($assetOREOwed) + Strings::replaceCommaValues($assetCarsOwed);

$totalAssetsOwed = round($totalAssetsOwed, 2);

$totalAssetsNetValue = Strings::replaceCommaValues($totalAssets) - Strings::replaceCommaValues($totalAssetsOwed);
$totalAssetsNetValue = round($totalAssetsNetValue, 2);

if (trim($netSocialSecurity1) == '') $netSocialSecurity1 = $netDefaultText;
if (trim($socialSecurity1) == '') $socialSecurity1 = $grossDefaultText;
if (trim($netPensionOrRetirement1) == '') $netPensionOrRetirement1 = $netDefaultText;
if (trim($pensionOrRetirement1) == '') $pensionOrRetirement1 = $grossDefaultText;
if (trim($netDisability1) == '') $netDisability1 = $netDefaultText;
if (trim($disability1) == '') $disability1 = $grossDefaultText;
if (trim($netUnemployment1) == '') $netUnemployment1 = $netDefaultText;
//if (trim($unemployment1) == '') $unemployment1 = $unemployment1;
if (trim($netRental1) == '') $netRental1 = $netDefaultText;
if (trim($rental1) == '') $rental1 = $grossDefaultText;
if (trim($netEarnedInterest1) == '') $netEarnedInterest1 = $netDefaultText;
if (trim($earnedInterest1) == '') $earnedInterest1 = $grossDefaultText;
if (trim($netRoomRental1) == '') $netRoomRental1 = $netDefaultText;
if (trim($roomRental1) == '') $roomRental1 = $grossDefaultText;
if (trim($netSecondJobIncome1) == '') $netSecondJobIncome1 = $netDefaultText;
if (trim($secondJobIncome1) == '') $secondJobIncome1 = $grossDefaultText;

if (trim($netSocialSecurity2) == '') $netSocialSecurity2 = $netDefaultText;
if (trim($socialSecurity2) == '') $socialSecurity2 = $grossDefaultText;
if (trim($netPensionOrRetirement2) == '') $netPensionOrRetirement2 = $netDefaultText;
if (trim($pensionOrRetirement2) == '') $pensionOrRetirement2 = $grossDefaultText;
if (trim($netDisability2) == '') $netDisability2 = $netDefaultText;
if (trim($disability2) == '') $disability2 = $grossDefaultText;
if (trim($netUnemployment2) == '') $netUnemployment2 = $netDefaultText;
//if (trim($unemployment2) == '') $unemployment2 = $unemployment2;
if (trim($netRental2) == '') $netRental2 = $netDefaultText;
if (trim($rental2) == '') $rental2 = $grossDefaultText;
if (trim($netEarnedInterest2) == '') $netEarnedInterest2 = $netDefaultText;
if (trim($earnedInterest2) == '') $earnedInterest2 = $grossDefaultText;
if (trim($netRoomRental2) == '') $netRoomRental2 = $netDefaultText;
if (trim($roomRental2) == '') $roomRental2 = $grossDefaultText;
if (trim($netSecondJobIncome2) == '') $netSecondJobIncome2 = $netDefaultText;
if (trim($secondJobIncome2) == '') $secondJobIncome2 = $grossDefaultText;
$secArr = BaseHTML::sectionAccess2(['sId' => 'BEI', 'opt' => $fileTab]);
loanForm::pushSectionID('BEI');

$cosecArr = BaseHTML::sectionAccess(['sId' => 'CBEI', 'opt' => $fileTab]);



?>

<?php echo loanForm::hidden('grossText', $grossDefaultText); ?>
<?php echo loanForm::hidden('netText', $netDefaultText); ?>
<?php echo loanForm::hidden('isCoBorrowerExists', Strings::showField('isCoBorrower', 'LMRInfo')); ?>
<?php echo loanForm::hidden('mortgage1MonthlyPayment', Strings::showField('lien1Payment', 'LMRInfo')); ?>
<?php echo loanForm::hidden('mortgage2MonthlyPayment', Strings::showField('lien2Payment', 'LMRInfo')); ?>
<?php echo loanForm::hidden('assetId', $assetId); ?>
<?php echo loanForm::hidden('lien1PaymentPITIAValue', $tempLien1PaymentPITIA); ?>
<?php echo loanForm::hidden('isLOOpt', htmlspecialchars($isLO)); ?>
<?php echo loanForm::hidden('isHMLOOpt', htmlspecialchars($isHMLO)); ?>
<?php echo loanForm::hidden('additionalEmpCnt', count($borEmploymentInfo)); ?>
<?php echo loanForm::hidden('additionalCoBEmpCnt', count($coBEmploymentInfo)); ?>

<div class="row ">
    <div class="col-md-12">
        <?php require_once __DIR__ . '/LMRequest/sections/incExpForm/borrowerEmploymentInfo.php'; ?>
    </div>
    <?php
    if (Strings::showField('isCoBorrower', 'LMRInfo') == 1 || $debugCoBorrower) { ?>
        <div class="col-md-12">
            <?php require_once __DIR__ . '/LMRequest/sections/incExpForm/coborrowerEmploymentInfo.php'; ?>
        </div>
    <?php } ?>
</div>

<div class="row ">
    <div class="col-md-12">
        <?php require_once __DIR__ . '/LMRequest/sections/incExpForm/borrowerMonthlyIncomeForm.php'; ?>
    </div>
    <?php
    if (Strings::showField('isCoBorrower', 'LMRInfo') == 1 || $debugCoBorrower) { ?>
        <div class="col-md-12">
            <?php require_once __DIR__ . '/LMRequest/sections/incExpForm/coborrowerMonthlyIncomeForm.php'; ?>
        </div>
    <?php } ?>
</div>

<div class="row ">
    <div class="col-md-12">
        <?php require_once __DIR__ . '/LMRequest/sections/incExpForm/4506TYearRequested.php'; ?>
    </div>
</div>

<?php if (!($isLO == 1 || $isHMLO == 1)) { ?>
    <div class=" row">
        <div class="col-md-12">
            <div class="card card-custom ">
                <div class="card-body">
                    <div class="form-group row">
                        <div class="col-md-3">
                            <label class="font-weight-bold">Total Joint Household Income (Gross)</label>
                            <br>
                            $
                            <span id="totalHouseHoldIncome"><?php echo Currency::formatDollarAmountWithDecimal($totalHouseHoldIncome) ?></span>
                        </div>
                        <div class="col-md-3">
                            <label class="font-weight-bold">- Total Joint Household Expenses</label>
                            <br>
                            $ <span
                                    id="totalHouseHoldExpenses"><?php echo Currency::formatDollarAmountWithDecimal($totalHouseHoldExpenses) ?></span>
                        </div>
                        <div class="col-md-3">
                            <label class="font-weight-bold">= Disposable Income (Net) <?php
                                if ($allowToEdit) { ?>
                                    <i data-html="true"
                                       title="Disposable Income is what your household has left over after paying all monthly expenses.<br>If you are too negative or too positive, you may NOT be approved for a loan modification.<br>Multiple factors play a part in approved loan modifications, like hardship explanation, current mortgage rate/term, <br>who the lender is/who the investor is, how persistent you are, and several other factors.<br>Read the Instruction Guide with your Bank Ready loan modification package."
                                       class="fa fa-info-circle  text-primary tooltipClass"></i>
                                    <?php
                                }
                                ?></label>
                            <br>
                            $ <span
                                    id="totalDisposableIncome"><?php echo Currency::formatDollarAmountWithDecimal($totalDisposableIncome) ?></span>
                        </div>
                        <div class="col-md-3">
                            <label class="font-weight-bold">Current Mortgage DTI
                                <?php
                                if ($allowToEdit) { ?>
                                    <i data-html="true"
                                       title="DTI is calculated using your 1st Mortgage PITIA payment divided by Total Gross Income times 100. Do NOT include private monthly mortgage insurance"
                                       class="fa fa-info-circle tooltipClass text-primary"></i>
                                    <?php
                                } ?></label>
                            <br>
                            <span id="lien1IncomeDTI"><?php echo Currency::formatDollarAmountWithDecimal($lien1DTI) ?></span>%
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php } ?>


<?php if ($isLO == 1) { ?>

    <?php require_once __DIR__ . '/LMRequest/sections/incExpForm/expenses.php'; ?>

<?php }


//if ($isHMLO == 1) { ?>
    <div class="row">
        <div class="col-md-12">
            <?php require_once __DIR__ . '/LMRequest/sections/incExpForm/borrowerMonthlyExpensesForm.php'; ?>
        </div>
        <?php
        if (Strings::showField('isCoBorrower', 'LMRInfo') == 1 || $debugCoBorrower) { ?>
            <div class="col-md-12">
                <?php require_once __DIR__ . '/LMRequest/sections/incExpForm/coborrowerMonthlyExpensesForm.php'; ?>
            </div>
        <?php } ?>
    </div>

<?php //} ?>

<table style="width:100%">
    <tr>
        <td style="text-align:center;">
            <input type="submit" class="btn btn-primary btnSave scrollSave" name="scrollSave" value="Save"
                   tabindex="<?php echo $tabIndex++; ?>"
                   onclick="return this.disabled===false;"/>
            <input type="submit" class="btn btn-primary btnSave" name="btnSave" value="Save"
                   tabindex="<?php echo $tabIndex++; ?>"
                   onclick="return this.disabled===false;"/>
            <input type="submit" class="btn btn-primary btnSave" name="btnSave" value="Save & Next"
                   tabindex="<?php echo $tabIndex++; ?>"
                   onclick="return this.disabled===false;"/>
        </td>
    </tr>
</table>
<?php
if (!$allowToEdit) { ?>
    <script type="text/javascript">
        $(function () {
            disableFormFields('loanModForm');
        });

        function disableFormFields(oForm) {
            //		   document.getElementById(oForm).className = 'grey';
            let frm_elements = document.getElementById(oForm).elements;
            for (let i = 0; i < frm_elements.length; i++) {
                // let field_type = frm_elements[i].type.toLowerCase();
                frm_elements[i].disabled = true;
            }
        }
    </script>

<?php } ?>

<script type="text/javascript" src="/backoffice/LMRequest/js/incExpForm.js"></script>

<!-- incExpForm.php -->
