INSERT INTO tblFormFieldsMaster (fieldName, fieldType, fieldLabel, sectionID, tabName, activeStatus, tableName, displayOrder, fileType, BODefaultShow, enableAllLoanPrograms)
VALUES
    ('YRF4506TDate1', 'Date', 'Date Requested', 'YRF4506', 'LI', 1, 'tblQAInfo', 1, 'HMLO,loc', 1, 1),
    ('borrowerNotes', 'string', 'Notes', 'BCI', 'LI', 1, 'tblFile2', 1001, 'HMLO,loc', 1, 1),
    ('propUsedExpValidated', 'string', '# of Properties Used for Experience Validated', 'CD', 'LI', 1, 'tblCreditDecision', 1, 'HMLO', 1, 1),
    ('creditDecision', 'int', 'Decision', 'CD', 'LI', 1, 'tblCreditDecision', 2, 'HMLO', 1, 1),
    ('creditDecisionDate', 'Date', 'Decision Date', 'CD', 'LI', 1, 'tblCreditDecision', 3, 'HMLO', 1, 1),
    ('guidelineVersion', 'string', 'Guideline Version', 'CD', 'LI', 1, 'tblCreditDecision', 4, 'HMLO', 1, 1),
    ('creditDecisionBy', 'int', 'Decision By', 'CD', 'LI', 1, 'tblCreditDecision', 5, 'HMLO', 1, 1),
    ('creditDecisionNotes', 'string', 'Decision Notes', 'CD', 'LI', 1, 'tblCreditDecision', 6, 'HMLO', 1, 1),
    ('doNotSell', 'string', 'DO NOT SELL', 'CD', 'LI', 1, 'tblCreditDecisionForm', 7, 'HMLO', 1, 1),
    ('doNotSellDate', 'Date', 'Do Not Sell Date', 'CD', 'LI', 1, 'tblCreditDecisionForm', 8, 'HMLO', 1, 1),
    ('doNotSellReason', 'string', 'Do Not Sell Reason', 'CD', 'LI', 1, 'tblCreditDecisionForm', 9, 'HMLO', 1, 1),
    ('secondSignDate', 'Date', 'Request for Second Sign Date', 'SS', 'LI', 1, 'tblCreditDecisionForm', 1, 'HMLO', 1, 1),
    ('secondSignReason', 'int', 'Reason For Second Sign', 'SS', 'LI', 1, 'tblCreditDecisionForm', 2, 'HMLO', 1, 1),
    ('secondSignDecision', 'int', 'Second Sign Decision', 'SS', 'LI', 1, 'tblCreditDecisionForm', 3, 'HMLO', 1, 1),
    ('secondSignDecisionDate', 'Date', 'Second Sign Decision Date', 'SS', 'LI', 1, 'tblCreditDecisionForm', 4, 'HMLO', 1, 1),
    ('secondSignDecisionBy', 'int', 'Second Sign Decision By', 'SS', 'LI', 1, 'tblCreditDecisionForm', 5, 'HMLO', 1, 1),
    ('secondSignDecisionNotes', 'string', 'Second Sign Decision Notes', 'SS', 'LI', 1, 'tblCreditDecisionForm', 6, 'HMLO', 1, 1),
    ('clearToCloseDate', 'Date', 'Clear to Close Date', 'CLTCL', 'LI', 1, 'tblCreditDecisionForm', 1, 'HMLO', 1, 1),
    ('clearToCloseBy', 'int', 'Clear to Close By', 'CLTCL', 'LI', 1, 'tblCreditDecisionForm', 2, 'HMLO', 1, 1),
    ('clearToCloseNotes', 'string', 'Clear to Close Notes', 'CLTCL', 'LI', 1, 'tblCreditDecisionForm', 3, 'HMLO', 1, 1),
    ('dealDeskReason', 'int', 'Deal Desk Reason', 'DD', 'LI', 1, 'tblCreditDecisionDealDesk', 1, 'HMLO', 1, 1),
    ('dateActionTaken', 'Date', 'Date Action Taken', 'DD', 'LI', 1, 'tblCreditDecisionDealDesk', 2, 'HMLO', 1, 1),
    ('actionTakenBy', 'int', 'Action Taken By', 'DD', 'LI', 1, 'tblCreditDecisionDealDesk', 3, 'HMLO', 1, 1),
    ('actionTaken', 'string', 'Action Taken', 'DD', 'LI', 1, 'tblCreditDecisionDealDesk', 4, 'HMLO', 1, 1),
    ('exceptionCategory', 'int', 'Exception Category', 'CDE', 'LI', 1, 'tblCreditDecisionException', 1, 'HMLO', 1, 1),
    ('exceptionSubCategory', 'int', 'Exception Sub-Category', 'CDE', 'LI', 1, 'tblCreditDecisionException', 2, 'HMLO', 1, 1),
    ('exceptionDate', 'Date', 'Exception Date', 'CDE', 'LI', 1, 'tblCreditDecisionException', 3, 'HMLO', 1, 1),
    ('exceptionApprovedBy', 'int', 'Exception Approved By', 'CDE', 'LI', 1, 'tblCreditDecisionException', 4, 'HMLO', 1, 1),
    ('compensatingFactors', 'string', 'Compensating Factors', 'CDE', 'LI', 1, 'tblCreditDecisionException', 5, 'HMLO', 1, 1),
    ('actualGuideline', 'string', 'Actual Guideline', 'CDE', 'LI', 1, 'tblCreditDecisionException', 6, 'HMLO', 1, 1),
    ('exceptionDescription', 'string', 'Exception Description', 'CDE', 'LI', 1, 'tblCreditDecisionException', 7, 'HMLO', 1, 1),
    ('exceptionNotes', 'string', 'Exception Notes', 'CDE', 'LI', 1, 'tblCreditDecisionException', 8, 'HMLO', 1, 1),
    ('accountDescription', 'string', 'Description of Account, Transaction, or Requested Credit', 'AA', 'LI', 1, 'tblAdverseAction', 11, 'HMLO', 1, 1),
    ('HMDAActionTaken', 'string', 'HMDA Action Taken', 'AA', 'LI', 1, 'tblAdverseAction', 12, 'HMLO', 1, 1),
    ('actionDate', 'Date', 'Action Date', 'AA', 'LI', 1, 'tblAdverseAction', 13, 'HMLO', 1, 1),
    ('actionNoticeDetails', 'string', 'Notice Detail', 'AA', 'LI', 1, 'tblAdverseAction', 14, 'HMLO', 1, 1),
    ('informationByDate', 'string', 'Counter Offer Expires / Missing Information By', 'AA', 'LI', 1, 'tblAdverseAction', 15, 'HMLO', 1, 1),
    ('dateDenied', 'Date', 'Date Denied', 'AA', 'LI', 1, 'tblAdverseAction', 16, 'HMLO', 1, 1),
    ('denialReason', 'string', 'Principal Reason(s) for Denial or Other Action Taken', 'AA', 'LI', 1, 'tblAdverseAction', 17, 'HMLO', 1, 1),
    ('otherReason', 'string', 'Other Reason(s)', 'AA', 'LI', 1, 'tblAdverseAction', 18, 'HMLO', 1, 1),
    ('obtainedFromAgency', 'string', 'Was information obtained in a report from a consumer reporting agency?', 'AA', 'LI', 1, 'tblAdverseAction', 19, 'HMLO', 1, 1),
    ('creditReportDate', 'Date', 'Date Report Created', 'AA', 'LI', 1, 'tblAdverseAction', 20, 'HMLO', 1, 1),
    ('agencyName', 'string', 'Agency', 'AA', 'LI', 1, 'tblAdverseAction', 21, 'HMLO', 1, 1),
    ('agencyAddress', 'string', 'Address', 'AA', 'LI', 1, 'tblAdverseAction', 22, 'HMLO', 1, 1),
    ('agencyPhone', 'string', 'Phone', 'AA', 'LI', 1, 'tblAdverseAction', 23, 'HMLO', 1, 1),
    ('agencyScoreRange', 'int', 'Range of Possible Scores', 'AA', 'LI', 1, 'tblAdverseAction', 24, 'HMLO', 1, 1),
    ('agencyScore', 'int', 'Score', 'AA', 'LI', 1, 'tblAdverseAction', 25, 'HMLO', 1, 1),
    ('agencyInquiries', 'string', 'Number of Recent Inquiries on Credit Report', 'AA', 'LI', 1, 'tblAdverseAction', 26, 'HMLO', 1, 1),
    ('additionalStatement', 'string', 'Additional Statement if information source was other than a consumer reporting agency', 'AA', 'LI', 1, 'tblAdverseAction', 27, 'HMLO', 1, 1),
    ('deliveryMethod', 'string', 'Delivery Method', 'AA', 'LI', 1, 'tblAdverseAction', 28, 'HMLO', 1, 1),
    ('dateActionDelivered', 'Date', 'Date Delivered', 'AA', 'LI', 1, 'tblAdverseAction', 29, 'HMLO', 1, 1),
    ('completionOfLetter', 'string', 'Completion of Letter By', 'AA', 'LI', 1, 'tblAdverseAction', 30, 'HMLO', 1, 1),
    ('NOILoanNumber', 'string', 'Loan Number', 'NOI', 'LI', 1, 'tblAdverseAction', 1, 'HMLO', 1, 1),
    ('noticeForBorrowerName', 'string', 'Notice For (Borrower Name)', 'NOI', 'LI', 1, 'tblAdverseAction', 2, 'HMLO', 1, 1),
    ('informationNeeded', 'string', 'Information Still Needed', 'NOI', 'LI', 1, 'tblAdverseAction', 3, 'HMLO', 1, 1),
    ('daysForCondition', 'string', 'Number of Days to Meet Conditions', 'NOI', 'LI', 1, 'tblAdverseAction', 4, 'HMLO', 1, 1),
    ('lenderLetterContactName', 'string', 'Lender Contact Name on Letter', 'NOI', 'LI', 1, 'tblAdverseAction', 5, 'HMLO', 1, 1),
    ('lenderLetterEmail', 'string', 'Lender Email on Letter', 'NOI', 'LI', 1, 'tblAdverseAction', 6, 'HMLO', 1, 1),
    ('lenderLetterPhone', 'string', 'Lender Phone on Letter', 'NOI', 'LI', 1, 'tblAdverseAction', 7, 'HMLO', 1, 1),
    ('dateNOIAEmailSent', 'Date', 'Date NOIA Email Sent', 'NOI', 'LI', 1, 'tblAdverseAction', 8, 'HMLO', 1, 1);
    

INSERT INTO tblFormFieldsMaster (fieldName, fieldLabel, sectionID, tabName, activeStatus, displayOrder, fileType, BODefaultShow, enableAllLoanPrograms)
VALUES 
    ('dtiTable', 'DTI Table', 'BME', 'LI', 1, 90, 'HMLO', 1, 1),
    ('creditReportUpload', 'Upload Document', 'CRU', 'LI', 1, 1, 'HMLO,loc', 1, 1);

INSERT INTO tblFormFieldsMaster (fieldName, fieldType, fieldLabel, sectionID, tabName, activeStatus, displayOrder, fileType, BODefaultShow, enableAllLoanPrograms)
VALUES 
    ('totalLoanAmount', 'int', 'Total Loan Amount', 'AA', 'LI', 1, 1, 'HMLO', 1, 1),
    ('interestRate', 'int', 'Interest Rate', 'AA', 'LI', 1, 2, 'HMLO', 1, 1),
    ('loanTerm', 'string', 'Loan Term', 'AA', 'LI', 1, 3, 'HMLO', 1, 1),
    ('borrowerFName', 'string', 'First Name', 'AA', 'LI', 1, 4, 'HMLO', 1, 1),
    ('borrowerMName', 'string', 'Middle Name', 'AA', 'LI', 1, 5, 'HMLO', 1, 1),
    ('borrowerLName', 'string', 'Last Name', 'AA', 'LI', 1, 6, 'HMLO', 1, 1),
    ('presentAddress', 'string', 'Borrower Address', 'AA', 'LI', 1, 7, 'HMLO', 1, 1),
    ('entityName', 'string', 'Entity Name', 'AA', 'LI', 1, 8, 'HMLO', 1, 1),
    ('entityAddress', 'string', 'Entity Address', 'AA', 'LI', 1, 10, 'HMLO', 1, 1),
    ('fileId', 'int', 'File ID', 'FI', 'LI', 1, 1, 'HMLO,loc', 1, 1),
    ('fileCreatedDate', 'Date', 'File Created Date', 'FI', 'LI', 1, 2, 'HMLO,loc', 1, 1),
    ('loanNumber', 'string', 'Loan number', 'FI', 'LI', 1, 3, 'HMLO,loc', 1, 1),
    ('leadSource', 'string', 'Lead Source', 'FI', 'LI', 1, 4, 'HMLO,loc', 1, 1),
    ('receivedDate', 'Date', 'Application Received Date', 'FI', 'LI', 1, 5, 'HMLO,loc', 1, 1),
    ('actualClosingDate', 'Date', 'Actual - Closing Date', 'FI', 'LI', 1, 6, 'HMLO,loc', 1, 1),
    ('closedDisposition', 'int', 'Closed Disposition', 'FI', 'LI', 1, 8, 'loc', 1, 1),
    ('borrowerCallBack', 'Date', 'Borrower Call back', 'FI', 'LI', 1, 9, 'HMLO,loc', 1, 1),
    ('lenderCallBack', 'string', 'Lender Call back', 'FI', 'LI', 1, 10, 'loc', 1, 1),
    ('lenderSubmission', 'Date', 'File Submission Date', 'FI', 'LI', 1, 11, 'loc', 1, 1),
    ('HAFADate', 'Date', 'HAFA Date', 'FI', 'LI', 1, 11, 'loc', 1, 1),
    ('welcomeCallDate', 'Date', 'Welcome Call Date', 'FI', 'LI', 1, 12, 'loc', 1, 1),
    ('bankCallCompleted', 'string', 'Bank Call Completed', 'FI', 'LI', 1, 13, 'loc', 1, 1),
    ('maturityDate', 'Date', 'Maturity Date', 'FI', 'LI', 1, 14, 'HMLO', 1, 1),
    ('fundingDate', 'Date', 'Funding Date', 'FI', 'LI', 1, 15, 'HMLO', 1, 1),
    ('eCoaWaiverStatus', 'int', 'ECOA Waiver Status', 'FI', 'LI', 1, 16, 'HMLO,loc', 1, 1),
    ('assignedEmployee', 'string', 'Assigned Employees', 'FI', 'LI', 1, 17, 'HMLO,loc', 1, 1),
    ('appealDate', 'Date', 'Appeal Date', 'FI', 'LI', 1, 18, 'loc', 1, 1),
    ('attorneyReviewDate', 'Date', 'Servicer Attorney Review Date', 'FI', 'LI', 1, 20, 'loc', 1, 1),
    ('salesDate', 'Date', 'Enter Foreclosure Sale Date if provided by bank/attorney/trustee', 'FI', 'LI', 1, 21, 'loc', 1, 1),
    ('priorityLevel', 'string', 'Priority Level', 'FI', 'LI', 1, 22, 'HMLO,loc', 1, 1),
    ('resolutionType', 'int', 'Resolution Type', 'FI', 'LI', 1, 23, 'loc', 1, 1),
    ('trialModReceivedDate', 'Date', 'All Docs/ Date Mod Received', 'FI', 'LI', 1, 24, 'loc', 1, 1),
    ('firstModPaymentAmt', 'string', 'Amount of 1st Mod Payment', 'FI', 'LI', 1, 25, 'loc', 1, 1),
    ('LMRProcessorStatus', 'string', 'File Sub Status', 'FI', 'LI', 1, 27, 'HMLO,loc', 1, 1),
    ('borrowerNotes', 'string', 'Borrower Notes', 'FI', 'LI', 1, 28, 'HMLO', 1, 1),
    ('lenderInternalNotes', 'string', 'Lender Internal Notes', 'FI', 'LI', 1, 29, 'HMLO', 1, 1);