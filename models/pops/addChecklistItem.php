<?php

namespace models\pops;

use models\composite\oBranch\getBranches;
use models\composite\oChecklist\getChecklistForPC;
use models\composite\oFile\getFileInfo;
use models\composite\oFile\getFileInfo\PCStatusInfo;
use models\composite\oPC\getPCInternalServiceType;
use models\composite\oPC\getPCModules;
use models\composite\oPC\getPCServiceType;
use models\constants\gl\glBorrowerType;
use models\constants\gl\glCustomPCNotifySection;
use models\constants\gl\glEntityTypeArray;
use models\constants\gl\glHMLOCreditScoreRange;
use models\constants\gl\glHMLOHouseType;
use models\constants\gl\glRequiredByArray;
use models\constants\gl\gltypeOfHMLOLoanRequesting;
use models\constants\GpropertyTypeNumbArray;
use models\cypher;
use models\lendingwise\tblPCChecklistCategory;
use models\lendingwise\tblPCChecklistNotification;
use models\lendingwise\tblPCChecklistNotificationFileStatus;
use models\standard\Arrays;
use models\standard\Integers;
use models\standard\Strings;
use models\types\strongType;


/**
 *
 */
class addChecklistItem extends strongType
{

    public ?string $thankYouMessage = '';
    public ?string $error = '';

    /**
     * @param array $inArray
     * @return string
     */
    public function getFormHtml(array $inArray = []): string
    {
        $glRequiredByArray = glRequiredByArray::$glRequiredByArray;
        $glEntityTypeArray = glEntityTypeArray::$glEntityTypeArray;
        $gltypeOfHMLOLoanRequesting = gltypeOfHMLOLoanRequesting::$gltypeOfHMLOLoanRequesting;
        $glHMLOHouseType = glHMLOHouseType::$glHMLOHouseType;
        $glHMLOCreditScoreRange = glHMLOCreditScoreRange::$glHMLOCreditScoreRange;
        $glCustomPCNotifySection = glCustomPCNotifySection::$glCustomPCNotifySection;

        $PCID = $inArray['PCID'];
        $glHMLOCreditScoreRange = glHMLOCreditScoreRange::getCreditScoreRange($PCID);
        $formHtml = '';

        $saveUrl = CONST_URL_POPS . 'saveCheckListInfo.php';

        $PCClientTypeInfoArray =   [];
        $checklistInfoArray = null;
        $fileInfoArray = null;
        $checklistId = 0;
        $docName = '';
        $displayOrder = '';
        $PCModuleTypeInfoArray = [];
        $moduleServiceInfo = null;
        $resultChecklistInfoArray = null;
        $resultRequiredBy = null;
        $LMRInfoArray = $filePCInfo = $BranchInfoArray = $BrokerInfoArray = $AssignedStaffArray = $employeeInfo = null;
        $fileMultiContacts = null;
        $SecondaryBrokerInfoArray = null;
        $isCoBorrower = $borrowerEmail = $coBorrowerEmail = $clientName = $coBorrowerName = '';
        $LMRExecutive = '';
        $executiveEmail = '';
        $brokerName = $brokerEmail = '';
        $allowPCToSendCustomText = '';
        $checklistDesc = '';
        $stateArray = Arrays::fetchStates();
        $coBorrowerRelatedReqdoc = '';
        $rehabRelatedReqdoc = '';
        $noCrossCollRelatedReqdoc = '';
        $usCitizenRelatedReqdoc = '';
        $refDocName = '';
        $refDocUrl = '';
        $notifyUsersDis = 'hide';
        $fieldClass = '';

        if (array_key_exists('checklistId', $inArray)) {
            $checklistId = $inArray['checklistId'];
        }

        $MultiMType = $inArray['SMC'];
        $searchModuleType = $inArray['searchModuleType'];
        $inArray['opt'] = 'WMS';
        $fileID = $inArray['fileID'];
        $selServiceType = $inArray['selServiceType'];
        $addOpt = $inArray['addOpt'];
        $tabOpt = $inArray['tabOpt'];
        $executiveId = $inArray['executiveId'];
        $userName = $inArray['userName'];
        $userEmail = $inArray['userEmail'];

        $fileInArr = ['LMRId' => $fileID];
        if ($fileID > 0) {
            $fileInfoArray = getFileInfo::getReport($fileInArr);
        }

        if ($PCID == 0) {
            $fieldClass = "style='display:none'";
        } 

        if (array_key_exists($fileID, $fileInfoArray ?? [])) {
            $LMRInfoKeyArray = $fileInfoArray[$fileID];
            $LMRInfoArray = $LMRInfoKeyArray['LMRInfo'];
            $filePCInfo = $LMRInfoKeyArray['PCInfo'];
            $BranchInfoArray = $LMRInfoKeyArray['BranchInfo'];
            $BrokerInfoArray = $LMRInfoKeyArray['BrokerInfo'];
            $AssignedStaffArray = $LMRInfoKeyArray['AssignedBOStaffInfo'];
            $employeeInfo = $LMRInfoKeyArray['employeeInfo'];
            $fileMultiContacts = $LMRInfoKeyArray['fileContacts']['multiContact'];
            $SecondaryBrokerInfoArray = $LMRInfoKeyArray['SecondaryBrokerInfo'];
        }

        if ($LMRInfoArray) {
            $borrowerName = $LMRInfoArray['borrowerName'];
            $borrowerLName = $LMRInfoArray['borrowerLName'];
            $isCoBorrower = trim($LMRInfoArray['isCoBorrower']);
            $coBorrowerFName = $LMRInfoArray['coBorrowerFName'];
            $coBorrowerLName = $LMRInfoArray['coBorrowerLName'];
            $borrowerEmail = $LMRInfoArray['borrowerEmail'];
            $coBorrowerEmail = $LMRInfoArray['coBorrowerEmail'];
            $clientName = ucwords($borrowerName . ' ' . $borrowerLName);
            $coBorrowerName = ucwords($coBorrowerFName . ' ' . $coBorrowerLName);
        }

        $staffArray = [];
        foreach ($AssignedStaffArray as $item) {
            $staffArray[trim($item['AID'])] = trim($item['AID']);
        }

        if ($BranchInfoArray) {
            $LMRExecutive = trim($BranchInfoArray['LMRExecutive']);
            $executiveEmail = trim($BranchInfoArray['executiveEmail']);
            $executiveId = $BranchInfoArray['executiveId'];
        }

        if ($BrokerInfoArray) {
            $brokerFName = $BrokerInfoArray['firstName'];
            $brokerLName = $BrokerInfoArray['lastName'];
            $brokerName = ucwords($brokerFName . ' ' . $brokerLName);
            $brokerEmail = $BrokerInfoArray['email'];
        }

        $SecondarybrokerName = '';
        $SecondarybrokerEmail = '';
        if ($SecondaryBrokerInfoArray) {
            $SecondarybrokerFName = $SecondaryBrokerInfoArray['firstName'];
            $SecondarybrokerLName = $SecondaryBrokerInfoArray['lastName'];
            $SecondarybrokerName = ucwords($SecondarybrokerFName . ' ' . $SecondarybrokerLName);
            $SecondarybrokerEmail = $SecondaryBrokerInfoArray['email'];
        }

        if ($filePCInfo) {
            $allowPCToSendCustomText = $filePCInfo['allowPCToSendCustomText'];
        }


        $myModulesArray = [];
        $ip['PCID'] = $PCID;
        $ip['keyNeeded'] = 'n';

        $modulesInfoArray = getPCModules::getReport($ip);

        foreach ($modulesInfoArray as $module) {
            $myModulesArray [] = $module['moduleCode'];
        }
        if (in_array('HMLO', $myModulesArray)) {
            $displayModulesText = 'Programs';
        } else {
            $displayModulesText = 'Services';
        }

        $UID = '';
        $userGroup = '';
        if (isset($_REQUEST['userGroup'])) {
            $userGroup = cypher::myDecryption(trim($_REQUEST['userGroup']));
        }
        if (isset($_REQUEST['userNumber'])) {
            $UID = cypher::myDecryption(trim($_REQUEST['userNumber']));
        }

        $resultTransactionType = [];
        $resultPropertyType = [];
        $resultBorrowerOcc = [];
        $resultPropertyState = [];
        $resultEntityType = [];
        $resultEntityState = [];
        $resultBorrowerTypes = [];

        $inArray['moduleCode'] = $MultiMType;
        $PCLMRClientTypeInfoArray = getPCServiceType::getReport($inArray);
        $inArray['moduleCode'] = '';
        if (array_key_exists('checklistId', $inArray)) {
            $resultChecklistInfoArray = getChecklistForPC::getReport($inArray);
        }

        $resultBorrowerCreditScore = null;

        if ($resultChecklistInfoArray) {
            $checklistInfoArray = $resultChecklistInfoArray['checklistInfo'];
            $moduleServiceInfo = $resultChecklistInfoArray['moduleServiceInfo'];
            $resultRequiredBy = $resultChecklistInfoArray['resultRequiredBy'];

            $resultTransactionType = $resultChecklistInfoArray['resultTransactionType'];
            $resultPropertyType = $resultChecklistInfoArray['resultPropertyType'];
            $resultBorrowerOcc = $resultChecklistInfoArray['resultBorrowerOcc'];
            $resultPropertyState = $resultChecklistInfoArray['resultPropertyState'];
            $resultEntityType = $resultChecklistInfoArray['resultEntityType'];
            $resultEntityState = $resultChecklistInfoArray['resultEntityState'];
            $resultBorrowerCreditScore = $resultChecklistInfoArray['resultBorrowerCreditScore'];
            $resultBranchIdList = $resultChecklistInfoArray['resultBranchIds'];
            $resultBorrowerTypes = $resultChecklistInfoArray['resultBorrowerTypes'];
        }
        $resultBranchIdsArray = array_keys($resultBranchIdList ?? []);
        $resultBorrowerTypeArray = array_keys($resultBorrowerTypes ?? []);

        $notifyEmail = null;
        $notifyUsersOnUpload = null;
        $notifyWithAttachment = null;
        $categoryId = null;
        foreach ($checklistInfoArray as $checkList) {
            $docName = htmlspecialchars(trim($checkList['docName']));
            $displayOrder = trim($checkList['displayOrder']);
            $MultiMType = trim($checkList['MultiMType']);
            $checklistDesc = trim($checkList['description']);
            $refDocName = trim($checkList['refDocName']);
            $refDocUrl = trim($checkList['refDocUrl']);
            $categoryId = trim($checkList['categoryId']);

            $coBorrowerRelatedReqdoc = $checkList['coBorrowerRelatedReqdoc'];
            $rehabRelatedReqdoc = $checkList['rehabRelatedReqdoc'];
            $noCrossCollRelatedReqdoc = $checkList['noCrossCollRelatedReqdoc'];
            $usCitizenRelatedReqdoc = $checkList['usCitizenRelatedReqdoc'];
        }

        $moduleTypeArray = explode(',', $MultiMType);

        $tblPCChecklistNotification = tblPCChecklistNotification::GET(['PCID' => $PCID, 'docName' => $docName]) ?? new tblPCChecklistNotification();
        $notifyUsersOnUpload = $tblPCChecklistNotification->notifyUsersOnUpload;
        $notifyEmail = $tblPCChecklistNotification->notifyEmail;
        $notifyWithAttachment = $tblPCChecklistNotification->notifyWithAttachment;
        //$notifyWhenFileStatusContains = $tblPCChecklistNotification->notifyWhenFileStatusContains;
        $tblPCChecklistNotificationFileStatus = tblPCChecklistNotificationFileStatus::GetAll(['checklistNotificationId' => $tblPCChecklistNotification->id]);
        $notifyWhenFileStatusContainsArray = [];
        foreach ($tblPCChecklistNotificationFileStatus as $eachNotificationPrimaryStatus) {
            $notifyWhenFileStatusContainsArray[] = $eachNotificationPrimaryStatus->primaryStatusId;
        }
        /** RequiredBy Start - March 23, 2017**/
        $tempRequiredBy = '';

        foreach ($resultRequiredBy as $r => $resultRequired) {
            if ($r > 0) {
                $tempRequiredBy .= ',' . trim($resultRequired['requiredBy']);
            } else {
                $tempRequiredBy .= trim($resultRequired['requiredBy']);
            }
        }
        $resultRequiredByArray = array_unique(explode(',', $tempRequiredBy));

        /* Checklist Transaction Type */
        $resultTransactionTypeArray = [];
        foreach ($resultTransactionType as $transactionValue) {
            $resultTransactionTypeArray[] = $transactionValue['transactionType'];
        }
        /*End of  Checklist Transaction Type /*

        /* Checklist Property Type */
        $resultPropertyArray = [];
        foreach ($resultPropertyType as $propertyValue) {
            $resultPropertyArray[] = $propertyValue['propertyType'];
        }
        $resultPropertyArray = (array_unique($resultPropertyArray));
        /*End of  Checklist Property Type */

        /* Checklist Borrower Occ*/
        $resultBorrowerOccArray = [];
        foreach ($resultBorrowerOcc as $borrowerOccVal) {
            $resultBorrowerOccArray[] = $borrowerOccVal['borrowerOccupancy'];
        }
        $resultBorrowerOccArray = (array_unique($resultBorrowerOccArray));
        /*End of Checklist Borrower Occ*/

        /* Checklist Property State*/
        $resultPropertyStateArray = [];
        foreach ($resultPropertyState as $propertyStateVal) {
            $resultPropertyStateArray[] = $propertyStateVal['propertyState'];
        }
        $resultPropertyStateArray = (array_unique($resultPropertyStateArray));
        /*End of Checklist Property State*/

        /* Checklist Entity Type*/
        $resultEntityTypeArray = [];
        foreach ($resultEntityType as $entityTypeKeyVal) {
            $resultEntityTypeArray[] = $entityTypeKeyVal['entityType'];
        }
        $resultEntityTypeArray = (array_unique($resultEntityTypeArray));
        /*End of Checklist Entity Type*/

        /* Checklist Entity State*/
        $resultEntityStateArray = [];
        foreach ($resultEntityState as $entityStateVal) {
            $resultEntityStateArray[] = $entityStateVal['entityState'];
        }
        $resultEntityStateArray = (array_unique($resultEntityStateArray));
        /*End of Checklist Entity State*/

        /* */
        $resultBorrowerCreditScoreRangeArray = [];
        foreach ($resultBorrowerCreditScore as $borrowerCreditScoreVal) {
            $resultBorrowerCreditScoreRangeArray[] = $borrowerCreditScoreVal['borrowerCreditScore'];
        }
        $resultBorrowerCreditScoreRangeArray = (array_unique($resultBorrowerCreditScoreRangeArray));

        /* */

        if ($fileID > 0 && $fileID) {
            unset($glRequiredByArray['Lender']);
        }

        /** RequiredBy END**/

        if (count($PCLMRClientTypeInfoArray ?? []) > 0) {
            if (array_key_exists($PCID, $PCLMRClientTypeInfoArray)) {
                $PCClientTypeInfoArray = $PCLMRClientTypeInfoArray[$PCID];
            }
        }

        for ($k = 0; $k < count($PCClientTypeInfoArray ?? []); $k++) {
            $PCModuleTypeInfoArray[$PCClientTypeInfoArray[$k]['moduleCode']] = $PCClientTypeInfoArray[$k];
        }
        $tempKeys = array_keys($PCModuleTypeInfoArray);

        if (count($PCLMRClientTypeInfoArray ?? []) > 0) {
            if (array_key_exists($PCID, $PCLMRClientTypeInfoArray)) {
                $PCClientTypeInfoArray = $PCLMRClientTypeInfoArray[$PCID];
            }
        }

        $PCServiceTypeInternalLoanInfoArray = [];
        $ip['moduleCode'] = $MultiMType;
        if ($PCID > 0) {
            $PCServiceTypeInternalLoanInfoArray = getPCInternalServiceType::getReport($ip);
        }
        $PCClientTypeInfoArray = (array_merge($PCClientTypeInfoArray, $PCServiceTypeInternalLoanInfoArray));
        $PCClientTypeInfoArraySortByModule = Arrays::buildKeyByValue($PCClientTypeInfoArray, 'moduleCode');

        $PCClientTypeInfoArray = [];
        foreach ($PCClientTypeInfoArraySortByModule as $ModuleCodeArr) {
            $PCClientTypeInfoArray = array_merge($PCClientTypeInfoArray, $ModuleCodeArr);
        }

        $notifyUsersOn = '';
        $notifyUsersVal = '';
        if (in_array($PCID, $glCustomPCNotifySection)) {
            $notifyUsersOn = 'checked="checked" ';
            $notifyUsersVal = 1;
            $notifyUsersDis = '';
        }

        $branchListArray = getBranches::getReport(['PCID' => $PCID, 'stats' => 1]);
        $branchList = $branchListArray['branchList'];

        $PCStatusInfo = PCStatusInfo::getReport('',
            $PCID,
            explode(',', $MultiMType)
        );

        $tblPCChecklistCategory = tblPCChecklistCategory::getPCCategories($PCID,1);
        $formHtml .= <<<EOT
<script>
/* Show and Hide PC Checklist Sevices Div's */
function showAndHideModuleServiceTypesForCheckList(t, val, opt) {
	if (opt === 'show') {
		document.getElementById("div_"+val).style.display = 'block';
	} else {
		document.getElementById("div_"+val).style.display = 'none';
	}
}
</script>
<script type="text/javascript" src="/assets/js/popup.js"></script>
 <form name="checklistForm" class="form" id="checklistForm" method="POST"   enctype="multipart/form-data" action="$saveUrl" >
EOT;

        $refDocNameHtml = '';
        if ($refDocName) {
            $refDocNameUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($refDocName) . '&fd=' . cypher::myEncryption(CONST_PATH_PC_UP_DOC . $PCID) . '&opt=enc';
            $checklistIdTxt = cypher::myEncryption($checklistId);
            $reqDocType = cypher::myEncryption('PCL');
            $refDocNameHtml = '<div class="form-group mt-2 refDocNameHtmlDiv">
<a 
    class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon tooltipClass" 
    style="text-decoration:none;"
    rel="nofollow" 
    target="_blank" 
    href="' . $refDocNameUrl . '"
    title="Click to view File Doc Info"><i class="fa fa-eye tooltipClass" ></i></a>
<a 
    class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon tooltipClass" 
    style="text-decoration:none;"  
    onclick="addChecklistItemControls.deleteRefDoc(\'' . $checklistIdTxt . '\',\'' . $reqDocType . '\')"  
    title="Click to Delete"><i class="tooltipClass flaticon2-trash" ></i></a>
</div>';
        }

        $formHtml .= <<<EOT
	<input type="hidden" name="PCID" id="PCID" value="$PCID">
	<input type="hidden" name="checklistId" id="checklistId" value="$checklistId">
	<input type="hidden" name="exC" id="exC" value="$docName">
	<input type="hidden" name="searchModuleType" id="searchModuleType" value="$searchModuleType">
	<input type="hidden" name="UID" id="UID" value="$UID">
	<input type="hidden" name="userGroup" id="userGroup" value="$userGroup">
	<input type="hidden" name="fileID" id="fileID" value="$fileID">
	<input type="hidden" name="serviceType" id="serviceType" value="$selServiceType">
	<input type="hidden" name="tabOpt" id="tabOpt" value="$tabOpt">
	<input type="hidden" name="executiveId" id="executiveId" value="$executiveId">
    <input type="hidden" name="SecondarybrokerName" id="SecondarybrokerName" value="$SecondarybrokerName">
	<input type="hidden" name="brokerName" id="brokerName" value="$brokerName">
	<input type="hidden" name="clientName" id="clientName" value="$clientName">
	<input type="hidden" name="coBorrowerName" id="coBorrowerName" value="$coBorrowerName">
	<input type="hidden" name="LMRExecutive" id="LMRExecutive" value="$LMRExecutive">
	<input type="hidden" name="execName" id="execName" value="$userName">
	<input type="hidden" name="userEmail" id="userEmail" value="$userEmail">
	<input type="hidden" name="LMRId" id="LMRId" value="$fileID">
	<input type="hidden" name="allowPCToSendCustomText" id="allowPCToSendCustomText" value="$allowPCToSendCustomText">
	<input type="hidden" name="contact_ids" id="contact_ids" value="" >

	 <div class="card-body">
            <div class="form-group">
                     <label class="font-weight-bolder">Please Select Category:</label>
                     <select name="categoryId" 
                             id="categoryId" 
                             class="form-control chzn-select mandatory"
                             data-placeholder="Please Select Category">
                        <option value=""></option>
EOT;
        foreach ($tblPCChecklistCategory as $category) {
            $encryptedId = cypher::myEncryption($category->id);
            $isSelected = Arrays::isSelected($categoryId, $category->id);
            $formHtml .= <<<EOT
                    <option value="{$encryptedId}" {$isSelected}>{$category->categoryName}</option>
EOT;
        }
        $formHtml .= <<<EOT
                     </select>      
            </div>
            
            
            <div class="form-group">
                     <label class="font-weight-bolder">Enter Required Docs Name:</label>
                     <input  value="$docName" type="text"  name="checklistItem" id="checklistItem" class="form-control mandatory">
            </div>
            <div class="form-group">
                    <label class="font-weight-bolder">Description:</label>
                    <textarea class="form-control" rows="3" cols="200" name="checklistDesc" id="checklistDesc">$checklistDesc</textarea>
            </div>
            <div class="form-group" $fieldClass>
                    <label class="font-weight-bolder">Reference Document/Template:
                    <a style="text-decoration:none" class="fa fa-info-circle fa-lg tip-bottom icon-dark-grey" 
                    data-toggle="tooltip"
                    data-html="true"
                    data-theme="dark"
                    title="You can upload a reference file or URL link that allows users to download. It will display next to the required doc name.">&nbsp;</a></label>
                    <input type="FILE"  class="filelevelDocChooser form-control" name="refDocName" id="refDocFile" value="$refDocName"  />
                    <input type="hidden" name="uploadedRefDocName" id="uploadedRefDocName"  value="$refDocName"> 
                     $refDocNameHtml
            </div>
            <div class="form-group" $fieldClass>
                    <label class="font-weight-bolder">Ref URL:
                    <a style="text-decoration:none" class="fa fa-info-circle fa-lg tip-bottom icon-dark-grey"
                    data-toggle="tooltip"
                    data-html="true"
                    data-theme="dark"
                     title="You can upload a reference file or URL link that allows users to download. It will display next to the required doc name."></a></label>
                    <input class="form-control" type="text"  name="refDocUrl" id="refDocUrl" value="$refDocUrl"> 
            </div>
EOT;
        if ($addOpt != 'FCL') {
            if ($checklistId) {
                $formHtml .= <<<EOT
            	<input type="hidden" name="dispOrder" id="dispOrder" value="$displayOrder" maxlength="3">
EOT;
            } else {
                $formHtml .= <<<EOT
                     <div class="form-group" >
                         <label class="font-weight-bolder">Display Order:</label>
                         <input class="form-control" type="text" name="dispOrder" id="dispOrder" value="$displayOrder" maxlength="3">
                     </div>
EOT;
            }
        }
        $formHtml .= <<<EOT
  <div class="form-group">
             <label class="font-weight-bolder">Display & make available for other users:</label>
             <select title="" name="requiredBy[]" id="requiredBy" class="form-control chzn-select" multiple="" style="width:100%;">
EOT;
        $resultRequiredByArray = (array_filter($resultRequiredByArray));
        foreach ($glRequiredByArray as $reqByKey => $reqByValue) {
            $selOpt = Arrays::isSelectedArray($resultRequiredByArray, $reqByKey);
            if (count($resultRequiredByArray ?? []) == 0) {
                $selOpt = 'selected';
            }
            if ($reqByValue != 'Back Office') {
                $formHtml .= <<<EOT
                    <option $selOpt value="$reqByKey">$reqByValue</option>
EOT;
            }
        }
        $formHtml .= <<<EOT
              </select>
              <small class="text-warning">Note: All the Required Docs list will be visible to the backoffice users.</small>
  </div>
EOT;
        if ($addOpt == 'FCL') {
            $tempChkFldName = 'services_' . $searchModuleType . '[]';
            $formHtml .= <<<EOT
	<input type="hidden" name="moduleName[]" id="moduleName" value="$searchModuleType"/>
	<input type="hidden" name="$tempChkFldName" id = "services_$tempChkFldName" value="$selServiceType"/>
EOT;
        } else {
            $formHtml .= <<<EOT
	      <div class="form-group" >
         <label class="font-weight-bolder">File Type:</label>
            <input type="hidden" name="moduleName[]" id="moduleName" value="$MultiMType"/>
EOT;
            for ($j = 0; $j < count($tempKeys ?? []); $j++) {
                $moduleCode = $tempKeys[$j];
                $moduleName = $PCModuleTypeInfoArray[$moduleCode]['moduleName'];

                if ($MultiMType == $moduleCode) {
                    $formHtml .= <<<EOT
			<p class="text-dark">$moduleName</p>
EOT;
                }
            }
            $formHtml .= <<<EOT
</div>
		     
EOT;
        }
        if ($addOpt != 'FCL') {
            $formHtml .= <<<EOT
            <div class="form-group">    
EOT;
            if (in_array('HMLO', $myModulesArray)) {
                $formHtml .= <<<EOT
            <label class="font-weight-bolder">Loan Programs:</label>
EOT;
            } else {
                $formHtml .= <<<EOT
            <label class="font-weight-bolder">Service Type:</label>
EOT;
            }

            $tempCodeVal = '';
            $tempModuleCode = '';
            $j = 0;
            $countOfServices = array_count_values(array_column($PCClientTypeInfoArray, 'moduleCode')) ?? [];

            foreach ($PCClientTypeInfoArray as $n => $pcType) {
                $moduleCodeVal = $pcType['moduleCode'];
                $serviceTypeCodeVal = $pcType['LMRClientType'];
                $serviceTypeValue = $pcType['serviceType'];
                $moduleNameVal = $pcType['moduleName'];
                $internalLoanProgram = $pcType['internalLoanProgram'];

                if (in_array($moduleCodeVal, $moduleTypeArray)) {
                    $displayModuleServicesDiv = 'display: block';
                } else {
                    $displayModuleServicesDiv = 'display: none';
                }
                $tempModuleCode = $moduleCodeVal . '_Cnt';
                $serviceCount = 0;
                if(array_key_exists($moduleCodeVal, $countOfServices)) {
                    $serviceCount = $countOfServices[$moduleCodeVal];
                }

                $serviceTypeArray = [];
                if (count($moduleServiceInfo ?? []) > 0) {
                    if (array_key_exists($moduleCodeVal, $moduleServiceInfo)) {
                        $serviceTypeArray = $moduleServiceInfo[$moduleCodeVal];
                    }
                }
                $chk = Strings::isKeyChecked($serviceTypeArray, 'serviceType', $serviceTypeCodeVal);
                if ($selServiceType && $selServiceType == $serviceTypeCodeVal) {
                    $chk = 'checked';
                }

                if ($moduleCodeVal != $tempCodeVal) {
                    if ($j == 0) {
                        $formHtml .= <<<EOT
                            <input type="hidden" name="$tempModuleCode" id="$tempModuleCode" value="$serviceCount"/>
EOT;
                    }
                    if ($j > 0) {
                        $formHtml .= <<<EOT
		</table>
		</div>
		</div>
		<input type="hidden" name="$tempModuleCode" id="$tempModuleCode" value="$serviceCount"/>
EOT;
                    }
                    $formHtml .= <<<EOT
<div class="row" id = "div_$moduleCodeVal" style="$displayModuleServicesDiv">
<div class="col-md-12">
	<table style="border:0;width:100%">
	<tr class="even">
		<td nowrap colspan="3">
			<b>$moduleNameVal</b>		
	 </td>
	</tr>
<tr><td nowrap colspan="3"><label for="module_$moduleCodeVal" class="font-weight-bold"><input class="newCheck" type="checkbox" name="checkFields"  id="module_$moduleCodeVal" onclick="clickToAllFields(this.checked,'$moduleCodeVal');"/> Click to select all $moduleNameVal $displayModulesText</label></td></tr>
EOT;
                    $j = 0;
                }

                $tempCodeVal = $moduleCodeVal;
                $tempVal = $moduleCodeVal . '_' . $j;
                if (($n % 3 == 0) || ($n == 0)) {
                    $formHtml .= <<<EOT
	<tr>
EOT;
                }
                $internalLoanTxt = '';
                if ($internalLoanProgram == 1) {
                    $internalLoanTxt = '<span style="color: red; font-size: small !important;" title="Internale Loan Program">*Internal</span>';
                }
                $tempChkFldName = 'services_' . $moduleCodeVal . '[]';
                $formHtml .= <<<EOT
		<td>
			<label for="services_$tempVal"><input class="newCheck" type="checkbox" name="$tempChkFldName" id = "services_$tempVal" value="$serviceTypeCodeVal"   $chk/><span>&nbsp;</span>$serviceTypeValue $internalLoanTxt </label>
			</td> 
EOT;
                $j++;
                if ((($n + 1) % 3 == 0) || (($n + 1) == count($PCClientTypeInfoArray ?? []))) {
                    $formHtml .= <<<EOT
	</tr>
EOT;
                }
            }
            if ($j > 0 && $n >= 0) {
                $formHtml .= <<<EOT
		</table>
		</div>
		<input type="hidden" name="$tempModuleCode" id ="$tempModuleCode"  value="$j"/>
EOT;
            }

            $formHtml .= <<<EOT
          </td>
        </tr>
EOT;
        }


        if ($addOpt != 'FCL') {
            $formHtml .= <<<EOT
            </div>
            </div>
            <div class="form-group ">
            <label class="text-danger">If any conditions selected below, the required doc in the loan file will display respectively. If unselected, the system will ignore the condition and only follow the mandatory logic above for loan program.**</label>
        </div>
        
               <div class="form-group ">
       	 			<label class="font-weight-bolder">Branch:</label>
     <select  title="- Select Branch -"  name="branchList[]"  id= "branchList" class="chzn-selectShowSelectAll form-control" multiple="" data-live-search="true"  data-actions-box="true"  style="width:250px;">
EOT;
            foreach ($branchList as $eachBranch) {
                $branchName = trim($eachBranch['LMRExecutive']) . ' (' . $eachBranch['executiveEmail'] . ')';
                $branchId = trim($eachBranch['executiveId']);
                $sOpt = Arrays::isSelectedArray($resultBranchIdsArray, $eachBranch['executiveId']);
                $formHtml .= <<<EOT
		<option value="$branchId" $sOpt>$branchName</option>
EOT;
            }
            $formHtml .= <<<EOT
						</select>
       </div>
       
       
       <div class="form-group ">
       	 			<label class="font-weight-bolder">Transaction Type:</label>
     <select  title="- Select Transaction Type -"  name="transactionType[]"  id= "transactionType" class="chzn-selectShowSelectAll form-control" multiple="" data-live-search="true"  data-actions-box="true"  style="width:250px;">
EOT;

            for ($i = 0; $i < count($gltypeOfHMLOLoanRequesting ?? []); $i++) {
                $typeOfLoan = trim($gltypeOfHMLOLoanRequesting[$i]);
                $sOpt = Arrays::isSelectedArray($resultTransactionTypeArray, $typeOfLoan);
                $formHtml .= <<<EOT
		<option value="$typeOfLoan" $sOpt>$typeOfLoan</option>
EOT;
            }
            $formHtml .= <<<EOT
						</select>
       </div>
EOT;
            $formHtml .= <<<EOT
<div class="form-group ">
       	 			<label class="font-weight-bolder">Borrower Occupancy</label>
						<select title="- Select Occupancy -" name="borrowerOccupancy[]" id="borrowerOccupancy" class="chzn-selectShowSelectAll form-control"  	 data-live-search="true"  data-actions-box="true"   multiple="" style="width:250px;">
EOT;

            for ($i = 0; $i < count($glHMLOHouseType ?? []); $i++) {
                $glOccupancy = trim($glHMLOHouseType[$i]);
                $sOpt = Arrays::isSelectedArray($resultBorrowerOccArray, $glOccupancy);
                $formHtml .= <<<EOT
								<option value="$glOccupancy" $sOpt>$glOccupancy</option>
EOT;
            }
            $formHtml .= <<<EOT
						</select></div>
	<div class="form-group ">
       	 			<label class="font-weight-bolder">Property Type:</label>
						<select title="- Select Property Type -" name="propertyType[]" id= "propertyType" class="chzn-selectShowSelectAll form-control" multiple="" data-live-search="true"  data-actions-box="true"  style="width:250px;">
EOT;
            $propertyTypeKeyArray = [];
            if (count(GpropertyTypeNumbArray::$GpropertyTypeNumbArray ?? []) > 0) {
                $propertyTypeKeyArray = array_keys(GpropertyTypeNumbArray::$GpropertyTypeNumbArray);
            }

            for ($o = 0; $o < count($propertyTypeKeyArray ?? []); $o++) {
                $propKey = trim($propertyTypeKeyArray[$o]);
                $selOpt = Arrays::isSelectedArray($resultPropertyArray, $propKey);
                $propVal = trim(GpropertyTypeNumbArray::$GpropertyTypeNumbArray[$propertyTypeKeyArray[$o]]);
                if ($propertyTypeKeyArray[$o] != '1000' && $propertyTypeKeyArray[$o] != '1001') {  //exclude separators
                    $formHtml .= <<<EOT
								<option value="$propKey" $selOpt> $propVal</option>
EOT;
                }
            }
            $formHtml .= <<<EOT
						</select>
						</div>
<div class="form-group ">
       	 			<label class="font-weight-bolder">Property State:</label>
       	 			<select title="- Select State -" name="propertyState[]" id="propertyState" 	 class="chzn-selectShowSelectAll form-control" multiple="" 	 data-live-search="true"  data-actions-box="true"  style="width:250px;">
EOT;
            foreach ($stateArray as $s => $state) {
                $stateCode = trim($state['stateCode']);
                $sOpt = Arrays::isSelectedArray($resultPropertyStateArray, trim($state['stateCode']));
                $stateName = trim($state['stateName']);
                $formHtml .= <<<EOT
								<option value="$stateCode" $sOpt> $stateName</option>
EOT;
            }
            $formHtml .= <<<EOT
                        </select>
                        </div>
                        
						<div class="form-group ">
                                                <label class="font-weight-bolder">Borrower Type:</label>
                                                    <select title="- Select Borrower Type -"  
                                                            name="borrowerType[]" 
                                                            id="borrowerType" 
                                                            class="chzn-selectShowSelectAll form-control" 
                                                            multiple=""
                                                            data-live-search="true"
                                                            data-actions-box="true"  style="width:250px;" >
EOT;
            foreach (glBorrowerType::$glBorrowerTypeArray as $EntityType) {
                $sOpt = '';
                if (in_array($EntityType, $resultBorrowerTypeArray)) {
                    $sOpt = 'selected';
                }
                $formHtml .= <<<EOT
                                                                    <option value="$EntityType" $sOpt>$EntityType</option>
                                                            EOT;
            }
            $formHtml .= <<<EOT
                                                    </select>
                            </div>

						<div class="form-group ">
       	 			<label class="font-weight-bolder">Entity Type:</label>
						<select title="- Select Entity Type -"  name="entityType[]" id="entityType" 	 class="chzn-selectShowSelectAll form-control" multiple="" 	 data-live-search="true"  data-actions-box="true"  style="width:250px;" >
EOT;
            foreach ($glEntityTypeArray as $i => $glEntityType) {
                $sOpt = '';
                if (in_array($glEntityType, $resultEntityTypeArray)) {
                    $sOpt = 'selected';
                }
                $formHtml .= <<<EOT
                                    <option value="$glEntityType" $sOpt>$glEntityType</option>
EOT;
            }
            $formHtml .= <<<EOT
                            </select>
</div>
						<div class="form-group ">
       	 			<label class="font-weight-bolder">Entity State:</label>
					<select title="- Select State -" name="entityState[]" id="entityState"  class="chzn-selectShowSelectAll form-control" multiple=""	 data-live-search="true"  data-actions-box="true"  style="width:250px;">
EOT;

            foreach ($stateArray as $s => $state) {
                $stateCode = trim($state['stateCode']);
                $sOpt = Arrays::isSelectedArray($resultEntityStateArray, trim($state['stateCode']));
                $stateName = trim($state['stateName']);
                $formHtml .= <<<EOT
								<option value="$stateCode" $sOpt> $stateName</option>
EOT;
            }
            $formHtml .= <<<EOT
						</select>
					</div>
					
					<div class="form-group ">
       	 			<label class="font-weight-bolder">Credit Score Range:</label>	
			<select title="- Select Credit Score Range -" name="borrowerCreditScoreRange[]" id="borrowerCreditScoreRange" 	 class="chzn-selectShowSelectAll form-control" multiple="" 	 data-live-search="true"  data-actions-box="true"  style="width:250px;">
EOT;
            foreach ($glHMLOCreditScoreRange as $glCreditScoreRange) {
                $sOpt = Arrays::isSelectedArray($resultBorrowerCreditScoreRangeArray, trim($glCreditScoreRange));
                $formHtml .= <<<EOT
	<option value="$glCreditScoreRange" $sOpt>$glCreditScoreRange</option>
EOT;
            }
            $formHtml .= <<<EOT
						</select>
						</div>
EOT;

            $coBorrowerRelatedReqdocValFalse = '';
            $rehabRelatedReqdocValFalse = '';
            $noCrossCollRelatedReqdocValFalse = '';
            $usCitizenRelatedReqdocValFalse = '';
            $coBorrowerRelatedReqdocValTrue = '';
            $rehabRelatedReqdocValTrue = '';
            $noCrossCollRelatedReqdocValTrue = '';
            $usCitizenRelatedReqdocValTrue = '';
            $notifyUsersOnUploadTrue = '';
            $notifyUsersOnUploadFalse = '';
            $notifyEmailDiv = 'hide';

            ($coBorrowerRelatedReqdoc) ? ($coBorrowerRelatedReqdocValTrue = 'checked') : ($coBorrowerRelatedReqdocValFalse = 'checked');
            ($rehabRelatedReqdoc) ? ($rehabRelatedReqdocValTrue = 'checked') : ($rehabRelatedReqdocValFalse = 'checked');
            ($noCrossCollRelatedReqdoc) ? ($noCrossCollRelatedReqdocValTrue = 'checked') : ($noCrossCollRelatedReqdocValFalse = 'checked');
            ($usCitizenRelatedReqdoc) ? ($usCitizenRelatedReqdocValTrue = 'checked') : ($usCitizenRelatedReqdocValFalse = 'checked');
            if ($notifyUsersOnUpload) {
                $notifyUsersOnUploadTrue = 'checked';
                $notifyEmailDiv = '';
            } else {
                $notifyUsersOnUploadFalse = 'checked';
            }

            $notifyWithAttachmentTrue = '';
            $notifyWithAttachmentFalse = '';
            if ($notifyWithAttachment) {
                $notifyWithAttachmentTrue = 'checked';
            } else {
                $notifyWithAttachmentFalse = 'checked';
            }
            // ($notifyUsersOnUpload) ? ($notifyUsersOnUploadTrue = 'checked' && $notifyEmailDiv = '') : ($notifyUsersOnUploadFalse = 'checked' && $notifyEmailDiv = 'hide');

            $formHtml .= <<<EOT
        <div class="form-group row align-items-center">
            <label class="col-lg-6 font-weight-bolder">Show Only If Co-Borrower Available:</label>
            <div class="col-lg-6">
                <div class="radio-inline">
                           <label class="font-weight-bolder radio radio-solid"  for="coBorrowerRelatedReqdoc1" >
                             <input type="radio" id="coBorrowerRelatedReqdoc1" name="coBorrowerRelatedReqdoc" $coBorrowerRelatedReqdocValTrue  class="coBorrowerAvailable" value="1" >
                                 <span></span> Yes
                           </label>
                          <label class="font-weight-bolder radio radio-solid" for="coBorrowerRelatedReqdoc2">
                            <input type="radio" id="coBorrowerRelatedReqdoc2" name="coBorrowerRelatedReqdoc" $coBorrowerRelatedReqdocValFalse class="coBorrowerAvailable" value="0" >
                                <span></span> Not Applicable
                          </label>                         
                  </div>
              </div>
        </div>

        <div class="form-group row align-items-center">
        <label class="col-lg-6 font-weight-bolder">Show Only If Rehab Construction:</label>
        <div class="col-lg-6">
            <div class="radio-inline">
                   <label class="font-weight-bolder radio radio-solid"  for="rehabRelatedReqdoc1" >
                     <input type="radio" id="rehabRelatedReqdoc1" name="rehabRelatedReqdoc" $rehabRelatedReqdocValTrue  class="rehabRelatedReqdoc" value="1" >
                         <span></span> Yes
                   </label>
                  <label class="font-weight-bolder radio radio-solid" for="rehabRelatedReqdoc2">
                   <input type="radio" id="rehabRelatedReqdoc2" name="rehabRelatedReqdoc" $rehabRelatedReqdocValFalse class="rehabRelatedReqdoc" value="0" >
                        <span></span> Not Applicable
                  </label>                                  
            </div>
          </div>
        </div>
               
            <div class="form-group row align-items-center">
                <label class="col-lg-6 font-weight-bolder">Show Only If Cross Collateralize:</label>
                <div class="col-lg-6">
                    <div class="radio-inline">
                           <label class="font-weight-bolder radio radio-solid"  for="noCrossCollRelatedReqdoc1" >
                             <input type="radio" id="noCrossCollRelatedReqdoc1" name="noCrossCollRelatedReqdoc" $noCrossCollRelatedReqdocValTrue 
                    class="noCrossCollRelatedReqdoc" value="1" >
                                 <span></span>
                                 Yes
                              </label>
                          <label class="font-weight-bolder radio radio-solid" for="noCrossCollRelatedReqdoc2">
                           <input type="radio" id="noCrossCollRelatedReqdoc2" name="noCrossCollRelatedReqdoc" $noCrossCollRelatedReqdocValFalse class="noCrossCollRelatedReqdoc" value="0" >
                                <span></span>
                                Not Applicable
                             </label>                                  
                    </div>
                </div>
            </div>     
            <div class="form-group row align-items-center">
                <label class="col-lg-6 font-weight-bolder">Show Only If Not U.S. Citizen:</label>
                <div class="col-lg-6">
                    <div class="radio-inline">
                           <label class="font-weight-bolder radio radio-solid"  for="usCitizenRelatedReqdoc1" >
                             <input type="radio" id="usCitizenRelatedReqdoc1" name="usCitizenRelatedReqdoc" $usCitizenRelatedReqdocValTrue 
                    class="usCitizenRelatedReqdoc" value="1" >
                                 <span></span>
                                 Yes
                              </label>
                          <label class="font-weight-bolder radio radio-solid" for="usCitizenRelatedReqdoc2">
                           <input type="radio" id="usCitizenRelatedReqdoc2" name="usCitizenRelatedReqdoc" $usCitizenRelatedReqdocValFalse class="usCitizenRelatedReqdoc" value="0" >
                                <span></span>
                                Not Applicable
                             </label>                                  
                    </div>
                </div>
            </div>
            
            <div class="form-group row align-items-center">
                <label class="col-lg-6 font-weight-bolder">Do you want to email anyone when a documents is uploaded to this container:</label>
                <div class="col-lg-6">
                    <div class="radio-inline">
                           <label class="font-weight-bolder radio radio-solid"  for="notifyUsersOnUploadYes" >
                             <input type="radio" 
                             id="notifyUsersOnUploadYes" 
                             name="notifyUsersOnUpload"
                             onclick="$('.notifyEmailDiv').removeClass('hide');"
                                $notifyUsersOnUploadTrue 
                                    value="1" >
                                 <span></span>
                                 Yes
                              </label>
                          <label class="font-weight-bolder radio radio-solid" for="notifyUsersOnUploadNo">
                           <input type="radio" 
                           id="notifyUsersOnUploadNo" 
                           name="notifyUsersOnUpload" 
                           onclick="$('.notifyEmailDiv').addClass('hide');"
                            $notifyUsersOnUploadFalse
                             value="0" >
                                <span></span>
                                No
                             </label>                                  
                    </div>
                </div>
            </div>
             <div class="form-group row align-items-center notifyEmailDiv $notifyEmailDiv" >
                <label class="col-lg-6 font-weight-bolder">Enter Emails here. Separate with ; to add more than one: </label>
                <div class="col-lg-6">
                       <textarea class="form-control" 
                       name="notifyEmail" 
                       id="notifyEmail" >$notifyEmail</textarea>
                </div>
            </div>          
                <div class="form-group row align-items-center notifyEmailDiv $notifyEmailDiv" >
                <label class="col-lg-6 font-weight-bolder">Send Email When File Status is: </label>
                <div class="col-lg-6">
                             <select title=""
                              name="notifyWhenFileStatusContains[]"
                              id="notifyWhenFileStatusContains" 
                              class="form-control chzn-select" 
                              multiple="" 
                              data-placeholder="Please Select Primary Status"
                              style="width:100%;">
EOT;
            foreach ($PCStatusInfo as $eachPrimaryStatus) {
                $primaryStatus = $eachPrimaryStatus['primaryStatus'];
                $PSID = $eachPrimaryStatus['PSID'];
                $sel = Arrays::isSelectedArray($notifyWhenFileStatusContainsArray, $PSID);
                $formHtml .= <<<EOT
          <option value="$PSID" $sel>$primaryStatus</option>
EOT;
            }
            $formHtml .= <<<EOT
                            </select>
                </div>
                </div>
            
            
             <div class="form-group row align-items-center notifyEmailDiv $notifyEmailDiv" >
                <label class="col-lg-6 font-weight-bolder">Attach Uploaded Document To Mail?</label>
                <div class="col-lg-6">
                    <div class="radio-inline">
                       <label class="font-weight-bolder radio radio-solid"  for="notifyWithAttachmentYes" >
                         <input type="radio" 
                                id="notifyWithAttachmentYes" 
                                name="notifyWithAttachment"
                                $notifyWithAttachmentTrue
                                value="1">
                             <span></span>
                             Yes
                       </label>
                       <label class="font-weight-bolder radio radio-solid" for="notifyWithAttachmentNo">
                           <input type="radio" 
                                  id="notifyWithAttachmentNo" 
                                  name="notifyWithAttachment" 
                                  $notifyWithAttachmentFalse
                                  value="0">
                                <span></span>
                                No
                       </label>                                  
                    </div>
                </div>
            </div>
EOT;
        }
        /** Notifying Section Start. **/
        if ($addOpt == 'FCL') {

            $formHtml .= <<<EOT
			<div class="separator separator-dashed my-5"></div>
  <div class="form-group row ">
    <label class="col-lg-4 font-weight-bolder font-weight-bolder">Do you want to notify anyone?</label>
									   <div class="col-lg-6">

<span class="switch switch-icon">
	<label>
     <input class="form-control " id="notifyUsersDiv" type="checkbox" $notifyUsersOn value="$notifyUsersVal"  onchange="toggleSwitch('notifyUsersDiv','notifyUsers','1','0' ); showAndHideNotifyUsers();"  />
                                           <input type="hidden" name="notifyUsers" id="notifyUsers" value="0">
                                           <span></span>
	</label>
</span>
 </div>
					  </div>
  <div class="form-group $notifyUsersDis notifyUsers row align-items-center">
									   <label class="col-lg-12 font-weight-bolder bg-gray-200 text-center font-weight-bolder py-2">Select the user(s) to notify reg. this update:</label>
					  </div>
EOT;

            /* Borrower Info */
            if (trim($borrowerEmail)) {
                if (Integers::checkEmailIsDummy($borrowerEmail) == '0') {
                    $formHtml .= <<<EOT
<div class="form-group sendEmailForBor $notifyUsersDis  notifyUsers  row align-items-center px-10"  >
  <label class="col-lg-4 font-weight-bolder font-weight-bolder">Borrower:</label>
    <div class="checkbox-list col-lg-6">
        <label for="borrowerEmail1" class="checkbox">
         <input type="checkbox" name="borrowerEmail" id="borrowerEmail1" value="$borrowerEmail"/>
         <span></span>
            $clientName
        </label>
    </div>
</div>	 
EOT;
                }
            }

            /* Co Borrower Info */
            if (trim($coBorrowerEmail) && $isCoBorrower == 1) {
                if (Integers::checkEmailIsDummy($borrowerEmail) == '0') {
                    $formHtml .= <<<EOT
	           <div class="form-group $notifyUsersDis  notifyUsers  row align-items-center px-10">
                    <label class="col-lg-4 font-weight-bolder font-weight-bolder">Co-Borrower:</label>
                    <div class="checkbox-list col-lg-6">
                            <label for="coBorrowerEmail1" class="checkbox">
                             <input type="checkbox" name="coBorrowerEmail" id="coBorrowerEmail1" value="$coBorrowerEmail"/>
                             <span></span>
                             $coBorrowerName
                            </label>
                            </div>
                    </div>
EOT;
                }
            }
            /* Agent/Broker and Branch Info */
            if ($brokerEmail && strpos($brokerEmail, '@dummyAgentemail.com') === false) {
                $formHtml .= <<<EOT
<div class="form-group $notifyUsersDis  notifyUsers  row align-items-center px-10" id="agentDiv1">
                    <label class="col-lg-4 font-weight-bolder font-weight-bolder">Broker</label>
            <div class="checkbox-list col-lg-6">
                    <label for="brokerEmail" class="checkbox">
                        <input type="checkbox" class="newCheck" type="checkbox" name="brokerEmail" id="brokerEmail" value="$brokerEmail"/>
                        <span></span>
                        $brokerName
                    </label>
            </div>
      </div>
EOT;
            }
            if ($SecondarybrokerEmail) {
                $formHtml .= <<<EOT
          <div class="form-group $notifyUsersDis  notifyUsers  row align-items-center px-10" id="SecondarybrokerEmail">
                    <label class="col-lg-4 font-weight-bolder font-weight-bolder">Loan Officer:</label>
            <div class="checkbox-list col-lg-6">
                    <label for="SecondarybrokerEmail" class="checkbox">
                        <input type="checkbox" class="newCheck" type="checkbox" name="SecondarybrokerEmail" id="SecondarybrokerEmail" value="$SecondarybrokerEmail"/>
                        <span></span>
                        $SecondarybrokerName
                    </label>                
            </div>
    </div>	  
EOT;
            }
            $formHtml .= <<<EOT
			<div  class="form-group $notifyUsersDis  notifyUsers  row align-items-center px-10"  id="branchDiv1" >
                    <label  class="col-lg-4 font-weight-bolder font-weight-bolder">Branch:</label>
            <div class="checkbox-list col-lg-6">
                    <label for="branchEmail" class="checkbox">
                        <input type="checkbox" class="newCheck" type="checkbox" name="branchEmail" id="branchEmail" value="$executiveEmail"/>
                        <span></span>
                        $LMRExecutive
                    </label>                
            </div>
    </div>	  
EOT;
            /* Assigned Employee Info */
            $empCnt = count($employeeInfo ?? []);
            if ($employeeInfo) {
                $formHtml .= <<<EOT
     	  <div class="separator $notifyUsersDis notifyUsers separator-dashed my-5"></div>
		<input type="hidden" name="empCnt" id="empCnt" value="$empCnt" >
		 <div class="form-group $notifyUsersDis notifyUsers row align-items-center px-10">
        <label class="font-weight-bolder mb-4">Employee(s):</label>
           <select  title="- Select Employee(s) -"  name="employeeIds[]"  id= "employeeIds" class="chzn-selectShowSelectAll form-control" multiple="" data-live-search="true"  data-size="5" data-actions-box="true"  style="width:250px;">
EOT;
                $e1 = 0;
                foreach ($employeeInfo as $item) {
                    $employeeName = trim($item['processorName']);
                    $employeeRole = trim($item['role']);
                    $employeeId = trim($item['AID']);
                    $clsName = 'text-dark-75';

                    if (array_key_exists($employeeId, $staffArray)) {
                        $clsName = 'bg-danger-o-40 text-white';
                    }

                    $formHtml .= <<<EOT
	<option value="$employeeId" class="$clsName" data-subtext="$employeeRole">$employeeName</option>
EOT;
                    $e1++;

                }
                $formHtml .= <<<EOT
              </select>
</div>
EOT;
            }

            if ($fileMultiContacts) {
                $formHtml .= <<<EOT
<!-- FileContacts Start -->
     	  <div class="separator separator-dashed my-2"></div>
     <div class="form-group $notifyUsersDis notifyUsers row align-items-center px-10">
						   <label class="font-weight-bolder mb-4">File Contact(s):</label>
         <select  title="- Select File Contact(s) -"  name="CIDs[]"  id= "CIDs" class="chzn-selectShowSelectAll form-control" multiple="" data-live-search="true"  data-actions-box="true"  style="width:250px;">
EOT;
                foreach ($fileMultiContacts as $multiContactValue) {
                    foreach ($multiContactValue as $contact) {
                        if ($contact['email']) {
                            $fileContactRole = $contact['cRole'];
                            $mutiContactCID = $contact['CID'];
                            $formHtml .= <<<EOT
		        <option value="$mutiContactCID" data-subtext="$fileContactRole" >{$contact['contactName']} {$contact['contactLName']} - {$contact['companyName']}</option>
EOT;
                        }
                    }
                }
                $formHtml .= <<<EOT
   </select></div>
<!-- FileContacts End -->
EOT;
            }
            $formHtml .= <<<EOT
    <div class="form-group $notifyUsersDis notifyUsers px-10 ">
						   <label class="font-weight-bolder">To (EMAIL ADDRESSES ONLY):</label>
						   						<textarea class="form-control" name="customRecipientEmail" id="customRecipientEmail" cols="40" rows="2"></textarea>
<small class="text-muted">(semicolon) to seperate email addresses, only public notes will be sent.</small>
						   </div>
EOT;
        }

        $formHtml .= <<<EOT
    <div class="row d-none">
        <div class="col-xl-5 col-md-4"></div> 
        <div class="col-xl-6 col-md-6">
            <input class="btn btn-primary mr-2"  type="submit" name="submit1" id="submit" value="Save" alt="Start Search">
            <button type="reset" class="btn btn-secondary">Reset</button>
        </div>
    </div>
</form>
EOT;
        return $formHtml;
    }
}
