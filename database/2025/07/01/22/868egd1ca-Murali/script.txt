# Run the updater (basic):
php tasks/migration/updateTblFileCalculatedValues.php

# Run with controls:
# -p <PCID> (optional)
# -o <output_file> (optional) — append processed LMRIds (one per line)
# -c <checkpoint_file> (optional) — where to store/read last processed LMRId
# -r (flag) — resume from checkpoint file (-c) if it exists
# -s <start_from_LMRId> — start from specific LMRId (desc), overrides -r
# -L <max_rows> — process at most this many rows this run (soft stop)
# -x <stop_flag_file> — if this file exists, the script stops gracefully

# Examples:
# Start a controlled run limited to 20k rows and write checkpoints
php tasks/migration/updateTblFileCalculatedValues.php -L=20000 -c=logs/updateTblFileCalculatedValues_checkpoint_PCID123.txt -p=123

# Pause/Stop: create the stop flag file (the script will exit shortly):
#   touch logs/updateTblFileCalculatedValues.stop
# Resume later from last checkpoint:
php tasks/migration/updateTblFileCalculatedValues.php -r -c=logs/updateTblFileCalculatedValues_checkpoint_PCID123.txt -p=123

# Rollback command
php tasks/migration/rollback/updateTblFileCalculatedValues_rollback.php -f={fileName from the previous command}

# Rollback example (using IDs file written by the updater):
php tasks/migration/rollback/updateTblFileCalculatedValues_rollback.php -f=logs/updateTblFileCalculatedValues_LMRIds_20250924_082914.txt


# Final Command to execute
 tasks/migration/updateTblFileCalculatedValues.php -L=5000 -c=logs/updateTblFileCalculatedValues_checkpoint.txt