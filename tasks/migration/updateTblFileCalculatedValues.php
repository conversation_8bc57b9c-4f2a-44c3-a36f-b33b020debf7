<?php

use models\Database2;
use models\lendingwise\tblFileCalculatedValues;
use models\Log;

require __DIR__ . '/../../public/includes/util.php';
@set_time_limit(0);
if (function_exists('ini_set')) { @ini_set('memory_limit', '512M'); }

// Options:
// -p <PCID> (optional)
// -o <output_file> (optional) — file to append processed LMRIds (one per line)
// -c <checkpoint_file> (optional) — path to a checkpoint file for resume. Defaults to logs/updateTblFileCalculatedValues_checkpoint[_PCID].txt
// -r (flag, optional) — resume from a checkpoint file (-c) if it exists
// -s <start_from_LMRId> (optional) — start processing from a specific LMRId (desc cursor). Overrides -r
// -L <max_rows> (optional) — stop after processing at most this many rows in this run
// -x <stop_flag_file> (optional) — if this file exists, the script will stop gracefully
$shortOpts = 'p::o::c::rs::L::x::';

$options = getopt($shortOpts);

$PCID = $options['p'] ?? null;

// Determine an output file for recording processed LMRIds
$outPath = $options['o'] ?? null;
if (!$outPath) {
    $ts = date('Ymd_His');
    $suffix = $PCID ? ('_PCID' . intval($PCID)) : '';
    $outPath = __DIR__ . '/../../logs/updateTblFileCalculatedValues_LMRIds_' . $ts . $suffix . '.txt';
}

// Determine a checkpoint file path
$checkpointPath = $options['c'] ?? null;
if (!$checkpointPath) {
    $suffix = $PCID ? ('_PCID' . intval($PCID)) : '';
    $checkpointPath = __DIR__ . '/../../logs/updateTblFileCalculatedValues_checkpoint' . $suffix . '.txt';
}

$resume = isset($options['r']);
$startFrom = isset($options['s']) ? (is_numeric($options['s']) ? (int)$options['s'] : null) : null;
$maxRows = isset($options['L']) && is_numeric($options['L']) ? (int)$options['L'] : null;
$stopFlag = $options['x'] ?? (__DIR__ . '/../../logs/updateTblFileCalculatedValues.stop');

$fh = null;
$written = 0;
try {
    // Try to open an output file for appending (create directories if missing)
    $dir = dirname($outPath);
    if (!is_dir($dir)) {
        @mkdir($dir, 0775, true);
    }
    $fh = @fopen($outPath, 'a');
    if ($fh) {
        fwrite($fh, "# LMRIds written by updateTblFileCalculatedValues at " . date('c') . ($PCID ? " for PCID=$PCID" : '') . "\n");
    } else {
        Log::Insert('Warning: Could not open output file for LMRIds: ' . $outPath);
    }
} catch (\Throwable $e) {
    Log::Insert('Warning: Failed to prepare output file: ' . $e->getMessage());
}

$batchSize = 5000; // process in small batches to reduce a DB load and avoid timeouts
$lastLMRId = null; // descending cursor; null means start from the top

// Initialize the cursor from startFrom or checkpoint resume
if ($startFrom !== null) {
    $lastLMRId = (int)$startFrom;
    Log::Insert('Starting from provided LMRId (desc): ' . $lastLMRId);
} elseif ($resume && is_readable($checkpointPath)) {
    $cp = trim((string)@file_get_contents($checkpointPath));
    if ($cp !== '' && is_numeric($cp)) {
        $lastLMRId = (int)$cp;
        Log::Insert('Resuming from checkpoint LMRId (desc): ' . $lastLMRId . ' using ' . $checkpointPath);
    } else {
        Log::Insert('Resume requested but checkpoint file is empty or invalid: ' . $checkpointPath);
    }
}

$processedThisRun = 0;
$checkpointEvery = 100; // write a checkpoint every N rows

while (true) {
    // Check for external stop signal between batches
    if ($stopFlag && file_exists($stopFlag)) {
        Log::Insert('Stop flag detected at ' . $stopFlag . '; exiting gracefully.');
        break;
    }

    $sql = 'SELECT t100.FPCID,
                   t100.LMRId
FROM tblFile t100
LEFT JOIN tblFileCalculatedValues t200 ON t100.LMRId = t200.LMRId
WHERE t200.LMRId IS NULL
  AND (:PCID IS NULL OR t100.FPCID = :PCID)'
        . ($lastLMRId !== null ? ' AND t100.LMRId < :LastLMRId' : '') . '
ORDER BY t100.LMRId DESC
LIMIT ' . intval($batchSize);

    $params = [
        'PCID' => $PCID ?? null,
    ];
    if ($lastLMRId !== null) {
        $params['LastLMRId'] = $lastLMRId;
    }

    $res = Database2::getInstance()->queryData($sql, $params);
    $m = sizeof($res);
    if (!$m) {
        break;
    }

    foreach ($res as $i => $row) {
        // Check maxRows and stop flag within the batch as well
        if ($maxRows !== null && $processedThisRun >= $maxRows) {
            Log::Insert('Max rows limit reached for this run: ' . $maxRows . '; stopping.');
            break 2; // break out of both foreach and while
        }
        if ($stopFlag && file_exists($stopFlag)) {
            Log::Insert('Stop flag detected at ' . $stopFlag . ' during batch; exiting gracefully.');
            break 2;
        }

        $LMRId = intval($row['LMRId']);
        $FPCID = intval($row['FPCID']);
        if ((($i + 1) % 50) === 0 || $i === 0 || ($i + 1) === $m) {
            Log::Insert(($i + 1) . ' / ' . $m . ' - ' . $LMRId . ' ' . $FPCID);
        }
        try {
            tblFileCalculatedValues::GetCalculatedValuesForOtherPcLoans($LMRId);
            if ($fh) { fwrite($fh, $LMRId . "\n"); $written++; }
            $processedThisRun++;
        } catch (\Throwable $e) {
            Log::Insert('Error processing LMRId ' . $LMRId . ': ' . $e->getMessage());
        } finally {
            Database2::resetLogQuery();
        }
        $lastLMRId = isset($lastLMRId) ? min($lastLMRId, $LMRId) : $LMRId; // advance cursor

        // Periodic checkpoint
        if ($processedThisRun % $checkpointEvery === 0 && $lastLMRId !== null) {
            try {
                @file_put_contents($checkpointPath, (string)$lastLMRId);
            } catch (\Throwable $e) {
                Log::Insert('Warning: failed writing checkpoint: ' . $e->getMessage());
            }
        }
    }

    // Optional small pause to reduce DB pressure in very large runs
    usleep(20000);
}

// Final checkpoint write
if ($lastLMRId !== null) {
    try { @file_put_contents($checkpointPath, (string)$lastLMRId); } catch (\Throwable $e) { /* ignore */ }
}

if ($fh) {
    fwrite($fh, "# Total IDs this run: $written\n");
    fclose($fh);
    Log::Insert('Processed LMRIds written to: ' . $outPath . ' (count=' . $written . ')');
} else {
    Log::Insert('No output file was created for LMRIds; use logs to compile IDs if needed.');
}