<?php

namespace pages\backoffice\api_v2\draw_management\loanFile;

use pages\backoffice\api_v2\draw_management\base\DrawManagementApiBase;
use models\composite\oDrawManagement\dto\request\SaveDrawRequestRequest;

/**
 * Class LoanFile
 *
 * API endpoint for updating and fetching draw request data
 *
 * @package pages\backoffice\api_v2\draw_management
 */
class LoanFile extends DrawManagementApiBase
{
    /**
     * Handle POST requests to update draw request data
     *
     * @return void
     */
    public static function Post(): void
    {
        parent::Init();

        $postData = static::parseJsonInput();
        if (!$postData) return; // parseJsonInput handles error response

        // Process LMRId before creating DTO to ensure it's a proper integer
        if (isset($postData['LMRId'])) {
            $postData['LMRId'] = static::getLMRId($postData['LMRId']);
        }

        static::handlePostRequest(
            SaveDrawRequestRequest::class,
            $postData,
            function($dto) {
                $LMRId = $dto->LMRId;

                // Validate loan file exists
                static::validateLoanFile($LMRId);

                // Get draw request manager
                $drawRequestManager = static::getDrawRequestManager($LMRId);

                // Convert DTO to array format for existing manager
                $requestData = static::dtoToArray($dto);

                // Save data
                $result = $drawRequestManager->saveDrawRequestData($requestData);

                if (!$result) {
                    return ['success' => false, 'data' => null];
                }

                // Get updated data
                $updatedData = $drawRequestManager->getDrawRequestDataArray();
                return ['success' => true, 'data' => $updatedData];
            },
            'Draw request data saved successfully',
            'Failed to save draw request data'
        );
    }

}
