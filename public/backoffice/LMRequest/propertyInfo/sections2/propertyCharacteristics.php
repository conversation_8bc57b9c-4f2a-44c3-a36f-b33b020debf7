<?php

use models\composite\oPC\getPCHMLOBasicLoanInfoForFileLevel;
use models\constants\gl\glConstructionType;
use models\constants\GpropertyTypeNumbArray;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\Property;
use models\Controllers\loanForm;
use models\lendingwise\tblPropertiesCharacteristics;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Strings;

//LMRequest::$loanProgram;

//pr(LMRequest::File()->getTblFileResponse_by_LMRId());
//pr(LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType);
//pr(LMRequest::File()->getTblFilePropertyInfo_by_LMRId());
$glConstructionType = glConstructionType::$glConstructionType;

$propChars = null;

if(Property::$propertyInfo) {
    $propChars = Property::$propertyInfo->getTblPropertiesCharacteristics_by_propertyId();
}

if(!$propChars) {
    $propChars = new tblPropertiesCharacteristics();
}

$propertyType = $propChars->propertyType;
$futurePropertyType = $propChars->futurePropertyType;
$propertySqFt = $propChars->propertySqFt;
$propertyAcres = $propChars->propertyAcres;
$propertyYearBuilt = $propChars->propertyYearBuilt;
$propertyCondoEligibility = $propChars->propertyCondoEligibility;
$propertyPropertyFeatures = $propChars->propertyFeatures;
$propertyAdjustedSqFt = $propChars->propertyAdjustedSqFt;
$propertyWaterFront = $propChars->propertyWaterFront;
$propertyConstructionType = $propChars->propertyConstructionType;
$propertyConstructionMethod = $propChars->propertyConstructionMethod;
$propertyYearPurchased = $propChars->propertyYearPurchased;
$propertyHowManyRoom = $propChars->propertyNumberOfRooms;
$propertyHowManyBedRoom = $propChars->propertyNumberOfBedRooms;
$propertyHowManyBathRoom = $propChars->propertyNumberOfBathRooms;
$propertyHowManyHalfBathRoom = $propChars->propertyNumberOfHalfBathRooms;
$propertyBasementHome = $propChars->propertyIsHomeHaveBasement;
$propertyBasementFinish = $propChars->propertyIsBasementFinished;
$propertyGarageHome = $propChars->propertyIsHomeHaveGarage;
$propertyAddRentableSqFt = $propChars->propertyRentableSqFt;
$propertyNoOfParking = $propChars->propertyNumberOfParkings;
$propertyNoOfBuildings = $propChars->propertyNumberOfBuildings;
$propertyStabilizedRate = $propChars->propertyStabilizedRate;
$propertyNoUnitsOccupied = $propChars->propertyNumberOfUnits;
$propertyPropertyValue = $propChars->propertyValue;
$propertyNoOfParcels = $propChars->propertyNumberOfParcels;
$propertyOwnerOccupancyPercentage = $propChars->propertyOwnerOccupancy;
$propertyCurrentCommercialTenantOccupancyRate = $propChars->propertyCommercialTenantOccupancyRate;
$propertyAnchorTenant = $propChars->propertyIsAnchorTenant;
$propertyAnchorTenantName = $propChars->propertyAnchorTenantName;
$propertyCre = $propChars->propertyCRE;
$propertyResi = $propChars->propertyRESI;
$propertyClass = $propChars->propertyClass;
$propertyAddNoOfStories = $propChars->propertyNumberOfStories;
$propertyLeaseType = $propChars->propertyLeaseType;

$dispBasementFinish = ($propertyBasementHome == '1') ? '' : 'none';


if (trim(BaseHTML::fieldAccess(['fNm' => 'propertyType', 'sArr' => Property::$secArr, 'opt' => 'D'])) == 'secShow'
    && trim(BaseHTML::fieldAccess(['fNm' => 'condoEligibility', 'sArr' => Property::$secArr, 'opt' => 'D'])) == 'secShow'
    && in_array($propertyType, [4, 5, 48])) {
    $condoEligibilityDispOpt = '';
} else {
    $condoEligibilityDispOpt = ' d-none ';
}
?>

<div class="row PropertiesCharacteristics">
    <label class="col-lg-12 mb-4 px-4 py-2 bg-secondary font-weight-bold" id="propchartitle">
        <?php echo BaseHTML::getSubSectionHeading('PD', 'propCharacteristicsSubSection'); ?>
    </label>

    <div class="propchar  col-md-6  propertyType_disp
            <?php echo loanForm::showField('propertyType'); ?>">
        <div class="row form-group"><?php echo loanForm::label('propertyType', 'col-md-5'); ?>
            <div class="col-md-7">
                <?php
                if (Property::$allowToEdit) { ?>
                    <select class="autosavePropertyCharacteristics form-control input-sm chzn-select  <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyType', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                            name="properties[<?php echo Property::$propertyIndex; ?>][propertyType]"
                            id="propertyType"
                            data-table="tblPropertiesCharacteristics"
                            data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                            data-id="<?php echo $propChars->id; ?>"
                            onchange="Property.showAndHideZillowValue(this);showHideCondoEligibility(this.value,'condoEligibility_disp')"
                            tabindex="<?php echo Property::$tabIndex++; ?>"
                            data-placeholder="Please Select Property Type"
                        <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyType', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>>
                        <option value=""></option>
                        <?php foreach (GpropertyTypeNumbArray::$GpropertyTypeNumbArray as $key => $type) {
                            if (sizeof(LMRequest::$fileCustomLoanGuidelinePropertyTypes) && !in_array($key, LMRequest::$fileCustomLoanGuidelinePropertyTypes)) {
                                continue;
                            }
                            $key = intval(trim($key));
                            if ($key == 1000) {
                                echo "<option disabled style=\"color:white;background-color: rgb(0, 130, 187);\">---Residential---</option>\n";
                            } else if ($key == 1001) {
                                echo "<option disabled style=\"color:white;background-color: rgb(0, 130, 187);\">---Commercial---</option>\n";
                            } else {
                                echo '<option value="' . $key . '" ' . Arrays::isSelected($key, $propertyType) . '>' . $type . '</option>';
                            }
                        }
                        if($propertyType && !in_array($propertyType, LMRequest::$fileCustomLoanGuidelinePropertyTypes)) {
                            echo '<option class="text-danger" value="' . $propertyType . '" selected>' . GpropertyTypeNumbArray::$GpropertyTypeNumbArray[$propertyType] . '</option>';
                        }
                        ?>
                    </select>
                    <?php
                } else { ?>
                    <h5><?php echo GpropertyTypeNumbArray::$GpropertyTypeNumbArray[$propertyType]; ?></h5>
                <?php } ?>
            </div>
        </div>
    </div>
    <div class="propchar  col-md-6  condoEligibility_disp <?php echo $condoEligibilityDispOpt; ?> <?php echo loanForm::showField('condoEligibility'); ?> ">
        <div class="row form-group">
            <?php echo loanForm::label('condoEligibility', 'col-md-5'); ?>
            <div class="col-md-7">
                <?php if (Property::$allowToEdit) { ?>
                    <select class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'condoEligibility', 'sArr' => Property::$secArr, 'opt' => 'M']); ?> chzn-select"
                            name="properties[<?php echo Property::$propertyIndex; ?>][condoEligibility]"
                            id="condoEligibility"
                            data-column="propertyCondoEligibility"
                            data-table="tblPropertiesCharacteristics"
                            data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                            data-id="<?php echo $propChars->id; ?>"
                            data-placeholder="Please Select Condo Eligibility"
                            tabindex="<?php echo Property::$tabIndex++; ?>"
                        <?php echo BaseHTML::fieldAccess(['fNm' => 'condoEligibility', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>>
                        <option value=""></option>
                        <option value="Warrantable"
                            <?php if ($propertyCondoEligibility == 'Warrantable') {
                                echo 'selected';
                            } ?>>
                            Warrantable
                        </option>
                        <option value="Non Warrantable"
                            <?php if ($propertyCondoEligibility == 'Non Warrantable') {
                                echo 'selected';
                            } ?>>
                            Non Warrantable
                        </option>
                    </select>
                    <?php
                } else { ?>
                    <h5><?php echo $propertyCondoEligibility; ?></h5>
                <?php } ?>
            </div>
        </div>
    </div>
    <?php if (Property::$activeTab != 'CI') { ?>
        <div class="propchar  col-md-6  propertySqFt_disp
                <?php echo loanForm::showField('propertySqFt'); ?>">
            <div class="row form-group">
                <?php echo loanForm::label('propertySqFt', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="number"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               name="properties[<?php echo Property::$propertyIndex; ?>][propertySqFt]"
                               id="propertySqFt"
                               data-column="propertySqFt"
                               class="autosavePropertyCharacteristics form-control input-sm
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'propertySqFt', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                               tabindex="<?php echo Property::$tabIndex++; ?>"
                               value="<?php echo $propertySqFt; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'propertySqFt', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>>
                    <?php } else { ?>
                        <h5><?php echo $propertySqFt ?></h5>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propchar   col-md-6  acres_disp
                <?php echo loanForm::showField('acres'); ?>">
            <div class="row form-group"><?php echo loanForm::label('acres', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="text"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               name="properties[<?php echo Property::$propertyIndex; ?>][acres]"
                               id="acres"
                               data-column="propertyAcres"
                               class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'acres', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                               placeholder="0.0"
                               tabindex="<?php echo Property::$tabIndex++; ?>"
                               value="<?php echo $propertyAcres; ?>"
                               autocomplete="off"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'acres', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>>
                    <?php } else { ?>
                        <h5><?php echo $propertyAcres; ?></h5>
                    <?php } ?>
                </div>
            </div>
        </div>
        <?php if (Property::$hideThisField) { ?>
            <div class="propchar  col-md-6  yearBuilt_disp
                <?php echo loanForm::showField('yearBuilt'); ?> ">
                <div class="row form-group"><?php echo loanForm::label('yearBuilt', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if (Property::$allowToEdit) { ?>
                            <input type="number"
                                   data-table="tblPropertiesCharacteristics"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo $propChars->id; ?>"
                                   data-column="propertyYearBuilt"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][yearBuilt]"
                                   id="yearBuilt"
                                   class="autosavePropertyCharacteristics form-control input-sm yearClass <?php echo BaseHTML::fieldAccess(['fNm' => 'yearBuilt', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                   tabindex="<?php echo Property::$tabIndex++; ?>"
                                   value="<?php echo $propertyYearBuilt; ?>"
                                   autocomplete="off"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'yearBuilt', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>>
                        <?php } else { ?>
                            <h5><?php echo $propertyYearBuilt; ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>
        <?php } ?>
        <div class="propchar  col-md-6  propertyFeatures_disp
                <?php echo loanForm::showField('propertyFeatures'); ?>">
            <div class="row form-group"><?php echo loanForm::label('propertyFeatures', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <textarea
                                data-table="tblPropertiesCharacteristics"
                                data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                data-id="<?php echo $propChars->id; ?>"
                                data-column="propertyFeatures"
                                class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyFeatures', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                name="properties[<?php echo Property::$propertyIndex; ?>][propertyFeatures]"
                                id="propertyFeatures"
                                tabindex="<?php echo Property::$tabIndex++; ?>"
                    <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyFeatures', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>><?php echo $propertyPropertyFeatures; ?></textarea>
                    <?php } else { ?>
                        <h5><?php echo $propertyPropertyFeatures; ?></h5>
                    <?php } ?>
                </div>
            </div>
        </div>
    <?php } ?>

    <?php if (Property::$activeTab != 'CI') { ?>
        <div class="propchar  col-md-6  adjustedSqFt_disp
                <?php echo loanForm::showField('adjustedSqFt'); ?>">
            <div class="row form-group"><?php echo loanForm::label('adjustedSqFt', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="number"
                               name="properties[<?php echo Property::$propertyIndex; ?>][adjustedSqFt]"
                               id="adjustedSqFt"
                               data-column="propertyAdjustedSqFt"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               class="autosavePropertyCharacteristics form-control input-sm
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'adjustedSqFt', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                               value="<?php echo $propertyAdjustedSqFt; ?>"
                               size="10"
                               maxlength="18"
                               tabindex="<?php echo Property::$tabIndex++; ?>"
                               autocomplete="off"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'adjustedSqFt', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>>
                    <?php } else { ?>
                        <b><?php echo $propertyAdjustedSqFt; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propchar  col-md-6  waterFront_disp
                <?php echo loanForm::showField('waterFront'); ?>">
            <div class="row form-group"><?php echo loanForm::label('waterFront', 'col-md-5'); ?>
                <div class="col-md-2">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="switch switch-icon <?php echo BaseHTML::fieldAccess(['fNm' => 'waterFront', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>">
                            <label class="font-weight-bold">
                                <input class="autosavePropertyCharacteristics form-control"
                                    <?php if ($propertyWaterFront == '1') { ?> checked="checked" <?php } ?>
                                       data-table="tblPropertiesCharacteristics"
                                       data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                       data-id="<?php echo $propChars->id; ?>"
                                       data-column="propertyWaterFront"
                                       id="propertyWaterFront<?php echo Property::$propertyIndex; ?>"
                                       type="checkbox"
                                       onchange="toggleSwitch('propertyWaterFront<?php echo Property::$propertyIndex; ?>', 'waterFront<?php echo Property::$propertyIndex; ?>', '1', '0' );"
                                       tabindex="<?php echo Property::$tabIndex++; ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'waterFront', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>>
                                <input type="hidden"
                                       name="properties[<?php echo Property::$propertyIndex; ?>][waterFront]"
                                       id="waterFront<?php echo Property::$propertyIndex; ?>"
                                       value="<?php echo $propertyWaterFront; ?>">
                                <span></span>
                            </label>
                        </div>
                    <?php } else {
                        echo '<h5>' . Strings::booleanTextVal($propertyWaterFront) . '</h5>';
                    } ?>
                </div>
            </div>
        </div>
        <div class="propchar  col-md-6  propConstructionType_disp
                <?php echo loanForm::showField('propConstructionType'); ?>">
            <div class="row form-group"><?php echo loanForm::label('propConstructionType', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <select name="properties[<?php echo Property::$propertyIndex; ?>][propConstructionType]"
                                data-table="tblPropertiesCharacteristics"
                                data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                data-id="<?php echo $propChars->id; ?>"
                                data-column="propertyConstructionType"
                                id="propConstructionType"
                                class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propConstructionType', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                tabindex="<?php echo Property::$tabIndex++; ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'propConstructionType', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>>
                            <option value=''> - Select -</option>
                            <?php
                            foreach ($glConstructionType as $eachConstructionType) {
                                echo "<option value=\"" . $eachConstructionType . "\" " . Arrays::isSelected($eachConstructionType, $propertyConstructionType) . '>' . $eachConstructionType . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else {
                        echo '<h5>' . $glConstructionType[$propertyConstructionType] . '</h5>';
                    } ?>
                </div>
            </div>
        </div>
        <div class="propchar  col-md-6  propConstructionMethod_disp
                <?php echo loanForm::showField('propConstructionMethod'); ?>">
            <div class="row form-group"><?php echo loanForm::label('propConstructionMethod', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <select name="properties[<?php echo Property::$propertyIndex; ?>][propConstructionMethod]"
                                data-table="tblPropertiesCharacteristics"
                                data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                data-column="propertyConstructionMethod"
                                data-id="<?php echo $propChars->id; ?>"
                                id="propConstructionMethod"
                                class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propConstructionMethod', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                tabindex="<?php echo Property::$tabIndex++; ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'propConstructionMethod', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>>
                            <option value="">-Select-</option>
                            <option
                                    value="Site-Built" <?php echo Arrays::isSelected('Site-Built', $propertyConstructionMethod); ?>>
                                Site-Built
                            </option>
                            <option
                                    value="Manufactured"<?php echo Arrays::isSelected('Manufactured', $propertyConstructionMethod); ?>>
                                Manufactured
                            </option>
                            <option value="Modular" <?php echo Arrays::isSelected('Modular', $propertyConstructionMethod); ?>>
                                Modular
                            </option>
                        </select>
                    <?php } else {
                        echo '<h5>' . $propertyConstructionMethod . '</h5>';
                    } ?>
                </div>
            </div>
        </div>


        <div class="propchar  col-md-6  yearPurchased_disp
                <?php echo loanForm::showField('yearPurchased'); ?>">
            <div class="row form-group"><?php echo loanForm::label('yearPurchased', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="number"
                               class="autosavePropertyCharacteristics yearClass form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'yearPurchased', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-column="propertyYearPurchased"
                               data-id="<?php echo $propChars->id; ?>"
                               name="properties[<?php echo Property::$propertyIndex; ?>][yearPurchased]"
                               id="yearPurchased"
                               value="<?php echo $propertyYearPurchased; ?>"
                               maxlength="4"
                               size="4"
                               TABINDEX="<?php echo Property::$tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'yearPurchased', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                    <?php } else { ?>
                        <h5><?php echo $propertyYearPurchased ?></h5>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propchar col-md-6  propertyIsValueAddedView_disp
                <?php echo loanForm::showField('propertyIsValueAddedView'); ?>">
            <div class="row form-group ">
                <?php echo loanForm::label('propertyIsValueAddedView', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="radio-inline">
                            <label class="radio radio-solid font-weight-bold"
                                   for="propertyIsValueAddedViewYes_<?php echo Property::$propertyIndex; ?>">
                                <input type="radio"
                                       data-table="tblPropertiesCharacteristics"
                                       data-id="<?php echo $propChars->id; ?>"
                                       data-column="propertyIsValueAddedView"
                                       data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                       name="properties[<?php echo Property::$propertyIndex; ?>][propertyIsValueAddedView]"
                                       id="propertyIsValueAddedViewYes_<?php echo Property::$propertyIndex; ?>"
                                       value="1"
                                       class="autosavePropertyCharacteristics <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsValueAddedView', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                       onclick="propertyCharacteristics.showHideDiv('1','propertyTypeOfView_disp');"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsValueAddedView', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>
                                       tabindex="<?php echo Property::$tabIndex++; ?>"
                                    <?php echo Strings::isChecked('1', $propChars->propertyIsValueAddedView); ?>/>
                                <span></span>
                                Yes
                            </label>
                            <label class="radio radio-solid font-weight-bold"
                                   for="propertyIsValueAddedViewNo_<?php echo Property::$propertyIndex; ?>">
                                <input type="radio"
                                       data-table="tblPropertiesCharacteristics"
                                       data-id="<?php echo $propChars->id; ?>"
                                       data-column="propertyIsValueAddedView"
                                       data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                       name="properties[<?php echo Property::$propertyIndex; ?>][propertyIsValueAddedView]"
                                       id="propertyIsValueAddedViewNo_<?php echo Property::$propertyIndex; ?>"
                                       value="0"
                                       class="autosavePropertyCharacteristics <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyLocation', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                       onclick="propertyCharacteristics.showHideDiv('0','propertyTypeOfView_disp');"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsValueAddedView', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>
                                       tabindex="<?php echo Property::$tabIndex++; ?>"
                                    <?php echo Strings::isChecked('0', $propChars->propertyIsValueAddedView); ?>/>
                                <span></span>
                                No
                            </label>
                        </div>
                    <?php } else { ?>
                        <h5><?php echo Strings::booleanTextVal($propChars->propertyIsValueAddedView); ?></h5>
                    <?php } ?>
                </div>
            </div>
        </div>


        <div class="propchar  col-md-6  propertyTypeOfView_disp <?php echo BaseHTML::parentFieldAccess(['fNm' => 'propertyIsValueAddedView', 'sArr' => Property::$secArr, 'pv' => $propChars->propertyIsValueAddedView, 'av' => '1']); ?>
            <?php echo loanForm::showField('propertyTypeOfView'); ?>">
            <div class="row form-group">
                <?php echo loanForm::label('propertyTypeOfView', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <select name="properties[<?php echo Property::$propertyIndex; ?>][propertyTypeOfView]"
                                id="propertyTypeOfView"
                                data-table="tblPropertiesCharacteristics"
                                data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                data-id="<?php echo $propChars->id; ?>"
                                data-column="propertyTypeOfView"
                                class="autosavePropertyCharacteristics form-control input-sm chzn-select"
                                data-placeholder="Please Select"
                                tabindex="<?php echo Property::$tabIndex++; ?>">
                            <option value=""></option>
                            <?php foreach (Property::$propertyTypeOfViewList as $eachKey => $eachVal) { ?>
                                <option value="<?php echo $eachKey; ?>"
                                    <?php echo Arrays::isSelected($eachKey, $propChars->propertyTypeOfView); ?>>
                                    <?php echo $eachVal; ?></option>
                            <?php } ?>
                        </select>
                    <?php } else { ?>
                        <b><?php echo Property::$propertyTypeOfViewList[$propChars->propertyTypeOfView]; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>


        <div class="propchar  col-md-6  propertyTypeOfFrontage_disp <?php echo loanForm::showField('propertyTypeOfFrontage'); ?>">
            <div class="row form-group"><?php echo loanForm::label('propertyTypeOfFrontage', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <select name="properties[<?php echo Property::$propertyIndex; ?>][propertyTypeOfFrontage]"
                                id="propertyTypeOfFrontage"
                                data-table="tblPropertiesCharacteristics"
                                data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                data-id="<?php echo $propChars->id; ?>"
                                data-column="propertyTypeOfFrontage"
                                class="autosavePropertyCharacteristics chzn-select form-control input-sm"
                                data-placeholder="Please Select"
                                onchange="propertyCharacteristics.showAndHideTypeOfFrontageOtherDiv(this.value,'propertyTypeOfFrontageOther_disp');"
                                tabindex="<?php echo Property::$tabIndex++; ?>">
                            <option value=""></option>
                            <?php foreach (Property::$propertyTypeOfFrontageList as $eachKey => $eachVal) { ?>
                                <option value="<?php echo $eachKey; ?>"
                                    <?php echo Arrays::isSelected($eachKey, $propChars->propertyTypeOfFrontage); ?>>
                                    <?php echo $eachVal; ?></option>
                            <?php } ?>
                        </select>
                    <?php } else { ?>
                        <b><?php echo Property::$propertyTypeOfFrontageList[$propChars->propertyTypeOfFrontage]; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>


        <div class="propchar  col-md-6   propertyTypeOfFrontageOther_disp <?php echo BaseHTML::parentFieldAccess(['fNm' => 'propertyTypeOfFrontage', 'sArr' => Property::$secArr, 'pv' => $propChars->propertyTypeOfFrontage, 'av' => '7']); ?> <?php echo loanForm::showField('propertyTypeOfFrontageOther'); ?>">
            <div class="row form-group">
                <?php echo loanForm::label('propertyTypeOfFrontageOther', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="text"
                               name="properties[<?php echo Property::$propertyIndex; ?>][propertyTypeOfFrontageOther]"
                               id="propertyTypeOfFrontageOther"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               data-column="propertyTypeOfFrontageOther"
                               class="autosavePropertyCharacteristics form-control input-sm "
                               value="<?php echo $propChars->propertyTypeOfFrontageOther; ?>"
                               tabindex="<?php echo Property::$tabIndex++; ?>"/>
                    <?php } else { ?>
                        <b><?php echo $propChars->propertyTypeOfFrontageOther; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>


        <div class="col-md-6 howManyRoom_disp
                <?php echo loanForm::showField('howManyRoom'); ?>">
            <div class="row form-group"><?php echo loanForm::label('howManyRoom', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="number"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-column="propertyNumberOfRooms"
                               data-id="<?php echo $propChars->id; ?>"
                               name="properties[<?php echo Property::$propertyIndex; ?>][howManyRoom]"
                               id="howManyRoom"
                               value="<?php echo $propertyHowManyRoom; ?>"
                               maxlength="3"
                               tabindex="<?php echo Property::$tabIndex++; ?>"
                               autocomplete="off"
                               class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'howManyRoom', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'howManyRoom', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                    <?php } else { ?>
                        <b><?php echo $propertyHowManyRoom; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="col-md-6 howManyBedRoom_disp
                <?php echo loanForm::showField('howManyBedRoom'); ?>">
            <div class="row form-group"><?php echo loanForm::label('howManyBedRoom', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="number"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               data-column="propertyNumberOfBedRooms"
                               name="properties[<?php echo Property::$propertyIndex; ?>][howManyBedRoom]"
                               id="howManyBedRoom"
                               value="<?php echo $propertyHowManyBedRoom; ?>"
                               size="10"
                               maxlength="13"
                               TABINDEX="<?php echo Property::$tabIndex++; ?>"
                               autocomplete="off"
                               class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'howManyBedRoom', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'howManyBedRoom', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                    <?php } else { ?>
                        <h5><?php echo $propertyHowManyBedRoom; ?></h5>
                    <?php } ?>
                </div>
            </div>
        </div>
        <div class="col-md-6 howManyBathRoom_disp
                    <?php echo loanForm::showField('howManyBathRoom'); ?>">
            <div class="row form-group"><?php echo loanForm::label('howManyBathRoom', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="number"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-column="propertyNumberOfBathRooms"
                               data-id="<?php echo $propChars->id; ?>"
                               name="properties[<?php echo Property::$propertyIndex; ?>][howManyBathRoom]"
                               id="howManyBathRoom"
                               value="<?php echo $propertyHowManyBathRoom; ?>"
                               size="10"
                               maxlength="13"
                               TABINDEX="<?php echo Property::$tabIndex++; ?>"
                               autocomplete="off"
                               class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'howManyBathRoom', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'howManyBathRoom', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>/>
                    <?php } else { ?>
                        <h5><?php echo $propertyHowManyBathRoom; ?></h5>
                    <?php } ?>
                </div>
            </div>
        </div>
        <div class="col-md-6 howManyHalfBathRoom_disp
                <?php echo loanForm::showField('howManyHalfBathRoom'); ?>">
            <div class="row form-group"><?php echo loanForm::label('howManyHalfBathRoom', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="number"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-column="propertyNumberOfHalfBathRooms"
                               data-id="<?php echo $propChars->id; ?>"
                               class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'howManyHalfBathRoom', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                               name="properties[<?php echo Property::$propertyIndex; ?>][howManyHalfBathRoom]"
                               id="howManyHalfBathRoom"
                               value="<?php echo $propertyHowManyHalfBathRoom; ?>"
                               maxlength="15"
                               TABINDEX="<?php echo Property::$tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'howManyHalfBathRoom', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                    <?php } else { ?>
                        <h5><?php echo $propertyHowManyHalfBathRoom; ?></h5>
                    <?php } ?>
                </div>
            </div>
        </div>
        <!-- Half baths end MK -->

        <!-- Basement start MK -->
        <div class="col-md-6 basementHome_disp <?php echo loanForm::showField('basementHome'); ?>">
            <div class="row form-group"><?php echo loanForm::label('basementHome', 'col-md-5'); ?>
                <div class="col-md-2">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="switch switch-icon <?php echo BaseHTML::fieldAccess(['fNm' => 'basementHome', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>">
                            <label class="font-weight-bold">
                                <input class="autosavePropertyCharacteristics form-control" <?php if ($propertyBasementHome == '1') { ?> checked="checked" <?php } ?>
                                       data-table="tblPropertiesCharacteristics"
                                       data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                       data-column="propertyIsHomeHaveBasement"
                                       data-id="<?php echo $propChars->id; ?>"
                                       id="baseHome<?php echo Property::$propertyIndex; ?>"
                                       type="checkbox"
                                       onchange="toggleSwitch('baseHome<?php echo Property::$propertyIndex; ?>', 'basementHome<?php echo Property::$propertyIndex; ?>', '1', '0' );showBasementFinishDiv('divBasementFinish<?php echo Property::$propertyIndex; ?>', 'block');">
                                <input type="hidden"
                                       name="properties[<?php echo Property::$propertyIndex; ?>][basementHome]"
                                       id="basementHome<?php echo Property::$propertyIndex; ?>"
                                       value="<?php echo $propertyBasementHome ?>"/>
                                <span></span>
                            </label>
                        </div>
                    <?php } else { ?>
                        <h5><?php echo Strings::booleanTextVal($propertyBasementHome); ?></h5>
                    <?php } ?>
                </div>

                <div class="col-md-5">
                    <div class="row"
                         id="divBasementFinish<?php echo Property::$propertyIndex; ?>"
                         style="display:<?php echo $dispBasementFinish ?>;">
                        <?php echo loanForm::label('basementFinish', 'col-md-5'); ?>
                        <?php if (Property::$allowToEdit) { ?>
                            <span class="switch switch-icon">
                            <label class="font-weight-bold">
                                <input class="autosavePropertyCharacteristics form-control" <?php if ($propertyBasementFinish == '1') { ?> checked="checked" <?php } ?>
                                        data-table="tblPropertiesCharacteristics"
                                       data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                       data-column="propertyIsBasementFinished"
                                       data-id="<?php echo $propChars->id; ?>"
                                       id="baseFinish<?php echo Property::$propertyIndex; ?>"
                                       type="checkbox"
                                       onchange="toggleSwitch('baseFinish<?php echo Property::$propertyIndex; ?>', 'basementFinish<?php echo Property::$propertyIndex; ?>', '1', '0' );">
                               <input type="hidden"
                                      name="properties[<?php echo Property::$propertyIndex; ?>][basementFinish]"
                                      id="basementFinish<?php echo Property::$propertyIndex; ?>"
                                      value="<?php echo $propertyBasementFinish; ?>"/>
                                <span></span>
                            </label>
                        </span>
                        <?php } else { ?>
                            <h5><?php echo Strings::booleanTextVal($propertyBasementFinish); ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
        <!-- Basement end MK -->

        <!-- Garage start MK -->
        <div class="col-md-6 garageHome_disp <?php echo loanForm::showField('garageHome'); ?>">
            <div class="row form-group"><?php echo loanForm::label('garageHome', 'col-md-5'); ?>
                <div class="col-md-2">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="switch switch-icon <?php echo BaseHTML::fieldAccess(['fNm' => 'garageHome', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>">
                            <label class="font-weight-bold">
                                <input class="autosavePropertyCharacteristics form-control"
                                    <?php if ($propertyGarageHome == '1') { ?> checked="checked" <?php } ?>
                                       data-table="tblPropertiesCharacteristics"
                                       data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                       data-column="propertyIsHomeHaveGarage"
                                       data-id="<?php echo $propChars->id; ?>"
                                       id="homeGarage<?php echo Property::$propertyIndex; ?>"
                                       type="checkbox"
                                       onchange="toggleSwitch('homeGarage<?php echo Property::$propertyIndex; ?>', 'garageHome<?php echo Property::$propertyIndex; ?>', '1', '0' );">
                                <input type="hidden"
                                       name="properties[<?php echo Property::$propertyIndex; ?>][garageHome]"
                                       id="garageHome<?php echo Property::$propertyIndex; ?>"
                                       value="<?php echo $propertyGarageHome; ?>"/>
                                <span></span>
                            </label>
                        </div>
                    <?php } else {
                        echo '<h5>' . Strings::booleanTextVal($propertyGarageHome) . '</h5>';
                    } ?>
                </div>
            </div>
        </div>
        <!-- Garage end MK -->

        <!-- Rentable Sq. Ft. start MK -->
        <div class="propchar  col-md-6  addRentableSqFt addRentableSqFt_disp <?php echo loanForm::showField('addRentableSqFt'); ?>">
            <div class="row form-group"><?php echo loanForm::label('addRentableSqFt', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="number"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               data-column="propertyRentableSqFt"
                               name="properties[<?php echo Property::$propertyIndex; ?>][addRentableSqFt]"
                               id="addRentableSqFt"
                               class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'addRentableSqFt', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                               onchange="getReserveData()"
                               tabindex="<?php echo Property::$tabIndex++; ?>"
                               value="<?php echo $propertyAddRentableSqFt; ?>"
                               autocomplete="off">
                    <?php } else { ?>
                        <b><?php echo $propertyAddRentableSqFt ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>
        <!--//  Rentable Sq. Ft. end MK //-->

        <!--# of Parking start MK-->
        <div class="propchar  col-md-6  noOfParking noOfParking_disp <?php echo loanForm::showField('noOfParking'); ?>">
            <div class="row form-group"><?php echo loanForm::label('noOfParking', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="number"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               data-column="propertyNumberOfParkings"
                               name="properties[<?php echo Property::$propertyIndex; ?>][noOfParking]"
                               id="noOfParking"
                               class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'noOfParking', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                               tabindex="<?php echo Property::$tabIndex++; ?>"
                               value="<?php echo $propertyNoOfParking; ?>"
                               autocomplete="off">
                    <?php } else { ?>
                        <b><?php echo $propertyNoOfParking; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>
        <!--//# of Parking end MK//-->


        <!-- # of Buildings start MK -->
        <div class="propchar  noOfBuildings col-md-6 noOfBuildings_disp <?php echo loanForm::showField('noOfBuildings'); ?>">
            <div class="row form-group"><?php echo loanForm::label('noOfBuildings', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="number"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               data-column="propertyNumberOfBuildings"
                               name="properties[<?php echo Property::$propertyIndex; ?>][noOfBuildings]"
                               id="noOfBuildings"
                               class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'noOfBuildings', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                               value="<?php echo $propertyNoOfBuildings; ?>"
                               size="10"
                               maxlength="13"
                               TABINDEX="<?php echo Property::$tabIndex++; ?>"
                               autocomplete="off"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'noOfBuildings', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>>
                    <?php } else { ?>
                        <b><?php echo $propertyNoOfBuildings; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>
        <!-- # of Buildings end MK -->


        <div class="propchar col-md-6  propertyIsPool_disp <?php echo loanForm::showField('propertyIsPool'); ?>">
            <div class="row form-group ">
                <?php echo loanForm::label('propertyIsPool', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="radio-inline">
                            <label class="radio radio-solid font-weight-bold"
                                   for="propertyIsPoolYes_<?php echo Property::$propertyIndex; ?>">
                                <input type="radio"
                                       data-table="tblPropertiesCharacteristics"
                                       data-id="<?php echo $propChars->id; ?>"
                                       data-column="propertyIsPool"
                                       data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                       name="properties[<?php echo Property::$propertyIndex; ?>][propertyIsPool]"
                                       id="propertyIsPoolYes_<?php echo Property::$propertyIndex; ?>"
                                       value="1"
                                       class="autosavePropertyCharacteristics <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsPool', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsPool', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>
                                       tabindex="<?php echo Property::$tabIndex++; ?>"
                                    <?php echo Strings::isChecked('1', $propChars->propertyIsPool); ?>/>
                                <span></span>
                                Yes
                            </label>
                            <label class="radio radio-solid font-weight-bold"
                                   for="propertyIsPoolNo_<?php echo Property::$propertyIndex; ?>">
                                <input type="radio"
                                       data-table="tblPropertiesCharacteristics"
                                       data-id="<?php echo $propChars->id; ?>"
                                       data-column="propertyIsPool"
                                       data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                       name="properties[<?php echo Property::$propertyIndex; ?>][propertyIsPool]"
                                       id="propertyIsPoolNo_<?php echo Property::$propertyIndex; ?>"
                                       value="0"
                                       class="autosavePropertyCharacteristics <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsPool', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsPool', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>
                                       tabindex="<?php echo Property::$tabIndex++; ?>"
                                    <?php echo Strings::isChecked('0', $propChars->propertyIsPool); ?>/>
                                <span></span>
                                No
                            </label>
                        </div>
                    <?php } else { ?>
                        <h5><?php echo Strings::booleanTextVal($propChars->propertyIsPool); ?></h5>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propchar  col-md-6  futurePropertyType_disp <?php echo loanForm::showField('futurePropertyType'); ?>">
            <div class="row form-group"><?php echo loanForm::label('futurePropertyType', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <select name="properties[<?php echo Property::$propertyIndex; ?>][futurePropertyType]"
                                id="futurePropertyType"
                                data-table="tblPropertiesCharacteristics"
                                data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                data-id="<?php echo $propChars->id; ?>"
                                data-column="futurePropertyType"
                                class="autosavePropertyCharacteristics chzn-select form-control input-sm"
                                data-placeholder="Please Select"
                                tabindex="<?php echo Property::$tabIndex++; ?>">
                            <option value=""></option>
                            <?php foreach (Property::$propertyFuturePropertyType as $eachKey => $eachVal) {
                                if (sizeof(LMRequest::$fileCustomLoanGuidelineFuturePropertyTypes) && !in_array($eachKey, LMRequest::$fileCustomLoanGuidelineFuturePropertyTypes)) {
                                    continue;
                                }
                                $eachKey = intval(trim($eachKey));
                                if ($eachKey == 1000) {
                                    echo "<option disabled style=\"color:white;background-color: rgb(0, 130, 187);\">---Residential---</option>\n";
                                } else if ($eachKey == 1001) {
                                    echo "<option disabled style=\"color:white;background-color: rgb(0, 130, 187);\">---Commercial---</option>\n";
                                } else {
                                    echo '<option value="' . $eachKey . '" ' . Arrays::isSelected($eachKey, $futurePropertyType) . '>' . $eachVal . '</option>';
                                }
                            }
                            if($futurePropertyType && !in_array($futurePropertyType, LMRequest::$fileCustomLoanGuidelineFuturePropertyTypes)) {
                                echo '<option class="text-danger" value="' . $futurePropertyType . '" selected>' . GpropertyTypeNumbArray::$GpropertyTypeNumbArray[$futurePropertyType] . '</option>';
                            } ?>
                        </select>
                    <?php } else { ?>
                        <b><?php echo Property::$propertyFuturePropertyType[$futurePropertyType]; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>


        <div class="propchar  col-md-6 propertyFutureNumberOfBedRooms_disp <?php echo loanForm::showField('propertyFutureNumberOfBedRooms'); ?>">
            <div class="row form-group">
                <?php echo loanForm::label('propertyFutureNumberOfBedRooms', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="text"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               data-column="propertyFutureNumberOfBedRooms"
                               name="properties[<?php echo Property::$propertyIndex; ?>][propertyFutureNumberOfBedRooms]"
                               id="propertyFutureNumberOfBedRooms"
                               value="<?php echo $propChars->propertyFutureNumberOfBedRooms; ?>"
                               min="0"
                               max="99"
                               size="2"
                               maxlength="2"
                               TABINDEX="<?php echo Property::$tabIndex++; ?>"
                               autocomplete="off"
                               onkeyup="return restrictAlphabetsLoanTerms(this)"
                               class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyFutureNumberOfBedRooms', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyFutureNumberOfBedRooms', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                    <?php } else { ?>
                        <b><?php echo $propChars->propertyFutureNumberOfBedRooms; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propchar  col-md-6 propertyFutureNumberOfBathRooms_disp <?php echo loanForm::showField('propertyFutureNumberOfBathRooms'); ?>">
            <div class="row form-group">
                <?php echo loanForm::label('propertyFutureNumberOfBathRooms', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="text"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               data-column="propertyFutureNumberOfBathRooms"
                               name="properties[<?php echo Property::$propertyIndex; ?>][propertyFutureNumberOfBathRooms]"
                               id="propertyFutureNumberOfBathRooms"
                               value="<?php echo $propChars->propertyFutureNumberOfBathRooms; ?>"
                               min="0"
                               max="99"
                               size="2"
                               maxlength="2"
                               TABINDEX="<?php echo Property::$tabIndex++; ?>"
                               autocomplete="off"
                               onkeyup="return restrictAlphabetsLoanTerms(this)"
                               class="autosavePropertyCharacteristics  form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyFutureNumberOfBathRooms', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyFutureNumberOfBathRooms', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                    <?php } else { ?>
                        <b><?php echo $propChars->propertyFutureNumberOfBathRooms; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propchar  col-md-6 propertyFutureNumberOfHalfBathRooms_disp <?php echo loanForm::showField('propertyFutureNumberOfHalfBathRooms'); ?>">
            <div class="row form-group">
                <?php echo loanForm::label('propertyFutureNumberOfHalfBathRooms', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="text"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               data-column="propertyFutureNumberOfHalfBathRooms"
                               name="properties[<?php echo Property::$propertyIndex; ?>][propertyFutureNumberOfHalfBathRooms]"
                               id="propertyFutureNumberOfHalfBathRooms"
                               value="<?php echo $propChars->propertyFutureNumberOfHalfBathRooms; ?>"
                               min="0"
                               max="99"
                               size="2"
                               maxlength="2"
                               TABINDEX="<?php echo Property::$tabIndex++; ?>"
                               autocomplete="off"
                               onkeyup="return restrictAlphabetsLoanTerms(this)"
                               class="autosavePropertyCharacteristics  form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyFutureNumberOfHalfBathRooms', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyFutureNumberOfHalfBathRooms', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                    <?php } else { ?>
                        <b><?php echo $propChars->propertyFutureNumberOfHalfBathRooms; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>


        <div class="propchar col-md-6  propertyIsMidConstruction_disp <?php echo loanForm::showField('propertyIsMidConstruction'); ?>">
            <div class="row form-group ">
                <?php echo loanForm::label('propertyIsMidConstruction', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="radio-inline">
                            <label class="radio radio-solid font-weight-bold"
                                   for="propertyIsMidConstructionYes_<?php echo Property::$propertyIndex; ?>">
                                <input type="radio"
                                       data-table="tblPropertiesCharacteristics"
                                       data-id="<?php echo $propChars->id; ?>"
                                       data-column="propertyIsMidConstruction"
                                       data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                       name="properties[<?php echo Property::$propertyIndex; ?>][propertyIsMidConstruction]"
                                       id="propertyIsMidConstructionYes_<?php echo Property::$propertyIndex; ?>"
                                       value="1"
                                       onclick="propertyCharacteristics.showHideDiv('1','propertyConstructionOfPercentage_disp');"
                                       class="autosavePropertyCharacteristics <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsMidConstruction', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsMidConstruction', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>
                                       tabindex="<?php echo Property::$tabIndex++; ?>"
                                    <?php echo Strings::isChecked('1', $propChars->propertyIsMidConstruction); ?>/>
                                <span></span>
                                Yes
                            </label>
                            <label class="radio radio-solid font-weight-bold"
                                   for="propertyIsMidConstructionNo_<?php echo Property::$propertyIndex; ?>">
                                <input type="radio"
                                       data-table="tblPropertiesCharacteristics"
                                       data-id="<?php echo $propChars->id; ?>"
                                       data-column="propertyIsMidConstruction"
                                       data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                       name="properties[<?php echo Property::$propertyIndex; ?>][propertyIsMidConstruction]"
                                       id="propertyIsMidConstructionNo_<?php echo Property::$propertyIndex; ?>"
                                       value="0"
                                       onclick="propertyCharacteristics.showHideDiv('0','propertyConstructionOfPercentage_disp');"
                                       class="autosavePropertyCharacteristics <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsMidConstruction', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsMidConstruction', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>
                                       tabindex="<?php echo Property::$tabIndex++; ?>"
                                    <?php echo Strings::isChecked('0', $propChars->propertyIsMidConstruction); ?>/>
                                <span></span>
                                No
                            </label>
                        </div>
                    <?php } else { ?>
                        <h5><?php echo Strings::booleanTextVal($propChars->propertyIsMidConstruction); ?></h5>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propchar  col-md-6 propertyConstructionOfPercentage_disp <?php echo BaseHTML::parentFieldAccess(['fNm' => 'propertyIsMidConstruction', 'sArr' => Property::$secArr, 'pv' => $propChars->propertyIsMidConstruction, 'av' => '1']); ?>
<?php echo loanForm::showField('propertyConstructionOfPercentage'); ?>">
            <div class="row form-group"><?php echo loanForm::label('propertyConstructionOfPercentage', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="input-group">
                            <input type="number"
                                   data-table="tblPropertiesCharacteristics"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo $propChars->id; ?>"
                                   data-column="propertyConstructionOfPercentage"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][propertyConstructionOfPercentage]"
                                   id="propertyConstructionOfPercentage"
                                   value="<?php echo $propChars->propertyConstructionOfPercentage; ?>"
                                   size="10"
                                   maxlength="13"
                                   TABINDEX="<?php echo Property::$tabIndex++; ?>"
                                   autocomplete="off"
                                   onkeyup="validatePercentage(this);"
                                   class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyConstructionOfPercentage', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyConstructionOfPercentage', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                            <div class="input-group-append">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                    <?php } else { ?>
                        <b><?php echo $propChars->propertyConstructionOfPercentage; ?>
                            %</b>
                    <?php } ?>
                </div>
            </div>
        </div>


        <!-- Stabilized Rate start MK -->
        <div class="propchar  stabilizedRate col-md-6 stabilizedRate_disp <?php echo loanForm::showField('stabilizedRate'); ?>">
            <div class="row form-group"><?php echo loanForm::label('stabilizedRate', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="text"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               data-column="propertyStabilizedRate"
                               name="properties[<?php echo Property::$propertyIndex; ?>][stabilizedRate]"
                               id="stabilizedRate"
                               value="<?php echo number_format(Strings::replaceCommaValues($propertyStabilizedRate), 3); ?>"
                               size="10"
                               maxlength="13"
                               TABINDEX="<?php echo Property::$tabIndex++; ?>"
                               autocomplete="off"
                               class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'stabilizedRate', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'stabilizedRate', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                    <?php } else { ?>
                        <b><?php echo number_format(Strings::replaceCommaValues($propertyStabilizedRate), 3); ?>
                            &nbsp;%</b>
                    <?php } ?>
                </div>
            </div>
        </div>
        <!-- Stabilized Rate end MK -->

        <!-- # of Units Occupied start MK -->
        <div class="propchar  noUnitsOccupied col-md-6 noUnitsOccupied_disp <?php echo loanForm::showField('noUnitsOccupied'); ?>">
            <div class="row form-group"><?php echo loanForm::label('noUnitsOccupied', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="number"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               data-column="propertyNumberOfUnits"
                               name="properties[<?php echo Property::$propertyIndex; ?>][noUnitsOccupied]"
                               id="noUnitsOccupied"
                               value="<?php echo $propertyNoUnitsOccupied; ?>"
                               size="6"
                               maxlength="7"
                               TABINDEX="<?php echo Property::$tabIndex++; ?>"
                               autocomplete="off"
                               onchange="getReserveData()"
                               class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'noUnitsOccupied', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'noUnitsOccupied', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                    <?php } else { ?>
                        <b><?php echo $propertyNoUnitsOccupied; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propchar  futureNoOfUnits col-md-6 futureNoOfUnits_disp <?php echo loanForm::showField('futureNoOfUnits'); ?>">
            <div class="row form-group">
                <?php echo loanForm::label('futureNoOfUnits', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="number"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               data-column="futureNoOfUnits"
                               name="properties[<?php echo Property::$propertyIndex; ?>][futureNoOfUnits]"
                               id="futureNoOfUnits"
                               value="<?php echo $propChars->futureNoOfUnits; ?>"
                               size="6"
                               maxlength="7"
                               TABINDEX="<?php echo Property::$tabIndex++; ?>"
                               autocomplete="off"
                               onchange="getReserveData()"
                               class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'futureNoOfUnits', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'futureNoOfUnits', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                    <?php } else { ?>
                        <b><?php echo $propChars->futureNoOfUnits; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>


        <div class="propchar  propertyValue col-md-6 propertyValue_disp <?php echo loanForm::showField('propertyValue'); ?>">
            <div class="row form-group"><?php echo loanForm::label('propertyValue', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   data-table="tblPropertiesCharacteristics"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo $propChars->id; ?>"
                                   data-column="propertyValue"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][propertyValue]"
                                   id="propertyValue"
                                   placeholder="0.00"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($propertyPropertyValue); ?>"
                                   size="6"
                                   maxlength="7"
                                   TABINDEX="<?php echo Property::$tabIndex++; ?>"
                                   autocomplete="off"
                                   onblur="currencyConverter(this, this.value);"
                                   onkeyup='return numericValues(this,event)'
                                   class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyValue', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyValue', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                        </div>
                    <?php } else { ?>
                        <b><?php echo Currency::formatDollarAmountWithDecimal($propertyPropertyValue); ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propchar  noOfParcels col-md-6 noOfParcels_disp <?php echo loanForm::showField('noOfParcels'); ?>">
            <div class="row form-group"><?php echo loanForm::label('noOfParcels', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="number"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               data-column="propertyNumberOfParcels"
                               name="properties[<?php echo Property::$propertyIndex; ?>][noOfParcels]"
                               id="noOfParcels"
                               value="<?php echo $propertyNoOfParcels; ?>"
                               size="6"
                               maxlength="7"
                               TABINDEX="<?php echo Property::$tabIndex++; ?>"
                               autocomplete="off"
                               onkeyup="return onlyNumber(this,event)"
                               class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'noOfParcels', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'noOfParcels', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                    <?php } else { ?>
                        <b><?php echo $propertyNoOfParcels; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propchar  currentADU col-md-6 currentADU_disp <?php echo loanForm::showField('currentADU'); ?>">
            <div class="row form-group"><?php echo loanForm::label('currentADU', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="number"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo Property::$propertyInfo->getTblPropertiesCharacteristics_by_propertyId()->id; ?>"
                               data-column="propertyNumberOfParcels"
                               name="properties[<?php echo Property::$propertyIndex; ?>][currentADU]"
                               id="currentADU"
                               value="<?php echo Property::$propertyInfo->getTblPropertiesCharacteristics_by_propertyId()->currentADU; ?>"
                               size="6"
                               maxlength="7"
                               TABINDEX="<?php echo Property::$tabIndex++; ?>"
                               autocomplete="off"
                               onkeyup="return onlyNumber(this,event)"
                               class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'currentADU', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'currentADU', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                    <?php } else { ?>
                        <b><?php echo Property::$propertyInfo->getTblPropertiesCharacteristics_by_propertyId()->currentADU; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propchar  futureADU col-md-6 futureADU_disp <?php echo loanForm::showField('futureADU'); ?>">
            <div class="row form-group"><?php echo loanForm::label('futureADU', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="number"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo Property::$propertyInfo->getTblPropertiesCharacteristics_by_propertyId()->id; ?>"
                               data-column="propertyNumberOfParcels"
                               name="properties[<?php echo Property::$propertyIndex; ?>][futureADU]"
                               id="futureADU"
                               value="<?php echo Property::$propertyInfo->getTblPropertiesCharacteristics_by_propertyId()->futureADU; ?>"
                               size="6"
                               maxlength="7"
                               TABINDEX="<?php echo Property::$tabIndex++; ?>"
                               autocomplete="off"
                               onkeyup="return onlyNumber(this,event)"
                               class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'futureADU', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'futureADU', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                    <?php } else { ?>
                        <b><?php echo Property::$propertyInfo->getTblPropertiesCharacteristics_by_propertyId()->futureADU; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propchar  ownerOccupancyPercentage col-md-6 ownerOccupancyPercentage_disp <?php echo loanForm::showField('ownerOccupancyPercentage'); ?>">
            <div class="row form-group"><?php echo loanForm::label('ownerOccupancyPercentage', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="text"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               data-column="propertyOwnerOccupancy"
                               name="properties[<?php echo Property::$propertyIndex; ?>][ownerOccupancyPercentage]"
                               id="ownerOccupancyPercentage"
                               placeholder="0.0"
                               value="<?php echo $propertyOwnerOccupancyPercentage; ?>"
                               size="10"
                               maxlength="13"
                               TABINDEX="<?php echo Property::$tabIndex++; ?>"
                               autocomplete="off"
                               class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'ownerOccupancyPercentage', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'ownerOccupancyPercentage', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                    <?php } else { ?>
                        <b><?php echo $propertyOwnerOccupancyPercentage; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propchar  col-md-6  currentCommercialTenantOccupancyRate currentCommercialTenantOccupancyRate_disp <?php echo loanForm::showField('currentCommercialTenantOccupancyRate'); ?>">
            <div class="row form-group"><?php echo loanForm::label('currentCommercialTenantOccupancyRate', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="text"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               data-column="propertyCommercialTenantOccupancyRate"
                               name="properties[<?php echo Property::$propertyIndex; ?>][currentCommercialTenantOccupancyRate]"
                               placeholder="0.00"
                               id="currentCommercialTenantOccupancyRate"
                               class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'currentCommercialTenantOccupancyRate', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                               tabindex="<?php echo Property::$tabIndex++; ?>"
                               value="<?php echo $propertyCurrentCommercialTenantOccupancyRate; ?>"
                               autocomplete="off">
                    <?php } else { ?>
                        <b><?php echo $propertyCurrentCommercialTenantOccupancyRate; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propchar  col-md-6  anchorTenant anchorTenant_disp <?php echo loanForm::showField('anchorTenant'); ?>">
            <div class="row form-group"><?php echo loanForm::label('anchorTenant', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="radio-inline">
                            <label class="radio radio-solid font-weight-bold"
                                   for="anchorTenantYes_<?php echo Property::$propertyIndex; ?>">
                                <input type="radio"
                                       data-table="tblPropertiesCharacteristics"
                                       data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                       data-id="<?php echo $propChars->id; ?>"
                                       data-column="propertyIsAnchorTenant"
                                       name="properties[<?php echo Property::$propertyIndex; ?>][anchorTenant]"
                                       tabindex="<?php echo Property::$tabIndex++; ?>"
                                       id="anchorTenantYes_<?php echo Property::$propertyIndex; ?>"
                                       class="autosavePropertyCharacteristics <?php echo BaseHTML::fieldAccess(['fNm' => 'anchorTenant', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                       value="1"
                                       onchange="Property.AnchorTenant(this);"
                                    <?php echo Strings::isChecked('1', $propertyAnchorTenant); ?>>
                                <span></span>
                                Yes
                            </label>
                            <label class="radio radio-solid font-weight-bold"
                                   for="anchorTenantNo_<?php echo Property::$propertyIndex; ?>">
                                <input type="radio"
                                       data-table="tblPropertiesCharacteristics"
                                       data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                       data-id="<?php echo $propChars->id; ?>"
                                       data-column="propertyIsAnchorTenant"
                                       name="properties[<?php echo Property::$propertyIndex; ?>][anchorTenant]"
                                       tabindex="<?php echo Property::$tabIndex++; ?>"
                                       id="anchorTenantNo_<?php echo Property::$propertyIndex; ?>"
                                       class="autosavePropertyCharacteristics <?php echo BaseHTML::fieldAccess(['fNm' => 'anchorTenant', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                       value="0"
                                       onchange="Property.AnchorTenant(this);"
                                    <?php echo Strings::isChecked('0', $propertyAnchorTenant); ?> >
                                <span></span>
                                No
                            </label>
                        </div>
                    <?php } else { ?>
                        <b><?php echo Strings::booleanTextVal($propertyAnchorTenant); ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>


        <div class="propchar  col-md-6  anchorTenantName anchorTenantName_disp     <?php echo BaseHTML::parentFieldAccess(['fNm' => 'anchorTenant', 'sArr' => Property::$secArr, 'pv' => $propertyAnchorTenant, 'av' => '1']); ?> <?php echo loanForm::showField('anchorTenantName'); ?>">
            <div class="row form-group">
                <?php echo loanForm::label('anchorTenantName', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="text"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               data-column="propertyAnchorTenantName"
                               name="properties[<?php echo Property::$propertyIndex; ?>][anchorTenantName]"
                               id="anchorTenantName"
                               class="autosavePropertyCharacteristics form-control input-sm"
                               tabindex="<?php echo Property::$tabIndex++; ?>"
                               value="<?php echo htmlspecialchars($propertyAnchorTenantName); ?>"
                               autocomplete="off">
                    <?php } else { ?>
                        <b><?php echo $propertyAnchorTenantName; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>
        <!--// Anchor Tenant Name end MK //-->

        <!--  what % is CRE & Resi. start MK -->
        <div class="propchar  col-md-6  cre cre_disp <?php echo loanForm::showField('cre'); ?>">
            <div class="row form-group"><?php echo loanForm::label('cre', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="text"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               data-column="propertyCRE"
                               name="properties[<?php echo Property::$propertyIndex; ?>][cre]"
                               id="cre"
                               placeholder="0.0"
                               class="autosavePropertyCharacteristics form-control input-sm creresical <?php echo BaseHTML::fieldAccess(['fNm' => 'cre', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                               tabindex="<?php echo Property::$tabIndex++; ?>"
                               value="<?php echo $propertyCre; ?>"
                               autocomplete="off">
                    <?php } else { ?>
                        <b><?php echo $propertyCre; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="col-md-6 resi resi_disp <?php echo loanForm::showField('resi'); ?>">
            <div class="row form-group"><?php echo loanForm::label('resi', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="text"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               data-column="propertyRESI"
                               name="properties[<?php echo Property::$propertyIndex; ?>][resi]"
                               id="resi"
                               placeholder="0.0"
                               class="autosavePropertyCharacteristics form-control input-sm creresical <?php echo BaseHTML::fieldAccess(['fNm' => 'resi', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                               tabindex="<?php echo Property::$tabIndex++; ?>"
                               value="<?php echo $propertyResi; ?>"
                               autocomplete="off">
                    <?php } else { ?>
                        <b><?php echo $propertyResi; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>
        <!--//what % is CRE & Resi. end MK //-->

        <!-- Property Class start MK-->
        <div class="propchar  col-md-6  propertyClass propertyClass_disp">
            <div class="row form-group"><?php echo loanForm::label('propertyClass', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <select name="properties[<?php echo Property::$propertyIndex; ?>][propertyClass]"
                                data-table="tblPropertiesCharacteristics"
                                data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                data-id="<?php echo $propChars->id; ?>"
                                data-column="propertyClass"
                                id="propertyClass"
                                class="autosavePropertyCharacteristics form-control input-sm chzn-select"
                                data-placeholder="Please Select"
                                tabindex="<?php echo Property::$tabIndex++; ?>">
                            <option value=""></option>
                            <option value="classA" <?php echo ($propertyClass == 'classA') ? 'selected' : ''; ?> >
                                Class
                                A
                            </option>
                            <option value="classB" <?php echo ($propertyClass == 'classB') ? 'selected' : ''; ?> >
                                Class
                                B
                            </option>
                            <option value="classC" <?php echo ($propertyClass == 'classC') ? 'selected' : ''; ?> >
                                Class
                                C
                            </option>
                        </select>
                    <?php } else { ?>
                        <b><?php echo $propertyClass ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>
        <!--// Property Class end MK //-->

        <!--no of stories start MK-->
        <div class="propchar  col-md-6  addNoOfStories addNoOfStories_disp <?php echo loanForm::showField('addNoOfStories'); ?>">
            <div class="row form-group"><?php echo loanForm::label('addNoOfStories', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="number"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               data-column="propertyNumberOfStories"
                               name="properties[<?php echo Property::$propertyIndex; ?>][addNoOfStories]"
                               id="addNoOfStories"
                               class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'addNoOfStories', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                               tabindex="<?php echo Property::$tabIndex++; ?>"
                               value="<?php echo $propertyAddNoOfStories; ?>"
                               autocomplete="off"
                               pattern="\d*"
                               maxlength="2"
                               oninvalid="setCustomValidity('Please enter 2 digit valid numbers')"
                               onchange="try{setCustomValidity('')}catch(e){}">
                    <?php } else { ?>
                        <b><?php echo $propertyAddNoOfStories; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propchar  col-md-6  leaseType leaseType_disp <?php echo loanForm::showField('leaseType'); ?>">
            <div class="row form-group"><?php echo loanForm::label('leaseType', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <select name="properties[<?php echo Property::$propertyIndex; ?>][leaseType]"
                                data-table="tblPropertiesCharacteristics"
                                data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                data-id="<?php echo $propChars->id; ?>"
                                data-column="propertyLeaseType"
                                id="leaseType"
                                class="autosavePropertyCharacteristics form-control input-sm chzn-select <?php echo BaseHTML::fieldAccess(['fNm' => 'leaseType', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                data-placeholder="Please Select"
                                tabindex="<?php echo Property::$tabIndex++; ?>">
                            <option value=""></option>
                            <option value="Gross" <?php echo ($propertyLeaseType == 'Gross') ? 'selected' : ''; ?>>
                                Gross
                            </option>
                            <option value="Net" <?php echo ($propertyLeaseType == 'Net') ? 'selected' : ''; ?>>
                                Net
                            </option>
                            <option value="Mixed" <?php echo ($propertyLeaseType == 'Mixed') ? 'selected' : ''; ?>>
                                Mixed
                            </option>
                            <option value="TripleNet" <?php echo ($propertyLeaseType == 'TripleNet') ? 'selected' : ''; ?>>
                                Triple Net
                            </option>
                            <option value="DoubleNet" <?php echo ($propertyLeaseType == 'DoubleNet') ? 'selected' : ''; ?>>
                                Double Net
                            </option>
                            <option value="ModifiedGross" <?php echo ($propertyLeaseType == 'ModifiedGross') ? 'selected' : ''; ?>>
                                Modified Gross
                            </option>
                        </select>
                    <?php } else { ?>
                        <b><?php echo $propertyLeaseType ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>
        <!--// Lease Type end MK//-->


        <div class="propchar  col-md-6   propertyLeaseValidation_disp <?php echo loanForm::showField('propertyLeaseValidation'); ?>">
            <div class="row form-group"><?php echo loanForm::label('propertyLeaseValidation', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <select name="properties[<?php echo Property::$propertyIndex; ?>][propertyLeaseValidation]"
                                id="propertyLeaseValidation"
                                data-table="tblPropertiesCharacteristics"
                                data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                data-id="<?php echo $propChars->id; ?>"
                                data-column="propertyLeaseValidation"
                                class="autosavePropertyCharacteristics form-control input-sm chzn-select <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyLeaseValidation', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                data-placeholder="Please Select"
                                onchange="propertyCharacteristics.showAndHideLeaseValidationOtherDiv(this.value,'propertyLeaseValidationOther_disp');"
                                tabindex="<?php echo Property::$tabIndex++; ?>">
                            <option value=""></option>
                            <?php foreach (Property::$propertyLeaseValidationList as $eachKey => $eachVal) { ?>
                                <option value="<?php echo $eachKey; ?>"
                                    <?php echo Arrays::isSelected($eachKey, $propChars->propertyLeaseValidation); ?>>
                                    <?php echo $eachVal; ?></option>
                            <?php } ?>
                        </select>
                    <?php } else { ?>
                        <b>
                            <?php echo Property::$propertyLeaseValidationList[$propChars->propertyLeaseValidation]; ?>
                        </b>
                    <?php } ?>
                </div>
            </div>
        </div>


        <div class="propchar  col-md-6   propertyLeaseValidationOther_disp <?php echo BaseHTML::parentFieldAccess(['fNm' => 'propertyLeaseValidation', 'sArr' => Property::$secArr, 'pv' => $propChars->propertyLeaseValidation, 'av' => '4']); ?>  <?php echo loanForm::showField('propertyLeaseValidationOther'); ?>">
            <div class="row form-group">
                <?php echo loanForm::label('propertyLeaseValidationOther', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="text"
                               name="properties[<?php echo Property::$propertyIndex; ?>][propertyLeaseValidationOther]"
                               id="propertyLeaseValidationOther"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               data-column="propertyLeaseValidationOther"
                               class="autosavePropertyCharacteristics form-control input-sm "
                               value="<?php echo $propChars->propertyLeaseValidationOther; ?>"
                               tabindex="<?php echo Property::$tabIndex++; ?>"/>
                    <?php } else { ?>
                        <b><?php echo $propChars->propertyLeaseValidationOther; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propchar  col-md-6   propertyCurrentlyLeased_disp <?php echo loanForm::showField('propertyCurrentlyLeased'); ?>">
            <div class="row form-group"><?php echo loanForm::label('propertyCurrentlyLeased', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <select name="properties[<?php echo Property::$propertyIndex; ?>][propertyCurrentlyLeased]"
                                id="propertyCurrentlyLeased"
                                data-table="tblPropertiesCharacteristics"
                                data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                data-id="<?php echo $propChars->id; ?>"
                                data-column="propertyCurrentlyLeased"
                                class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyCurrentlyLeased', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                data-placeholder="Please Select"
                                tabindex="<?php echo Property::$tabIndex++; ?>">
                            <option value="">--select---</option>
                            <?php foreach (Property::getPropertyCurrentlyLeasedList(LMRequest::$PCID) as $eachKey => $eachVal) { ?>
                                <option value="<?php echo $eachKey; ?>"
                                    <?php echo Arrays::isSelected($eachKey, $propChars->propertyCurrentlyLeased); ?>>
                                    <?php echo $eachVal; ?></option>
                            <?php } ?>
                        </select>
                    <?php } else { ?>
                        <b>
                            <?php echo Property::getPropertyCurrentlyLeasedList(LMRequest::$PCID)[$propChars->propertyCurrentlyLeased]; ?>
                        </b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propchar col-md-6  propertyIsPlannedUnitDevelopment_disp <?php echo loanForm::showField('propertyIsPlannedUnitDevelopment'); ?>">
            <div class="row form-group ">
                <?php echo loanForm::label('propertyIsPlannedUnitDevelopment', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="radio-inline">
                            <label class="radio radio-solid font-weight-bold"
                                   for="propertyIsPlannedUnitDevelopmentYes_<?php echo Property::$propertyIndex; ?>">
                                <input type="radio"
                                       data-table="tblPropertiesCharacteristics"
                                       data-id="<?php echo $propChars->id; ?>"
                                       data-column="propertyIsPlannedUnitDevelopment"
                                       data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                       name="properties[<?php echo Property::$propertyIndex; ?>][propertyIsPlannedUnitDevelopment]"
                                       id="propertyIsPlannedUnitDevelopmentYes_<?php echo Property::$propertyIndex; ?>"
                                       value="1"
                                       onclick="propertyCharacteristics.showHideDiv('1','propertyPUDName_disp');"
                                       class="autosavePropertyCharacteristics <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsPlannedUnitDevelopment', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsPlannedUnitDevelopment', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>
                                       tabindex="<?php echo Property::$tabIndex++; ?>"
                                    <?php echo Strings::isChecked('1', $propChars->propertyIsPlannedUnitDevelopment); ?>/>
                                <span></span>
                                Yes
                            </label>
                            <label class="radio radio-solid font-weight-bold"
                                   for="propertyIsPlannedUnitDevelopmentNo_<?php echo Property::$propertyIndex; ?>">
                                <input type="radio"
                                       data-table="tblPropertiesCharacteristics"
                                       data-id="<?php echo $propChars->id; ?>"
                                       data-column="propertyIsPlannedUnitDevelopment"
                                       data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                       name="properties[<?php echo Property::$propertyIndex; ?>][propertyIsPlannedUnitDevelopment]"
                                       id="propertyIsPlannedUnitDevelopmentNo_<?php echo Property::$propertyIndex; ?>"
                                       value="0"
                                       onclick="propertyCharacteristics.showHideDiv('0','propertyPUDName_disp');"
                                       class="autosavePropertyCharacteristics <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsPlannedUnitDevelopment', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsPlannedUnitDevelopment', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>
                                       tabindex="<?php echo Property::$tabIndex++; ?>"
                                    <?php echo Strings::isChecked('0', $propChars->propertyIsPlannedUnitDevelopment); ?>/>
                                <span></span>
                                No
                            </label>
                        </div>
                    <?php } else { ?>
                        <h5><?php echo Strings::booleanTextVal($propChars->propertyIsPlannedUnitDevelopment); ?></h5>
                    <?php } ?>
                </div>
            </div>
        </div>


        <div class="propchar  col-md-6   propertyPUDName_disp <?php echo BaseHTML::parentFieldAccess(['fNm' => 'propertyIsPlannedUnitDevelopment', 'sArr' => Property::$secArr, 'pv' => $propChars->propertyIsPlannedUnitDevelopment, 'av' => '1']); ?> <?php echo loanForm::showField('propertyPUDName'); ?>">
            <div class="row form-group">
                <?php echo loanForm::label('propertyPUDName', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="text"
                               name="properties[<?php echo Property::$propertyIndex; ?>][propertyPUDName]"
                               id="propertyPUDName"
                               data-table="tblPropertiesCharacteristics"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo $propChars->id; ?>"
                               data-column="propertyPUDName"
                               class="autosavePropertyCharacteristics form-control input-sm "
                               value="<?php echo $propChars->propertyPUDName; ?>"
                               tabindex="<?php echo Property::$tabIndex++; ?>"/>
                    <?php } else { ?>
                        <b><?php echo $propChars->propertyPUDName; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propchar  col-md-6   propertyMarketRents_disp <?php echo loanForm::showField('propertyMarketRents'); ?>">
            <div class="row form-group">
                <?php echo loanForm::label('propertyMarketRents', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][propertyMarketRents]"
                                   id="propertyMarketRents"
                                   data-table="tblPropertiesCharacteristics"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo $propChars->id; ?>"
                                   data-column="propertyMarketRents"
                                   class="autosavePropertyCharacteristics form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyMarketRents', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($propChars->propertyMarketRents); ?>"
                                   onkeyup="return restrictAlphabetsLoanTerms(this)"
                                   onblur="currencyConverter(this, this.value);"
                                   tabindex="<?php echo Property::$tabIndex++; ?>"/>
                        </div>
                    <?php } else { ?>
                        <b><?php echo Currency::formatDollarAmountWithDecimal($propChars->propertyMarketRents); ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>


    <?php } ?>

</div>
