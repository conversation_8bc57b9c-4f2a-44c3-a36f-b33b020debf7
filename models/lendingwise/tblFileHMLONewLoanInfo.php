<?php

namespace models\lendingwise;

use models\composite\debtServiceRatioPITIA;
use models\composite\proposalFormula;
use models\constants\accrualTypes;
use models\constants\gl\glTypeOfHMLOLoanRequestingCahFlow;
use models\constants\typeOfHMLOLoanRequesting;
use models\Controllers\backoffice\LMRequest;
use models\Database2;
use models\lendingwise\db\tblFileHMLONewLoanInfo_db;
use models\servicing\LoanTerms;
use models\standard\Dates;
use models\standard\Strings;

/**
 *
 */
class tblFileHMLONewLoanInfo extends tblFileHMLONewLoanInfo_db
{

    public function Save(): array
    {
        $this->brokerQuotedinterestRate = Strings::replaceCommaValues($this->brokerQuotedinterestRate);
        $this->paymentFrequency = Strings::replaceCommaValues($this->paymentFrequency);

        if (is_null($this->recordDate)) {
            $this->recordDate = Dates::Timestamp();
        }

        if(!$this->interestChargedEndDate) {
            $this->interestChargedEndDate = null;
        }

        if(!$this->interestChargedFromDate) {
            $this->interestChargedFromDate = null;
        }

        if(!$this->rateLockDate) {
            $this->rateLockDate = null;
        }

        if(!$this->rateLockExpirationDate) {
            $this->rateLockExpirationDate = null;
        }

        if(!$this->resaleClosingDate) {
            $this->resaleClosingDate = null;
        }

        if(!$this->loanTermExpireDate) {
            $this->loanTermExpireDate = null;
        }

        if(!$this->originalPurchaseDate) {
            $this->originalPurchaseDate = null;
        }

        if(!$this->PAExpirationDate) {
            $this->PAExpirationDate = null;
        }

        return parent::Save();
    }

    /**
     * @param string $isLoanPaymentAmt
     * @return string
     */
    public static function getPaymentMethodName(string $isLoanPaymentAmt): string
    {
        switch ($isLoanPaymentAmt) {
            case LoanTerms::ILA:
                return $isLoanPaymentAmt . ': Current Loan Balance - Non-Dutch';
            case LoanTerms::TLA:
                return $isLoanPaymentAmt . ': Total Loan Amount - Dutch';
            case 'SMP':
                return $isLoanPaymentAmt . ': Manual Payment Amount';
        }
        return $isLoanPaymentAmt;
    }

    /**
     * @return array
     */
    public static function getPaymentMethods(): array
    {
        $sql = '
        SELECT
            isLoanPaymentAmt
        FROM tblFileHMLONewLoanInfo
        WHERE isLoanPaymentAmt <> \'\' AND isLoanPaymentAmt IS NOT NULL
        GROUP BY isLoanPaymentAmt
        ORDER BY isLoanPaymentAmt
        ';
        $res = self::queryData($sql);
        $list = [];
        foreach ($res as $row) {
            $list[$row['isLoanPaymentAmt']] = self::getPaymentMethodName($row['isLoanPaymentAmt']);
        }
        return $list;
    }

    /**
     * @param int $LMRId
     * @param string $isLoanPaymentAmt
     * @return void
     */
    public static function setPaymentMethod(int $LMRId, string $isLoanPaymentAmt)
    {
        $sql = '
        UPDATE tblFileHMLONewLoanInfo
        SET isLoanPaymentAmt = :isLoanPaymentAmt
        WHERE fileID = :LMRId
        ';
        $res = self::executeQuery($sql, [
            'isLoanPaymentAmt' => $isLoanPaymentAmt,
            'LMRId'            => $LMRId,
        ]);
        if ($res['error']) {
            Debug($res);
        }
    }

    public function vacancy(): float
    {
        return ($this->vacancyFactor * $this->actualRentsInPlace) / 100.0;
    }

    public function vacancyCommercial(): float
    {
        return ($this->vacancyFactorCommercial * $this->actualRentsInPlaceCommercial) / 100.0;
    }

    public function vacancyOtherIncome(): float
    {
        return ($this->otherIncomeVacancyRate * $this->otherIncome) / 100.0;
    }

    public function vacancyTenantContribution(): float
    {
        return ($this->tenantContributionVacancyRate * $this->tenantContribution) / 100.0;
    }

    public function effectiveGrossIncome(): ?float
    {
        return $this->actualRentsInPlace
            + $this->actualRentsInPlaceCommercial
            + $this->otherIncome
            + $this->tenantContribution
            - $this->vacancy()
            - $this->vacancyCommercial()
            - $this->vacancyOtherIncome()
            - $this->vacancyTenantContribution();
    }

    public function netOperatingIncome(): float
    {
        LMRequest::setLMRId($this->fileID);

        $netOperatingIncome = $this->actualRentsInPlace
            + $this->actualRentsInPlaceCommercial
            + $this->tenantContribution
            + $this->otherIncome
            - $this->vacancy()
            - $this->vacancyCommercial()
            - $this->vacancyOtherIncome()
            - $this->vacancyTenantContribution()
            - $this->lessActualExpenses
            - $this->waterSewer
            - $this->electricity
            - $this->gas
            - $this->repairsMaintenance
            - $this->legal
            - $this->payroll
            - $this->misc
            - $this->commonAreaUtilities
            - $this->elevatorMaintenance
            - $this->replacementReserves
            - $this->other
            - $this->tenantReimursements
            - $this->managementExpense
            - LMRequest::File()->getTblIncomeInfo_by_LMRId()->taxes1
            - LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->annualPremium
            - LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->spcf_hoafees;

        return round($netOperatingIncome, 2);
    }

    public function capRate(): float
    {
        LMRequest::setLMRId($this->fileID);

        $capRate = 0.0;
        $homeValueForCashFlow = LMRequest::File()->homeValue; // Strings::replaceCommaValues(Strings::showField('homeValue', 'LMRInfo'));
        $typeOfHMLOLoanRequestingCahFlow = LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->typeOfHMLOLoanRequesting; // Strings::showField('typeOfHMLOLoanRequesting', 'fileHMLOPropertyInfo');
        $costBasisCashFlow = LMRequest::File()->getTblShortSale_by_LMRId()->costBasis; // Strings::showField('costBasis', 'listingRealtorInfo');
        $netOperatingIncome = $this->netOperatingIncome();

        if ($netOperatingIncome > 0
            && $homeValueForCashFlow > 0
            && ($typeOfHMLOLoanRequestingCahFlow == glTypeOfHMLOLoanRequestingCahFlow::CONST_CF_Commercial_Cash_Out_Refinance
                || $typeOfHMLOLoanRequestingCahFlow == glTypeOfHMLOLoanRequestingCahFlow::CONST_CF_Commercial_Rate_Term_Refinance)
        ) {
            $capRate = ($netOperatingIncome / $homeValueForCashFlow) * 100.0;
        }

        if ($netOperatingIncome > 0
            && $costBasisCashFlow > 0
            && ($typeOfHMLOLoanRequestingCahFlow == glTypeOfHMLOLoanRequestingCahFlow::CONST_CF_Commercial_Purchase)) {
            $capRate = ($netOperatingIncome / $costBasisCashFlow) * 100.0;
        }

        return $capRate;
    }

    public function reserveValue()
    {
        LMRequest::setLMRId($this->fileID);

        $reserveFactoron = LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->reserveFactoron;

        $reserveValue = 0;
        if ($reserveFactoron == 1) {
            $reserveValue = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyRentableSqFt;
            if ($reserveValue == '') {
                $reserveValue = 0;
            }
        } elseif ($reserveFactoron == 2) {
            $reserveValue = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfUnits;
        }

        return $reserveValue;
    }

    public function reserves(): float
    {
        LMRequest::setLMRId($this->fileID);

        $reserveFactor = LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->reserveFactor;

        $reserveValue = $this->reserveValue();

        return $reserveFactor * $reserveValue;
    }

    public function rehabCostFinanced(): float
    {
        LMRequest::setLMRId($this->fileID);

        return proposalFormula::calculateRehabCostFinancedByPercentage(
            LMRequest::myFileInfo()->fileHMLOInfo()->rehabCost,
            $this->rehabCostPercentageFinanced
        );
    }

    public function acquisitionPriceFinanced(): ?float
    {
        LMRequest::setLMRId($this->fileID);

        return LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->initialLoanAmount;
    }

    public function prepaidInterestReserveForCal(): ?float
    {
        $prepaidInterestReserveForCal = $this->prepaidInterestReserve;

        if ($this->haveInterestreserve == 'No' || $this->haveInterestreserve == '') {
            $prepaidInterestReserveForCal = 0;
        }

        return $prepaidInterestReserveForCal;
    }

    public function totalMonthlyPayment(): float
    {
        LMRequest::setLMRId($this->fileID);

        $tempTotalLoanAmount = $this->totalLoanAmount;
        $typeOfHMLOLoanRequesting = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting;

        if ($typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::PURCHASE &&
            $typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::COMMERCIAL_PURCHASE) {
            $tempTotalLoanAmount = proposalFormula::calculateHMLOFeeCostTotalLoanAmount(
                $this->acquisitionPriceFinanced(),
                $this->rehabCostFinanced(),
                $this->closingCostFinanced,
                $this->payOffMortgage1,
                $this->payOffMortgage2,
                $this->payOffOutstandingTaxes,
                $this->payOffOtherOutstandingAmounts,
                $this->cashOutAmt,
                $this->typeOfHMLOLoanRequesting,
                $this->prepaidInterestReserveForCal()
            );
        }

        if ($this->isLoanPaymentAmt != 'SMP') {
            $totalMonthlyPayment = proposalFormula::calculateHMLOPaymentValue(
                $tempTotalLoanAmount,
                Strings::replaceCommaValues(LMRequest::File()->lien1Rate),
                LMRequest::File()->lien1Terms,
                LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->purchaseCloseDate,
                LMRequest::myFileInfo()->fileHMLOPropertyInfo()->perDiemAccrualType ?: accrualTypes::ACCRUAL_TYPE_30_360
            );
        } else {
            $totalMonthlyPayment = LMRequest::myFileInfo()->LMRData()->lien1Payment;
        }

        return Strings::replaceCommaValues($totalMonthlyPayment);
    }

    public function debtServiceRatio(): float
    {
        return proposalFormula::calculateDebtServiceRatio(
            $this->totalMonthlyPayment(),
            $this->netOperatingIncome()
        );
    }

    public function serviceDebt(): float
    {
        return $this->netOperatingIncome() - $this->reserves();
    }

    public function debtServiceRatioPITIA(): float
    {
        return debtServiceRatioPITIA::getReportParams(
            $this->effectiveGrossIncome(),
            $this->netMonthlyPayment(),
            $this->spcf_hoafees
        );
    }

    public function netMonthlyPayment(): float
    {
        LMRequest::setLMRId($this->fileID);

        return proposalFormula::calculateHMLONetMonthlyPayment(
            $this->totalMonthlyPayment(),
            LMRequest::File()->getTblIncomeInfo_by_LMRId()->taxes1,
            LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->annualPremium,
            $this->spcf_hoafees
        );
    }
}