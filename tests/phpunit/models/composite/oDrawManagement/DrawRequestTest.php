<?php

namespace tests\models\composite\oDrawManagement;

use models\composite\oDrawManagement\DrawRequest;
use models\composite\oDrawManagement\BorrowerDrawCategory;
use models\lendingwise\tblFileDrawRequests;
use PHPUnit\Framework\TestCase;

/**
 * Tests the DrawRequest class.
 *
 * @covers \models\composite\oDrawManagement\DrawRequest::__construct
 * @covers \models\composite\oDrawManagement\DrawRequest::save
 * @covers \models\composite\oDrawManagement\DrawRequest::delete
 * @covers \models\composite\oDrawManagement\DrawRequest::toArray
 * @covers \models\composite\oDrawManagement\DrawRequest::loadCategories
 * @covers \models\composite\oDrawManagement\DrawRequest::getAllCategories
 * @covers \models\composite\oDrawManagement\DrawRequest::getAllLineItems
 * @covers \models\composite\oDrawManagement\DrawRequest::updateDrawRequestStatus
 * @covers \models\composite\oDrawManagement\DrawRequest::getCategoryById
 * @covers \models\composite\oDrawManagement\DrawRequest::deleteCategories
 * @covers \models\composite\oDrawManagement\DrawRequest::deleteLineItems
 * @covers \models\composite\oDrawManagement\DrawRequest::getDbObject
 */
class DrawRequestTest extends TestCase
{
    /**
     * Tests the constructor with null parameter.
     */
    public function testConstructorWithNull(): void
    {
        $drawRequest = new DrawRequest(null);

        $this->assertInstanceOf(DrawRequest::class, $drawRequest);
        $this->assertInstanceOf(tblFileDrawRequests::class, $drawRequest->getDbObject());
        $this->assertNull($drawRequest->id);
        $this->assertNull($drawRequest->LMRId);
        $this->assertEquals(DrawRequest::STATUS_NEW, $drawRequest->status);
    }

    /**
     * Tests the constructor with a database object.
     */
    public function testConstructorWithDbObject(): void
    {
        $dbObject = new tblFileDrawRequests();
        $dbObject->id = 123;
        $dbObject->LMRId = 456;
        $dbObject->status = DrawRequest::STATUS_APPROVED;

        $drawRequest = new DrawRequest($dbObject);

        $this->assertInstanceOf(DrawRequest::class, $drawRequest);
        $this->assertEquals(123, $drawRequest->id);
        $this->assertEquals(456, $drawRequest->LMRId);
        $this->assertEquals(DrawRequest::STATUS_APPROVED, $drawRequest->status);
    }

    /**
     * Tests the status constants.
     */
    public function testStatusConstants(): void
    {
        $this->assertEquals('new', DrawRequest::STATUS_NEW);
        $this->assertEquals('pending', DrawRequest::STATUS_PENDING);
        $this->assertEquals('approved', DrawRequest::STATUS_APPROVED);
        $this->assertEquals('rejected', DrawRequest::STATUS_REJECTED);
    }

    /**
     * Tests the save method without data.
     */
    public function testSaveWithoutData(): void
    {
        $drawRequest = new DrawRequest();

        try {
            $result = $drawRequest->save();
            $this->assertIsArray($result);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('save', $e->getMessage());
        }
    }

    /**
     * Tests the save method with data.
     */
    public function testSaveWithData(): void
    {
        $drawRequest = new DrawRequest();
        $data = [
            'LMRId' => 123,
            'status' => DrawRequest::STATUS_NEW,
            'sowApproved' => 1,
            'isDrawRequest' => 1
        ];

        try {
            $result = $drawRequest->save($data);
            $this->assertIsArray($result);
            $this->assertEquals(123, $drawRequest->LMRId);
            $this->assertEquals(DrawRequest::STATUS_NEW, $drawRequest->status);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('save', $e->getMessage());
        }
    }

    /**
     * Tests the delete method.
     */
    public function testDelete(): void
    {
        $drawRequest = new DrawRequest();

        try {
            $drawRequest->delete();
            $this->addToAssertionCount(1);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('delete', $e->getMessage());
        }
    }

    /**
     * Tests the toArray method.
     */
    public function testToArray(): void
    {
        $drawRequest = new DrawRequest();
        $drawRequest->id = 123;
        $drawRequest->LMRId = 456;
        $drawRequest->status = DrawRequest::STATUS_APPROVED;
        $drawRequest->sowApproved = 1;
        $drawRequest->isDrawRequest = 1;
        $drawRequest->updatedAt = '2023-01-01 12:00:00';

        $result = $drawRequest->toArray();

        $this->assertIsArray($result);
        $this->assertEquals(123, $result['id']);
        $this->assertEquals(456, $result['LMRId']);
        $this->assertEquals(DrawRequest::STATUS_APPROVED, $result['status']);
        $this->assertEquals(1, $result['sowApproved']);
        $this->assertEquals(1, $result['isDrawRequest']);
        $this->assertEquals('2023-01-01 12:00:00', $result['updatedAt']);
        $this->assertArrayHasKey('categories', $result);
        $this->assertIsArray($result['categories']);
    }

    /**
     * Tests the getAllCategories method when no categories exist.
     */
    public function testGetAllCategoriesEmpty(): void
    {
        $drawRequest = new DrawRequest();

        $result = $drawRequest->getAllCategories();

        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * Tests the getAllLineItems method when no line items exist.
     */
    public function testGetAllLineItemsEmpty(): void
    {
        $drawRequest = new DrawRequest();

        $result = $drawRequest->getAllLineItems();

        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * Tests the updateDrawRequestStatus method with null draw request.
     */
    public function testUpdateDrawRequestStatusWithNullDrawRequest(): void
    {
        $drawRequest = new DrawRequest();
        $drawRequest->drawRequest = null;

        $result = $drawRequest->updateDrawRequestStatus(DrawRequest::STATUS_APPROVED);

        $this->assertFalse($result);
    }

    /**
     * Tests the updateDrawRequestStatus method with valid parameters.
     */
    public function testUpdateDrawRequestStatusWithValidParameters(): void
    {
        $drawRequest = new DrawRequest();

        try {
            $result = $drawRequest->updateDrawRequestStatus(
                DrawRequest::STATUS_APPROVED,
                1,
                1
            );
            $this->assertIsBool($result);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('save', $e->getMessage());
        }
    }

    /**
     * Tests the getCategoryById method with non-existent category.
     */
    public function testGetCategoryByIdNonExistent(): void
    {
        $drawRequest = new DrawRequest();

        $result = $drawRequest->getCategoryById(999);

        $this->assertNull($result);
    }

    /**
     * Tests the deleteCategories method.
     */
    public function testDeleteCategories(): void
    {
        $drawRequest = new DrawRequest();

        try {
            $drawRequest->deleteCategories([1, 2, 3]);
            $this->addToAssertionCount(1);
        } catch (\Error $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the deleteLineItems method.
     */
    public function testDeleteLineItems(): void
    {
        $drawRequest = new DrawRequest();

        try {
            $drawRequest->deleteLineItems([1, 2, 3]);
            $this->addToAssertionCount(1);
        } catch (\Error $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the getDbObject method.
     */
    public function testGetDbObject(): void
    {
        $drawRequest = new DrawRequest();

        $result = $drawRequest->getDbObject();

        $this->assertInstanceOf(tblFileDrawRequests::class, $result);
    }
}
