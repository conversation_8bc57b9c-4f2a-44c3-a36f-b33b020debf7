<?php

use models\Controllers\loanForm;
use models\standard\Currency;
use models\standard\Strings;

global $presentRent, $presentFirstMortgage, $presentOtherFinancing, $presentHazardInsurance, $presentRealEstateTaxes, $presentMortgageInsurance;
global $presentHomeownerAssnDues, $presentHomeownerAssnDues, $presentOther;
global $proposedRent, $proposedFirstMortgage, $proposedOtherFinancing, $proposedHazardInsurance, $proposedRealEstateTaxes, $proposedMortgageInsurance;
global $proposedHomeownerAssnDues, $proposedOther, $tabIndex;


$totalPresentTotalAmt = 0;
$totalProposedAmt = 0;
$totalPresentTotalAmt = Strings::replaceCommaValues($presentRent) +
    Strings::replaceCommaValues($presentFirstMortgage) +
    Strings::replaceCommaValues($presentOtherFinancing) +
    Strings::replaceCommaValues($presentHazardInsurance) +
    Strings::replaceCommaValues($presentRealEstateTaxes) +
    Strings::replaceCommaValues($presentMortgageInsurance) +
    Strings::replaceCommaValues($presentHomeownerAssnDues) +
    Strings::replaceCommaValues($presentOther);

$totalProposedAmt = Strings::replaceCommaValues($proposedRent) +
    Strings::replaceCommaValues($proposedFirstMortgage) +
    Strings::replaceCommaValues($proposedOtherFinancing) +
    Strings::replaceCommaValues($proposedHazardInsurance) +
    Strings::replaceCommaValues($proposedRealEstateTaxes) +
    Strings::replaceCommaValues($proposedMortgageInsurance) +
    Strings::replaceCommaValues($proposedHomeownerAssnDues) +
    Strings::replaceCommaValues($proposedOther);

?>


<div class="card card-custom expenses">
    <div class="card-header card-header-tabs-line bg-gray-100">
        <?php echo loanForm::toggleSectionV2('expenses', true, true, '', 'Expenses'); ?>
    </div>

    <div class="card-body expenses_body">

        <table>
            <tr class="columnHeading">
                <td></td>
                <td>
                    <h4>Present</h4>
                </td>
                <td>
                    <h4>Proposed</h4>
                </td>
            </tr>
            <tr>
                <td>
                    <?php echo loanForm::labelWithValue(
                        'presentRent',
                        $presentRent,
                        'Rent'
                    ); ?>
                </td>
                <td>
                    <?php echo loanForm::currency(
                        'presentRent',
                        true,
                        $tabIndex++,
                        $presentRent,
                        null,
                        'calculateTotLOPresentExpenses(this.value);',
                            '',
                    ); ?>

                </td>
                <td>
                    <?php echo loanForm::currency(
                        'proposedRent',
                        true,
                        $tabIndex++,
                        $proposedRent,
                        null,
                        'calculateTotLOProposedExpenses(this.value);',
                            '',
                    ); ?>
                </td>
            </tr>
            <tr>
                <td nowrap>
                    <?php echo loanForm::labelWithValue(
                        'presentFirstMortgage',
                        $presentFirstMortgage,
                        'First Mortgage (P&I)'
                    ); ?>
                </td>
                <td>
                    <?php echo loanForm::currency(
                        'presentFirstMortgage',
                        true,
                        $tabIndex++,
                        $presentFirstMortgage,
                        null,
                        'calculateTotLOPresentExpenses(this.value);',
                            '',
                    ); ?>

                </td>
                <td>
                    <?php echo loanForm::currency(
                        'proposedFirstMortgage',
                        true,
                        $tabIndex++,
                        $proposedFirstMortgage,
                        null,
                        'calculateTotLOProposedExpenses(this.value);',
                            '',
                    ); ?>
                </td>
            </tr>
            <tr>
                <td>
                    <?php echo loanForm::labelWithValue(
                        'presentOtherFinancing',
                        $presentOtherFinancing,
                        'Other Financing (P&I)'
                    ); ?>
                </td>
                <td>
                    <?php echo loanForm::currency(
                        'presentOtherFinancing',
                        true,
                        $tabIndex++,
                        $presentOtherFinancing,
                        null,
                        'calculateTotLOPresentExpenses(this.value);',
                            '',
                    ); ?>
                </td>
                <td>
                    <?php echo loanForm::currency(
                        'proposedOtherFinancing',
                        true,
                        $tabIndex++,
                        $proposedOtherFinancing,
                        null,
                        'calculateTotLOProposedExpenses(this.value);',
                            '',
                    ); ?>
                </td>
            </tr>
            <tr>
                <td>
                    <?php echo loanForm::labelWithValue(
                        'presentHazardInsurance',
                        $presentHazardInsurance,
                        'Hazard Insurance'
                    ); ?>
                </td>
                <td>
                    <?php echo loanForm::currency(
                        'presentHazardInsurance',
                        true,
                        $tabIndex++,
                        $presentHazardInsurance,
                        null,
                        'calculateTotLOPresentExpenses(this.value);',
                            '',
                    ); ?>

                </td>
                <td>
                    <?php echo loanForm::currency(
                        'proposedHazardInsurance',
                        true,
                        $tabIndex++,
                        $proposedHazardInsurance,
                        null,
                        'calculateTotLOProposedExpenses(this.value);',
                            '',
                    ); ?>
                </td>
            </tr>
            <tr>
                <td>
                    <?php echo loanForm::labelWithValue(
                        'presentRealEstateTaxes',
                        $presentRealEstateTaxes,
                        'Real Estate Taxes'
                    ); ?>
                </td>
                <td>

                    <?php echo loanForm::currency(
                        'presentRealEstateTaxes',
                        true,
                        $tabIndex++,
                        $presentRealEstateTaxes,
                        null,
                        'calculateTotLOPresentExpenses(this.value);',
                            '',
                    ); ?>
                </td>
                <td>
                    <?php echo loanForm::currency(
                        'proposedRealEstateTaxes',
                        true,
                        $tabIndex++,
                        $proposedRealEstateTaxes,
                        null,
                        'calculateTotLOProposedExpenses(this.value);',
                            '',
                    ); ?>
                </td>
            </tr>
            <tr>
                <td>
                    <?php echo loanForm::labelWithValue(
                        'presentMortgageInsurance',
                        $presentMortgageInsurance,
                        'Mortgage Insurance'
                    ); ?>
                </td>
                <td>

                    <?php echo loanForm::currency(
                        'presentMortgageInsurance',
                        true,
                        $tabIndex++,
                        $presentMortgageInsurance,
                        null,
                        'calculateTotLOPresentExpenses(this.value);',
                            '',
                    ); ?>

                </td>
                <td>
                    <?php echo loanForm::currency(
                        'proposedMortgageInsurance',
                        true,
                        $tabIndex++,
                        $proposedMortgageInsurance,
                        null,
                        'calculateTotLOProposedExpenses(this.value);',
                            '',
                    ); ?>
                </td>
            </tr>
            <tr>
                <td>
                    <?php echo loanForm::labelWithValue(
                        'presentHomeownerAssnDues',
                        $presentHomeownerAssnDues,
                        'Homeowner Assn. Dues'
                    ); ?>
                </td>
                <td>

                    <?php echo loanForm::currency(
                        'presentHomeownerAssnDues',
                        true,
                        $tabIndex++,
                        $presentHomeownerAssnDues,
                        null,
                        'calculateTotLOPresentExpenses(this.value);',
                            '',
                    ); ?>

                </td>
                <td>
                    <?php echo loanForm::currency(
                        'proposedHomeownerAssnDues',
                        true,
                        $tabIndex++,
                        $proposedHomeownerAssnDues,
                        null,
                        'calculateTotLOProposedExpenses(this.value);',
                            '',
                    ); ?>
                </td>
            </tr>
            <tr>
                <td>
                    <?php echo loanForm::labelWithValue(
                        'presentOther',
                        $presentOther,
                        'Other'
                    ); ?>
                </td>
                <td>

                    <?php echo loanForm::currency(
                        'presentOther',
                        true,
                        $tabIndex++,
                        $presentOther,
                        null,
                        'calculateTotLOPresentExpenses(this.value);',
                            '',
                    ); ?>
                </td>
                <td>
                    <?php echo loanForm::currency(
                        'proposedOther',
                        true,
                        $tabIndex++,
                        $proposedOther,
                        null,
                        'calculateTotLOProposedExpenses(this.value);',
                            '',
                    ); ?>
                </td>
            </tr>
            <tr>
                <td>
                    <h3>Total Expenses</h3>
                </td>
                <td>
                    <div class="left">
                        <h3>$
                            <span id="totalPresentTotalAmt"><?php echo Currency::formatDollarAmountWithDecimal($totalPresentTotalAmt) ?></span>
                        </h3>
                    </div>
                </td>
                <td>
                    <div class="left">
                        <h3>$
                            <span id="totalProposedAmt"><?php echo Currency::formatDollarAmountWithDecimal($totalProposedAmt) ?></span>
                        </h3>
                    </div>
                </td>
            </tr>
        </table>


    </div>
</div>

