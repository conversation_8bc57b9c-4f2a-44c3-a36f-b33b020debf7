<?php
use models\Controllers\loanForm;

global $isTaxReturnDisp, $op, $allowToEdit, $tabIndex;
global $YRF4506TDate1, $YRF4506TDate2, $YRF4506TDate3, $YRF4506TDate4;

loanForm::pushSectionID('YRF4506');
?>

<!-- 4506TYearRequested.php -->
<div class="card card-custom 4506TYearRequested <?php echo loanForm::showField('YRF4506TDate1'); ?>" style="<?php echo $isTaxReturnDisp; ?>" id="taxReturnDiv2">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                4506T Year(s) requested
            </h3>
        </div>
        <div class="card-toolbar ">
            <?php if ($op != 'view') { ?>
                <?php echo loanForm::sectionButtonAdd(
                    'Click to Add Date Requested',
                    'showAndHide4506TYearRequested(\'requestedYears\', 4);'
                ); ?>
            <?php } ?>
            <?php echo loanForm::sectionButtonShowHide(
                true,
                '',
                null, [
                    'card-tool' => 'toggle',
                    'section' => '4506TYearRequested',
                    'toggle' => 'tooltip',
                    'placement' => 'top',
                ]
            ); ?>

        </div>
    </div>
    <div class="card-body 4506TYearRequested_body">
        <div class="form-group row <?php echo loanForm::showField('YRF4506TDate1'); ?>">
            <div class="col-md-3">
                <?php echo loanForm::label('YRF4506TDate1', null, null, null, null, 'Date Requested'); ?>
            </div>
            <div class="col-md-3" id="requestedYears1_Div">
                <?php echo loanForm::date(
                    'YRF4506TDate1',
                    $allowToEdit,
                    $tabIndex++,
                    $YRF4506TDate1
                ); ?>
            </div>
        </div>

        <div class="form-group row <?php echo loanForm::showField('YRF4506TDate2'); ?>"
             id="requestedYears2_Id" <?php if ($YRF4506TDate2 == '') { ?> style="display: none" <?php } ?>>
            <div class="col-md-3">
                <?php echo loanForm::label('YRF4506TDate2', null, null, null, null, 'Date Requested'); ?>
            </div>
            <div class="col-md-3" id="requestedYears2_Div">
                <?php echo loanForm::date(
                    'YRF4506TDate2',
                    $allowToEdit,
                    $tabIndex++,
                    $YRF4506TDate2
                ); ?>
            </div>
        </div>
        <div class="form-group row <?php echo loanForm::showField('YRF4506TDate3'); ?>"
             id="requestedYears3_Id" <?php if ($YRF4506TDate3 == '') { ?> style="display: none" <?php } ?>>
            <div class="col-md-3">
                <?php echo loanForm::label('YRF4506TDate3', null, null, null, null, 'Date Requested'); ?>
            </div>
            <div class="col-md-3" id="requestedYears3_Div">
                <?php echo loanForm::date(
                    'YRF4506TDate3',
                    $allowToEdit,
                    $tabIndex++,
                    $YRF4506TDate3
                ); ?>
            </div>
        </div>
        <div class="form-group row <?php echo loanForm::showField('YRF4506TDate4'); ?>"
             id="requestedYears4_Id" <?php if ($YRF4506TDate4 == '') { ?> style="display: none" <?php } ?>>
            <div class="col-md-3">
                <?php echo loanForm::label('YRF4506TDate4', null, null, null, null, 'Date Requested'); ?>
            </div>
            <div class="col-md-3" id="requestedYears4_Div">
                <?php echo loanForm::date(
                    'YRF4506TDate4',
                    $allowToEdit,
                    $tabIndex++,
                    $YRF4506TDate4
                ); ?>
            </div>
        </div>
    </div>

</div>

<!-- 4506TYearRequested.php -->