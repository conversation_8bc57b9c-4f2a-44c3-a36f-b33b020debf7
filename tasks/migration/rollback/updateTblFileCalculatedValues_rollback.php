<?php

use models\Database2;
use models\Log;

require __DIR__ . '/../../../public/includes/util.php';
@set_time_limit(0);
if (function_exists('ini_set')) { @ini_set('memory_limit', '512M'); }

// Usage examples:
// Dry run with PCID filter and IDs file:
//   php tasks/migration/rollback/updateTblFileCalculatedValues_rollback.php -f /tmp/lmrs.txt -p 123 -n
// Execute with inline IDs (comma-separated):
//   php tasks/migration/rollback/updateTblFileCalculatedValues_rollback.php -i 101,102,103 --execute

$shortOpts = 'f::p::ni::';
$longOpts  = ['execute'];
$options   = getopt($shortOpts, $longOpts);

$PCID      = isset($options['p']) ? (is_numeric($options['p']) ? (int)$options['p'] : null) : null;
$dryRun    = isset($options['n']) && !isset($options['execute']);
$idsCsv    = isset($options['i']) ? trim((string)$options['i']) : null;
$filePath  = isset($options['f']) ? trim((string)$options['f']) : null;

if (!$idsCsv && !$filePath) {
    fwrite(STDERR, "ERROR: Provide either -i <csv_ids> or -f <file_with_LMRIds>.\n");
    exit(1);
}

function parseIdsFromFile(string $path): array {
    if (!is_readable($path)) {
        fwrite(STDERR, "ERROR: Cannot read file: {$path}\n");
        exit(1);
    }
    $ids = [];
    $fh = fopen($path, 'r');
    if ($fh === false) {
        fwrite(STDERR, "ERROR: Failed to open file: {$path}\n");
        exit(1);
    }
    while (($line = fgets($fh)) !== false) {
        $line = trim($line);
        if ($line === '') continue;
        // Support CSV lines too
        foreach (preg_split('/[\s,]+/', $line) as $token) {
            if ($token === '') continue;
            if (is_numeric($token)) {
                $ids[(int)$token] = true;
            }
        }
    }
    fclose($fh);
    return array_map('intval', array_keys($ids));
}

$inputLMRIds = [];
if ($idsCsv) {
    foreach (preg_split('/\s*,\s*/', $idsCsv) as $tok) {
        if ($tok !== '' && is_numeric($tok)) {
            $inputLMRIds[(int)$tok] = true;
        }
    }
}
if ($filePath) {
    foreach (parseIdsFromFile($filePath) as $id) {
        $inputLMRIds[(int)$id] = true;
    }
}
$inputLMRIds = array_values(array_map('intval', array_keys($inputLMRIds)));

if (empty($inputLMRIds)) {
    fwrite(STDERR, "No valid numeric LMRIds were provided. Nothing to do.\n");
    exit(0);
}

$db = Database2::getInstance();

// Helper to fetch existing IDs from a table constrained by a list
function fetchIds(string $table, string $col, array $ids): array {
    global $db;
    $result = [];
    $chunkSize = 1000;
    for ($o = 0; $o < count($ids); $o += $chunkSize) {
        $chunk = array_slice($ids, $o, $chunkSize);
        if (empty($chunk)) { continue; }
        $in = implode(',', array_map('intval', $chunk));
        $sql = "SELECT {$col} AS id FROM {$table} WHERE {$col} IN ({$in})";
        $rows = $db->queryData($sql);
        foreach ($rows as $r) {
            $result[(int)$r['id']] = true;
        }
    }
    return array_map('intval', array_keys($result));
}

// Restrict to LMRIds that belong to tblFile, and if PCID was provided, restrict to that PCID
$validLMRIds = [];
$chunkSize = 1000;
for ($o = 0; $o < count($inputLMRIds); $o += $chunkSize) {
    $chunk = array_slice($inputLMRIds, $o, $chunkSize);
    if (empty($chunk)) { continue; }
    $in = implode(',', array_map('intval', $chunk));
    $sql = 'SELECT LMRId, FPCID FROM tblFile WHERE LMRId IN (' . $in . ')';
    if ($PCID !== null) {
        $sql .= ' AND FPCID = ' . intval($PCID);
    }
    $rows = $db->queryData($sql);
    foreach ($rows as $r) {
        $validLMRIds[(int)$r['LMRId']] = true;
    }
}
$validLMRIds = array_values(array_map('intval', array_keys($validLMRIds)));

if (empty($validLMRIds)) {
    echo "No matching LMRIds found in tblFile for the provided input" . ($PCID !== null ? " and PCID={$PCID}" : '') . ". Nothing to do.\n";
    exit(0);
}

// Intersect with those that actually exist in tblFileCalculatedValues
$existingCalcIds = fetchIds('tblFileCalculatedValues', 'LMRId', $validLMRIds);
if (empty($existingCalcIds)) {
    echo "None of the provided LMRIds have rows in tblFileCalculatedValues. Nothing to do.\n";
    exit(0);
}

sort($existingCalcIds);
$total = count($existingCalcIds);

if ($dryRun) {
    echo "DRY RUN: Would delete {$total} row(s) from tblFileCalculatedValues" . ($PCID !== null ? " for PCID={$PCID}" : '') . ".\n";
    $preview = array_slice($existingCalcIds, 0, 20);
    echo "Sample LMRIds (first " . count($preview) . "): " . implode(',', $preview) . "\n";
    exit(0);
}

// Execute deletions in batches in a transaction
$db->beginTransaction();
try {
    $deleted = 0;
    for ($o = 0; $o < $total; $o += 1000) {
        $chunk = array_slice($existingCalcIds, $o, 1000);
        if (empty($chunk)) { continue; }
        $in = implode(',', array_map('intval', $chunk));
        $sql = 'DELETE FROM tblFileCalculatedValues WHERE LMRId IN (' . $in . ')';
        $affected = $db->update($sql);
        $deleted += (int)$affected;
        Log::Insert('Rollback delete chunk: ' . count($chunk) . ' requested, ' . $affected . ' affected.');
    }
    $db->commit();
    echo "Deleted {$deleted} row(s) from tblFileCalculatedValues" . ($PCID !== null ? " for PCID={$PCID}" : '') . ".\n";
} catch (\Throwable $e) {
    $db->rollBack();
    Log::Insert('Rollback FAILED: ' . $e->getMessage());
    fwrite(STDERR, 'ERROR: Rollback failed: ' . $e->getMessage() . "\n");
    exit(1);
}
