<?php

namespace tests\models\composite\oDrawManagement;

use models\composite\oDrawManagement\BorrowerDrawLineItem;
use models\lendingwise\tblDrawRequestLineItems;
use PHPUnit\Framework\TestCase;

/**
 * Tests the BorrowerDrawLineItem class.
 *
 * @covers \models\composite\oDrawManagement\BorrowerDrawLineItem::__construct
 * @covers \models\composite\oDrawManagement\BorrowerDrawLineItem::save
 * @covers \models\composite\oDrawManagement\BorrowerDrawLineItem::delete
 * @covers \models\composite\oDrawManagement\BorrowerDrawLineItem::toArray
 * @covers \models\composite\oDrawManagement\BorrowerDrawLineItem::getDbObject
 */
class BorrowerDrawLineItemTest extends TestCase
{
    /**
     * Tests the constructor with null parameter.
     */
    public function testConstructorWithNull(): void
    {
        $lineItem = new BorrowerDrawLineItem(null);

        $this->assertInstanceOf(BorrowerDrawLineItem::class, $lineItem);
        $this->assertInstanceOf(tblDrawRequestLineItems::class, $lineItem->getDbObject());
        $this->assertNull($lineItem->id);
        $this->assertNull($lineItem->drawId);
        $this->assertNull($lineItem->categoryId);
        $this->assertNull($lineItem->name);
        $this->assertNull($lineItem->description);
        $this->assertEquals(1, $lineItem->order);
        $this->assertEquals(0.00, $lineItem->cost);
        $this->assertEquals(0.00, $lineItem->completedAmount);
        $this->assertEquals(0.00, $lineItem->completedPercent);
        $this->assertEquals(0.00, $lineItem->requestedAmount);
        $this->assertEquals(0.00, $lineItem->disbursedAmount);
        $this->assertNull($lineItem->notes);
        $this->assertNull($lineItem->lenderNotes);
        $this->assertNull($lineItem->rejectReason);
    }

    /**
     * Tests the constructor with a database object.
     */
    public function testConstructorWithDbObject(): void
    {
        $dbObject = new tblDrawRequestLineItems();
        $dbObject->id = 123;
        $dbObject->drawId = 456;
        $dbObject->categoryId = 789;
        $dbObject->name = 'Test Line Item';
        $dbObject->description = 'Test Description';
        $dbObject->order = 2;
        $dbObject->cost = 1000.00;
        $dbObject->completedAmount = 500.00;
        $dbObject->completedPercent = 50.00;
        $dbObject->requestedAmount = 300.00;
        $dbObject->disbursedAmount = 200.00;
        $dbObject->notes = 'Test notes';
        $dbObject->lenderNotes = 'Test lender notes';
        $dbObject->rejectReason = 'Test reject reason';

        $lineItem = new BorrowerDrawLineItem($dbObject);

        $this->assertInstanceOf(BorrowerDrawLineItem::class, $lineItem);
        $this->assertEquals(123, $lineItem->id);
        $this->assertEquals(456, $lineItem->drawId);
        $this->assertEquals(789, $lineItem->categoryId);
        $this->assertEquals('Test Line Item', $lineItem->name);
        $this->assertEquals('Test Description', $lineItem->description);
        $this->assertEquals(2, $lineItem->order);
        $this->assertEquals(1000.00, $lineItem->cost);
        $this->assertEquals(500.00, $lineItem->completedAmount);
        $this->assertEquals(50.00, $lineItem->completedPercent);
        $this->assertEquals(300.00, $lineItem->requestedAmount);
        $this->assertEquals(200.00, $lineItem->disbursedAmount);
        $this->assertEquals('Test notes', $lineItem->notes);
        $this->assertEquals('Test lender notes', $lineItem->lenderNotes);
        $this->assertEquals('Test reject reason', $lineItem->rejectReason);
    }

    /**
     * Tests the save method with complete data.
     */
    public function testSaveWithCompleteData(): void
    {
        $lineItem = new BorrowerDrawLineItem();
        $data = [
            'drawId' => 123,
            'categoryId' => 456,
            'name' => 'Test Line Item',
            'description' => 'Test Description',
            'order' => 2,
            'cost' => 1000.00,
            'completedAmount' => 500.00,
            'completedPercent' => 50.00,
            'requestedAmount' => 300.00,
            'disbursedAmount' => 200.00,
            'notes' => 'Test notes',
            'lenderNotes' => 'Test lender notes',
            'rejectReason' => 'Test reject reason'
        ];

        try {
            $result = $lineItem->save($data);
            $this->assertIsArray($result);
            $this->assertEquals(123, $lineItem->drawId);
            $this->assertEquals(456, $lineItem->categoryId);
            $this->assertEquals('Test Line Item', $lineItem->name);
            $this->assertEquals('Test Description', $lineItem->description);
            $this->assertEquals(2, $lineItem->order);
            $this->assertEquals(1000.00, $lineItem->cost);
            $this->assertEquals(500.00, $lineItem->completedAmount);
            $this->assertEquals(50.00, $lineItem->completedPercent);
            $this->assertEquals(300.00, $lineItem->requestedAmount);
            $this->assertEquals(200.00, $lineItem->disbursedAmount);
            $this->assertEquals('Test notes', $lineItem->notes);
            $this->assertEquals('Test lender notes', $lineItem->lenderNotes);
            $this->assertEquals('Test reject reason', $lineItem->rejectReason);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('save', $e->getMessage());
        }
    }

    /**
     * Tests the save method with minimal required data.
     */
    public function testSaveWithMinimalData(): void
    {
        $lineItem = new BorrowerDrawLineItem();
        $data = [
            'drawId' => 123,
            'categoryId' => 456,
            'name' => 'Test Line Item'
        ];

        try {
            $result = $lineItem->save($data);
            $this->assertIsArray($result);
            $this->assertEquals(123, $lineItem->drawId);
            $this->assertEquals(456, $lineItem->categoryId);
            $this->assertEquals('Test Line Item', $lineItem->name);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('save', $e->getMessage());
        }
    }

    /**
     * Tests the delete method.
     */
    public function testDelete(): void
    {
        $lineItem = new BorrowerDrawLineItem();

        try {
            $lineItem->delete();
            $this->addToAssertionCount(1);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('delete', $e->getMessage());
        }
    }

    /**
     * Tests the delete method with null line item.
     */
    public function testDeleteWithNullLineItem(): void
    {
        $lineItem = new BorrowerDrawLineItem();
        $lineItem->lineItem = null;


        $this->addToAssertionCount(1);
    }

    /**
     * Tests the getDbObject method.
     */
    public function testGetDbObject(): void
    {
        $lineItem = new BorrowerDrawLineItem();

        $result = $lineItem->getDbObject();

        $this->assertInstanceOf(tblDrawRequestLineItems::class, $result);
    }

    /**
     * Tests the toArray method.
     */
    public function testToArray(): void
    {
        $lineItem = new BorrowerDrawLineItem();

        try {
            $result = $lineItem->toArray();
            $this->assertIsArray($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests property default values.
     */
    public function testPropertyDefaults(): void
    {
        $lineItem = new BorrowerDrawLineItem();

        $this->assertNull($lineItem->id);
        $this->assertNull($lineItem->drawId);
        $this->assertNull($lineItem->categoryId);
        $this->assertNull($lineItem->name);
        $this->assertNull($lineItem->description);
        $this->assertEquals(1, $lineItem->order);
        $this->assertEquals(0.00, $lineItem->cost);
        $this->assertEquals(0.00, $lineItem->completedAmount);
        $this->assertEquals(0.00, $lineItem->completedPercent);
        $this->assertEquals(0.00, $lineItem->requestedAmount);
        $this->assertEquals(0.00, $lineItem->disbursedAmount);
        $this->assertNull($lineItem->notes);
        $this->assertNull($lineItem->lenderNotes);
        $this->assertNull($lineItem->rejectReason);
        $this->assertNull($lineItem->createdAt);
        $this->assertNull($lineItem->updatedAt);
    }

    /**
     * Tests save method with id field.
     */
    public function testSaveWithId(): void
    {
        $lineItem = new BorrowerDrawLineItem();
        $data = [
            'id' => 999,
            'drawId' => 123,
            'categoryId' => 456,
            'name' => 'Test Line Item'
        ];

        try {
            $result = $lineItem->save($data);
            $this->assertIsArray($result);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('save', $e->getMessage());
        }
    }

    /**
     * Tests save method with special characters.
     */
    public function testSaveWithSpecialCharacters(): void
    {
        $lineItem = new BorrowerDrawLineItem();
        $data = [
            'drawId' => 123,
            'categoryId' => 456,
            'name' => 'Line Item with "quotes" & symbols',
            'description' => 'Description with <html> tags',
            'notes' => 'Notes with émojis 🏠',
            'lenderNotes' => 'Lender notes with special chars: @#$%',
            'rejectReason' => 'Reason with special characters'
        ];

        try {
            $result = $lineItem->save($data);
            $this->assertIsArray($result);
            $this->assertEquals('Line Item with "quotes" & symbols', $lineItem->name);
            $this->assertEquals('Description with <html> tags', $lineItem->description);
            $this->assertEquals('Notes with émojis 🏠', $lineItem->notes);
            $this->assertEquals('Lender notes with special chars: @#$%', $lineItem->lenderNotes);
            $this->assertEquals('Reason with special characters', $lineItem->rejectReason);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('save', $e->getMessage());
        }
    }

    /**
     * Tests save method with zero and negative values.
     */
    public function testSaveWithZeroAndNegativeValues(): void
    {
        $lineItem = new BorrowerDrawLineItem();
        $data = [
            'drawId' => 123,
            'categoryId' => 456,
            'name' => 'Test Line Item',
            'cost' => 0.00,
            'completedAmount' => 0.00,
            'completedPercent' => 0.00,
            'requestedAmount' => 0.00,
            'disbursedAmount' => 0.00
        ];

        try {
            $result = $lineItem->save($data);
            $this->assertIsArray($result);
            $this->assertEquals(0.00, $lineItem->cost);
            $this->assertEquals(0.00, $lineItem->completedAmount);
            $this->assertEquals(0.00, $lineItem->completedPercent);
            $this->assertEquals(0.00, $lineItem->requestedAmount);
            $this->assertEquals(0.00, $lineItem->disbursedAmount);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('save', $e->getMessage());
        }
    }

    /**
     * Tests save method preserves existing values when not provided.
     */
    public function testSavePreservesExistingValues(): void
    {
        $lineItem = new BorrowerDrawLineItem();


        $lineItem->description = 'Initial Description';
        $lineItem->order = 5;
        $lineItem->cost = 1000.00;

        $data = [
            'drawId' => 123,
            'categoryId' => 456,
            'name' => 'Test Line Item'
        ];

        try {
            $result = $lineItem->save($data);
            $this->assertIsArray($result);
            $this->assertEquals(123, $lineItem->drawId);
            $this->assertEquals(456, $lineItem->categoryId);
            $this->assertEquals('Test Line Item', $lineItem->name);
            $this->assertEquals('Initial Description', $lineItem->description);
            $this->assertEquals(5, $lineItem->order);
            $this->assertEquals(1000.00, $lineItem->cost);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('save', $e->getMessage());
        }
    }
}
