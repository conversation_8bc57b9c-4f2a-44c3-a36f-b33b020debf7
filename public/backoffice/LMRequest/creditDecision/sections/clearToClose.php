<?php

use models\constants\creditDecision;
use models\constants\gl\glDate;
use models\Controllers\backoffice\LMRequest;

use models\Controllers\LMRequest\creditDecision as creditDecisionController;
use models\Controllers\loanForm;
use models\lendingwise\tblCreditDecisionForm;
use models\standard\Arrays;
use models\standard\Dates;

$LMRId = LMRequest:: $LMRId;
$boStaffArray = creditDecision::getBOEmployeeAssignedToFile('CD', $LMRId);

$creditDecisionFormData = creditDecisionController::$creditDecisionFormData;
if(!$creditDecisionFormData) {
    $creditDecisionFormData = [
        new tblCreditDecisionForm(),
    ];
}

loanForm::pushSectionID('CLTCL');
?>
<div class="card card-custom clearToClose">
    <div class="card-header card-header-tabs-line bg-gray-100">
        <div class="card-title">
            <h3 class="card-label">
                Clear to Close
            </h3>
        </div>
        <div class="card-toolbar">
            <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer"
                  data-card-tool="toggle"
                  data-section="clearToClose"
                  data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Section">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
        </div>
    </div>
    <div class="card-body clearToClose_body">
        <div class="form-group row">
            <div class="col-md-6 <?php echo loanForm::showField('clearToCloseDate'); ?>">
                <div class="form-group row">
                    <label class="font-weight-bold col-md-5" for="clearToCloseDate">
                        <?php echo loanForm::label('clearToCloseDate'); ?>
                    </label>
                    <div class="col-md-7 input-group ">
                        <div class="input-group-prepend actionDate">
                            <span class="input-group-text">
                                <i class="fa fa-calendar text-primary icon-lg"></i>
                            </span>
                        </div>
                        <input
                            class="form-control input-sm dateNewClass <?php echo loanForm::isMandatory('clearToCloseDate') ? 'mandatory' : ''; ?>"
                            type="text"
                            name="clearToCloseDate"
                            id="clearToCloseDate"
                            value="<?php echo Dates::formatDateWithRE($creditDecisionFormData->clearToCloseDate, 'YMD H:i:s', 'm/d/Y');?>"
                            data-before-creation-date="true"
                            data-start-date="<?php echo glDate::getCreationDate(LMRequest::File()->recordDate); ?>"
                            placeholder="MM/DD/YYYY"
                            maxlength="10"
                            autocomplete="off" >
                    </div>
                </div>
            </div>
            <div class="col-md-6 <?php echo loanForm::showField('clearToCloseBy'); ?>">
                <div class="form-group row">
                    <label class="font-weight-bold col-md-5" for="clearToCloseBy">
                        <?php echo loanForm::label('clearToCloseBy'); ?>
                    </label>
                    <div class="col-md-7">
                        <select name="clearToCloseBy" id="clearToCloseBy" class="form-control <?php echo loanForm::isMandatory('clearToCloseBy') ? 'mandatory' : ''; ?>">
                            <option value="">Select</option>
                            <?php foreach ($boStaffArray as $boStaffArrayKey => $boStaffArrayValue) { ?>
                                <option value="<?php echo $boStaffArrayValue['AID']; ?>" 
                                    <?php echo Arrays::isSelected($boStaffArrayValue['AID'], $creditDecisionFormData->clearToCloseBy);?> >
                                    <?php echo $boStaffArrayValue['role'] . ' - ' . $boStaffArrayValue['processorName']; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-md-6 <?php echo loanForm::showField('clearToCloseNotes'); ?>">
                <div class="form-group row">
                    <label class="font-weight-bold col-md-5" for="clearToCloseNotes">
                        <?php echo loanForm::label('clearToCloseNotes'); ?>
                    </label>
                    <div class="col-md-7">
                        <textarea name="clearToCloseNotes" id="clearToCloseNotes" class="form-control validateMaxLength 
                        <?php echo loanForm::isMandatory('clearToCloseNotes') ? 'mandatory' : ''; ?>"
                        maxlength="<?php echo loanForm::getFieldLength('clearToCloseNotes','tblCreditDecisionForm'); ?>"
                        ><?php echo $creditDecisionFormData->clearToCloseNotes;?></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php loanForm::popSectionID();?> 