class Property {

    static clear() {
        $("#create_property :input").val('');
        $('#create_property .chzn-select').trigger("chosen:updated");
    }

    static showModal() {
        Property.clear();
        $('#create_property').modal('show');
    }

    static save() {
        let propertyData = new FormData($('#loanModForm')[0]);
        HTTP.PostForm('/api/saveProperty.php', propertyData, function (res) {
            if (res.success) {
                $('#create_property').modal('hide');
                location.reload();
            } else {
                (res.msg) ? toastrNotification(res.msg, 'error') : '';
            }
        });
    }

    static loadNewProperty(element) {
        $(element).attr('disabled', 'disabled');
        let propertyData = {
            'LMRId': $('#propertyLMRId').val(),
            'publicUser': $('#publicUser').val(),
            'allowToEdit': $('#allowToEdit').val(),
            'activeTab': $('#activeTab').val(),
            'hideThisField': $('#hideThisField').val(),
            'fileTab': $('#fileTab').val(),
            'wfOpt': $('#wfOpt').val(),
            'encryptedPCID': $('#encryptedPCID').val(),
            'fileModule': $('#fileModule').val(),
            'clientType': $('#LMRClientType').val(),
            'propertyIndex': parseInt($('.subPropDetails').length) + 1,
        };
        HTTP.PostRaw('/api/getProperty.php',
            propertyData,
            function (res) {
                $('.propertyMultipleDiv').append(res);
                Property.init();
                $('.subPropDetails:last')
                    .find('.chzn-select').chosen({allow_single_deselect: true, search_contains: true}).end()
                    .find(".mask_phone:enabled").inputmask("mask", {mask: "(999) 999 - 9999 Ext 9999"}).end()
                    .find('.zipCode').inputmask("99999").end()
                    .find('.yearClass').mask('9999', {placeholder: "____"}).datepicker({
                    format: "yyyy",
                    viewMode: "years",
                    minViewMode: "years",
                    autoclose: true,
                    startDate: '01/01/1900',
                }).end();
                $(element).removeAttr('disabled');
            });
    }

    static create() {
        let propertyData = $("#propertyFields :input").serializeArray();
        let encryptedLId = $("#encryptedLId").val();
        propertyData.push({name: 'encryptedLId', value: encryptedLId});
        HTTP.Post('/api/saveProperty.php', propertyData, function (res) {
            if (res.success) {
                if (res.propertyId) {
                    Property.getProperty(res.propertyId);
                    $('#create_property').modal('hide');
                }
            } else {
                (res.msg) ? toastrNotification(res.msg, 'error') : '';
            }
        });
    }

    static clearProperty(_cs) {

        _cs.find('input,select,textarea,file').val('').end()
            .find('.chzn-select').each(function() {
                // Destroy existing chosen instance if it exists
                if ($(this).data('chosen')) {
                    $(this).chosen('destroy');
                }
            })
            .chosen({allow_single_deselect: true, search_contains: true}).trigger("chosen:updated").end()
            .find(":input")
            .attr('type', function (idx, attrVal) {
                if (attrVal === 'radio') {
                    $(this).prop("checked", false);
                } else {
                    $(this).val('');
                }
            }).removeAttr('checked').prop("checked", false).end();
        _cs.find('.propertyAddressLabel').html('1) Subject Property Details');
        _cs.find('.isPrimaryCheck').val('1').prop('checked', true);
    }

    static delete(element) {
        let propertyId = $(element).data('id');
        let LMRId = $(element).data('lmrid');
        let propertyName = $(element).data('propertyname');
        let $element = $(element);

        $.confirm({
            icon: 'fa fa-warning',
            closeIcon: true,
            title: 'Confirm',
            content: "Do You Want To Delete <b>" + propertyName + "</b> Property?",
            type: 'red',
            backgroundDismiss: false,
            buttons: {
                No: {
                    btnClass: 'btn btn-light-primary font-weight-bold',
                    action: function () {
                    }
                },
                yes: {
                    btnClass: 'btn btn-primary',
                    action: function () {
                        if (propertyId) {
                            HTTP.Post('/backoffice/api_v2/delete_property', {
                                propertyId: propertyId,
                                LMRId: LMRId,
                            }, function (res) {
                                if (res.success) {
                                    $element.closest('.subPropDetails').remove();
                                    toastrNotification('Property Deleted', 'success');
                            } else {
                                (res.msg) ? toastrNotification(res.msg, 'error') : '';
                            }
                        });
                        } else {
                            $element.closest('.subPropDetails').remove();  // Changed from $(element)
                            toastrNotification('Property Deleted', 'success');
                        }
                    }
                },
            }
        });
    }


    /*    static Submit() {
            //    let PropertyData = [];
            let propertyInfo = $("#propertyInfoFields :input").serialize();
            let rentRollInfo = $("#rentRollFields :input").serialize();

            let PropertyData = {
                propertyInfo: propertyInfo,
                rentRollInfo: rentRollInfo,
                encLMRId: $("#encryptedLId").val(),
                propertyId: $("#propertyId").val(),
            };

            HTTP.Post('/api/saveProperty.php', PropertyData, function (res) {
                if (res.success) {
                    $('#create_property').modal('hide');
                    location.reload();
                } else {
                    (res.msg) ? toastrNotification(res.msg, 'error') : '';
                }
            });
        }*/

    static showAndHideZillowValue(element) {
        console.log({
            func: 'Property.showAndHideZillowValue',
        });

        let fldValue = parseInt($(element).val());
        let propertyTypeBasedZillow = $('.propertyTypeBasedZillow');

        if ($.inArray(fldValue, [1, 48, 4, 5, 12, 3, 64, 9, 6, 7, 8, 37, 65, 66, 67, 10, 93, 94, 98, 101]) !== -1) {
            propertyTypeBasedZillow.show();
        } else {
            propertyTypeBasedZillow.hide();
        }
    }

    static appendPrimaryPropertyLabel(element, propertyIndex) {
        let _getAllCheckboxExceptChecked = $('.isPrimaryCheck').not(element);
        _getAllCheckboxExceptChecked.prop('checked', false);
        _getAllCheckboxExceptChecked.not(element).val('0');
        let primaryPropertyLabel = $('.primaryPropertyLabel' + propertyIndex);
        let primaryPropertyTitle = $('.primaryPropertyTitle' + propertyIndex);
        if (primaryPropertyTitle.length > 0) {
            $('.primaryPropertyLabels').remove();
            primaryPropertyLabel.remove();
            primaryPropertyTitle.append('<span class="label label-primary label-inline font-weight-lighter m-2 primaryPropertyLabels primaryPropertyLabel' + propertyIndex + '">Main*</span>');
        }
    }

    static getPrimaryPropertyAddress(element) {

        let propertyAddress = [];
        let _primaryProperty = $('.isPrimaryCheck:checked').not(element).parents('.subPropDetails');

        if (_primaryProperty.length) {
            _primaryProperty.find('input.propertyAddress').each(function () {
                if ($(this).val()) {
                    propertyAddress.push($(this).val());
                }
            });
            _primaryProperty.find('input.propertyCity').each(function () {
                if ($(this).val()) {
                    propertyAddress.push($(this).val());
                }
            });
            let propertyState = _primaryProperty.find('select.propertyState').val() !== '' ? _primaryProperty.find('select.propertyState option:selected').text() : null;
            if (propertyState) {
                propertyAddress.push(propertyState);
            }
            _primaryProperty.find('input.propertyZip').each(function () {
                if ($(this).val()) {
                    propertyAddress.push($(this).val());
                }
            });
            return propertyAddress.join(', ');
        }
        return null;
    }

    static checkIsPrimaryProperty(element, propertyIndex) {

        if ($(element).is(':checked')) {
            $(element).val(1);

            //let propertyId = $(element).data('id');
            //let _propertySection = $(element).parents('.subPropDetails');
            //let _propertyId = parseInt(_propertySection.find('.propertyId').val());
            let primaryPropertyAddress = Property.getPrimaryPropertyAddress(element);
            let _propertyConfirmText = 'Do You Want To Make This Property As Primary Property? ';
            if (primaryPropertyAddress) {
                _propertyConfirmText += " Existing Primary Address \n <br><b>" + (primaryPropertyAddress) + "</b>";
            }
            $.confirm({
                icon: 'fa fa-warning',
                closeIcon: false,
                title: 'Confirm',
                content: _propertyConfirmText,
                type: 'red',
                backgroundDismiss: false,
                buttons: {
                    No: {
                        btnClass: 'btn btn-light-primary font-weight-bold', action: function () {
                            $(element).val(0);
                            $(element).prop('checked', false);
                        }
                    }, yes: {
                        btnClass: 'btn btn-primary', action: function () {
                            Property.appendPrimaryPropertyLabel(element, propertyIndex);
                        }
                    },
                }
            });
        } else {
            $(element).val();
            if (!($('.isPrimaryCheck:checked').length)) {
                $(element).val(1);
                $(element).prop('checked', true);
            }
        }
    }

    static AnchorTenant(ele) {
        console.log('change.anchorTenantRadio');
        let anchorTenantName_disp = $(ele).parents('.anchorTenant').next('.anchorTenantName');
        let at = parseInt($(ele).val());
        if (at === 1) {
            anchorTenantName_disp.removeClass('actDisp');
            anchorTenantName_disp.removeClass('subSecDa');
            anchorTenantName_disp.removeClass('secHide');
            anchorTenantName_disp.addClass('secShow');
        } else {
            anchorTenantName_disp.addClass('secHide');
            anchorTenantName_disp.removeClass('secShow');
        }
    }

    static init() {
        $(".subPropDetails").each(function () {

            //propertyLocationSectionTitle //
            let propertyLocationHeaderLabel = $(this).find('#proploctitle');
            let totalPropertyLocationFieldsCount = parseInt($(this).find('.proploc').length);
            let showPropertyLocationFieldsCount = parseInt($(this).find('.proploc.secShow').length);
            let hidePropertyLocationFieldsCount = parseInt($(this).find('.proploc.secHide').length);

            if (totalPropertyLocationFieldsCount > 0) {
                propertyLocationHeaderLabel.show();
                if (showPropertyLocationFieldsCount > 0) {
                    propertyLocationHeaderLabel.show();
                }
                if (totalPropertyLocationFieldsCount === hidePropertyLocationFieldsCount) {
                    propertyLocationHeaderLabel.hide();
                }
            } else {
                propertyLocationHeaderLabel.hide();
            }
            // ENd of propertyLocationSectionTitle //


            //propertyCharacteristicsSectionTitle
            let propertyCharacteristicsLabelHeaderLabel = $(this).find("#propchartitle");
            let totalPropertyCharacteristicsLabelFieldsCount = parseInt($(this).find('.propchar').length);
            let showPropertyCharacteristicsLabelFieldsCount = parseInt($(this).find('.propchar.secShow').length);
            let hidePropertyCharacteristicsLabelFieldsCount = parseInt($(this).find('.propchar.secHide').length);

            if (totalPropertyCharacteristicsLabelFieldsCount > 0) {
                propertyCharacteristicsLabelHeaderLabel.show();
                if (showPropertyCharacteristicsLabelFieldsCount > 0) {
                    propertyCharacteristicsLabelHeaderLabel.show();
                }
                if (totalPropertyCharacteristicsLabelFieldsCount === hidePropertyCharacteristicsLabelFieldsCount) {
                    propertyCharacteristicsLabelHeaderLabel.hide();
                }
            } else {
                propertyCharacteristicsLabelHeaderLabel.hide();
            }
            // End of propertyCharacteristicsSectionTitle


            //Property Analysis section

            let propertyAnalysisHeaderLabel = $(this).find("#propertyAnalysisTitle");
            let totalPropertyAnalysisFieldsCount = parseInt($(this).find('.propertyAnalysisField').length);
            let showPropertyAnalysisFieldsCount = parseInt($(this).find('.propertyAnalysisField.secShow').length);
            let hidePropertyAnalysisFieldsCount = parseInt($(this).find('.propertyAnalysisField.secHide').length);

            if (totalPropertyAnalysisFieldsCount > 0) {
                propertyAnalysisHeaderLabel.show();
                if (showPropertyAnalysisFieldsCount > 0) {
                    propertyAnalysisHeaderLabel.show();
                }
                if (totalPropertyAnalysisFieldsCount === hidePropertyAnalysisFieldsCount) {
                    propertyAnalysisHeaderLabel.hide();
                }
            } else {
                propertyAnalysisHeaderLabel.hide();
            }
            //End of Property Analysis Section


            //propdetailstitle
            let propertyDetailsLabelHeaderLabel = $(this).find("#propdetailstitle");
            let totalPropertyDetailsLabelFieldsCount = parseInt($(this).find('.propdetails').length);
            let showPropertyDetailsLabelFieldsCount = parseInt($(this).find('.propdetails.secShow').length);
            let hidePropertyDetailsLabelFieldsCount = parseInt($(this).find('.propdetails.secHide').length);

            if (totalPropertyDetailsLabelFieldsCount > 0) {
                propertyDetailsLabelHeaderLabel.show();
                if (showPropertyDetailsLabelFieldsCount > 0) {
                    propertyDetailsLabelHeaderLabel.show();
                }
                if (totalPropertyDetailsLabelFieldsCount === hidePropertyDetailsLabelFieldsCount) {
                    propertyDetailsLabelHeaderLabel.hide();
                }
            } else {
                propertyDetailsLabelHeaderLabel.hide();
            }
            //End of propdetailstitle

            //propaccess
            let propertyAccessLabelHeaderLabel = $(this).find("#propaccesstitle");
            let totalPropertyAccessLabelFieldsCount = parseInt($(this).find('.propaccess').length);
            let showPropertyAccessLabelFieldsCount = parseInt($(this).find('.propaccess.secShow').length);
            let hidePropertyAccessLabelFieldsCount = parseInt($(this).find('.propaccess.secHide').length);

            if (totalPropertyAccessLabelFieldsCount > 0) {
                propertyAccessLabelHeaderLabel.show();
                if (showPropertyAccessLabelFieldsCount > 0) {
                    propertyAccessLabelHeaderLabel.show();
                }
                if (totalPropertyAccessLabelFieldsCount === hidePropertyAccessLabelFieldsCount) {
                    propertyAccessLabelHeaderLabel.hide();
                }
            } else {
                propertyAccessLabelHeaderLabel.hide();
            }
            //end of propaccess


            //propertyRentRollSection
            if ($(this).find('.rentRollSecFields.secShow').length > 0) {
                $(this).find(".rentRollSecFieldsClass").show();
            } else {
                $(this).find(".rentRollSecFieldsClass").hide();
            }
            //End of propertyRentRollSection

            //propertyFloodCertificatesSection
            if ($(this).find('.floodCertificatesFields.secShow').length > 0) {
                $(this).find(".floodCertificatesFieldsClass").show();
            } else {
                $(this).find(".floodCertificatesFieldsClass").hide();
            }
            //end of propertyFloodCertificatesSection


            //propertyAppraiserSection
            if ($(this).find('.propertyAppraisersFields.secShow').length > 0) {
                $(this).find(".propertyAppraisersFieldsClass").show();
            } else {
                $(this).find(".propertyAppraisersFieldsClass").hide();
            }
            //End of propertyAppraiserSection

            //propertyAppraiserSection
            if ($(this).find('.propertyHOAFields.secShow').length > 0) {
                $(this).find(".PropertyHOA").show();
            } else {
                $(this).find(".PropertyHOA").hide();
            }
            //End of propertyAppraiserSection


        });
    }


    static populateStateCounty(elem, countyId) {

        let stateCode = $(elem).val();
        let countyIdElement = $('#' + countyId);
        let typeOfAutoSave = typeof autoSave;
        if (typeOfAutoSave === 'function') {
            autoSave.toggleElem(countyIdElement);
        }

        HTTP.Get('/api/getCountyByState.php', {
            stateCode: stateCode,
        }, function (res) {
            countyIdElement.html('');
            countyIdElement.append($('<option>', {
                value: '', text: '- Select -'
            }));
            $.each(res.data, function (id, county) {
                countyIdElement.append($('<option>', {
                    value: county, text: county
                }));
            });
            if (typeOfAutoSave === 'function') {
                autoSave.toggleElem(countyIdElement);
            }
        });
    }

    static scrollToPropertyInfo(elem) {
        let propertyclass = $('.' + $(elem).attr('data-propertyclass'));
        $('html, body').animate({
            scrollTop: propertyclass.offset().top - 400
        }, 2000);
    }

    static importProperties(LMRId, PCID) {
        clear_form_elements('import_properties');
        $("#propertyImportLMRId").val(LMRId);
        $("#propertyImportPCID").val(PCID);
        $('#import_properties').modal('toggle');
    }

    static importPropertyData() {
        console.log({
            func: 'importPropertyData'
        });

        let _propertiesFileSrc = $("#propertiesFileSrc");
        let ext = _propertiesFileSrc.val().split('.').pop().toLowerCase();

        if (_propertiesFileSrc.get(0).files.length === 0) {
            toastrNotification("Please select the file", 'error');
            return false;
        } else if (ext !== 'csv' && ext !== 'xlsx' && ext !== 'xls') {
            toastrNotification("Please upload CSV, XLSX, or XLS file", 'error');
            return false;
        } else {
            let fd = new FormData($('#import_propertiesForm')[0]);
            fd.append('file', _propertiesFileSrc.get(0)); // since this is your file input

            $.ajax({
                url: "/backoffice/api/importPropertyData.php",
                type: "post",
                dataType: 'json',
                processData: false, // important
                contentType: false, // important
                data: fd,
                success: function (data) {

                    if (data.hasOwnProperty('errorMandatory')) {
                        toastrNotificationWithReloadDelay(data.errorMandatory, 'error');
                    } else if (data.hasOwnProperty('error')) {
                        $.confirm({
                            icon: 'fa fa-warning',
                            closeIcon: true,
                            title: 'Confirm',
                            content: data.error+' You have errors in your spreadsheet. ' +
                                'If you move forward without fixing errors then we will ' +
                                'ignore importing any values to the cells that experienced the error',
                            type: 'red',
                            backgroundDismiss: true,
                            buttons: {
                                yes: function () {
                                    if(data.hasOwnProperty('propertyData')){
                                        console.log(data.propertyData);
                                        $.ajax({
                                                url: "/backoffice/api/importPropertyData.php",
                                                type: "post",
                                                data:  jQuery.param({'propertyImportPCID': $('#propertyImportPCID').val(),
                                                    'propertyImportLMRId': $('#propertyImportLMRId').val(),
                                                    'opt':'forceImport',
                                                    'propertyData':data.propertyData
                                                }),
                                                success: function (data) {

                                                    if (data.hasOwnProperty('error')) {
                                                        toastrNotificationWithReloadDelay(data.error, 'error');
                                                    } else if (data.hasOwnProperty('success')) {
                                                        toastrNotificationWithReloadDelay("Properties imported successfully", 'success');
                                                    } else {
                                                        toastrNotificationWithReloadDelay(data, 'error');
                                                    }
                                                }
                                        });
                                    }

                                },
                                cancel: function () {

                                },
                            },
                            onClose: function () {

                            },
                        });
                    } else if (data.hasOwnProperty('success')) {
                        toastrNotificationWithReloadDelay("Properties imported successfully", 'success');
                    } else {
                        toastrNotificationWithReloadDelay(data, 'error');
                    }
                }
            });
        }
    }
}

$(function () {
    Property.init();
});


