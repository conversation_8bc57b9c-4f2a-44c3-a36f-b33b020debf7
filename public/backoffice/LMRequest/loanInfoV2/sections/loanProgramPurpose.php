<?php
global $servicesRequested;

use models\constants\gl\glDate;
use models\constants\gl\glGroundUpConstruction;
use models\constants\gl\glHMLOExitStrategy;
use models\constants\gl\glHMLOHouseType;
use models\constants\gl\glHMLOLienPosition;
use models\constants\gl\glHMLOLoanTerms;
use models\constants\gl\glPCID;
use models\constants\gl\glprePaymentPenalty;
use models\constants\gl\glPropertyConstructionLevel;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\loanPropertySummary;
use models\Controllers\loanForm;
use models\cypher;
use models\HMLOLoanTermsCalculation;
use models\lendingwise\tblFileHMLOPropInfo;
use models\standard\Arrays;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;


//pr(LMRequest::File());
//pr(LMRequest::myFileInfo()->fileHMLOPropertyInfo()->propertyNeedRehab);
//pr(LMRequest::myFileInfo()->LMRClientTypeInfo());
//pr(LMRequest::File()->getTblLMRClientType_by_LMRID());

$rehabCostFinancedDiv = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->propertyNeedRehab == 'Yes' ? '' : ' hide ';
HMLOLoanTermsCalculation::InitForLMRId(LMRequest::$LMRId);
?>
<div class="card card-custom  ">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                Loan Program & Purpose
            </h3>
        </div>
        <div class="card-toolbar">
            <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                  data-card-tool="toggle"
                  data-section="loanProgramsAndPurposeCard"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
            </span>
        </div>
    </div>
    <div class="card-body loanProgramsAndPurposeCard-body ">
        <div class="row">

            <?php loanForm::pushSectionID('Admin');
                $isDisabledField = loanForm::isEnabled('LMRClientType'); ?>
            <div class="col-md-3">
                <?php
                //   loanForm::pushSectionID('LT');
                /*      populatePCBasicLoanInfo('loanModForm',this.value,'<?php echo LMRequest::File()->FPCID; ?>',
                          '<?php echo LMRequest::File()->getTblFileModules_by_fileID()->moduleCode; ?>'
                      );*/
                ?>
                <div class=" form-group <?php echo loanForm::showField('LMRClientType'); ?>">
                    <label class="font-weight-bold"><?php echo loanForm::getFieldLabel('LMRClientType'); ?></label>
                    <div class="">
                        <select class="form-control input-sm chzn-select "
                            <?php echo $isDisabledField; ?>
                                data-placeholder="Please select"
                                onchange="formControl.controlFormFields('', 'HMLO', this.id, 'loanProgram');
                                        getPCMinMaxLoanGuidelines('loanModForm', '<?php echo LMRequest::File()->FPCID; ?>');
                                        populatePCBasicLoanInfo('loanModForm',this.value,'<?php echo LMRequest::File()->FPCID; ?>','<?php echo LMRequest::File()->getTblFileModules_by_fileID()->moduleCode; ?>','LIV2');
                                        loanInfoV2Form.showAndHideLoanProgramBasedFields(this,'<?php echo cypher::myEncryption(LMRequest::File()->FPCID); ?>');
                                <?php if (LMRequest::$PCID == glPCID::PCID_PROD_CV3) { ?>
                                        loanInfoV2Form.showHidePropertyRehabCv3(this.value);
                                        loanInfoV2Form.hideInitialLoanAmountCV3();
                                <?php } ?>
                                        "
                                name="LMRClientType"
                                id="LMRClientType">
                            <option value=""></option>
                            <?php foreach (LMRequest::myFileInfo()->branchClientTypeInfo() as $eachLoanProgram) { ?>
                                <option value="<?php echo $eachLoanProgram->STCode; ?>" <?php echo Arrays::isSelected(LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType, $eachLoanProgram->STCode) ?>><?php echo $eachLoanProgram->serviceType; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <?php loanForm::popSectionID(); ?>

            <?php loanForm::pushSectionID('LT'); ?>
            <div class="col-md-3 ">
                <div class=" form-group <?php echo loanForm::showField('CORTotalLoanAmt'); ?>">
                    <?php
                    echo loanForm::label('CORTotalLoanAmt');
                    echo loanForm::currency(
                        'CORTotalLoanAmt',
                        LMRequest::$allowToEdit,
                        '1',
                        HMLOLoanTermsCalculation::$totalLoanAmount,
                        null,
                        null,
                            '',
                        true,
                        null,
                        true
                    );
                    ?>
                </div>
            </div>
            <?php loanForm::popSectionID(); ?>

            <?php loanForm::pushSectionID('RCI'); ?>
            <div class="col-md-3 ">
                <div class="showHidePropertyRehab"
                     style="<?php echo(loanPropertySummary::showHidePropertyRehabCv3(LMRequest::File()->FPCID, LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType) ? 'display:none;' : ''); ?>">
                    <div class=" form-group <?php echo loanForm::showField('propertyNeedRehab'); ?>">
                        <?php echo loanForm::label('propertyNeedRehab');
                        echo loanForm::radio(
                            'propertyNeedRehab',
                            LMRequest::$allowToEdit,
                            1,
                            loanPropertySummary::showHidePropertyRehabCv3(LMRequest::File()->FPCID, LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType) ? 'Yes' : LMRequest::myFileInfo()->fileHMLOPropertyInfo()->propertyNeedRehab,
                            [
                                'Yes' => 'Yes',
                                'No'  => 'No',
                            ],
                            'loanInfoV2Form.showHideRehabCostFinanced(this);',
                            ''
                        ); ?>
                    </div>
                </div>
            </div>
            <?php loanForm::popSectionID(); ?>

            <?php loanForm::pushSectionID('LST'); ?>

            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('typeOfHMLOLoanRequesting'); ?>">
                    <?php
                    echo loanForm::label('typeOfHMLOLoanRequesting');
                    echo loanForm::select(
                        'typeOfHMLOLoanRequesting',
                        LMRequest::$allowToEdit,
                        1,
                        LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting,
                        loanPropertySummary::$transactionType,
                        'loanInfoV2Form.updateTransactionType(this);',
                        'chzn-select form-control',
                        ' ',
                        'Please Select ' . loanForm::$permissions['typeOfHMLOLoanRequesting']->fieldLabel,
                        '',
                        'data-value = "' . LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting . '"'
                    );
                    ?>
                </div>
            </div>

            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('loanTerm'); ?>">
                    <?php
                    echo loanForm::label('loanTerm');
                    echo loanForm::select(
                        'loanTerm',
                        LMRequest::$allowToEdit,
                        1,
                        LMRequest::myFileInfo()->fileHMLOPropertyInfo()->loanTerm,
                        array_combine(glHMLOLoanTerms::$glHMLOLoanTerms, glHMLOLoanTerms::$glHMLOLoanTerms),
                        'loanInfoV2Form.updateLoanTerm();',
                        'chzn-select form-control',
                        ' ',
                        'Please Select ' . loanForm::$permissions['loanTerm']->fieldLabel,
                        '',
                        'data-value = "' . LMRequest::myFileInfo()->fileHMLOPropertyInfo()->loanTerm . '"'
                    );
                    ?>
                </div>
            </div>
            <?php loanForm::popSectionID(); ?>

            <?php loanForm::pushSectionID('LT'); ?>

            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('sub1DSCR'); ?>">
                    <?php
                    echo loanForm::label('sub1DSCR');
                    echo loanForm::switch(
                        'sub1DSCR',
                        LMRequest::$allowToEdit,
                        1,
                        LMRequest::myFileInfo()->getLoanPropertySummary()->sub1DSCR,
                        '',
                        'toggleSwitch(\'sub1DSCR_switch\', \'sub1DSCR\', \'1\', \'0\' )',
                    );
                    ?>
                </div>
            </div>


            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('totalPropertiesLoanAmount'); ?>">
                    <?php
                    echo loanForm::label('totalPropertiesLoanAmount');
                    echo loanForm::currency(
                        'totalPropertiesLoanAmount',
                        LMRequest::$allowToEdit,
                        '1',
                        LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesLoanAmount,
                        ' totalPropertiesLoanAmount ',
                        '',
                        true,
                            '',
                        false,
                        false,
                        'Aggregate Allocated Loan Amount(s) <br> + Rehab/Construction Cost Financed <br>'
                    ); ?>
                </div>
            </div>
            <?php loanForm::popSectionID(); ?>

            <?php loanForm::pushSectionID('LST'); ?>
            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('loanLoanNumber'); ?>">
                    <?php
                    echo loanForm::label('loanLoanNumber');
                    echo loanForm::text(
                        'loanNumber',
                        LMRequest::$allowToEdit,
                        null,
                        LMRequest::myFileInfo()->tblFile()->loanNumber,
                        '',
                        '',
                        true,
                        25
                    ); ?>
                </div>
            </div>
            <?php loanForm::popSectionID(); ?>

            <?php loanForm::pushSectionID('ALS'); ?>
            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('lienPosition'); ?>">
                    <?php
                    echo loanForm::label('lienPosition');
                    echo loanForm::select(
                        'lienPosition',
                        LMRequest::$allowToEdit,
                        1,
                        LMRequest::myFileInfo()->fileHMLOPropertyInfo()->lienPosition,
                        glHMLOLienPosition::getLienPosition(LMRequest::File()->FPCID),
                        ' ',
                        'chzn-select form-control',
                        ' ',
                        'Please Select ' . loanForm::$permissions['lienPosition']->fieldLabel
                    );
                    ?>
                </div>
            </div>


            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('isHouseProperty'); ?>">
                    <?php
                    echo loanForm::label('isHouseProperty');
                    echo loanForm::select(
                        'isHouseProperty',
                        LMRequest::$allowToEdit,
                        1,
                        LMRequest::myFileInfo()->FileProInfo()->isHouseProperty,
                        array_combine(glHMLOHouseType::$glHMLOHouseType, glHMLOHouseType::$glHMLOHouseType),
                        ' ',
                        'chzn-select form-control',
                        ' ',
                        'Please Select ' . loanForm::$permissions['isHouseProperty']->fieldLabel
                    );
                    ?>
                </div>
            </div>
            <?php loanForm::popSectionID(); ?>

            <?php loanForm::pushSectionID('TI'); ?>
            <div class="col-md-3 ">
                <div class="form-group <?php echo loanForm::showField('titleName'); ?>">
                    <?php
                    echo loanForm::label('titleName');
                    echo loanForm::text('',
                        LMRequest::$allowToEdit,
                        1,
                        LMRequest::myFileInfo()->FileProInfo()->titleName,
                        '',
                        '',
                        true,
                    ); ?>
                </div>
            </div>
            <?php loanForm::popSectionID(); ?>

            <?php loanForm::pushSectionID('ALS'); ?>

            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('applicationLoanExitPlan'); ?>">
                    <?php
                    echo loanForm::label(
                        'applicationLoanExitPlan',
                        ' ',
                        '',
                        loanForm::changeLog(
                            LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->HMLOPID,
                            'applicationLoanExitPlan',
                            tblFileHMLOPropInfo::class
                        )
                    );

                    echo loanForm::select(
                        'applicationLoanExitPlan',
                        LMRequest::$allowToEdit,
                        1,
                        LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->applicationLoanExitPlan,
                        glHMLOExitStrategy::$filteredOptions,
                        ' ',
                        'chzn-select',
                        ' ',
                        'Please Select ' . loanForm::$permissions['applicationLoanExitPlan']->fieldLabel,
                        (LMRequest::File()->FPCID == glPCID::PCID_PROD_CV3 && LMRequest::$activeTab == 'LIV2')
                    ); ?>
                </div>
            </div>


            <div class="col-md-3 ">
                <div class="form-group  <?php echo loanForm::showField('exitStrategy'); ?>">
                    <?php
                    echo loanForm::label('exitStrategy');
                    echo loanForm::select(
                        'exitStrategy_mirror',
                        LMRequest::$allowToEdit,
                        1,
                        LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->exitStrategy,
                        glHMLOExitStrategy::$filteredOptions,
                        'exitStrategyHideShow(this.value, \'exitStrategyExplain\', \'\');fileCommon.mirrorField(this,\'exitStrategy_mirror\');',
                        ' chzn-select exitStrategy_mirror ',
                        ' ',
                        'Please Select ' . loanForm::$permissions['exitStrategy']->fieldLabel
                    ); ?>
                </div>
            </div>


            <div class="col-md-3 ">
                <div class="form-group  <?php echo loanForm::showField('accountExecutiveLoanExitNotes'); ?>">
                    <?php echo loanForm::label(
                        'accountExecutiveLoanExitNotes',
                        '',
                        '',
                        loanForm::changeLog(
                            LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->HMLOPID,
                            'accountExecutiveLoanExitNotes',
                            tblFileHMLOPropInfo::class
                        )
                    );
                    echo loanForm::textarea(
                        'accountExecutiveLoanExitNotes',
                        LMRequest::$allowToEdit,
                        1,
                        LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->accountExecutiveLoanExitNotes,
                        'validateMaxLength',
                        '',
                        '',
                        '500',
                    ); ?>
                </div>
            </div>
            <?php loanForm::popSectionID(); ?>

            <?php loanForm::pushSectionID('AQ'); ?>
            <div class="col-md-3">
                <div class=" form-group <?php echo loanForm::showField('useOfProceeds'); ?>">
                    <div class="useOfProceedsDiv"
                         style="<?php echo loanPropertySummary::$useOfProceedsShow ? '' : 'display:none;'; ?>">
                        <?php echo loanForm::label2(
                            'useOfProceeds',
                            ' ',
                            '',
                            loanForm::changeLog(
                                LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->HMLOPID,
                                'useOfProceeds',
                                tblFileHMLOPropInfo::class
                            )
                        );
                        echo loanForm::text(
                            'useOfProceeds',
                            LMRequest::$allowToEdit,
                            1,
                            LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->useOfProceeds,
                            null,
                            null,
                            null,
                            null,
                            null,
                            !loanPropertySummary::$useOfProceedsShow
                        ); ?>
                    </div>
                </div>
            </div>
            <?php loanForm::popSectionID(); ?>

            <?php loanForm::pushSectionID('IMPDATES'); ?>
            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('desiredClosingDate'); ?>">
                    <?php echo loanForm::label('desiredClosingDate'); ?>
                    <div class="">
                        <?php
                        $isDisabledField = loanForm::isEnabled('desiredClosingDate');
                        if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                                </div>
                                <input type="text"
                                       class="form-control input-sm  dateNewClass desiredClosingDate"
                                       name="desiredClosingDate"
                                       id="desiredClosingDate"
                                       data-future-date="true"
                                       data-start-date="<?php echo glDate::getFutureDateOnly(); ?>"
                                       value="<?php echo Dates::formatDateWithRE(LMRequest::myFileInfo()->tblFile()->getTblQAInfo_by_LMRId()[0]->desiredClosingDate, 'YMD', 'm/d/Y'); ?>"
                                       autocomplete="off"
                                       placeholder="MM/DD/YYYY"
                                    <?php echo $isDisabledField; ?>/>
                            </div>
                        <?php } else { ?>
                            <b><?php echo Dates::formatDateWithRE(LMRequest::myFileInfo()->tblFile()->getTblQAInfo_by_LMRId()[0]->desiredClosingDate, 'YMD', 'm/d/Y'); ?></b>
                        <?php } ?>
                    </div>
                </div>
            </div>
            <?php loanForm::popSectionID(); ?>


            <?php loanForm::pushSectionID('Admin'); ?>
            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('targetClosingDate'); ?>">
                    <?php echo loanForm::label('targetClosingDate'); ?>
                    <div class="">
                        <?php
                        $isDisabledField = loanForm::isEnabled('targetClosingDate');
                        if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend ">
                                    <div class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </div>
                                </div>
                                <input
                                        class="form-control input-sm targetClosingDateClass dateNewClass "
                                        type="text"
                                        name="targetClosingDate"
                                        id="targetClosingDate"
                                        value="<?php echo Dates::formatDateWithRE(LMRequest::myFileInfo()->fileHMLOInfo()->targetClosingDate, 'YMD', 'm/d/Y'); ?>"
                                        size="10"
                                        autocomplete="off"
                                        placeholder="MM/DD/YYYY"
                                    <?php echo $isDisabledField; ?>>
                            </div>
                        <?php } else { ?>
                            <b><?php echo Dates::formatDateWithRE(LMRequest::myFileInfo()->fileHMLOInfo()->targetClosingDate, 'YMD', 'm/d/Y'); ?></b>
                        <?php } ?>
                    </div>
                </div>
            </div>
            <?php loanForm::popSectionID(); ?>


            <?php loanForm::pushSectionID('IMPDATES'); ?>
            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('daysUntilClose'); ?>">
                    <?php
                    echo loanForm::label('daysUntilClose');
                    echo loanForm::number('daysUntilClose',
                        LMRequest::$allowToEdit,
                        1,
                        loanPropertySummary::$daysUntilClose,
                        '',
                        '',
                        true,
                        'Days'
                    ); ?>
                </div>
            </div>
            <?php loanForm::popSectionID(); ?>

            <?php loanForm::pushSectionID('LT'); ?>

            <div class="col-md-4">
                <div class="form-group <?php echo loanForm::showField('propertyConstructionLevel'); ?>">
                    <?php
                    echo loanForm::label('propertyConstructionLevel');
                    echo loanForm::select('propertyConstructionLevel',
                        LMRequest::$allowToEdit,
                        1,
                        LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->propertyConstructionLevel,
                        array_combine(glPropertyConstructionLevel::$glPropertyConstructionLevel, glPropertyConstructionLevel::$glPropertyConstructionLevel),
                        '',
                        '',
                        null,
                        null,
                        true,
                    ); ?>
                </div>
            </div>
            <?php loanForm::popSectionID(); ?>


            <?php loanForm::pushSectionID('RCI'); ?>

            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('constructionType'); ?>">
                    <?php echo loanForm::label(
                        'constructionType',
                        ' ',
                        '',
                        loanForm::changeLog(
                            LMRequest::myFileInfo()->getLoanPropertySummary()->id,
                            'constructionType',
                            \models\lendingwise\tblLoanPropertySummary::class,
                            loanForm::getFieldLabel('constructionType')
                        ),
                    );
                    echo loanForm::select(
                        'constructionType',
                        LMRequest::$allowToEdit,
                        1,
                        LMRequest::myFileInfo()->getLoanPropertySummary()->constructionType,
                        glGroundUpConstruction::$constructionType,
                        ' ',
                        'chzn-select form-control',
                        ' ',
                        'Please Select ' . loanForm::$permissions['constructionType']->fieldLabel
                    ); ?>
                </div>
            </div>


            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('constructionHardCost'); ?>">
                    <?php echo loanForm::label(
                        'constructionHardCost',
                        ' ',
                        '',
                        loanForm::changeLog(
                            LMRequest::myFileInfo()->getLoanPropertySummary()->id,
                            'constructionHardCost',
                            \models\lendingwise\tblLoanPropertySummary::class,
                            loanForm::getFieldLabel('constructionHardCost')
                        ),
                    );
                    echo loanForm::currency(
                        'constructionHardCost',
                        LMRequest::$allowToEdit,
                        '1',
                        LMRequest::myFileInfo()->getLoanPropertySummary()->constructionHardCost,
                        null,
                        'loanInfoV2Form.calculateContingencyAmount();loanInfoV2Form.cloneHardCost();',
                            '',
                    ); ?>
                </div>
            </div>


            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('constructionSoftCost'); ?>">
                    <?php echo loanForm::label(
                        'constructionSoftCost',
                        ' ',
                        '',
                        loanForm::changeLog(
                            LMRequest::myFileInfo()->getLoanPropertySummary()->id,
                            'constructionSoftCost',
                            \models\lendingwise\tblLoanPropertySummary::class,
                            loanForm::getFieldLabel('constructionSoftCost')
                        ),
                    );
                    echo loanForm::currency(
                        'constructionSoftCost',
                        LMRequest::$allowToEdit,
                        '1',
                        LMRequest::myFileInfo()->getLoanPropertySummary()->constructionSoftCost,
                        null,
                        'loanInfoV2Form.calculateContingencyAmount();loanInfoV2Form.cloneSoftCost();',
                            '',
                    ); ?>
                </div>
            </div>

            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('costSpent'); ?>">
                    <?php echo loanForm::label(
                        'costSpent',
                        ' ',
                        '',
                        loanForm::changeLog(
                            LMRequest::myFileInfo()->listingRealtorInfo()->SSID,
                            'costSpent',
                            \models\lendingwise\tblShortSale::class,
                            loanForm::getFieldLabel('costSpent')
                        ),
                    );
                    echo loanForm::currency(
                        'costSpent',
                        LMRequest::$allowToEdit,
                        '1',
                        LMRequest::myFileInfo()->listingRealtorInfo()->costSpent,
                        '  ',
                        'currencyConverter(this, this.value);loanInfoV2Form.cloneCostSpent();loanInfoV2Form.calculatePropertyTotalProjectCost();',
                            '',
                        '',
                        ''
                    ); ?>
                </div>
            </div>

            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('contingencyTypeOption'); ?>">
                    <?php echo loanForm::label(
                        'contingencyTypeOption',
                        ' ',
                        '',
                        loanForm::changeLog(
                            LMRequest::myFileInfo()->getLoanPropertySummary()->id,
                            'contingencyTypeOption',
                            \models\lendingwise\tblLoanPropertySummary::class,
                            loanForm::getFieldLabel('contingencyTypeOption')
                        ),
                    );
                    echo loanForm::select(
                        'contingencyTypeOption',
                        LMRequest::$allowToEdit,
                        1,
                        LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyTypeOption ? LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyTypeOption : 10,
                        glGroundUpConstruction::$contingencyType,
                        'loanInfoV2Form.calculateContingencyAmount();',
                        'chzn-select form-control',
                        ' ',
                        'Please Select ' . loanForm::$permissions['contingencyTypeOption']->fieldLabel
                    );
                    ?>
                </div>
            </div>

            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('contingencyPercentage'); ?>">
                    <?php echo loanForm::label(
                        'contingencyPercentage',
                        ' ',
                        '',
                        loanForm::changeLog(
                            LMRequest::myFileInfo()->getLoanPropertySummary()->id,
                            'contingencyPercentage',
                            \models\lendingwise\tblLoanPropertySummary::class,
                            loanForm::getFieldLabel('contingencyPercentage')
                        ),
                    );
                    echo loanForm::percentage(
                        'contingencyPercentage',
                        LMRequest::$allowToEdit,
                        '1',
                        LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyPercentage ? LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyPercentage : 10,
                        '  ',
                        'loanInfoV2Form.calculateContingencyAmount();',
                        LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyTypeOption != 'Other',
                        5);
                    ?>
                </div>
            </div>

            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('contingencyAmount'); ?>">
                    <?php echo loanForm::label(
                        'contingencyAmount',
                        ' ',
                        '',
                        loanForm::changeLog(
                            LMRequest::myFileInfo()->getLoanPropertySummary()->id,
                            'contingencyAmount',
                            \models\lendingwise\tblLoanPropertySummary::class,
                            loanForm::getFieldLabel('contingencyAmount')
                        ),
                    );
                    echo loanForm::currency(
                        'contingencyAmount',
                        LMRequest::$allowToEdit,
                        '1',
                        LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyAmount,
                        '  ',
                        'loanInfoV2Form.calculateContingencyPercentage();',
                            '',
                        LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyTypeOption != 'Other',
                        '');
                    ?>
                </div>
            </div>
            <?php loanForm::popSectionID(); ?>


            <?php loanForm::pushSectionID('LT'); ?>
            <div class="col-md-3"></div>

            <div class="col-md-4">
                <div class="form-group <?php echo loanForm::showField('financedInterestReserve'); ?>">
                    <?php echo loanForm::label('financedInterestReserve'); ?>
                    <div class="validated">
                        <?php if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">M</span>
                                </div>
                                <input class="form-control input-sm "
                                       type="number"
                                       name="financedInterestReserveMonths"
                                       id="financedInterestReserveMonths"
                                       min="<?php echo loanPropertySummary::$financedInterestReserveMonthsMin; ?>"
                                       max="<?php echo loanPropertySummary::$financedInterestReserveMonthsMax; ?>"
                                       value="<?php echo loanPropertySummary::$financedInterestReserveMonths ?: LMRequest::myFileInfo()->getLoanPropertySummary()->financedInterestReserveMonths; ?>"
                                    <?php echo loanPropertySummary::$financedInterestReserveMonthsField; ?>
                                       onblur="loanInfoV2Form.calculateInterestReserve();"
                                       autocomplete="off">
                                <div class="input-group-append">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input class="form-control input-sm "
                                       type="text"
                                       placeholder="0.00"
                                       name="financedInterestReserve"
                                       id="financedInterestReserve"
                                       readonly
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->financedInterestReserve); ?>"
                                       autocomplete="off">
                                <div class="input-group-append tooltipClass financedInterestReserveToolTip"
                                     title="<?php echo loanPropertySummary::$financedInterestReserveToolTip; ?>">
                                    <div class="input-group-text">
                                        <i class="fa fa-info-circle text-primary  "></i>
                                    </div>
                                </div>
                            </div>
                        <?php } else { ?>
                            <b> <?php echo LMRequest::myFileInfo()->getLoanPropertySummary()->financedInterestReserveMonths . ' Months' . ' $' . Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->financedInterestReserve); ?> </b>
                        <?php } ?>
                    </div>
                </div>
            </div>


            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('interestReserveType'); ?>">
                    <?php
                    echo loanForm::label('interestReserveType');
                    echo loanForm::select(
                        'interestReserveType',
                        LMRequest::$allowToEdit,
                        1,
                        LMRequest::myFileInfo()->getLoanPropertySummary()->interestReserveType ? LMRequest::myFileInfo()->getLoanPropertySummary()->interestReserveType : loanPropertySummary::$defaultInterestReserveType,
                        glGroundUpConstruction::$interestReserveType,
                        ' ',
                        'chzn-select form-control',
                        ' ',
                        'Please Select ' . loanForm::$permissions['interestReserveType']->fieldLabel
                    );
                    ?>
                </div>
            </div>
            <?php loanForm::popSectionID(); ?>


            <?php loanForm::pushSectionID('LT'); ?>
            <div class="col-md-3"></div>

            <div class="col-md-3">
                <div class=" form-group <?php echo loanForm::showField('rehabCost'); ?>   ">
                    <div class=" rehabCostFinancedDiv <?php echo $rehabCostFinancedDiv; ?>">
                        <?php
                        echo loanForm::label('rehabCost');
                        echo loanForm::currency(
                            'rehabCost',
                            LMRequest::$allowToEdit,
                            '1',
                            LMRequest::myFileInfo()->fileHMLOInfo()->rehabCost,
                            '  ',
                            'loanInfoV2Form.calculatePercentageRehabCostFinanced();',
                                '',
                            null,
                            null);
                        ?>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="form-group  <?php echo loanForm::showField('rehabCostPercentageFinanced'); ?> ">
                    <div class="rehabCostFinancedDiv <?php echo $rehabCostFinancedDiv; ?>">
                        <?php
                        echo loanForm::label('rehabCostPercentageFinanced');
                        echo loanForm::percentage(
                            'rehabCostPercentageFinanced',
                            LMRequest::$allowToEdit,
                            '1',
                            Strings::formatNumber(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->rehabCostPercentageFinanced,10),
                            '  ',
                            'validatePercentage(this);loanInfoV2Form.calculateRehabCostFinancedByPercentage();',
                            false,
                            10);
                        ?>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="form-group  <?php echo loanForm::showField('rehabCostFinanced'); ?> ">
                    <div class=" rehabCostFinancedDiv <?php echo $rehabCostFinancedDiv; ?>">
                        <?php
                        echo loanForm::label('rehabCostFinanced');
                        echo loanForm::currency(
                            'rehabCostFinanced',
                            LMRequest::$allowToEdit,
                            '1',
                            LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->rehabCostFinanced,
                            '  ',
                            'loanInfoV2Form.calculateRehabValues();',
                                '',
                        );
                        ?>
                    </div>
                </div>
            </div>
            <?php loanForm::popSectionID(); ?>

            <?php loanForm::pushSectionID('EEP'); ?>
            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('isTherePrePaymentPenalty'); ?>">
                    <?php echo loanForm::label('isTherePrePaymentPenalty');
                    echo loanForm::radio(
                        'isTherePrePaymentPenalty',
                        LMRequest::$allowToEdit,
                        1,
                        LMRequest::myFileInfo()->fileHMLOPropertyInfo()->isTherePrePaymentPenalty,
                        [
                            'Yes' => 'Yes',
                            'No'  => 'No',
                        ],
                        'loanInfoV2Form.showAndHidePrePaymentPenalty(this);',
                        ' '
                    );
                    ?>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('prePaymentSelectVal'); ?> prePaymentPenaltyChildDiv <?php echo(LMRequest::myFileInfo()->fileHMLOPropertyInfo()->isTherePrePaymentPenalty == 'Yes' ? '' : ' hide '); ?>">
                    <?php echo loanForm::label('prePaymentSelectVal');

                    $prePaymentPenaltyResArr = glprePaymentPenalty::getPCLevelPrePaymentPenalty(LMRequest::myFileInfo()->LMRInfo()->FPCID, 'FC');
                    $prePaymentSelectValArr = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->prePaymentSelectVal ? explode(',', LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->prePaymentSelectVal) : [];

                    echo loanForm::selectMulti('prePaymentSelectVal',
                        LMRequest::$allowToEdit,
                        0,
                        array_combine($prePaymentSelectValArr, $prePaymentSelectValArr),
                        array_combine($prePaymentPenaltyResArr, $prePaymentPenaltyResArr),
                        '',
                        'chzn-select',
                        ''
                    );
                    ?>
                </div>
            </div>
            <?php loanForm::popSectionID(); ?>

            <?php loanForm::pushSectionID('LT'); ?>

            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('isPropertyHaveSubordinateFinancing'); ?>">
                    <?php
                    echo loanForm::label('isPropertyHaveSubordinateFinancing');
                    echo loanForm::radio(
                        'isPropertyHaveSubordinateFinancing',
                        LMRequest::$allowToEdit,
                        1,
                        LMRequest::myFileInfo()->getLoanPropertySummary()->isPropertyHaveSubordinateFinancing,
                        [
                            '1' => 'Yes',
                            '0' => 'No',
                        ],
                        "loanInfoV2Form.showHideChildFields(this,'bridgeSubordinateFinance');",
                    );
                    ?>
                </div>
            </div>

            <div class="col-md-3">
                <div class="form-group <?php echo loanForm::showField('subordinateFinancingAmount');
                ?> ">
                    <div class="bridgeSubordinateFinance <?php echo !LMRequest::myFileInfo()->getLoanPropertySummary()->isPropertyHaveSubordinateFinancing ? ' hide ' : ''; ?>">
                        <?php
                        echo loanForm::label('subordinateFinancingAmount');
                        echo loanForm::currency(
                            'subordinateFinancingAmount',
                            LMRequest::$allowToEdit,
                            '1',
                            LMRequest::myFileInfo()->getLoanPropertySummary()->subordinateFinancingAmount,
                            null,
                            'loanInfoV2Form.mirrorSubordinateFinancingAmount(this);loanInfoV2Form.calculateBridgeCombinedLoanToValue();',
                                '',
                        );
                        ?>
                    </div>
                </div>
            </div>

        </div>
    </div>

</div>
