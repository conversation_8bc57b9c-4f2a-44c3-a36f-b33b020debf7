<?php
    use models\composite\oDrawManagement\SowTemplateManager;

    $maxCategories = SowTemplateManager::MAX_CATEGORIES;
?>

<div class="card-body workCategoriesCard">
    <!-- Stepper UI-->
    <?php
        require __DIR__ . '/../components/stepper.php';
    ?>
    <?php if($userType == 'borrower' && isset($displayStatus)) { ?>
        <div class="alert <?= $displayStatusClass; ?>" role="alert">
            <i class="fas fa-info-circle"></i>
            <?= $displayStatus; ?>
        </div>
    <?php } ?>
    <!-- Stepper Content -->
    <div class="step-content">
        <div id="content-step-categories">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <h4 class="mb-0 text-success">Categories</h4>
                    <p class="text-muted mb-0">Manage up to <?= $maxCategories; ?> categories for your scope of work template.</p>
                </div>
                <button class="btn btn-sm btn-primary" id="openAddCategoryModalBtn"><i class="fas fa-plus"></i> Add Category</button>
            </div>
            <div class="categories-container sortable">
                <!-- Categories will be loaded here dynamically -->
                <template id="category-item-template">
                    <div class="category-item" data-category-id="" data-name="" data-description="">
                        <div class="category-details">
                            <span class="category-name font-weight-bold"></span>
                            <p class="category-description"></p>
                        </div>
                        <div class="actions">
                            <a href="#" class="edit-category-btn"><i class="fas fa-edit"></i></a>
                            <a href="#" class="delete-category-btn"><i class="fas fa-trash-alt"></i></a>
                        </div>
                    </div>
                </template>
            </div>
            <div class="d-flex justify-content-center mt-5">
                <button type="button" class="btn btn-primary save-cat mr-2" id="save-next">Save</button>
                <button type="button" class="btn btn-secondary switch-step" data-target-step="line-items">Next</button>
            </div>
        </div>
        <?php require __DIR__ . "/_line-item-step-content-{$userType}.php"; ?>
    </div>
</div>

<input type="hidden" id="maxCategories" value="<?= $maxCategories; ?>">
<?php require __DIR__ . '/../components/modals.php'; ?>
<?php require __DIR__ . '/_line-item-category-card.php'; ?>
