<?php

namespace tests\models\composite\oDrawManagement;

use models\composite\oDrawManagement\DrawRequestsHistory;
use models\composite\oDrawManagement\BorrowerDrawLineItemHistory;
use models\lendingwise\tblDrawRequests_h;
use PHPUnit\Framework\TestCase;

/**
 * Tests the DrawRequestsHistory class.
 *
 * @covers \models\composite\oDrawManagement\DrawRequestsHistory::__construct
 * @covers \models\composite\oDrawManagement\DrawRequestsHistory::save
 * @covers \models\composite\oDrawManagement\DrawRequestsHistory::setFromArray
 * @covers \models\composite\oDrawManagement\DrawRequestsHistory::loadLineItems
 * @covers \models\composite\oDrawManagement\DrawRequestsHistory::addLineItem
 * @covers \models\composite\oDrawManagement\DrawRequestsHistory::createHistoryRecord
 * @covers \models\composite\oDrawManagement\DrawRequestsHistory::getHistoryByDrawId
 * @covers \models\composite\oDrawManagement\DrawRequestsHistory::updateLatestHistoryRecord
 * @covers \models\composite\oDrawManagement\DrawRequestsHistory::calculateTotalRequestedAmount
 * @covers \models\composite\oDrawManagement\DrawRequestsHistory::toArray
 */
class DrawRequestsHistoryTest extends TestCase
{
    /**
     * Tests the constructor with null parameter.
     */
    public function testConstructorWithNull(): void
    {
        try {
            $history = new DrawRequestsHistory(null);

            $this->assertInstanceOf(DrawRequestsHistory::class, $history);
            $this->assertNull($history->id);
            $this->assertNull($history->drawId);
            $this->assertNull($history->status);
            $this->assertNull($history->submittedAt);
            $this->assertNull($history->approvedAt);
            $this->assertEquals(0.0, $history->requestedAmount);
            $this->assertEquals(0.0, $history->approvedAmount);
            $this->assertIsArray($history->lineItems);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the constructor with a database object.
     */
    public function testConstructorWithDbObject(): void
    {
        $dbObject = new tblDrawRequests_h();
        $dbObject->id = 123;
        $dbObject->drawId = 456;
        $dbObject->status = DrawRequestsHistory::STATUS_PENDING;
        $dbObject->submittedAt = '2023-01-01 12:00:00';
        $dbObject->requestedAmount = 5000.0;
        $dbObject->approvedAmount = 4500.0;

        try {
            $history = new DrawRequestsHistory($dbObject);

            $this->assertInstanceOf(DrawRequestsHistory::class, $history);
            $this->assertEquals(123, $history->id);
            $this->assertEquals(456, $history->drawId);
            $this->assertEquals(DrawRequestsHistory::STATUS_PENDING, $history->status);
            $this->assertEquals('2023-01-01 12:00:00', $history->submittedAt);
            $this->assertEquals(5000.0, $history->requestedAmount);
            $this->assertEquals(4500.0, $history->approvedAmount);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the status constants.
     */
    public function testStatusConstants(): void
    {
        $this->assertEquals('pending', DrawRequestsHistory::STATUS_PENDING);
        $this->assertEquals('approved', DrawRequestsHistory::STATUS_APPROVED);
        $this->assertEquals('rejected', DrawRequestsHistory::STATUS_REJECTED);
    }

    /**
     * Tests the save method without data.
     */
    public function testSaveWithoutData(): void
    {
        try {
            $history = new DrawRequestsHistory();

            $result = $history->save();
            $this->assertIsArray($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the save method with data.
     */
    public function testSaveWithData(): void
    {
        try {
            $history = new DrawRequestsHistory();
            $data = [
                'drawId' => 456,
                'status' => DrawRequestsHistory::STATUS_PENDING,
                'submittedAt' => '2023-01-01 12:00:00',
                'requestedAmount' => 5000.0,
                'approvedAmount' => 4500.0
            ];

            $result = $history->save($data);
            $this->assertIsArray($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the addLineItem method.
     */
    public function testAddLineItem(): void
    {
        try {
            $history = new DrawRequestsHistory();
            $history->id = 123; // Set ID to simulate saved record

            $lineItemData = [
                'lineItemId' => 789,
                'completedAmount' => 500.0,
                'completedPercent' => 50.0,
                'requestedAmount' => 300.0,
                'disbursedAmount' => 200.0,
                'notes' => 'Test notes',
                'lenderNotes' => 'Test lender notes'
            ];

            $lineItem = $history->addLineItem($lineItemData);

            $this->assertInstanceOf(BorrowerDrawLineItemHistory::class, $lineItem);
            $this->assertCount(1, $history->lineItems);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the createHistoryRecord static method.
     */
    public function testCreateHistoryRecord(): void
    {
        try {
            $lineItemsData = [
                1 => ['requestedAmount' => 1000.0],
                2 => ['requestedAmount' => 2000.0]
            ];

            $history = DrawRequestsHistory::createHistoryRecord(
                456,
                DrawRequestsHistory::STATUS_PENDING,
                $lineItemsData
            );

            $this->assertInstanceOf(DrawRequestsHistory::class, $history);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the getHistoryByDrawId static method.
     */
    public function testGetHistoryByDrawId(): void
    {
        try {
            $result = DrawRequestsHistory::getHistoryByDrawId(456);

            $this->assertIsArray($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the updateLatestHistoryRecord static method.
     */
    public function testUpdateLatestHistoryRecord(): void
    {
        try {
            $lineItemsData = [
                1 => [
                    'completedAmount' => 500.0,
                    'requestedAmount' => 300.0,
                    'disbursedAmount' => 200.0
                ]
            ];

            $result = DrawRequestsHistory::updateLatestHistoryRecord(
                456,
                DrawRequestsHistory::STATUS_APPROVED,
                $lineItemsData
            );

            $this->assertIsBool($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the toArray method.
     */
    public function testToArray(): void
    {
        try {
            $history = new DrawRequestsHistory();
            $history->id = 123;
            $history->drawId = 456;
            $history->status = DrawRequestsHistory::STATUS_PENDING;
            $history->submittedAt = '2023-01-01 12:00:00';
            $history->approvedAt = '2023-01-02 12:00:00';
            $history->requestedAmount = 5000.0;
            $history->approvedAmount = 4500.0;
            $history->wireAmount = 4000.0;
            $history->wireSentDate = '2023-01-03 12:00:00';
            $history->createdAt = '2023-01-01 10:00:00';

            $result = $history->toArray();

            $this->assertIsArray($result);
            $this->assertEquals(123, $result['id']);
            $this->assertEquals(456, $result['drawId']);
            $this->assertEquals(DrawRequestsHistory::STATUS_PENDING, $result['status']);
            $this->assertEquals('2023-01-01 12:00:00', $result['submittedAt']);
            $this->assertEquals('2023-01-02 12:00:00', $result['approvedAt']);
            $this->assertEquals(5000.0, $result['requestedAmount']);
            $this->assertEquals(4500.0, $result['approvedAmount']);
            $this->assertEquals(4000.0, $result['wireAmount']);
            $this->assertEquals('2023-01-03 12:00:00', $result['wireSentDate']);
            $this->assertEquals('2023-01-01 10:00:00', $result['createdAt']);
            $this->assertArrayHasKey('lineItems', $result);
            $this->assertIsArray($result['lineItems']);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests property initialization.
     */
    public function testPropertyInitialization(): void
    {
        try {
            $history = new DrawRequestsHistory();

            $this->assertNull($history->id);
            $this->assertNull($history->drawId);
            $this->assertNull($history->status);
            $this->assertNull($history->submittedAt);
            $this->assertNull($history->approvedAt);
            $this->assertEquals(0.0, $history->requestedAmount);
            $this->assertEquals(0.0, $history->approvedAmount);
            $this->assertNull($history->wireAmount);
            $this->assertNull($history->wireSentDate);
            $this->assertNull($history->createdAt);
            $this->assertIsArray($history->lineItems);
            $this->assertEmpty($history->lineItems);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests save method with complete data.
     */
    public function testSaveWithCompleteData(): void
    {
        try {
            $history = new DrawRequestsHistory();
            $data = [
                'drawId' => 456,
                'status' => DrawRequestsHistory::STATUS_APPROVED,
                'submittedAt' => '2023-01-01 12:00:00',
                'approvedAt' => '2023-01-02 12:00:00',
                'requestedAmount' => 5000.0,
                'approvedAmount' => 4500.0,
                'wireAmount' => 4000.0,
                'wireSentDate' => '2023-01-03 12:00:00'
            ];

            $result = $history->save($data);
            $this->assertIsArray($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests createHistoryRecord with empty line items.
     */
    public function testCreateHistoryRecordWithEmptyLineItems(): void
    {
        try {
            $history = DrawRequestsHistory::createHistoryRecord(
                456,
                DrawRequestsHistory::STATUS_PENDING,
                []
            );

            $this->assertInstanceOf(DrawRequestsHistory::class, $history);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests createHistoryRecord with line items containing various amounts.
     */
    public function testCreateHistoryRecordWithVariousAmounts(): void
    {
        try {
            $lineItemsData = [
                1 => ['requestedAmount' => 1000.0],
                2 => ['requestedAmount' => 2000.0],
                3 => ['requestedAmount' => 500.0],
                4 => []
            ];

            $history = DrawRequestsHistory::createHistoryRecord(
                456,
                DrawRequestsHistory::STATUS_PENDING,
                $lineItemsData
            );

            $this->assertInstanceOf(DrawRequestsHistory::class, $history);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests addLineItem with minimal data.
     */
    public function testAddLineItemWithMinimalData(): void
    {
        try {
            $history = new DrawRequestsHistory();
            $history->id = 123;

            $lineItemData = [
                'lineItemId' => 789,
                'requestedAmount' => 300.0
            ];

            $lineItem = $history->addLineItem($lineItemData);

            $this->assertInstanceOf(BorrowerDrawLineItemHistory::class, $lineItem);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests toArray with line items.
     */
    public function testToArrayWithLineItems(): void
    {
        try {
            $history = new DrawRequestsHistory();
            $history->id = 123;
            $history->drawId = 456;
            $history->status = DrawRequestsHistory::STATUS_PENDING;

            // Mock line items
            $mockLineItem = new class {
                public function toArray(): array {
                    return [
                        'id' => 1,
                        'lineItemId' => 789,
                        'requestedAmount' => 300.0
                    ];
                }
            };

            $history->lineItems = [$mockLineItem];

            $result = $history->toArray();

            $this->assertIsArray($result);
            $this->assertArrayHasKey('lineItems', $result);
            $this->assertCount(1, $result['lineItems']);
            $this->assertEquals(1, $result['lineItems'][0]['id']);
            $this->assertEquals(789, $result['lineItems'][0]['lineItemId']);
            $this->assertEquals(300.0, $result['lineItems'][0]['requestedAmount']);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests save method preserves existing values when not provided.
     */
    public function testSavePreservesExistingValues(): void
    {
        try {
            $history = new DrawRequestsHistory();


            $history->drawId = 456;
            $history->status = DrawRequestsHistory::STATUS_PENDING;
            $history->requestedAmount = 5000.0;

            $data = [
                'approvedAmount' => 4500.0
            ];

            $result = $history->save($data);
            $this->assertIsArray($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }
}
