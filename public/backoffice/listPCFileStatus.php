<?php
global $fileStatusModules, $assignedPCID, $modulesArray;

use models\composite\oPC\getPCModules;
use models\composite\oPrimaryStatus\getNoOfFilesOnStatusForPC;
use models\composite\oPrimaryStatus\getPCPrimaryStatus;
use models\cypher;
use models\standard\Strings;

$PSId = 0;
$inputArray = [];
$primaryStatus = '';
$dispOrder = '';
$allowOthersToUpdate = 1;
$rowCnt = 0;
$filesCntArray = [];
$buttonDisp = 'display:none';
$countDisp = 'display:none';
$fileStatusArray = [];
$statusArrayKeys = [];
$statusArray = [];

$inArray['searchTerm'] = $fileStatusModules;
$inArray['PCID'] = $assignedPCID;
$inArray['opt1'] = 'list';
$inArray['opt2'] = 'Y';


$fileStatusArray = getPCPrimaryStatus::getReport($inArray);
if (count($fileStatusArray ?? []) > 0) {
    $statusArray = $fileStatusArray['primaryStatusInfo'];
}
$inArray['searchFor'] = 'all';



$filesCntArray = getNoOfFilesOnStatusForPC::getReport($inArray);


$PCModulesArray = [];
$isHMLO = 0;

if ($assignedPCID > 0) {
    $ip['PCID'] = $assignedPCID;
    $ip['keyNeeded'] = 'n';
    $modulesInfoArray = getPCModules::getReport($ip);
}

for ($i = 0; $i < count($modulesInfoArray ?? []); $i++) {
    $PCModulesArray[] = $modulesInfoArray[$i]['moduleCode'];
}
if (in_array('HMLO', $PCModulesArray)) $isHMLO = 1;



$PCSelectedModulesArray = [];
if ($fileStatusModules != '') {
    for ($j = 0; $j < count($modulesArray ?? []); $j++) {
        if (trim($fileStatusModules) == trim($modulesArray[$j]['moduleCode'])) {
            $PCSelectedModulesArray[] = $modulesArray[$j];
            break;
        }
    }
} else {
    $PCSelectedModulesArray = $modulesArray;
}
?>
<div class="accordion accordion-solid accordion-toggle-plus mb-7 col-md-12" id="accordionPCReqDocs">
    <?php
    for ($j = 0; $j < count($PCSelectedModulesArray ?? []); $j++) {
        $moduleCode = '';
        $moduleName = '';
        $moduleCode = trim($PCSelectedModulesArray[$j]['moduleCode']);
        $moduleName = trim($PCSelectedModulesArray[$j]['moduleName']);
        $dataId = '';
        if ($moduleName == 'Loan Mod/Foreclosure Defense') {
            $dataId = 'loanmodaddnew';
        } elseif ($moduleName == 'Short Sale') {
            $dataId = 'shortsaleaddnew';
        } elseif ($moduleName == 'Commercial/Residential Real Estate') {
            $dataId = 'commercialaddnew';
        } elseif ($moduleName == 'Business Funding') {
            $dataId = 'businessfundingaddnew';
        } elseif ($moduleName == 'Conventional/Agency') {
            $dataId = 'conventionaladdnew';
        } elseif ($moduleName == 'Credit REPAIR') {
            $dataId = 'creditaddnew';
        } elseif ($moduleName == 'Debt Settlement') {
            $dataId = 'debtsettlementaddnew';
        } elseif ($moduleName == 'Equipment Financing') {
            $dataId = 'equipmentaddnew';
        } elseif ($moduleName == 'Funding') {
            $dataId = 'fundingaddnew';
        } elseif ($moduleName == 'Loan Origination') {
            $dataId = 'loanaddnew';
        } elseif ($moduleName == 'Biz Funding') {
            $dataId = 'bizaddnew';
        } elseif ($moduleName == 'Private Money') {
            $dataId = 'privateaddnew';
        } elseif ($moduleName == 'Student Loan MOD') {
            $dataId = 'studentaddnew';
        } elseif ($moduleName == 'Tax Settlement') {
            $dataId = 'taxaddnew';
        }
        ?>
        <div class="card">
            <div class="card-header" id="heading_<?php echo $moduleCode; ?>">
                <div class="card-title" data-toggle="collapse" data-target="#collapse_<?php echo $moduleCode; ?>">
                    <?php echo $moduleName ?>&nbsp;
                </div>
            </div>
            <div id="collapse_<?php echo $moduleCode; ?>" class="collapse show" data-parent="">
                <div class="card-body px-4 m-0">
                    <div id="buttonDisp" style="text-align: right" class=" mb-2"><a style="text-decoration:none"
                                                                                    id="<?php echo $dataId; ?>"
                                                                                    data-href="<?php echo CONST_URL_POPS; ?>addNewPCFileStatus.php"
                                                                                    title="Click to add new Status"
                                                                                    data-wsize='modal-lg'
                                                                                    data-toggle='modal'
                                                                                    data-target='#exampleModal1'
                                                                                    data-name="Add / Edit File Status "
                                                                                    class="btn btn-primary tooltipClass"
                                                                                    data-id="PCID=<?php echo cypher::myEncryption($assignedPCID) ?>&MC=<?php echo cypher::myEncryption($moduleCode) ?>&fileStatusModules=<?php echo cypher::myEncryption($fileStatusModules) ?>&isHMLO=<?php echo cypher::myEncryption($isHMLO) ?>">Add
                            New</a></div>

                    <div class="table-responsive">
                        <table class="table table-hover LWcustomTable  table-bordered table-condensed table-sm table-vertical-center"
                               id="PCFileStatusTable_<?php echo $moduleCode; ?>">
                            <thead class="thead-light">
                            <tr>
                                <!--<td class="pad2">Display Order</td> -->
                                <th colspan="3">
                                    <div class="align-center">Primary File Status</div>
                                </th>
                                <th id="th_files_<?php echo $moduleCode?>"># files</th>
                                <th style="text-align: center;"><span id="th_backoffice_<?php echo $moduleCode?>">BackOffice</span></th>
                                <th style="text-align: center;"><span id="th_branch_<?php echo $moduleCode?>">Branch</span></th>
                                <th style="text-align: center;"><span id="th_lo_<?php echo $moduleCode?>">Loan Officer</span></th>
                                <th style="text-align: center;"><span id="th_broker_<?php echo $moduleCode?>">Broker</span></th>
                                <th style="text-align: center;"><span id="th_borrower_<?php echo $moduleCode?>"><?php if ($isHMLO == 1) { ?>Borrower<?php } else { ?>Client<?php } ?></span></th>
                                <th>&nbsp;</th>
                            </tr>
                            </thead>
                            <tbody id="tabledivbody1_<?php echo $moduleCode; ?>" class="ui-sortable-helper">
                            <?php
                            $dispOrderArray = [];
                            $PCFileStatusArray = [];
                            if (array_key_exists($moduleCode, $statusArray)) {
                                $PCFileStatusArray = $statusArray[$moduleCode];
                            }
                            for ($c = 0; $c < count($PCFileStatusArray ?? []); $c++) {
                                $primaryStatus = '';
                                $dispOrder = '';
                                $allowOthersToUpdate = 0;
                                $PSId = 0;
                                $noOfFileCnt = 0;
                                $statusDesc = '';
                                $PSMID = 0;
                                $allowAgentToEditFile = 0;
                                $allowLoanOfficerToEditFile = 0;
                                $allowClientToEditFile = 0;
                                $allowBOToEditLMRFile = 0;

                                $PSId = trim($PCFileStatusArray[$c]['PSID']);
                                $PSMID = trim($PCFileStatusArray[$c]['PSMID']);

                                $primaryStatus = trim($PCFileStatusArray[$c]['primaryStatus']);
                                $dispOrder = trim($PCFileStatusArray[$c]['displayOrder']);
                                $allowOthersToUpdate = trim($PCFileStatusArray[$c]['allowOthersToUpdate']);
                                $statusDesc = trim($PCFileStatusArray[$c]['statusDesc']);
                                $allowAgentToEditFile = trim($PCFileStatusArray[$c]['allowAgentToEditFile']);
                                $allowLoanOfficerToEditFile = trim($PCFileStatusArray[$c]['allowLoanOfficerToEditFile']);
                                $allowClientToEditFile = trim($PCFileStatusArray[$c]['allowClientToEditFile']);
                                $allowBOToEditLMRFile = trim($PCFileStatusArray[$c]['allowBOToEditFile']);
                                $dispOrderArray[] = $dispOrder;
                                /*
                                        if($primaryStatus == "New"){
                                            $allowOthersToUpdate = 1;
                                            $allowAgentToEditFile = 1;
                                            $allowClientToEditFile = 1;
                                            $allowBOToEditLMRFile = 1;
                                        }
                                        if($primaryStatus == "Lead"){
                                            $allowOthersToUpdate = 1;
                                            $allowAgentToEditFile = 1;
                                            $allowClientToEditFile = 1;
                                            $allowBOToEditLMRFile = 1;
                                        }
                                */
                                if ($allowOthersToUpdate == 1) $allowLink = "<a class=\"fa fa-check text-success \" style=\"text-decoration:none;\"></a>";
                                else  $allowLink = "<a class=\"text-danger fa fa-times \" style=\"text-decoration:none;\"></a>";


                                if ($allowAgentToEditFile == 1) $allowAgentLink = "<a class=\"fa fa-check text-success \" style=\"text-decoration:none;\"></a>";
                                else  $allowAgentLink = "<a class=\"text-danger fa fa-times \" style=\"text-decoration:none;\"></a>";

                                if ($allowLoanOfficerToEditFile == 1) $allowLoanOfficerLink = "<a class=\"fa fa-check text-success \" style=\"text-decoration:none;\"></a>";
                                else  $allowLoanOfficerLink = "<a class=\"text-danger fa fa-times \" style=\"text-decoration:none;\"></a>";


                                if ($allowClientToEditFile == 1) $allowClientLink = "<a class=\"fa fa-check text-success \" style=\"text-decoration:none;\"></a>";
                                else $allowClientLink = "<a class=\"text-danger fa fa-times \" style=\"text-decoration:none;\"></a>";
                                if ($allowBOToEditLMRFile == 1) $allowBoLink = "<a class=\"fa fa-check text-success \" style=\"text-decoration:none;\"></a>";
                                else  $allowBoLink = "<a class=\"text-danger fa fa-times \" style=\"text-decoration:none;\"></a>";

                                if (count($filesCntArray ?? []) > 0) {
                                    if (array_key_exists($moduleCode, $filesCntArray)) {
                                        if (array_key_exists($PSId, $filesCntArray[$moduleCode])) {
                                            $noOfFileCnt = $filesCntArray[$moduleCode][$PSId];
                                        }
                                    }
                                }
                                ?>
                                <tr id="PSID_<?php echo $PSMID ?>" title="<?php echo $primaryStatus ?>">
                                    <!-- <td width="30" class="pad2"><?php echo $dispOrder ?></td>  -->
                                    <td>
                                        <?php
                                        if ($statusDesc != '') {
//        $statusDesc = replaceProcessTextareaString($statusDesc);
                                            if (strlen($statusDesc) > 400) {
                                                $tipWidth = '450';
                                            } else {
                                                $tipWidth = '0';
                                            }
                                            ?>
                                            <a class="fa fa-info-circle  tooltipClass" data-html="true" style="text-decoration:none;"
                                               title="<?php echo nl2br(htmlentities($statusDesc)); ?>"></a>
                                            <?php
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <?php
                                        //    if(($primaryStatus == 'New') || ($primaryStatus == 'Lead')) {
                                        //    } else {
                                        ?>
                                        <?php if (strtolower($primaryStatus) != 'lead') { ?>
                                            <div>
                                                <a class="change btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon m-1"
                                                   style="text-decoration:none;"
                                                   data-href="<?php echo CONST_URL_POPS; ?>addNewPCFileStatus.php"
                                                   title="Click to add new Status"
                                                   data-wsize='modal-lg' data-toggle='modal'
                                                   data-target='#exampleModal1'
                                                   data-name="Add / Edit File Status "
                                                   class="btn btn-primary tooltipClass"
                                                   data-id="PCID=<?php echo cypher::myEncryption($assignedPCID) ?>&PSId=<?php echo cypher::myEncryption($PSId) ?>&isHMLO=<?php echo cypher::myEncryption($isHMLO) ?>">
                                                    <i class="tooltipClass far fa-edit"
                                                       id="editstatus_<?php echo $moduleCode . '_' . Strings::removeSpaceWithSpecialChars($primaryStatus); ?>"
                                                       title="Click to edit"></i></a>
                                            </div>
                                        <?php } ?>
                                        <?php
                                        //    }
                                        ?>
                                    </td>
                                    <td>
                                        <div style="float:left"><?php echo $primaryStatus ?></div>
                                    </td>
                                    <td><?php echo $noOfFileCnt ?></td>
                                    <td>
                                        <div style="text-align: center;" id="icon_bo_<?php echo Strings::removeSpaceWithSpecialChars($primaryStatus)?>"><?php echo $allowBoLink ?></div>
                                    </td>
                                    <td>
                                        <div style="text-align: center;" id="icon_branch_<?php echo Strings::removeSpaceWithSpecialChars($primaryStatus)?>"><?php echo $allowLink ?></div>
                                    </td>
                                    <td title="Loan Officer">
                                        <div style="text-align: center;" id="icon_lo_<?php echo Strings::removeSpaceWithSpecialChars($primaryStatus)?>"><?php echo $allowLoanOfficerLink ?></div>
                                    </td>
                                    <td title="Broker">
                                        <div style="text-align: center;" id="icon_broker_<?php echo Strings::removeSpaceWithSpecialChars($primaryStatus)?>"><?php echo $allowAgentLink ?></div>
                                    </td>
                                    <td>
                                        <div style="text-align: center;" id="icon_borrower_<?php echo Strings::removeSpaceWithSpecialChars($primaryStatus)?>"><?php echo $allowClientLink ?></div>
                                    </td>
                                    <td>
                                        <?php
                                        if (($primaryStatus == 'New') || ($primaryStatus == 'Lead')) {
                                        } else {
                                            ?>
                                            <div class="align-center"><a
                                                        class="change btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon m-1"
                                                        style="text-decoration:none;"
                                                        href="javascript:deleteStatus('<?php echo cypher::myEncryption($PSId) ?>', 'PSID_<?php echo $PSMID ?>', '<?php echo $noOfFileCnt ?>','<?php echo $moduleCode; ?>','<?php echo cypher::myEncryption($assignedPCID); ?>');">
                                                    <i class="tooltipClass flaticon2-trash"
                                                       title="Click to Delete"
                                                       id="deletestatus_<?php echo Strings::removeSpaceWithSpecialChars($primaryStatus); ?>"></i></a></div>
                                            <?php
                                        }
                                        ?>
                                    </td>
                                </tr>
                                <?php
                                $rowCnt++;

                            }
                            ?>
                            </tbody>
                        </table>
                    </div> <!-- Module Div END -->
                </div>
            </div>
        </div>
        <?php
        if (($j + 1) % 2 == 0) echo "<div class=\"clear\"></div>";
    }
    ?>
</div>
<input type="hidden" name="totalRowCount" id="totalRowCount" value="<?php echo $rowCnt ?>">
<input type="hidden" name="multipleDisplayOrder" id="multipleDisplayOrder"
       value="<?php echo implode(',', $dispOrderArray); ?>">

<div class="clear"></div>

<script type='text/javascript'>
    $(document).ready(function () {

        <?php
        for($j = 0;$j < count($PCSelectedModulesArray ?? []);$j++) {
        $moduleCode = '';
        $moduleCode = trim($PCSelectedModulesArray[$j]['moduleCode']);
        ?>
        $("#tabledivbody1_<?php echo $moduleCode;?>").sortable({
            items: "tr",
            cursor: 'move',
            opacity: 0.6,
            placeholder: "ui-state-highlight",
            axis: 'y',
            containment: "parent",
            update: function () {
                $('.saveDispOrder').css("display", "block");
                //$('.with-children-tip > *').hideTip();
            }
        });
        // $( "#tabledivbody" ).disableSelection();

        $('.dispButton').click(function (event) {
            var order = $("#tabledivbody1_<?php echo $moduleCode;?>").sortable("serialize");
            $('#PCUpdMsgBottom').html('<img src="<?php echo IMG_PROGRESS_BAR; ?>">');
            $.post("updatePCFileStatusOrder.php", order, function (theResponse) {
                $("#sessMsg").html('<h4>' + theResponse + '<h4>');
                $('.saveDispOrder').css("display", "none");
                $('#PCUpdMsgBottom').html('');
                //$('.with-tip, .with-children-tip > *').tip();
            });

            event.preventDefault();
        });
        <?php
        }
        ?>
    });
</script>
