<?php

use models\constants\creditDecision;
use models\constants\gl\glDate;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\creditDecision as creditDecisionController;
use models\Controllers\loanForm;
use models\lendingwise\tblCreditDecisionException;
use models\standard\Arrays;
use models\standard\Dates;
use models\standard\Strings;

$LMRId = LMRequest::$LMRId;

$exceptionCategoryArray = creditDecision::exceptionCategory();
$boStaffArray = creditDecision::getBOEmployeeAssignedToFile('CD', $LMRId);

$creditDecisionExceptionData = creditDecisionController::$creditDecisionExceptionData;
if(!sizeof($creditDecisionExceptionData ?? [])) {
    $creditDecisionExceptionData = [
            new tblCreditDecisionException(),
    ];
}

loanForm::pushSectionID('CDE');
?>
<div class="card card-custom exception">
    <div class="card-header card-header-tabs-line bg-gray-100">
        <div class="card-title">
            <h3 class="card-label">
                Exception
            </h3>
        </div>
        <div class="card-toolbar">
            <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer"
                  data-card-tool="toggle"
                  data-section="exception"
                  data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Section">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
        </div>
    </div>
    <div class="card-body exception_body">
        <?php
        $exp_i = 1;
        foreach ($creditDecisionExceptionData as $expData) {
            $classRemoveIcon = $exp_i == '1' ? 'hide' : 'show';
            ?>
            <div class="exceptionFields" id="exceptionFields_<?php echo $exp_i;?>">
                <input type="hidden" name="exceptionFields[<?php echo $exp_i;?>][creditDecisionExceptionId]" id="creditDecisionExceptionId" value="<?php echo $expData->id;?>">
                <div class="exceptionFieldsRemove text-right <?php echo $classRemoveIcon;?>">
                <span class="btn btn-sm btn-danger btn-icon ml-2 cursor-pointer"
                      data-toggle="popover"
                      data-content="Click to remove"
                      onclick="creditDecisionJs.removeFields(this)"
                      data-clone-section="exceptionFields"
                      data-inc-id="<?php echo $exp_i;?>"
                      data-row-id="<?php echo $expData->id;?>"
                      data-LMRId="<?php echo $LMRId;?>"
                >
                    <i class="icon-md fas fa-minus"></i>
                </span>
                </div>
                <div class="form-group row">
                    <div class="col-md-6 <?php echo loanForm::showField('exceptionCategory'); ?>">
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="exceptionCategory_1">
                                <?php echo loanForm::label('exceptionCategory'); ?>
                            </label>
                            <div class="col-md-7">
                                <select name="exceptionFields[<?php echo $exp_i;?>][exceptionCategory]" 
                                id="exceptionCategory_<?php echo $exp_i;?>" class="form-control 
                                <?php echo loanForm::isMandatory('exceptionCategory') ? 'mandatory' : ''; ?>"
                                onchange="creditDecisionJs.exceptionSubCategory(this)">
                                <option value="">Select</option>
                                <?php foreach ($exceptionCategoryArray as $exceptionCategoryKey => $exceptionCategoryValue) { ?>
                                    <option value="<?php echo $exceptionCategoryKey; ?>" 
                                    <?php echo Arrays::isSelected($exceptionCategoryKey, $expData->exceptionCategory);?>
                                    ><?php echo $exceptionCategoryValue; ?></option>
                                <?php } ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 <?php echo loanForm::showField('exceptionSubCategory'); ?>">
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="exceptionSubCategory_<?php echo $exp_i;?>">
                                <?php echo loanForm::label('exceptionSubCategory'); ?>
                            </label>
                            <div class="col-md-7">
                                <select name="exceptionFields[<?php echo $exp_i;?>][exceptionSubCategory]" 
                                id="exceptionSubCategory_<?php echo $exp_i;?>" class="form-control 
                                <?php echo loanForm::isMandatory('exceptionSubCategory') ? 'mandatory' : ''; ?>">
                                <option value="">Select</option>
                                <?php
                                if($expData->exceptionCategory) {
                                    $exceptionSubCategory = creditDecision::getExceptionSubCategory($expData->exceptionCategory);
                                    foreach ($exceptionSubCategory as $exceptionSubCategoryKey => $exceptionSubCategoryValue) { ?>
                                        <option value="<?php echo $exceptionSubCategoryKey; ?>" 
                                        <?php echo Arrays::isSelected($exceptionSubCategoryKey, $expData->exceptionSubCategory);?> 
                                        ><?php echo $exceptionSubCategoryValue; ?></option>
                                        <?php }
                                    } ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 <?php echo loanForm::showField('exceptionDate'); ?>">
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="exceptionDate_<?php echo $exp_i;?>">
                                <?php echo loanForm::label('exceptionDate'); ?>
                            </label>
                            <div class="col-md-7 input-group">
                                <div class="input-group-prepend actionDate">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary icon-lg"></i>
                                    </span>
                                </div>
                                <input
                                class="form-control input-sm exceptionDate dateNewClass <?php echo loanForm::isMandatory('exceptionDate') ? 'mandatory' : ''; ?>"
                                type="text"
                                name="exceptionFields[<?php echo $exp_i;?>][exceptionDate]"
                                id="exceptionDate_<?php echo $exp_i;?>"
                                value="<?php echo Dates::formatDateWithRE($expData->exceptionDate, 'YMD H:i:s', 'm/d/Y');?>"
                                data-before-creation-date="true"
                                data-start-date="<?php echo glDate::getCreationDate(LMRequest::File()->recordDate); ?>"
                                placeholder="MM/DD/YYYY"
                                maxlength="10"
                                autocomplete="off" >
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 <?php echo loanForm::showField('exceptionApprovedBy'); ?>">
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="exceptionApprovedBy_<?php echo $exp_i;?>">
                                <?php echo loanForm::label('exceptionApprovedBy'); ?>
                            </label>
                            <div class="col-md-7">
                                <select name="exceptionFields[<?php echo $exp_i;?>][exceptionApprovedBy]" 
                                id="exceptionApprovedBy_<?php echo $exp_i;?>" class="form-control 
                                <?php echo loanForm::isMandatory('exceptionApprovedBy') ? 'mandatory' : ''; ?>">
                                    <option value="">Select</option>
                                    <?php foreach ($boStaffArray as $boStaffArrayKey => $boStaffArrayValue) { ?>
                                        <option value="<?php echo $boStaffArrayValue['AID']; ?>" 
                                        <?php echo Arrays::isSelected($boStaffArrayValue['AID'], $expData->exceptionApprovedBy);?> 
                                        ><?php echo $boStaffArrayValue['role'] . ' - ' . $boStaffArrayValue['processorName']; ?>
                                    </option>
                                    <?php } ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-md-3 <?php echo loanForm::showField('compensatingFactors'); ?>">
                        <label class="font-weight-bold"><?php echo loanForm::label('compensatingFactors'); ?></label>
                        <div class="checkbox-inline">
                            <label class="checkbox checkbox-lg">
                                <input type="checkbox" name="exceptionFields[<?php echo $exp_i;?>][borrowerREOExperience]" value="Yes" <?php echo Strings::isChecked('Yes', $expData->borrowerREOExperience);?> >
                                <span></span>Borrower REO Experience</label>
                        </div>
                        <div class="checkbox-inline">
                            <label class="checkbox checkbox-lg">
                                <input type="checkbox" name="exceptionFields[<?php echo $exp_i;?>][performingRepeatBorrower]" value="Yes" <?php echo Strings::isChecked('Yes', $expData->performingRepeatBorrower);?> >
                                <span></span>Performing Repeat Borrower</label>
                        </div>
                        <div class="checkbox-inline">
                            <label class="checkbox checkbox-lg">
                                <input type="checkbox" name="exceptionFields[<?php echo $exp_i;?>][reserves]" value="Yes"  <?php echo Strings::isChecked('Yes', $expData->reserves);?> >
                                <span></span>Reserves</label>
                        </div>
                        <div class="checkbox-inline">
                            <label class="checkbox checkbox-lg">
                                <input type="checkbox" name="exceptionFields[<?php echo $exp_i;?>][positivePortfolioCashFlow]" value="Yes"  <?php echo Strings::isChecked('Yes', $expData->positivePortfolioCashFlow);?> >
                                <span></span>Positive Portfolio Cash Flow</label>
                        </div>
                        <div class="checkbox-inline">
                            <label class="checkbox checkbox-lg">
                                <input type="checkbox" name="exceptionFields[<?php echo $exp_i;?>][dscr]" value="Yes"  <?php echo Strings::isChecked('Yes', $expData->dscr);?> >
                                <span></span>DSCR</label>
                        </div>
                    </div>
                    <div class="col-md-3 <?php echo loanForm::showField('compensatingFactors'); ?>">
                        <label></label>
                        <div class="checkbox-inline">
                            <label class="checkbox checkbox-lg">
                                <input type="checkbox" name="exceptionFields[<?php echo $exp_i;?>][lowLTV]" value="Yes"  <?php echo Strings::isChecked('Yes', $expData->lowLTV);?> >
                                <span></span>Low LTV (Less than 65%)</label>
                        </div>
                        <div class="checkbox-inline">
                            <label class="checkbox checkbox-lg">
                                <input type="checkbox" name="exceptionFields[<?php echo $exp_i;?>][creditFICO]" value="Yes"  <?php echo Strings::isChecked('Yes', $expData->creditFICO);?> >
                                <span></span>Credit / FICO</label>
                        </div>
                        <div class="checkbox-inline">
                            <label class="checkbox checkbox-lg">
                                <input type="checkbox" name="exceptionFields[<?php echo $exp_i;?>][interestHoldBack]" value="Yes" <?php echo Strings::isChecked('Yes', $expData->interestHoldBack);?> >
                                <span></span>Interest Holdback</label>
                        </div>
                        <div class="checkbox-inline">
                            <label class="checkbox checkbox-lg">
                                <input type="checkbox" name="exceptionFields[<?php echo $exp_i;?>][ownsPrimaryResidence]" value="Yes" <?php echo Strings::isChecked('Yes', $expData->ownsPrimaryResidence);?> >
                                <span></span>Owns Primary Residence</label>
                        </div>
                        <div class="checkbox-inline">
                            <label class="checkbox checkbox-lg">
                                <input type="checkbox" name="exceptionFields[<?php echo $exp_i;?>][collateralLocation]" value="Yes" <?php echo Strings::isChecked('Yes', $expData->collateralLocation);?> >
                                <span></span>Collateral Location</label>
                        </div>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-md-6 <?php echo loanForm::showField('actualGuideline'); ?>">
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="actualGuideline_<?php echo $exp_i;?>">
                                <?php echo loanForm::label('actualGuideline'); ?>
                            </label>
                            <div class="col-md-7">
                                <textarea name="exceptionFields[<?php echo $exp_i;?>][actualGuideline]" 
                                id="actualGuideline_<?php echo $exp_i;?>" class="form-control validateMaxLength 
                                <?php echo loanForm::isMandatory('actualGuideline') ? 'mandatory' : ''; ?>"
                                  maxlength="<?php echo loanForm::getFieldLength('actualGuideline','tblCreditDecisionException'); ?>"
                                ><?php echo $expData->actualGuideline;?></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 <?php echo loanForm::showField('exceptionDescription'); ?>">
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="exceptionDescription_<?php echo $exp_i;?>">
                                <?php echo loanForm::label('exceptionDescription'); ?>
                            </label>
                            <div class="col-md-7">
                                <textarea name="exceptionFields[<?php echo $exp_i;?>][exceptionDescription]"
                                 id="exceptionDescription_<?php echo $exp_i;?>" class="form-control validateMaxLength 
                                 <?php echo loanForm::isMandatory('exceptionDescription') ? 'mandatory' : ''; ?>"
                                  maxlength="<?php echo loanForm::getFieldLength('exceptionDescription','tblCreditDecisionException'); ?>"
                                 ><?php echo $expData->exceptionDescription;?></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 <?php echo loanForm::showField('exceptionNotes'); ?>">
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="exceptionNotes_<?php echo $exp_i;?>">
                                <?php echo loanForm::label('exceptionNotes'); ?>
                            </label>
                            <div class="col-md-7">
                                <textarea name="exceptionFields[<?php echo $exp_i;?>][exceptionNotes]" 
                                id="exceptionNotes_<?php echo $exp_i;?>" class="form-control validateMaxLength 
                                <?php echo loanForm::isMandatory('exceptionNotes') ? 'mandatory' : ''; ?>"
                                  maxlength="<?php echo loanForm::getFieldLength('exceptionNotes','tblCreditDecisionException'); ?>"
                                 ><?php echo $expData->exceptionNotes;?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php $exp_i++; } ?>


        <div class="row">
            <div class="col-lg-3">
                <label class="font-weight-bold">Add Exception</label>
                <span class="btn btn-sm btn-success btn-icon ml-2 cursor-pointer"
                      data-toggle="popover"
                      data-content="Click to add Exception (Max 5)"
                      onclick="creditDecisionJs.addMoreFields('exceptionFields');" >
                    <i class=" icon-md fas fa-plus "></i>
                </span>
            </div>
        </div>

    </div>
</div>
