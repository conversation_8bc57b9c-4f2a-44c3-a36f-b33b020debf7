<?php
global $QAInfo, $fileTab, $fieldsInfo, $HMLOLoanInfoSectionsDisp, $allowToEdit, $tabIndex, $PCquickAppFieldsInfo,
       $coBorDisp, $publicUser,$borrowerType;

use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\CustomField;
use models\lendingwise\tblFile;
use models\lendingwise\tblQAInfo;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Strings;

$BEthnicity = [];
$BRace = [];
$bFiEthnicitySub = [];
$bFiRaceSub = [];
$CBEthnicity = [];
$CBRace = [];
$CBEthnicitySub = [];
$CBRaceSub = [];
if (count($QAInfo) > 0) {
    $QAID = Strings::showField('QAID', 'QAInfo');
    $bankruptcyDispositionStatus = Strings::showField('bankruptcyDispositionStatus', 'QAInfo');
    $saleForHowLong = Strings::showField('saleForHowLong', 'QAInfo');
    $PublishBInfo = Strings::showField('PublishBInfo', 'QAInfo');
    $BEthnicity = Strings::showField('BEthnicity', 'QAInfo') != '' ? Strings::showField('BEthnicity', 'QAInfo') : Strings::showField('ethnicity', 'clientInfo');
    $BEthnicity = ($BEthnicity != '') ? explode(',', $BEthnicity) : [];
    $BRace = Strings::showField('BRace', 'QAInfo') != '' ? Strings::showField('BRace', 'QAInfo') : Strings::showField('race', 'clientInfo');
    $BRace = ($BRace != '') ? explode(',', $BRace) : [];
    $BGender = Strings::showField('BGender', 'QAInfo') != '' ? Strings::showField('BGender', 'QAInfo') : Strings::showField('gender', 'clientInfo');
    $BVeteran = Strings::showField('BVeteran', 'QAInfo') != '' ? Strings::showField('BVeteran', 'QAInfo') : Strings::showField('veteran', 'clientInfo');
    $bFiEthnicity = Strings::showField('bFiEthnicity', 'QAInfo') != '' ? Strings::showField('bFiEthnicity', 'QAInfo') : Strings::showField('FIEthnicity', 'clientInfo');
    $bFiEthnicitySub = Strings::showField('bFiEthnicitySub', 'QAInfo') != '' ? Strings::showField('bFiEthnicitySub', 'QAInfo') : Strings::showField('FIEthnicitySub', 'clientInfo');
    $bFiEthnicitySub = ($bFiEthnicitySub != '') ? explode(',', $bFiEthnicitySub) : [];
    $bFiEthnicitySubOther = Strings::showField('bFiEthnicitySubOther', 'QAInfo') != '' ? Strings::showField('bFiEthnicitySubOther', 'QAInfo') : Strings::showField('FIEthnicitySubOther', 'clientInfo');
    $bFiSex = Strings::showField('bFiSex', 'QAInfo') != '' ? Strings::showField('bFiSex', 'QAInfo') : Strings::showField('FISex', 'clientInfo');
    $bFiRace = Strings::showField('bFiRace', 'QAInfo') != '' ? Strings::showField('bFiRace', 'QAInfo') : Strings::showField('FIRace', 'clientInfo');
    $bFiRaceSub = Strings::showField('bFiRaceSub', 'QAInfo') != '' ? Strings::showField('bFiRaceSub', 'QAInfo') : Strings::showField('FIRaceSub', 'clientInfo');
    $bFiRaceSub = ($bFiRaceSub != '') ? explode(',', $bFiRaceSub) : [];
    $bFiRaceAsianOther = Strings::showField('bFiRaceAsianOther', 'QAInfo') != '' ? Strings::showField('bFiRaceAsianOther', 'QAInfo') : Strings::showField('FIRaceAsianOther', 'clientInfo');
    $bFiRacePacificOther = Strings::showField('bFiRacePacificOther', 'QAInfo') != '' ? Strings::showField('bFiRacePacificOther', 'QAInfo') : Strings::showField('FIRacePacificOther', 'clientInfo');
    $bDemoInfo = Strings::showField('bDemoInfo', 'QAInfo') != '' ? Strings::showField('bDemoInfo', 'QAInfo') : Strings::showField('DemoInfo', 'clientInfo');
    $PublishCBInfo = Strings::showField('PublishCBInfo', 'QAInfo');
    $CBEthnicity = Strings::showField('CBEthnicity', 'QAInfo');
    $CBEthnicity = ($CBEthnicity != '') ? explode(',', $CBEthnicity) : [];
    $CBRace = Strings::showField('CBRace', 'QAInfo');
    $CBRace = ($CBRace != '') ? explode(',', $CBRace) : [];
    $CBGender = Strings::showField('CBGender', 'QAInfo');
    $CBVeteran = Strings::showField('CBVeteran', 'QAInfo');
    $CBFiEthnicity = Strings::showField('CBFiEthnicity', 'QAInfo');
    $CBEthnicitySub = Strings::showField('CBEthnicitySub', 'QAInfo');
    $CBEthnicitySub = ($CBEthnicitySub != '') ? explode(',', $CBEthnicitySub) : [];
    $CBEthnicitySubOther = Strings::showField('CBEthnicitySubOther', 'QAInfo');
    $CBFiGender = Strings::showField('CBFiGender', 'QAInfo');
    $CBFiRace = Strings::showField('CBFiRace', 'QAInfo');
    $CBRaceSub = Strings::showField('CBRaceSub', 'QAInfo');
    $CBRaceSub = ($CBRaceSub != '') ? explode(',', $CBRaceSub) : [];
    $CBRaceAsianOther = Strings::showField('CBRaceAsianOther', 'QAInfo');
    $CBRacePacificOther = Strings::showField('CBRacePacificOther', 'QAInfo');
    $CBDDemoInfo = Strings::showField('CBDDemoInfo', 'QAInfo');
}

$secArr = BaseHTML::sectionAccess2(['sId' => 'GOVT', 'opt' => $fileTab]);
loanForm::pushSectionID('GOVT');

?>
<!-- Information for Government Info Section  Start-->
<!-- governmentInfo.php -->
<div class="card card-custom  HMLOLoanInfoSections GOVT govtCard <?php if (count(Arrays::getValueFromArray('GOVT', $fieldsInfo)) <= 0) {
    echo 'secHide';
} if($borrowerType != 'Individual'){ echo ' d-none';} ?>"
     style="<?php echo $HMLOLoanInfoSectionsDisp; ?>">

    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                <?php echo BaseHTML::getSectionHeading('GOVT'); ?>
            </h3>
            <?php if (trim(BaseHTML::getSectionTooltip('GOVT')) != '') { ?>&nbsp;
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="<?php echo BaseHTML::getSectionTooltip('GOVT'); ?>"></i>
            <?php } ?>
        </div>
        <div class="card-toolbar ">
            <a href="javascript:void(0);"
               class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
               data-card-tool="toggle"
               data-section="govtCard"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>


            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none" data-card-tool="reload"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                <i class="ki ki-close icon-nm"></i>
            </a>
        </div>
    </div>
    <div class="card-body govtCard_body">
        <div class="row ">
            <div class=" col-md-6 PublishBInfo_disp <?php echo loanForm::showField('PublishBInfo'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('PublishBInfo', 'col-md-5 '); ?>
                    <div class="col-md-7 isClientInfo">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold" for="PublishBInfoYes">
                                    <input type="radio"
                                           class="BrYes <?php echo BaseHTML::fieldAccess(['fNm' => 'PublishBInfo', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="PublishBInfo" id="PublishBInfoYes" value="2"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('2', $PublishBInfo); ?>
                                           onclick="showAndHideQADiv16(this.value, 'borrowerSelector');"> <span></span>Yes
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="PublishBInfoNo">
                                    <input type="radio" name="PublishBInfo" id="PublishBInfoNo" value="1"
                                           class="BrNo <?php echo BaseHTML::fieldAccess(['fNm' => 'PublishBInfo', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('1', $PublishBInfo); ?>
                                           onclick="showAndHideQADiv16(this.value, 'borrowerSelector');" <?php echo BaseHTML::fieldAccess(['fNm' => 'PublishBInfo', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span> No</label>
                                <label class="radio radio-solid font-weight-bold d-none" for="PublishBInfoNA">
                                    <input type="radio" name="PublishBInfo" id="PublishBInfoNA" value="3"
                                           class="BrNA <?php echo BaseHTML::fieldAccess(['fNm' => 'PublishBInfo', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('3', $PublishBInfo); ?>
                                           onclick="showAndHideQADiv16(this.value, 'borrowerSelector');" <?php echo BaseHTML::fieldAccess(['fNm' => 'PublishBInfo', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                    <span></span>N/A

                                </label>
                            </div>
                        <?php } else {
                            if ($PublishBInfo == 2) {
                                echo '<h5>Yes</h5>';
                            } elseif ($PublishBInfo == 1) {
                                echo '<h5>No</h5>';
                            } else {
                                echo '<h5>N/A</h5>';
                            }
                        } ?>
                    </div>
                </div>
            </div>

            <div class=" col-md-6 BEthnicity_disp <?php echo loanForm::showField('BEthnicity'); ?>">
                <div class="form-group row">
                    <?php
                    echo loanForm::getFieldLabel('BEthnicity');
                    echo ' &nbsp;' . loanForm::changeLog(
                        LMRequest::File()->getTblQAInfo_by_LMRId()[0]->QAID,
                        'BEthnicity',
                        tblQAInfo::class,
                        'What is your Ethnicity'
                    );
                    ?>
                    <div class="col-md-7 isClientInfo">
                        <?php if ($allowToEdit) { ?>
                            <div class="checkbox-list">
                                <label class="checkbox checkbox-solid font-weight-bold" for="BEthnicityH">
                                    <input type="checkbox"
                                           class="Hispanic BorChildRadio <?php echo BaseHTML::fieldAccess(['fNm' => 'BEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>
                                           BEYes borrowerSelector borrower_ethnicity"
                                           name="BEthnicity1" id="BEthnicityH"
                                           value="2" <?php echo Arrays::inArrayCheck('2', $BEthnicity); ?>
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'BEthnicity', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span> Hispanic or Latino
                                </label>
                                <div class="col-md-12 my-4 pl-6" id="Hispanic">
                                    <label for="bFiEthnicitySub" class="hidden">Mexican, Puerto Rican, Cuban, Other
                                        Hispanic or Latino</label>
                                    <div class="checkbox-list">
                                        <label class="checkbox checkbox-solid font-weight-bold" for="bFiEthnicitySubMexi">
                                            <input type="checkbox" name="bFiEthnicitySub1" id="bFiEthnicitySubMexi"
                                                   class="borrowerSelector HispanicPrintOrigin BorChildRadio borrower_ethnicity
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'BEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="1" <?php echo Arrays::inArrayCheck('1', $bFiEthnicitySub); ?> >
                                            <span></span> Mexican
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="bFiEthnicitySubPuerto">
                                            <input type="checkbox" name="bFiEthnicitySub2" id="bFiEthnicitySubPuerto"
                                                   class="borrowerSelector HispanicPrintOrigin BorChildRadio borrower_ethnicity
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'BEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="2" <?php echo Arrays::inArrayCheck('2', $bFiEthnicitySub); ?> >
                                            <span></span> Puerto Rican
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="bFiEthnicitySubCuban">
                                            <input type="checkbox" name="bFiEthnicitySub3" id="bFiEthnicitySubCuban"
                                                   class="borrowerSelector HispanicPrintOrigin BorChildRadio borrower_ethnicity
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'BEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="3" <?php echo Arrays::inArrayCheck('3', $bFiEthnicitySub); ?> >
                                            <span></span> Cuban
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="bFiEthnicitySubOther">
                                            <input type="checkbox" name="bFiEthnicitySub4" id="bFiEthnicitySubOther"
                                                   class="borrowerSelector HispanicPrintOrigin BorChildRadio borrower_ethnicity
                                                   <?php echo BaseHTML::fieldAccess(['fNm' => 'BEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="4" <?php echo Arrays::inArrayCheck('4', $bFiEthnicitySub); ?> >
                                            <span></span> Other Hispanic or Latino
                                        </label>
                                    </div>
                                    <div class="col-md-12 pl-6"
                                         id="HispanicPrintOriginDiv">
                                        <label class="col-md-12 font-weight-bold" for="bFiEthnicitySubOthertxt">
                                            Print Origin
                                        </label>
                                        <input type="text" name="bFiEthnicitySubOther" id="bFiEthnicitySubOthertxt"
                                               class="form-control input-sm"
                                               value="<?php echo htmlspecialchars($bFiEthnicitySubOther); ?>">
                                        <span class="text-muted">For example: Argentinean, Colombian, Dominican, Nicaraguan,Salvadoran,
                                        Spaniard, and so on</span>
                                    </div>
                                </div>

                                <label class="checkbox checkbox-solid font-weight-bold" for="BEthnicityNH">
                                    <input type="checkbox" name="BEthnicity2" id="BEthnicityNH"
                                           value="1" <?php echo Arrays::inArrayCheck('1', $BEthnicity); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           class="Hispanic borrowerSelector <?php echo BaseHTML::fieldAccess(['fNm' => 'BEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>
                                           BENo BorChildRadio borrower_ethnicity
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'BEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>">
                                    <span></span> Not Hispanic or Latino
                                </label>
                                <label class="checkbox checkbox-solid font-weight-bold" for="BEthnicityND">
                                    <input type="checkbox" name="BEthnicity3" id="BEthnicityND"
                                           value="3" <?php echo Arrays::inArrayCheck('3', $BEthnicity); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           class="Hispanic borrowerSelector <?php echo BaseHTML::fieldAccess(['fNm' => 'BEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>
                                           BEND BorChildRadio borrower_ethnicity
                                           <?php echo BaseHTML::fieldAccess(['fNm' => 'BEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>">
                                    <span></span> Not Disclosed
                                </label>
                            </div>
                        <?php } else { ?>
                            <input type="checkbox"
                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'BEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="BEthnicity" id="BEthnicity" value="2"
                                   disabled <?php echo Arrays::inArrayCheck('2', $BEthnicity); ?>
                                   tabindex="<?php echo $tabIndex++; ?>"> Hispanic or Latino
                            <div class="col-md-12" id="Hispanic">
                                <input type="checkbox" name="bFiEthnicitySub" id="bFiEthnicitySub" class="" disabled
                                       value="1" <?php echo Arrays::inArrayCheck('1', $bFiEthnicitySub); ?>> Mexican
                                <br>
                                <input type="checkbox" name="bFiEthnicitySub" id="bFiEthnicitySub" class="" disabled
                                       value="2" <?php echo Arrays::inArrayCheck('2', $bFiEthnicitySub); ?>> Puerto Rican
                                <br>
                                <input type="checkbox" name="bFiEthnicitySub" id="bFiEthnicitySub" class="" disabled
                                       value="3" <?php echo Arrays::inArrayCheck('3', $bFiEthnicitySub); ?>> Cuban
                                <br>
                                <input type="checkbox" name="bFiEthnicitySub" id="bFiEthnicitySub" class="" disabled
                                       value="4" <?php echo Arrays::inArrayCheck('4', $bFiEthnicitySub); ?>> Other
                                Hispanic or
                                Latino
                            </div>
                            <br>
                            <input type="checkbox" name="BEthnicity" id="BEthnicity" value="1"
                                   disabled <?php echo Arrays::inArrayCheck('1', $BEthnicity); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'BEthnicity', 'sArr' => $secArr, 'opt' => 'I']); ?> > Not Hispanic or Latino
                            <br>
                            <input type="checkbox" name="BEthnicity" id="BEthnicity" value="3"
                                   disabled <?php echo Arrays::inArrayCheck('3', $BEthnicity); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'BEthnicity', 'sArr' => $secArr, 'opt' => 'I']); ?> > Not Disclosed
                        <?php } ?>
                    </div>
                </div>
            </div>

            <div class="col-md-6 BRace_disp <?php echo loanForm::showField('BRace'); ?>">
                <div class="form-group row">
                    <?php
                    echo loanForm::getFieldLabel('BRace');
                    echo ' &nbsp;' . loanForm::changeLog(
                        LMRequest::File()->getTblQAInfo_by_LMRId()[0]->QAID,
                        'BRace',
                        tblQAInfo::class,
                        'What is your Race'
                    );
                    ?>
                    <div class="col-md-7 isClientInfo">
                        <?php if ($allowToEdit) { ?>
                            <div class="checkbox-list">
                                <label class="checkbox checkbox-solid font-weight-bold" for="BRace1">
                                    <input type="checkbox"
                                           class="BorChildRadio Race <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'M']); ?>
                                           BRaceAM borrowerSelector borrower_race"
                                           name="BRace1" id="BRace1"
                                           value="1" <?php echo Arrays::inArrayCheck('1', $BRace); ?>
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span> American Indian or Alaska Native
                                </label>
                                <label class="checkbox checkbox-solid font-weight-bold" for="BRace2">
                                    <input type="checkbox" name="BRace2" id="BRace2"
                                           value="2" <?php echo Arrays::inArrayCheck('2', $BRace); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           class="BorChildRadio Race borrowerSelector BRaceAS borrower_race
                                           <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'M']); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span> Asian
                                </label>
                                <div class="col-md-12 px-6" id="Asian">
                                    <label for="bFiRaceSub" class="hidden">Asian Indian, Chinese, Filipino, Japanese,
                                        Korean, Vietnamese, Other Asian</label>

                                    <div class="checkbox-list">
                                        <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSubAsian">
                                            <input type="checkbox" name="bFiRaceSub1" id="bFiRaceSubAsian"
                                                   class="BorChildRadio borrowerSelector Asian borrower_race
                                                   <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="1" <?php echo Arrays::inArrayCheck('1', $bFiRaceSub); ?> >
                                            <span></span> Asian Indian
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSubChinese">
                                            <input type="checkbox" name="bFiRaceSub2" id="bFiRaceSubChinese"
                                                   class="BorChildRadio borrowerSelector Asian borrower_race
                                                   <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="2" <?php echo Arrays::inArrayCheck('2', $bFiRaceSub); ?> >
                                            <span></span> Chinese
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSubFil">
                                            <input type="checkbox" name="bFiRaceSub3" id="bFiRaceSubFil"
                                                   class="BorChildRadio borrowerSelector Asian borrower_race
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="3" <?php echo Arrays::inArrayCheck('3', $bFiRaceSub); ?> >
                                            <span></span> Filipino
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSubJap">
                                            <input type="checkbox" name="bFiRaceSub4" id="bFiRaceSubJap"
                                                   class="BorChildRadio borrowerSelector Asian borrower_race
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="4" <?php echo Arrays::inArrayCheck('4', $bFiRaceSub); ?> >
                                            <span></span> Japanese
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSubKor">
                                            <input type="checkbox" name="bFiRaceSub5" id="bFiRaceSubKor"
                                                   class="BorChildRadio borrowerSelector Asian borrower_race
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="5" <?php echo Arrays::inArrayCheck('5', $bFiRaceSub); ?> >
                                            <span></span> Korean
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSubVi">
                                            <input type="checkbox" name="bFiRaceSub6" id="bFiRaceSubVi"
                                                   class="BorChildRadio borrowerSelector Asian borrower_race
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="6" <?php echo Arrays::inArrayCheck('6', $bFiRaceSub); ?> >
                                            <span></span> Vietnamese
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSubOther">
                                            <input type="checkbox" name="bFiRaceSub7" id="bFiRaceSubOther"
                                                   class="BorChildRadio borrowerSelector Asian borrower_race
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="7" <?php echo Arrays::inArrayCheck('7', $bFiRaceSub); ?> >
                                            <span></span> Other Asian
                                        </label>
                                        <div class="col-md-12 pl-6" id="AsianDiv">
                                            <label class="col-md-12 font-weight-bold" for="bFiRaceAsianOther">
                                                Print Race
                                            </label>
                                            <input type="text" class="form-control input-sm"
                                                   name="bFiRaceAsianOther"
                                                   id="bFiRaceAsianOther"
                                                   value="<?php echo htmlspecialchars($bFiRaceAsianOther); ?>">
                                            <span class="text-muted">For example: Hmong, Laotian, Thai, Pakistani, Cambodian, and so on.</span>

                                        </div>
                                    </div>
                                </div>
                                <label class="checkbox checkbox-solid font-weight-bold" for="BRace3">
                                    <input type="checkbox" name="BRace3" id="BRace3"
                                           value="3" <?php echo Arrays::inArrayCheck('3', $BRace); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           class="BorChildRadio Race borrowerSelector BRaceBA borrower_race
                                           <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span> Black or African American
                                </label>
                                <label class="checkbox checkbox-solid font-weight-bold" for="BRace4">
                                    <input type="checkbox" name="BRace4" id="BRace4"
                                           value="4" <?php echo Arrays::inArrayCheck('4', $BRace); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           class="BorChildRadio Race borrowerSelector BRaceNH borrower_race
                                           <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span> Native Hawaiian or Other Pacific Islander
                                </label>
                                <div class="col-md-12 px-6" id="Native">
                                    <label for="NativeHOPI" class="hidden">Native Hawaiian, Guamanian or Chamorro, Samoan, Other Pacific Islander</label>
                                    <div class="checkbox-list">
                                        <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSub2Na">
                                            <input type="checkbox" name="bFiRaceSub8" id="bFiRaceSub2Na"
                                                   class="BorChildRadio borrowerSelector Asian NativeHOPI borrower_race
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="8" <?php echo Arrays::inArrayCheck('8', $bFiRaceSub); ?> >
                                            <span></span> Native Hawaiian
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSub2Gua">
                                            <input type="checkbox" name="bFiRaceSub9" id="bFiRaceSub2Gua"
                                                   class="BorChildRadio borrowerSelector Asian NativeHOPI borrower_race
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="9" <?php echo Arrays::inArrayCheck('9', $bFiRaceSub); ?> >
                                            <span></span> Guamanian or Chamorro
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSub2Samoan">
                                            <input type="checkbox" name="bFiRaceSub10" id="bFiRaceSub2Samoan"
                                                   class="BorChildRadio borrowerSelector Asian NativeHOPI borrower_race
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="10" <?php echo Arrays::inArrayCheck('10', $bFiRaceSub); ?> >
                                            <span></span> Samoan
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSub2Other">
                                            <input type="checkbox" name="bFiRaceSub11" id="bFiRaceSub2Other"
                                                   class="BorChildRadio borrowerSelector Asian NativeHOPI borrower_race
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="11" <?php echo Arrays::inArrayCheck('11', $bFiRaceSub); ?> >
                                            <span></span> Other Pacific Islander
                                        </label>
                                        <div class="col-md-12 px-6" id="PacificDiv">
                                            <label class="col-md-12 font-weight-bold" for="bFiRacePacificOther">
                                                Print Race
                                            </label>
                                            <input type="text" class="form-control input-sm" name="bFiRacePacificOther"
                                                   id="bFiRacePacificOther"
                                                   value="<?php echo htmlspecialchars($bFiRacePacificOther); ?>">
                                            <span class="text-muted">For example: Fijian, Tongan, and so on.</span>
                                        </div>
                                    </div>
                                </div>
                                <label class="checkbox checkbox-solid font-weight-bold" for="BRace5">
                                    <input type="checkbox" name="BRace5" id="BRace5"
                                           value="5" <?php echo Arrays::inArrayCheck('5', $BRace); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           class="BorChildRadio Race borrowerSelector BRaceW borrower_race
                                           <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span> White
                                </label>
                                <label class="checkbox checkbox-solid font-weight-bold" for="BRace6">
                                    <input type="checkbox" name="BRace6" id="BRace6"
                                           value="6" <?php echo Arrays::inArrayCheck('6', $BRace); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           class="BorChildRadio Race borrowerSelector BRaceND borrower_race
                                           <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span> Not Disclosed
                                </label>
                            </div>
                            <div class="clsAnswer"></div>
                        <?php } else { ?>
                            <input type="checkbox" name="BRace1" id="BRace1" value="1"
                                   disabled <?php echo Arrays::inArrayCheck('1', $BRace); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'I']); ?> >&nbsp;American Indian
                            <br>
                            <input type="checkbox" name="BRace2" id="BRace2" value="2"
                                   disabled <?php echo Arrays::inArrayCheck('2', $BRace); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'I']); ?> >&nbsp;Asian
                            <div class="col-md-12" id="Asian">
                                <input type="checkbox" name="bFiRaceSub1" id="bFiRaceSub1" class="" disabled
                                       value="1" <?php echo Arrays::inArrayCheck('1', $bFiRaceSub); ?> > Asian Indian
                                <br>
                                <input type="checkbox" name="bFiRaceSub2" id="bFiRaceSub2" class="" disabled
                                       value="2" <?php echo Arrays::inArrayCheck('2', $bFiRaceSub); ?> > Chinese
                                <br>
                                <input type="checkbox" name="bFiRaceSub3" id="bFiRaceSub3" class="" disabled
                                       value="3" <?php echo Arrays::inArrayCheck('3', $bFiRaceSub); ?> > Filipino
                                <br>
                                <input type="checkbox" name="bFiRaceSub4" id="bFiRaceSub4" class="" disabled
                                       value="4" <?php echo Arrays::inArrayCheck('4', $bFiRaceSub); ?> > Japanese
                                <br>
                                <input type="checkbox" name="bFiRaceSub5" id="bFiRaceSub5" class="" disabled
                                       value="5" <?php echo Arrays::inArrayCheck('5', $bFiRaceSub); ?> > Korean
                                <br>
                                <input type="checkbox" name="bFiRaceSub6" id="bFiRaceSub6" class="" disabled
                                       value="6" <?php echo Arrays::inArrayCheck('6', $bFiRaceSub); ?> > Vietnamese
                                <br>
                                <input type="checkbox" name="bFiRaceSub7" id="bFiRaceSub7" class="" disabled
                                       value="7" <?php echo Arrays::inArrayCheck('7', $bFiRaceSub); ?> > Other Asian
                            </div>
                            <br>
                            <input type="checkbox" name="BRace3" id="BRace3" value="3"
                                   disabled <?php echo Arrays::inArrayCheck('3', $BRace); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'I']); ?> >&nbsp;Black or African American
                            <br>
                            <input type="checkbox" name="BRace4" id="BRace4" value="4"
                                   disabled <?php echo Arrays::inArrayCheck('4', $BRace); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'I']); ?> >&nbsp;Native Hawaiian or Other Pacific Islander
                            <div class="col-md-12" id="Native">
                                <input type="checkbox" name="bFiRaceSub8" id="bFiRaceSub8" class="" disabled
                                       value="8" <?php echo Arrays::inArrayCheck('8', $bFiRaceSub); ?> > Native Hawaiian
                                <br>
                                <input type="checkbox" name="bFiRaceSub9" id="bFiRaceSub9" class="" disabled
                                       value="9" <?php echo Arrays::inArrayCheck('9', $bFiRaceSub); ?> > Guamanian or Chamorro
                                <br>
                                <input type="checkbox" name="bFiRaceSub10" id="bFiRaceSub10" class="" disabled
                                       value="10" <?php echo Arrays::inArrayCheck('10', $bFiRaceSub); ?> > Samoan
                                <br>
                                <input type="checkbox" name="bFiRaceSub11" id="bFiRaceSub11" class="" disabled
                                       value="11" <?php echo Arrays::inArrayCheck('11', $bFiRaceSub); ?> > Other Pacific Islander
                            </div>
                            <br>
                            <input type="checkbox" name="BRace5" id="BRace5" value="5"
                                   disabled <?php echo Arrays::inArrayCheck('5', $BRace); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'I']); ?> >&nbsp;White
                            <br>
                            <input type="checkbox" name="BRace6" id="BRace6" value="6"
                                   disabled <?php echo Arrays::inArrayCheck('6', $BRace); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'BRace', 'sArr' => $secArr, 'opt' => 'I']); ?> >&nbsp;Not Disclosed
                        <?php } ?>
                    </div>
                </div>
            </div>
            <div class=" col-md-6 BGender_disp <?php echo loanForm::showField('BGender'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('BGender', 'col-md-5 '); ?>
                    <div class="col-md-7 isClientInfo">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold" for="BGendeMale">
                                    <input type="radio"
                                           class="BorChildRadio BGender borrowerSelector <?php echo BaseHTML::fieldAccess(['fNm' => 'BGender', 'sArr' => $secArr, 'opt' => 'M']); ?> BGenderM"
                                           name="BGender" id="BGendeMale"
                                           value="2" <?php echo Strings::isChecked('2', $BGender); ?>
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'BGender', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>
                                    Male
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="BGenderFE">
                                    <input type="radio" name="BGender" id="BGenderFE"
                                           value="1" <?php echo Strings::isChecked('1', $BGender); ?>
                                           class="BorChildRadio BGender borrowerSelector <?php echo BaseHTML::fieldAccess(['fNm' => 'BGender', 'sArr' => $secArr, 'opt' => 'M']); ?> BGenderF"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'BGender', 'sArr' => $secArr, 'opt' => 'I']); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'BGender', 'sArr' => $secArr, 'opt' => 'I']); ?> ><span></span>
                                    Female
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="BGenderNotDis">
                                    <input type="radio" name="BGender" id="BGenderNotDis"
                                           value="3" <?php echo Strings::isChecked('3', $BGender); ?>
                                           class="BorChildRadio BGender borrowerSelector <?php echo BaseHTML::fieldAccess(['fNm' => 'BGender', 'sArr' => $secArr, 'opt' => 'M']); ?> BGenderND"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'BGender', 'sArr' => $secArr, 'opt' => 'I']); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'BGender', 'sArr' => $secArr, 'opt' => 'I']); ?> ><span></span>
                                    Not Disclosed
                                </label></div>
                        <?php } else { ?>
                            <input type="radio"
                                   class="<?php echo BaseHTML::chkMandField('BGender', $PCquickAppFieldsInfo, ''); ?>"
                                   name="BGender" id="BGender" value="2"
                                   disabled <?php echo Strings::isChecked('2', $BGender); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'BGender', 'sArr' => $secArr, 'opt' => 'I']); ?> > Male
                            <input type="radio" name="BGender" id="BGender" value="1"
                                   disabled <?php echo Strings::isChecked('1', $BGender); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'BGender', 'sArr' => $secArr, 'opt' => 'I']); ?> > Female
                            <input type="radio" name="BGender" id="BGender" value="3"
                                   disabled <?php echo Strings::isChecked('3', $BGender); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'BGender', 'sArr' => $secArr, 'opt' => 'I']); ?> > Not Disclosed
                        <?php } ?>
                    </div>
                </div>
            </div>


            <div class="col-md-6 BVeteran_disp <?php echo loanForm::showField('BVeteran'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('BVeteran', 'col-md-5 '); ?>
                    <label for="BVeteran1" class="hidden">an option for veteran</label>
                    <div class="col-md-7 isClientInfo">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-list">
                                <label class="radio radio-solid font-weight-bold" for="BVeteran1">
                                    <input type="radio" name="BVeteran" id="BVeteran1" value="1"
                                           class="BorChildRadio BVeteran borrowerSelector <?php echo BaseHTML::fieldAccess(['fNm' => 'BVeteran', 'sArr' => $secArr, 'opt' => 'M']); ?> BVeteran"
                                        <?php echo Strings::isChecked('1', $BVeteran); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'BVeteran', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span>Non-Veteran
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="BVeteran2">
                                    <input type="radio" name="BVeteran" id="BVeteran2" value="2"
                                           class="BorChildRadio BVeteran borrowerSelector <?php echo BaseHTML::fieldAccess(['fNm' => 'BVeteran', 'sArr' => $secArr, 'opt' => 'M']); ?> BVeteran"
                                        <?php echo Strings::isChecked('2', $BVeteran); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'BVeteran', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span> Veteran
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="BVeteran3">
                                    <input type="radio" name="BVeteran" id="BVeteran3" value="3"
                                           class="BorChildRadio BVeteran borrowerSelector <?php echo BaseHTML::fieldAccess(['fNm' => 'BVeteran', 'sArr' => $secArr, 'opt' => 'M']); ?> BVeteran"
                                        <?php echo Strings::isChecked('3', $BVeteran); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'BVeteran', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span>Service-Disabled Veteran
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="BVeteran4">

                                    <input type="radio" name="BVeteran" id="BVeteran4" value="4"
                                           class="BorChildRadio BVeteran borrowerSelector <?php echo BaseHTML::fieldAccess(['fNm' => 'BVeteran', 'sArr' => $secArr, 'opt' => 'M']); ?> BVeteran"
                                        <?php echo Strings::isChecked('4', $BVeteran); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'BVeteran', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span>Spouse of Veteran
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="BVeteran5">

                                    <input type="radio" name="BVeteran" id="BVeteran5" value="5"
                                           class="BorChildRadio BVeteran borrowerSelector <?php echo BaseHTML::fieldAccess(['fNm' => 'BVeteran', 'sArr' => $secArr, 'opt' => 'M']); ?> BVeteran"
                                        <?php echo Strings::isChecked('5', $BVeteran); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'BVeteran', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span> Not Disclosed
                                </label></div>
                        <?php } else { ?>
                            <input type="radio" name="BVeteran" id="BVeteran" value="1"
                                   disabled <?php echo Strings::isChecked('1', $BVeteran); ?>
                                   tabindex="<?php echo $tabIndex++; ?>"> Non-Veteran
                            <br>
                            <input type="radio" name="BVeteran" id="BVeteran" value="2"
                                   disabled <?php echo Strings::isChecked('2', $BVeteran); ?>
                                   tabindex="<?php echo $tabIndex++; ?>"> Veteran
                            <br>
                            <input type="radio" name="BVeteran" id="BVeteran" value="3"
                                   disabled <?php echo Strings::isChecked('3', $BVeteran); ?>
                                   tabindex="<?php echo $tabIndex++; ?>"> Service-Disabled Veteran
                            <br>
                            <input type="radio" name="BVeteran" id="BVeteran" value="4"
                                   disabled <?php echo Strings::isChecked('4', $BVeteran); ?>
                                   tabindex="<?php echo $tabIndex++; ?>"> Spouse of Veteran
                            <br>
                            <input type="radio" name="BVeteran" id="BVeteran" value="5"
                                   disabled <?php echo Strings::isChecked('5', $BVeteran); ?>
                                   tabindex="<?php echo $tabIndex++; ?>"> Not Disclosed
                        <?php } ?>
                    </div>
                </div>
            </div>

            <?php if (!$publicUser) { ?>
                <label class="col-lg-12 mb-4 px-4 py-2 bg-secondary font-weight-bolder" id="bFiTitle">
                    <?php echo BaseHTML::getSubSectionHeading('GOVT', 'FinInstSubSection'); ?>
                </label>

                <div class="col-md-12  mb-2 bFiTitle <?php echo loanForm::showField('bFiEthnicity'); ?>">
                    <div class="row form-group">
                        <?php echo loanForm::label('bFiEthnicity', 'col-md-7 '); ?>
                        <div class="col-md-5">
                            <?php if ($allowToEdit) { ?>
                                <div class="radio-inline">
                                    <label class="radio radio-solid font-weight-bold" for="bFiEthnicityYes">
                                        <input type="radio" name="bFiEthnicity" id="bFiEthnicityYes" value="Yes"
                                               class="<?php echo BaseHTML::fieldAccess(['fNm' => 'bFiEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'bFiEthnicity', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                            <?php echo Strings::isChecked('Yes', $bFiEthnicity); ?>
                                        > <span></span>Yes
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="bFiEthnicityNo">

                                        <input type="radio" name="bFiEthnicity" id="bFiEthnicityNo" value="No"
                                               class="<?php echo BaseHTML::fieldAccess(['fNm' => 'bFiEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'bFiEthnicity', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                            <?php echo Strings::isChecked('No', $bFiEthnicity); ?>
                                        > <span></span>No
                                    </label></div>
                            <?php } else { ?>
                                <h5><?php echo $bFiEthnicity; ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-12  mb-2 bFiTitle <?php echo loanForm::showField('bFiSex'); ?>">
                    <div class="row form-group">
                        <?php echo loanForm::label('bFiSex', 'col-md-7 '); ?>
                        <div class="col-md-5">
                            <?php if ($allowToEdit) { ?>
                                <div class="radio-inline">
                                    <label class="radio radio-solid font-weight-bold" for="bFiSexYes">
                                        <input type="radio" name="bFiSex" id="bFiSexYes" value="Yes"
                                               class="<?php echo BaseHTML::fieldAccess(['fNm' => 'bFiSex', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'bFiSex', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                            <?php echo Strings::isChecked('Yes', $bFiSex); ?>
                                        > <span></span>Yes
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="bFiSexNo">
                                        <input type="radio" name="bFiSex" id="bFiSexNo" value="No"
                                               class="<?php echo BaseHTML::fieldAccess(['fNm' => 'bFiSex', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'bFiSex', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                            <?php echo Strings::isChecked('No', $bFiSex); ?>
                                        > <span></span>No
                                    </label></div>
                            <?php } else { ?>
                                <h5><?php echo $bFiSex; ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-12  mb-2 bFiTitle <?php echo loanForm::showField('bFiRace'); ?>">
                    <div class="row form-group">
                        <?php echo loanForm::label('bFiRace', 'col-md-7 '); ?>
                        <div class="col-md-5">
                            <?php if ($allowToEdit) { ?>
                                <div class="radio-inline">
                                    <label class="radio radio-solid font-weight-bold" for="bFiRaceYes">
                                        <input type="radio" name="bFiRace" id="bFiRaceYes" value="Yes"
                                               class="<?php echo BaseHTML::fieldAccess(['fNm' => 'bFiRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'bFiRace', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                            <?php echo Strings::isChecked('Yes', $bFiRace); ?>
                                        ><span></span> Yes
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="bFiRaceNo">
                                        <input type="radio" name="bFiRace" id="bFiRaceNo" value="No"
                                               class="<?php echo BaseHTML::fieldAccess(['fNm' => 'bFiRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'bFiRace', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                            <?php echo Strings::isChecked('No', $bFiRace); ?>
                                        > <span></span> No
                                    </label></div>
                            <?php } else { ?>
                                <h5><?php echo $bFiRace; ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <label class="col-lg-12 mb-4 px-4 py-2 bg-secondary font-weight-bolder" id="bDiTitle">
                    <?php echo BaseHTML::getSubSectionHeading('GOVT', 'DemoInfoSubSection'); ?>
                </label>
                <div class="col-md-12 bDiTitle <?php echo loanForm::showField('bDemoInfo'); ?>">
                    <?php echo loanForm::label('bDemoInfo', ''); ?>
                    <?php if ($allowToEdit) { ?>
                        <div class="radio-inline">
                            <label class="radio radio-solid font-weight-bold" for="bDemoInfo1">
                                <input type="radio" name="bDemoInfo" id="bDemoInfo1" value="1"
                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'bDemoInfo', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'bDemoInfo', 'sArr' => $secArr, 'opt' => 'I']); ?> <?php echo Strings::isChecked('1', $bDemoInfo); ?>>
                                <span></span>
                                Face-to-Face Interview (includes Electronic Media w/ Video Component)
                            </label>
                            <label class="radio radio-solid font-weight-bold" for="bDemoInfo2">

                                <input type="radio" name="bDemoInfo" id="bDemoInfo2" value="2"
                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'bDemoInfo', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'bDemoInfo', 'sArr' => $secArr, 'opt' => 'I']); ?> <?php echo Strings::isChecked('2', $bDemoInfo); ?>><span></span>
                                Telephone Interview
                            </label>
                            <label class="radio radio-solid font-weight-bold" for="bDemoInfo3">

                                <input type="radio" name="bDemoInfo" id="bDemoInfo3" value="3"
                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'bDemoInfo', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'bDemoInfo', 'sArr' => $secArr, 'opt' => 'I']); ?> <?php echo Strings::isChecked('3', $bDemoInfo); ?>>
                                <span></span>Fax or Mail
                            </label>
                            <label class="radio radio-solid font-weight-bold" for="bDemoInfo4">
                                <input type="radio" name="bDemoInfo" id="bDemoInfo4" value="4"
                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'bDemoInfo', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'bDemoInfo', 'sArr' => $secArr, 'opt' => 'I']); ?> <?php echo Strings::isChecked('4', $bDemoInfo); ?>>
                                <span></span>
                                Email or Internet
                            </label></div>
                    <?php } else { ?>
                        <input type="radio" name="bDemoInfo" id="bDemoInfo" value="1"
                               disabled <?php echo Strings::isChecked('1', $bDemoInfo); ?>>
                        Face-to-Face Interview (includes Electronic Media w/ Video Component)
                        <br>
                        <input type="radio" name="bDemoInfo" id="bDemoInfo" value="2"
                               disabled <?php echo Strings::isChecked('2', $bDemoInfo); ?>>
                        Telephone Interview
                        <br>
                        <input type="radio" name="bDemoInfo" id="bDemoInfo" value="3"
                               disabled <?php echo Strings::isChecked('3', $bDemoInfo); ?>>
                        Fax or Mail
                        <br>
                        <input type="radio" name="bDemoInfo" id="bDemoInfo" value="4"
                               disabled <?php echo Strings::isChecked('4', $bDemoInfo); ?>>
                        Email or Internet
                    <?php } ?>
                </div>
            <?php } ?>
        </div>

        <?php echo CustomField::RenderForTabSection(
            PageVariables::$PCID,
            tblFile::class,
            LMRequest::$LMRId,
            'GOVT',
            $fileTab,
            $activeTab,
            LMRequest::myFileInfo()->getFileTypes(),
            LMRequest::myFileInfo()->getLoanPrograms()
        ); ?>
    </div>
</div>
<!-- Information for Government Info Section  End-->


<?php
$secArr = BaseHTML::sectionAccess2(['sId' => 'GOVT-CB', 'opt' => $fileTab]);
loanForm::pushSectionID('GOVT-CB');

?>
<!-- governmentInfo.php -->
<div class="card card-custom coBorrowerSections GOVT-CB GOVTCBCard <?php if (count(Arrays::getValueFromArray('GOVT-CB', $fieldsInfo)) <= 0) {
    echo 'secHide';
} if($borrowerType !=  'Individual') { echo ' d-none ';}?> " style="<?php echo $coBorDisp; ?>">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                <?php echo BaseHTML::getSectionHeading('GOVT-CB'); ?>
            </h3>
            <?php if (trim(BaseHTML::getSectionTooltip('GOVT-CB')) != '') { ?>&nbsp;
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="<?php echo BaseHTML::getSectionTooltip('GOVT-CB'); ?>"></i>
            <?php } ?>
        </div>
        <div class="card-toolbar">
            <a href="javascript:void(0);"
               class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
               data-card-tool="toggle"
               data-section="GOVTCBCard"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>

            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none" data-card-tool="reload"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary  d-none" data-card-tool="remove"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                <i class="ki ki-close icon-nm"></i>
            </a>
        </div>
    </div>
    <div class="card-body GOVTCBCard_body">
        <div class="row  ">
            <div class=" col-md-6 <?php echo loanForm::showField('PublishCBInfo'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('PublishCBInfo', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold" for="PublishCBInfo2">
                                    <input type="radio"
                                           class="CoBrYes <?php echo BaseHTML::fieldAccess(['fNm' => 'PublishCBInfo', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="PublishCBInfo" id="PublishCBInfo2" value="2"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('2', $PublishCBInfo); ?>
                                           onclick="showAndHideQADivCB(this.value, 'coBorrower');" <?php echo BaseHTML::fieldAccess(['fNm' => 'PublishCBInfo', 'sArr' => $secArr, 'opt' => 'I']); ?> ><span></span>
                                    Yes
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="PublishCBInfo1">
                                    <input type="radio" name="PublishCBInfo" id="PublishCBInfo1" value="1"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'PublishCBInfo', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('1', $PublishCBInfo); ?>
                                           onclick="showAndHideQADivCB(this.value, 'coBorrower');" <?php echo BaseHTML::fieldAccess(['fNm' => 'PublishCBInfo', 'sArr' => $secArr, 'opt' => 'I']); ?> ><span></span>
                                    No
                                </label>
                                <label class="radio radio-solid font-weight-bold d-none" for="PublishCBInfo3">
                                    <input type="radio" name="PublishCBInfo" id="PublishCBInfo3" value="3"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'PublishCBInfo', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('3', $PublishCBInfo); ?>
                                           onclick="showAndHideQADivCB(this.value, 'coBorrower');" <?php echo BaseHTML::fieldAccess(['fNm' => 'PublishCBInfo', 'sArr' => $secArr, 'opt' => 'I']); ?> ><span></span>
                                    N/A
                                </label></div>
                        <?php } else {
                            if ($PublishCBInfo == 2) {
                                echo '<h5>Yes</h5>';
                            } elseif ($PublishCBInfo == 1) {
                                echo '<h5>No</h5>';
                            } else {
                                echo '<h5>N/A</h5>';
                            }
                        } ?>
                    </div>
                </div>
            </div>
            <div class=" col-md-6 <?php echo loanForm::showField('CBEthnicity'); ?>">
                <div class="form-group row">
                    <?php
                        echo loanForm::getFieldLabel('CBEthnicity');
                        echo ' &nbsp;' . loanForm::changeLog(
                            LMRequest::File()->getTblQAInfo_by_LMRId()[0]->QAID,
                            'CBEthnicity',
                            tblQAInfo::class,
                            'What is your Ethnicity'
                        );
                    ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="checkbox-list">
                                <label class="checkbox checkbox-solid font-weight-bold" for="CBEthnicity2">
                                    <input type="checkbox"
                                           class="CoBorChildRadio CBHispanic coBorrower co_borrower_ethnicity
                                           <?php echo BaseHTML::fieldAccess(['fNm' => 'CBEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="CBEthnicity2" id="CBEthnicity2"
                                           value="2" <?php echo Arrays::inArrayCheck('2', $CBEthnicity); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'CBEthnicity', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                    <span></span> Hispanic or Latino
                                </label>
                                <div class="col-md-12  px-6" id="CBHispanic">
                                    <label for="CBEthnicitySub" class="hidden">Mexican, Puerto Rican, Cuban, Other
                                        Hispanic
                                        or
                                        Latino</label>
                                    <div class="checkbox-list">
                                        <label class="checkbox checkbox-solid font-weight-bold" for="CBEthnicitySub1">
                                            <input type="checkbox" name="CBEthnicitySub1" id="CBEthnicitySub1"
                                                   class="CoBorChildRadio CBHispanicPrintOrigin co_borrower_ethnicity coBorrower
                                                   <?php echo BaseHTML::fieldAccess(['fNm' => 'CBEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="1" <?php echo Arrays::inArrayCheck('1', $CBEthnicitySub); ?> >
                                            <span></span> Mexican
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="CBEthnicitySub2">
                                            <input type="checkbox" name="CBEthnicitySub2" id="CBEthnicitySub2"
                                                   class="CoBorChildRadio CBHispanicPrintOrigin co_borrower_ethnicity coBorrower
                                                   <?php echo BaseHTML::fieldAccess(['fNm' => 'CBEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="2" <?php echo Arrays::inArrayCheck('2', $CBEthnicitySub); ?> >
                                            <span></span> Puerto Rican
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="CBEthnicitySub3">
                                            <input type="checkbox" name="CBEthnicitySub3" id="CBEthnicitySub3"
                                                   class="CoBorChildRadio CBHispanicPrintOrigin co_borrower_ethnicity coBorrower
                                                   <?php echo BaseHTML::fieldAccess(['fNm' => 'CBEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="3" <?php echo Arrays::inArrayCheck('3', $CBEthnicitySub); ?> >
                                            <span></span> Cuban
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="CBEthnicitySub4">
                                            <input type="checkbox" name="CBEthnicitySub4" id="CBEthnicitySub4"
                                                   class="CoBorChildRadio CBHispanicPrintOrigin co_borrower_ethnicity coBorrower
                                                   <?php echo BaseHTML::fieldAccess(['fNm' => 'CBEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="4" <?php echo Arrays::inArrayCheck('4', $CBEthnicitySub); ?> >
                                            <span></span> Other Hispanic or Latino
                                        </label>
                                        <div class="col-md-12" id="CBHispanicPrintOriginDiv">
                                            <label class="col-md-12 font-weight-bold" for="CBEthnicitySubOther">
                                                Print Origin
                                            </label>
                                            <input type="text" name="CBEthnicitySubOther" id="CBEthnicitySubOther"
                                                   class="form-control input-sm coBorrower"
                                                   value="<?php echo htmlspecialchars($CBEthnicitySubOther); ?>">
                                            <span class="text-muted">For example: Argentinean, Colombian, Dominican, Nicaraguan,Salvadoran,
                                            Spaniard,
                                            and so on</span>

                                        </div>
                                    </div>
                                </div>
                                <label class="checkbox checkbox-solid font-weight-bold" for="CBEthnicity1">
                                    <input type="checkbox" name="CBEthnicity1" id="CBEthnicity1"
                                           value="1" <?php echo Arrays::inArrayCheck('1', $CBEthnicity); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           class="CoBorChildRadio CBHispanic coBorrower co_borrower_ethnicity
                                           <?php echo BaseHTML::fieldAccess(['fNm' => 'CBEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'CBEthnicity', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span> Not Hispanic or Latino
                                </label>
                                <label class="checkbox checkbox-solid font-weight-bold" for="CBEthnicity3">
                                    <input type="checkbox" name="CBEthnicity3" id="CBEthnicity3"
                                           value="3" <?php echo Arrays::inArrayCheck('3', $CBEthnicity); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           class="CoBorChildRadio CBHispanic coBorrower co_borrower_ethnicity
                                           <?php echo BaseHTML::fieldAccess(['fNm' => 'CBEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'CBEthnicity', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span> Not Disclosed
                                </label>
                            </div>
                        <?php } else { ?>
                            <input type="checkbox" name="CBEthnicity2" id="CBEthnicity2" disabled
                                   value="2" <?php echo Arrays::inArrayCheck('2', $CBEthnicity); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBEthnicity', 'sArr' => $secArr, 'opt' => 'I']); ?>> Hispanic or Latino
                            <br>
                            <input type="checkbox" name="CBEthnicity1" id="CBEthnicity1" disabled
                                   value="1" <?php echo Arrays::inArrayCheck('1', $CBEthnicity); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBEthnicity', 'sArr' => $secArr, 'opt' => 'I']); ?> > Not Hispanic or Latino
                            <br>
                            <input type="checkbox" name="CBEthnicity3" id="CBEthnicity3" disabled
                                   value="3" <?php echo Arrays::inArrayCheck('3', $CBEthnicity); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBEthnicity', 'sArr' => $secArr, 'opt' => 'I']); ?> > Not Disclosed
                        <?php } ?>
                    </div>
                </div>
            </div>

            <div class=" col-md-6 <?php echo loanForm::showField('CBRace'); ?>">
                <div class="form-group row">
                    <?php
                        echo loanForm::getFieldLabel('CBRace');
                        echo ' &nbsp;' . loanForm::changeLog(
                            LMRequest::File()->getTblQAInfo_by_LMRId()[0]->QAID,
                            'CBRace',
                            tblQAInfo::class,
                            'What is your Race'
                        );
                    ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="checkbox-list">
                                <label class="checkbox checkbox-solid font-weight-bold" for="CBRace1">
                                    <input type="checkbox"
                                           class="CoBorChildRadio CBRace coBorrower co_borrower_race
                                           <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="CBRace1" id="CBRace1"
                                           value="1" <?php echo Arrays::inArrayCheck('1', $CBRace); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span> American Indian or Alaska Native
                                </label>
                                <label class="checkbox checkbox-solid font-weight-bold" for="CBRace2">
                                    <input type="checkbox" name="CBRace2" id="CBRace2"
                                           value="2" <?php echo Arrays::inArrayCheck( '2', $CBRace); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           class="CoBorChildRadio CBRace coBorrower co_borrower_race
                                           <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span> Asian
                                </label>

                                <div class="col-md-12" id="CBAsian">
                                    <label for="CBRaceSub" class="hidden">Asian Indian, Chinese, Filipino, Japanese,
                                        Korean,
                                        Vietnamese, Other Asian</label>
                                    <div class="checkbox-list">
                                        <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub1">
                                            <input type="checkbox" name="CBRaceSub1" id="CBRaceSub1"
                                                   class="CoBorChildRadio CBAsian co_borrower_race coBorrower
                                                   <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="1" <?php echo Arrays::inArrayCheck('1', $CBRaceSub); ?> >
                                            <span></span> Asian Indian
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub2">
                                            <input type="checkbox" name="CBRaceSub2" id="CBRaceSub2"
                                                   class="CoBorChildRadio CBAsian co_borrower_race coBorrower
                                                   <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="2" <?php echo Arrays::inArrayCheck('2', $CBRaceSub); ?> >
                                            <span></span> Chinese
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub3">
                                            <input type="checkbox" name="CBRaceSub3" id="CBRaceSub3"
                                                   class="CoBorChildRadio CBAsian co_borrower_race coBorrower
                                                   <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="3" <?php echo Arrays::inArrayCheck('3', $CBRaceSub); ?> >
                                            <span></span> Filipino
                                        </label>

                                        <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub4">
                                            <input type="checkbox" name="CBRaceSub4" id="CBRaceSub4"
                                                   class="CoBorChildRadio CBAsian co_borrower_race coBorrower
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="4" <?php echo Arrays::inArrayCheck('4', $CBRaceSub); ?> >
                                            <span></span> Japanese
                                        </label>

                                        <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub5">
                                            <input type="checkbox" name="CBRaceSub5" id="CBRaceSub5"
                                                   class="CoBorChildRadio CBAsian co_borrower_race coBorrower
                                                   <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="5" <?php echo Arrays::inArrayCheck('5', $CBRaceSub); ?> >
                                            <span></span> Korean
                                        </label>

                                        <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub6">
                                            <input type="checkbox" name="CBRaceSub6" id="CBRaceSub6"
                                                   class="CoBorChildRadio CBAsian co_borrower_race coBorrower
                                                   <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="6" <?php echo Arrays::inArrayCheck('6', $CBRaceSub); ?> >
                                            <span></span> Vietnamese
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub7">
                                            <input type="checkbox" name="CBRaceSub7" id="CBRaceSub7"
                                                   class="CoBorChildRadio CBAsian co_borrower_race coBorrower
                                                   <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="7" <?php echo Arrays::inArrayCheck('7', $CBRaceSub); ?> >
                                            <span></span> Other Asian
                                        </label>
                                        <div class="col-md-12 px-6" id="CBAsianDiv">
                                            <label class="col-md-12 font-weight-bold" for="CBRaceAsianOther">
                                                Print Race
                                            </label>
                                            <input type="text" class="form-control input-sm coBorrower" name="CBRaceAsianOther"
                                                   id="CBRaceAsianOther"
                                                   value="<?php echo htmlspecialchars($CBRaceAsianOther); ?>">
                                            <small class="text-muted">For example: Hmong, Laotian, Thai, Pakistani,
                                                Cambodian,
                                                and so on.</small>
                                        </div>
                                    </div>
                                </div>
                                <label class="checkbox checkbox-solid font-weight-bold" for="CBRace3">
                                    <input type="checkbox" name="CBRace3" id="CBRace3"
                                           value="3" <?php echo Arrays::inArrayCheck('3', $CBRace); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           class="CoBorChildRadio CBRace coBorrower co_borrower_race
                                           <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                    <span></span> Black or African American
                                </label>
                                <label class="checkbox checkbox-solid font-weight-bold" for="CBRace4">
                                    <input type="checkbox" name="CBRace4" id="CBRace4"
                                           value="4" <?php echo Arrays::inArrayCheck('4', $CBRace); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           class="CoBorChildRadio CBRace coBorrower co_borrower_race
                                           <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                    <span></span> Native Hawaiian or Other Pacific Islander
                                </label>

                                <div class="col-md-12 pl-6" id="CBNative">
                                    <label for="CBNativeHOPI" class="hidden">Native Hawaiian, Guamanian or Chamorro,
                                        Samoan,
                                        Other
                                        Pacific Islander</label>
                                    <div class="checkbox-list">
                                        <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub2Nat">
                                            <input type="checkbox" name="CBRaceSub8" id="CBRaceSub2Nat"
                                                   class="CoBorChildRadio CBAsian CBNativeHOPI co_borrower_race coBorrower
                                                   <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="8" <?php echo Arrays::inArrayCheck('8', $CBRaceSub); ?> >
                                            <span></span> Native Hawaiian
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub2Gua">
                                            <input type="checkbox" name="CBRaceSub9" id="CBRaceSub2Gua"
                                                   class="CoBorChildRadio CBAsian CBNativeHOPI co_borrower_race coBorrower
                                                   <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="9" <?php echo Arrays::inArrayCheck('9', $CBRaceSub); ?> >
                                            <span></span> Guamanian or Chamorro
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub2Sam">
                                            <input type="checkbox" name="CBRaceSub10" id="CBRaceSub2Sam"
                                                   class="CoBorChildRadio CBAsian CBNativeHOPI co_borrower_race coBorrower
                                                   <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="10" <?php echo Arrays::inArrayCheck('10', $CBRaceSub); ?> >
                                            <span></span> Samoan
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub2Other">
                                            <input type="checkbox" name="CBRaceSub11" id="CBRaceSub2Other"
                                                   class="CoBorChildRadio CBAsian CBNativeHOPI co_borrower_race coBorrower
                                                   <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="11" <?php echo Arrays::inArrayCheck('11', $CBRaceSub); ?> >
                                            <span></span> Other Pacific Islander
                                        </label>
                                        <div class="col-md-12 pl-6" id="CBPacificDiv">
                                            <label class="col-md-12 font-weight-bold" for="CBRacePacificOther">
                                                Print Race
                                            </label>
                                            <input type="text" class="form-control input-sm coBorrower" name="CBRacePacificOther"
                                                   id="CBRacePacificOther"
                                                   value="<?php echo htmlspecialchars($CBRacePacificOther); ?>">
                                            <span class="text-muted">For example: Fijian, Tongan, and so on.</span>
                                        </div>
                                    </div>
                                </div>

                                <label class="checkbox checkbox-solid font-weight-bold" for="CBRace5">
                                    <input type="checkbox" name="CBRace5" id="CBRace5"
                                           value="5" <?php echo Arrays::inArrayCheck('5', $CBRace); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           class="CoBorChildRadio CBRace coBorrower co_borrower_race
                                           <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span> White
                                </label>

                                <label class="checkbox checkbox-solid font-weight-bold" for="CBRace6">
                                    <input type="checkbox" name="CBRace6" id="CBRace6"
                                           value="6" <?php echo Arrays::inArrayCheck('6', $CBRace); ?>
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           class="CoBorChildRadio CBRace coBorrower co_borrower_race
                                           <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span> Not Disclosed
                                </label>
                            </div>
                        <?php } else { ?>
                            <input type="checkbox" name="CBRace1" id="CBRace1" value="1"
                                   disabled <?php echo Arrays::inArrayCheck('1', $CBRace); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'I']); ?> >&nbsp;American Indian or Alaska Native
                            <br>
                            <input type="checkbox" name="CBRace2" id="CBRace2" value="2"
                                   disabled <?php echo Arrays::inArrayCheck('2', $CBRace); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'I']); ?> >&nbsp;Asian
                            <br>
                            <input type="checkbox" name="CBRace3" id="CBRace3" value="3"
                                   disabled <?php echo Arrays::inArrayCheck('3', $CBRace); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'I']); ?> >&nbsp;Black or African American
                            <br>
                            <input type="checkbox" name="CBRace4" id="CBRace4" value="4"
                                   disabled <?php echo Arrays::inArrayCheck('4', $CBRace); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'I']); ?> >&nbsp;Native Hawaiian or Other Pacific Islander
                            <br>
                            <div class="col-md-12" id="CBNative">
                                <input type="checkbox" name="CBRaceSub" id="CBRaceSub" class="" disabled
                                       value="8" <?php echo Arrays::inArrayCheck('8', $CBRaceSub); ?> > Native Hawaiian
                                <br>
                                <input type="checkbox" name="CBRaceSub" id="CBRaceSub" class="" disabled
                                       value="9" <?php echo Arrays::inArrayCheck('9', $CBRaceSub); ?> > Guamanian or
                                Chamorro
                                <br>
                                <input type="checkbox" name="CBRaceSub" id="CBRaceSub" class="" disabled
                                       value="10" <?php echo Arrays::inArrayCheck('10', $CBRaceSub); ?> > Samoan
                                <br>
                                <input type="checkbox" name="CBRaceSub" id="CBRaceSub" class="" disabled
                                       value="11" <?php echo Arrays::inArrayCheck('11', $CBRaceSub); ?> > Other Pacific
                                Islander
                                <div class="col-md-12" id="CBPacificDiv">
                                    <div class="col-md-12">
                                        Print Race
                                        <input type="text" class="form-control input-sm" disabled
                                               name="CBRacePacificOther"
                                               id="CBRacePacificOther"
                                               value="<?php echo htmlspecialchars($CBRacePacificOther); ?>">
                                        <i>For example: Fijian, Tongan, and so on.</i>
                                    </div>
                                </div>
                            </div>
                            <input type="checkbox" name="CBRace5" value="5" id="CBRace5"
                                   disabled <?php echo Arrays::inArrayCheck('5', $CBRace); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'I']); ?> >&nbsp;White
                            <br>
                            <input type="checkbox" name="CBRace6" value="6" id="CBRace6"
                                   disabled <?php echo Arrays::inArrayCheck('6', $CBRace); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBRace', 'sArr' => $secArr, 'opt' => 'I']); ?> >&nbsp;Not Disclosed
                        <?php } ?>
                    </div>
                </div>
            </div>

            <div class=" col-md-6  <?php echo loanForm::showField('CBGender'); ?> ">
                <div class="form-group row">
                    <?php echo loanForm::label('CBGender', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold" for="CBGender2">
                                    <input type="radio"
                                           class="CoBorChildRadio CBGender coBorrower <?php echo BaseHTML::fieldAccess(['fNm' => 'CBGender', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="CBGender" id="CBGender2"
                                           value="2" <?php echo Strings::isChecked('2', $CBGender); ?>
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBGender', 'sArr' => $secArr, 'opt' => 'I']); ?> ><span></span>
                                    Male
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CBGender1">
                                    <input type="radio" name="CBGender" id="CBGender1"
                                           value="1" <?php echo Strings::isChecked('1', $CBGender); ?>
                                           class="CoBorChildRadio CBGender coBorrower"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBGender', 'sArr' => $secArr, 'opt' => 'I']); ?> ><span></span>
                                    Female
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CBGender3">
                                    <input type="radio" name="CBGender" id="CBGender3"
                                           value="3" <?php echo Strings::isChecked('3', $CBGender); ?>
                                           class="CoBorChildRadio CBGender coBorrower"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBGender', 'sArr' => $secArr, 'opt' => 'I']); ?> ><span></span>
                                    Not Disclosed
                                </label>
                            </div>
                        <?php } else { ?>
                            <input type="radio"
                                   class="<?php echo BaseHTML::chkMandField('CBGender', $PCquickAppFieldsInfo, ''); ?>"
                                   name="CBGender" id="CBGender" disabled
                                   value="2" <?php echo Strings::isChecked('2', $CBGender); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBGender', 'sArr' => $secArr, 'opt' => 'I']); ?> > Male
                            <input type="radio" name="CBGender" id="CBGender" disabled
                                   value="1" <?php echo Strings::isChecked('1', $CBGender); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBGender', 'sArr' => $secArr, 'opt' => 'I']); ?> > Female
                            <input type="radio" name="CBGender" id="CBGender" disabled
                                   value="3" <?php echo Strings::isChecked('3', $CBGender); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBGender', 'sArr' => $secArr, 'opt' => 'I']); ?> > Not Disclosed
                        <?php } ?>

                    </div>
                </div>
            </div>
            <div class=" col-md-6  <?php echo loanForm::showField('CBVeteran'); ?> ">
                <div class="form-group row">
                    <?php echo loanForm::label('CBVeteran', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-list">
                                <label class="radio radio-solid font-weight-bold" for="CBVeteran1">
                                    <input type="radio"
                                           class="CoBorChildRadio CBVeteran coBorrower <?php echo BaseHTML::fieldAccess(['fNm' => 'CBVeteran', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="CBVeteran" id="CBVeteran1"
                                           value="1" <?php echo Strings::isChecked('1', $CBVeteran); ?>
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBVeteran', 'sArr' => $secArr, 'opt' => 'I']); ?> ><span></span>
                                    Non-Veteran
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CBVeteran1V">
                                    <input type="radio"
                                           class="CoBorChildRadio CBVeteran coBorrower <?php echo BaseHTML::fieldAccess(['fNm' => 'CBVeteran', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="CBVeteran" id="CBVeteran1V"
                                           value="2" <?php echo Strings::isChecked('2', $CBVeteran); ?>
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBVeteran', 'sArr' => $secArr, 'opt' => 'I']); ?> ><span></span>
                                    Veteran
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CBVeteran1Se">
                                    <input type="radio"
                                           class="CoBorChildRadio CBVeteran coBorrower <?php echo BaseHTML::fieldAccess(['fNm' => 'CBVeteran', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="CBVeteran" id="CBVeteran1Se"
                                           value="3" <?php echo Strings::isChecked('3', $CBVeteran); ?>
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBVeteran', 'sArr' => $secArr, 'opt' => 'I']); ?> ><span></span>
                                    Service-Disabled Veteran
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CBVeteran1S">
                                    <input type="radio"
                                           class="CoBorChildRadio CBVeteran coBorrower <?php echo BaseHTML::fieldAccess(['fNm' => 'CBVeteran', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="CBVeteran" id="CBVeteran1S"
                                           value="4" <?php echo Strings::isChecked('4', $CBVeteran); ?>
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBVeteran', 'sArr' => $secArr, 'opt' => 'I']); ?> ><span></span>
                                    Spouse of Veteran
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CBVeteran1ND">
                                    <input type="radio"
                                           class="CoBorChildRadio CBVeteran coBorrower <?php echo BaseHTML::fieldAccess(['fNm' => 'CBVeteran', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="CBVeteran" id="CBVeteran1ND"
                                           value="5" <?php echo Strings::isChecked('5', $CBVeteran); ?>
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBVeteran', 'sArr' => $secArr, 'opt' => 'I']); ?> ><span></span>
                                    Not Disclosed
                                </label>
                            </div>
                        <?php } else { ?>
                            <input type="radio" disabled
                                   class="coBorrower <?php echo BaseHTML::fieldAccess(['fNm' => 'CBVeteran', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="CBVeteran" id="CBVeteran"
                                   value="1" <?php echo Strings::isChecked('1', $CBVeteran); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBVeteran', 'sArr' => $secArr, 'opt' => 'I']); ?> > Non-Veteran
                            <br>
                            <input type="radio" disabled
                                   class="coBorrower <?php echo BaseHTML::fieldAccess(['fNm' => 'CBVeteran', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="CBVeteran" id="CBVeteran"
                                   value="2" <?php echo Strings::isChecked('2', $CBVeteran); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBVeteran', 'sArr' => $secArr, 'opt' => 'I']); ?> > Veteran
                            <br>
                            <input type="radio" disabled
                                   class="coBorrower <?php echo BaseHTML::fieldAccess(['fNm' => 'CBVeteran', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="CBVeteran" id="CBVeteran"
                                   value="3" <?php echo Strings::isChecked('3', $CBVeteran); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBVeteran', 'sArr' => $secArr, 'opt' => 'I']); ?> > Service-Disabled Veteran
                            <br>
                            <input type="radio" disabled
                                   class="coBorrower <?php echo BaseHTML::fieldAccess(['fNm' => 'CBVeteran', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="CBVeteran" id="CBVeteran"
                                   value="4" <?php echo Strings::isChecked('4', $CBVeteran); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBVeteran', 'sArr' => $secArr, 'opt' => 'I']); ?> > Spouse of Veteran
                            <br>
                            <input type="radio" disabled
                                   class="coBorrower <?php echo BaseHTML::fieldAccess(['fNm' => 'CBVeteran', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="CBVeteran" id="CBVeteran"
                                   value="5" <?php echo Strings::isChecked('5', $CBVeteran); ?>
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'CBVeteran', 'sArr' => $secArr, 'opt' => 'I']); ?> > Not Disclosed
                        <?php } ?>

                    </div>
                </div>
            </div>

            <?php if (!$publicUser) { ?>

                <label class="col-lg-12 mb-4 px-4 py-2 bg-secondary font-weight-bolder" id="cbFiTitle">
                    <?php echo BaseHTML::getSubSectionHeading('GOVT-CB', 'CBFinInstSubSection'); ?>
                </label>
                <div class="col-md-12  cbFiTitle <?php echo loanForm::showField('CBFiEthnicity'); ?>">
                    <div class="form-group row">
                        <?php echo loanForm::label('CBFiEthnicity', 'col-md-7 '); ?>
                        <div class="col-md-5">
                            <?php if ($allowToEdit) { ?>
                                <div class="radio-inline">
                                    <label class="radio radio-solid font-weight-bold" for="CBFiEthnicityYes">
                                        <input type="radio" name="CBFiEthnicity" id="CBFiEthnicityYes" value="Yes"
                                               class="<?php echo BaseHTML::fieldAccess(['fNm' => 'CBFiEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'CBFiEthnicity', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                            <?php echo Strings::isChecked('Yes', $CBFiEthnicity); ?>>
                                        <span></span>Yes</label>
                                    <label class="radio radio-solid font-weight-bold" for="CBFiEthnicityNo">
                                        <input type="radio" name="CBFiEthnicity" id="CBFiEthnicityNo" value="No"
                                               class="<?php echo BaseHTML::fieldAccess(['fNm' => 'CBFiEthnicity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'CBFiEthnicity', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                            <?php echo Strings::isChecked('No', $CBFiEthnicity); ?>> <span></span>No
                                    </label>
                                </div>
                            <?php } else { ?>
                                <h5><?php echo $CBFiEthnicity; ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-12  cbFiTitle <?php echo loanForm::showField('CBFiGender'); ?>">
                    <div class="form-group row">
                        <?php echo loanForm::label('CBFiGender', 'col-md-7 '); ?>
                        <div class="col-md-5">
                            <?php if ($allowToEdit) { ?>
                                <div class="radio-inline">
                                    <label class="radio radio-solid font-weight-bold" for="CBFiGenderYes">
                                        <input type="radio" name="CBFiGender" id="CBFiGenderYes" value="Yes"
                                               class="<?php echo BaseHTML::fieldAccess(['fNm' => 'CBFiGender', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'CBFiGender', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                            <?php echo Strings::isChecked('Yes', $CBFiGender); ?>><span></span> Yes
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="CBFiGenderNo">
                                        <input type="radio" name="CBFiGender" id="CBFiGenderNo" value="No"
                                               class="<?php echo BaseHTML::fieldAccess(['fNm' => 'CBFiGender', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'CBFiGender', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                            <?php echo Strings::isChecked('No', $CBFiGender); ?>><span></span> No
                                    </label>
                                </div>
                            <?php } else { ?>
                                <h5><?php echo $CBFiGender; ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-12  cbFiTitle <?php echo loanForm::showField('CBFiRace'); ?>">
                    <div class="form-group row">
                        <?php echo loanForm::label('CBFiRace', 'col-md-7 '); ?>
                        <div class="col-md-5">
                            <?php if ($allowToEdit) { ?>
                                <div class="radio-inline">
                                    <label class="radio radio-solid font-weight-bold" for="CBFiRaceYes">
                                        <input type="radio" name="CBFiRace" id="CBFiRaceYes" value="Yes"
                                               class="<?php echo BaseHTML::fieldAccess(['fNm' => 'CBFiRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'CBFiRace', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                            <?php echo Strings::isChecked('Yes', $CBFiRace); ?>
                                        ><span></span> Yes
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="CBFiRaceNo">
                                        <input type="radio" name="CBFiRace" id="CBFiRaceNo" value="No"
                                               class="<?php echo BaseHTML::fieldAccess(['fNm' => 'CBFiRace', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'CBFiRace', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                            <?php echo Strings::isChecked('No', $CBFiRace); ?>
                                        ><span></span> No
                                    </label>
                                </div>
                            <?php } else { ?>
                                <h5><?php echo $CBFiRace; ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <label class="col-lg-12 mb-4 px-4 py-2 bg-secondary font-weight-bolder" id="cbDiTitle">
                    <?php echo BaseHTML::getSubSectionHeading('GOVT-CB', 'CBDemoInfoSubSection'); ?>
                </label>
                <div class=" col-md-12 cbDiTitle <?php echo loanForm::showField('CBDDemoInfo'); ?>">
                    <?php echo loanForm::label('CBDDemoInfo', ''); ?>
                    <?php if ($allowToEdit) { ?>
                        <div class="radio-inline">
                            <label class="radio radio-solid font-weight-bold" for="CBDDemoInfo1">
                                <input type="radio" name="CBDDemoInfo" id="CBDDemoInfo1" value="1"
                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'CBDDemoInfo', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'CBDDemoInfo', 'sArr' => $secArr, 'opt' => 'I']); ?> <?php echo Strings::isChecked('1', $CBDDemoInfo); ?>><span></span>
                                Face-to-Face Interview (includes Electronic Media w/ Video Component)
                            </label>
                            <label class="radio radio-solid font-weight-bold" for="CBDDemoInfo2">
                                <input type="radio" name="CBDDemoInfo" id="CBDDemoInfo2" value="2"
                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'CBDDemoInfo', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'CBDDemoInfo', 'sArr' => $secArr, 'opt' => 'I']); ?> <?php echo Strings::isChecked('2', $CBDDemoInfo); ?>><span></span>
                                Telephone Interview
                            </label>

                            <label class="radio radio-solid font-weight-bold" for="CBDDemoInfo3">
                                <input type="radio" name="CBDDemoInfo" id="CBDDemoInfo3" value="3"
                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'CBDDemoInfo', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'CBDDemoInfo', 'sArr' => $secArr, 'opt' => 'I']); ?> <?php echo Strings::isChecked('3', $CBDDemoInfo); ?>><span></span>
                                Fax or Mail
                            </label>
                            <label class="radio radio-solid font-weight-bold" for="CBDDemoInfo4">
                                <input type="radio" name="CBDDemoInfo" id="CBDDemoInfo4" value="4"
                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'CBDDemoInfo', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'CBDDemoInfo', 'sArr' => $secArr, 'opt' => 'I']); ?> <?php echo Strings::isChecked('4', $CBDDemoInfo); ?>><span></span>
                                Email or Internet
                            </label>
                        </div>

                    <?php } else { ?>
                        <input type="radio" name="CBDDemoInfo" id="CBDDemoInfo" value="1"
                               disabled <?php echo Strings::isChecked('1', $CBDDemoInfo); ?>>
                        Face-to-Face Interview (includes Electronic Media w/ Video Component)
                        <br>
                        <input type="radio" name="CBDDemoInfo" id="CBDDemoInfo" value="2"
                               disabled <?php echo Strings::isChecked('2', $CBDDemoInfo); ?>>
                        Telephone Interview
                        <br>
                        <input type="radio" name="CBDDemoInfo" id="CBDDemoInfo" value="3"
                               disabled <?php echo Strings::isChecked('3', $CBDDemoInfo); ?>>
                        Fax or Mail
                        <br>
                        <input type="radio" name="CBDDemoInfo" id="CBDDemoInfo" value="4"
                               disabled <?php echo Strings::isChecked('4', $CBDDemoInfo); ?>>
                        Email or Internet
                    <?php } ?>
                </div>
            <?php } ?>

        </div>

        <?php echo CustomField::RenderForTabSection(
            PageVariables::$PCID,
            tblFile::class,
            LMRequest::$LMRId,
            'GOVT-CB',
            $fileTab,
            $activeTab,
            LMRequest::myFileInfo()->getFileTypes(),
            LMRequest::myFileInfo()->getLoanPrograms()
        ); ?>
    </div>
</div>
<script>
    $(document).ready(function () {
        //HMDA - (Borrower)
        //Financial Institution
        if ($('.bFiTitle.secShow').length > 0) { //show title
            $("#bFiTitle").show();
        } else { //hide title
            $("#bFiTitle").hide();
        }
        //Demographic Information
        if ($('.bDiTitle.secShow').length > 0) { //show title
            $("#bDiTitle").show();
        } else { //hide title
            $("#bDiTitle").hide();
        }
        //Ethnicity
        /*$('.Hispanic').change(function () {
            var needle = $(this).val();
            $('input[type=radio][name=bFiEthnicitySub]').prop('checked', false);
            if (needle == 2) {
                $('.HispanicPrintOrigin').addClass('mandatory');
            } else {
                $('.HispanicPrintOrigin').removeClass('mandatory');
            }
        });*/
        //Race
        /*$('.Race').change(function () {
            var needle = $(this).val();
            $('input[type=radio][name=bFiRaceSub]').prop('checked', false);
            if (needle == 2) { //Asian
                $('.Asian').addClass('mandatory');
                $('.NativeHOPI').removeClass('mandatory');
            } else if (needle == 4) { // Native Hawaiian or Other Pacific Islander
                $('.Asian').removeClass('mandatory');
                $('.NativeHOPI').addClass('mandatory');
            } else { // Others
                $('.Asian').removeClass('mandatory');
                $('.NativeHOPI').removeClass('mandatory');
            }
            //Other Asian & Other Pacific Islander
            // $('#bFiRaceAsianOther').val('');
            // $('#bFiRacePacificOther').val('');
        });*/
        //Other Hispanic or Latino
        /*$('.HispanicPrintOrigin').change(function () {
            var hpo = $(this).val();
            if (hpo == 4) { //show Print Origin
                $('#HispanicPrintOriginDiv').removeClass('hidden');
            } else { //hide Print Origin
                $('#HispanicPrintOriginDiv').addClass('hidden');
            }
        });*/
        //Other Asian & Other Pacific Islander
        /*$('.Asian').change(function () {
            var subRace = $(this).val();
            if (subRace == 7) { //Other Asian
                $('#bFiRacePacificOther').val('');
            } else if (subRace == 11) { //Other Pacific Islander
                $('#bFiRaceAsianOther').val('');
            } else {
                $('#bFiRaceAsianOther').val('');
                $('#bFiRacePacificOther').val('');
            }
        });*/
        //If child options (Ethnicity, Race, Sex, Veteran) are selected
        // make parent (Do you wish to furnish this info) as Yes
        $('.BorChildRadio').change(function () {
            $('.BrYes').prop('checked', true);
        });

        //HMDA - (Co-Borrower)
        //Financial Institution
        if ($('.cbFiTitle.secShow').length > 0) { //show title
            $("#cbFiTitle").show();
        } else { //hide title
            $("#cbFiTitle").hide();
        }
        //Demographic Information
        if ($('.cbDiTitle.secShow').length > 0) { //show title
            $("#cbDiTitle").show();
        } else { //hide title
            $("#cbDiTitle").hide();
        }
        //CB-Ethnicity
        /*$('.CBHispanic').change(function () {
            var needle = $(this).val();
            $('input[type=radio][name=CBEthnicitySub]').prop('checked', false);
            if (needle == 2) {
                $('.CBHispanicPrintOrigin').addClass('mandatory');
            } else {
                $('.CBHispanicPrintOrigin').removeClass('mandatory');
            }
        });*/
        //CB-Other Hispanic or Latino
        /*$('.CBHispanicPrintOrigin').change(function () {
            var hpo = $(this).val();
            if (hpo == 4) { //show Print Origin
                $('#CBHispanicPrintOriginDiv').removeClass('hidden');
            } else { //hide Print Origin
                $('#CBHispanicPrintOriginDiv').addClass('hidden');
            }
        });*/
        //CB-Race
        /*$('.CBRace').change(function () {
            var needle = $(this).val();
            $('input[type=radio][name=CBRaceSub]').prop('checked', false);
            if (needle == 2) { //Asian
                $('.CBAsian').addClass('mandatory');
                $('.CBNativeHOPI').removeClass('mandatory');
            } else if (needle == 4) { // Native Hawaiian or Other Pacific Islander
                $('.CBAsian').removeClass('mandatory');
                $('.CBNativeHOPI').addClass('mandatory');
            } else { // Others
                $('.CBAsian').removeClass('mandatory');
                $('.CBNativeHOPI').removeClass('mandatory');
            }
            //Other Asian & Other Pacific Islander
            // $('#CBRaceAsianOther').val('');
            // $('#CBRacePacificOther').val('');
        });*/
        //CB-Other Asian & Other Pacific Islander
        /*$('.CBAsian').change(function () {
            var CBSubRace = $(this).val();
            if (CBSubRace == 7) { //Other Asian
                $('#CBRacePacificOther').val('');
            } else if (CBSubRace == 11) { //Other Pacific Islander
                $('#CBRaceAsianOther').val('');
            } else {
                $('#CBRaceAsianOther').val('');
                $('#CBRacePacificOther').val('');
            }
        });*/
        //If child options (Ethnicity, Race, Sex, Veteran) are selected
        // make parent (Do you wish to furnish this info) as Yes
        $('.CoBorChildRadio').change(function () {
            $('.CoBrYes').prop('checked', true);
        });
    });

    //copied from the QAForm.js
    function showAndHideQADiv16(fldValue, clsName) {
        //disable Ethnicity/Race/Sex/Veteran for furnish this information = No or N/A
        if (fldValue == 1 || fldValue == 3) {
            $("." + clsName).prop("checked", false);
            //empty other fields
            $('#bFiEthnicitySubOther').val('');
            $('#bFiRaceAsianOther').val('');
            $('#bFiRacePacificOther').val('');
            //$('#Asian, #Native, #Hispanic, #HispanicPrintOriginDiv, #AsianDiv, #PacificDiv').addClass('hidden');
        } else {
            // Instant Validation for child fields
            //HMDA (Borrower) Validate child options
            //hmdaBorValidation();
        }
    }

    /* for co-borrower*/
    function showAndHideQADivCB(fldValue, clsName) {
        //disable Ethnicity/Race/Sex/Veteran for furnish this information = No or N/A
        if (fldValue == 1 || fldValue == 3) {
            $("." + clsName).prop("checked", false);
            //empty other text fields
            $('#CBEthnicitySubOther').val('');
            $('#CBRaceAsianOther').val('');
            $('#CBRacePacificOther').val('');
            //$('#CBAsian, #CBNative, #CBHispanic, #CBHispanicPrintOriginDiv, #CBAsianDiv, #CBPacificDiv').addClass('hidden');
        } else {
            //HMDA (Co-Borrower) Validate child options
            //hmdaCoBorValidation();
        }
    }
</script>

<!-- governmentInfo.php -->