<?php

use models\constants\employedInfo1Array;
use models\constants\gl\glCountryArray;
use models\constants\states;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\CustomField;
use models\lendingwise\tblFile;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\Strings;

global $tabIndex, $isHMLO, $allowToEdit, $borrowerHireDate, $boremptypeshare, $borempmonthlyincome, $paidOften, $employedByOtherParty, $ownerOrSelfEmpoyed;
global $employer1Phone, $isLO, $fieldsInfo, $activeTab;

loanForm::pushSectionID('BEI');
?>
<!-- borrowerEmploymentInfo.php -->
<div
    class="card card-custom borrowerEmploymentInfo <?php if (count(Arrays::getValueFromArray('BEI', $fieldsInfo, $activeTab)) <= 0) {
        echo 'secHide';
    } ?>">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <?php echo loanForm::toggleSectionV2('borrowerEmploymentInfo', true, true, '', 'Borrower Employment Information'); ?>
    </div>

    <div class="card-body borrowerEmploymentInfo_body">
        <div class="form-group row">
            <div
                class="col-md-12 <?php echo loanForm::showField('occupation1', Strings::showField('occupation1', 'incomeInfo')); ?> mb-2">
                <?php echo loanForm::labelWithValue(
                    'occupation1',
                    Strings::showField('occupation1', 'incomeInfo')
                ); ?>
                <?php echo loanForm::text(
                    'occupation1',
                    true,
                    $tabIndex++,
                    Strings::showField('occupation1', 'incomeInfo')
                ); ?>
            </div>
        </div>
        <div class="form-group row">
            <div class="col-md-6 <?php echo loanForm::showField('employedInfo1'); ?>">
                <?php echo loanForm::label2(
                    'employedInfo1'
                ); ?>
                <?php echo loanForm::select(
                    'employedInfo1',
                    true,
                    $tabIndex++,
                    $myFileInfo['incomeInfo']['employedInfo1'] ?? '',
                    employedInfo1Array::$options,
                    null,
                    null,
                    '- Select One -'
                ); ?>
            </div>
            <div class="col-md-6 <?php echo loanForm::showField('borrowerHireDate'); ?>">
                <?php echo loanForm::label2(
                    'borrowerHireDate',
                    '',
                    '',
                    '',
                    null,
                    $isHMLO ? 'Start Date' : 'Hire Date'
                ); ?>
                <?php echo loanForm::date(
                    'borrowerHireDate',
                    $allowToEdit,
                    $tabIndex++,
                    $borrowerHireDate,
                    'dateNewClass'
                ); ?>
            </div>
        </div>
        <div class="form-group row">
            <div class=" col-md-12 <?php echo loanForm::showField('borEmpBusiness'); ?>">
                <div class="row form-group">
                    <?php echo loanForm::label('borEmpBusiness', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <?php echo loanForm::textarea(
                            'borEmpBusiness',
                            true,
                            $tabIndex++,
                            Strings::showField('borEmpBusiness', 'incomeInfo')
                        ); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group row boremploymentType_Disp">
            <div class="col-md-6 <?php echo loanForm::showField('ownershipShare'); ?>">

                <?php echo loanForm::radio(
                    'ownershipShare',
                    true,
                    $tabIndex++,
                    $boremptypeshare, [
                        'lessthan25' => 'I have ownership share of less than 25%',
                        'eqmorethan25' => 'I have ownership share of 25% or more',
                    ]
                ); ?>

            </div>

            <div class="col-md-6 <?php echo loanForm::showField('empmonthlyincome1'); ?>">
                <?php echo loanForm::label('empmonthlyincome1', null); ?>
                <?php echo loanForm::currency(
                    'empmonthlyincome1',
                    true,
                    $tabIndex++,
                    $borempmonthlyincome,
                        '',
                        '',
                        '',
                ); ?>
            </div>
        </div>

        <div class="form-group row">
            <div class="col-md-12">
                <?php echo loanForm::label('paidOften', null, null, null, null, 'How often are you paid?'); ?>
                <?php echo loanForm::radio(
                    'paidOften',
                    true,
                    $tabIndex++,
                    $paidOften, [
                        'weekly' => 'Weekly',
                        'monthly' => 'Monthly',
                        'every2Weeks' => 'Every 2 weeks',
                        'twiceMonth' => 'Twice a month',
                    ]
                ); ?>
            </div>
        </div>

        <?php echo loanForm::sectionHeader('emplInformationH', 'Employer Information'); ?>
        <div class="form-group row">
            <div class="col-md-6 <?php echo loanForm::showField('employer1'); ?>">
                <?php echo loanForm::label2(
                    'employer1',
                    null,
                    null,
                    null,
                    null,
                    'Employer Name',
                    Strings::showField('employer1', 'incomeInfo')
                ); ?>

                <?php echo loanForm::text(
                    'employer1',
                    true,
                    $tabIndex++,
                    Strings::showField('employer1', 'incomeInfo')
                ); ?>

            </div>
            <div class="col-md-6 <?php echo loanForm::showField('yearsAtJob1'); ?>">
                <?php echo loanForm::label2(
                    'yearsAtJob1',
                    null,
                    null,
                    null,
                    null,
                    'Years at Job',
                    Strings::showField('yearsAtJob1', 'incomeInfo')
                ); ?>

                <?php echo loanForm::number(
                    'yearsAtJob1',
                    true,
                    $tabIndex++,
                    Strings::showField('yearsAtJob1', 'incomeInfo')
                ); ?>
            </div>
        </div>
        <div class="form-group row">
            <div class="col-md-12 <?php echo loanForm::showField('employer1Add'); ?>">
                <?php echo loanForm::label('employer1Add'); ?>

                <?php echo loanForm::textarea(
                    'employer1Add',
                    true,
                    $tabIndex++,
                    Strings::showField('employer1Add', 'incomeInfo')
                ); ?>
            </div>
        </div>

        <div class="form-group row">
            <div class=" col-md-4 <?php echo loanForm::showField('employer1City'); ?>">
                <?php echo loanForm::label('employer1City'); ?>

                <?php echo loanForm::text(
                    'employer1City',
                    true,
                    $tabIndex++,
                    Strings::showField('employer1City', 'incomeInfo')
                ); ?>
            </div>

            <div class=" col-md-4 <?php echo loanForm::showField('employer1State'); ?>">
                <?php echo loanForm::label('employer1State'); ?>
                <?php echo loanForm::select(
                    'employer1State',
                    true,
                    $tabIndex++,
                    Strings::showField('employer1State', 'incomeInfo'),
                    states::getOptions()
                ); ?>
            </div>

            <div class=" col-md-4 <?php echo loanForm::showField('employer1Zip'); ?>">
                <?php echo loanForm::label('employer1Zip'); ?>

                <?php echo loanForm::text(
                    'employer1Zip',
                    true,
                    $tabIndex++,
                    Strings::showField('employer1Zip', 'incomeInfo'),
                ); ?>
            </div>
        </div>

        <div class="form-group row">
            <div class=" col-md-6 <?php echo loanForm::showField('employer1Country'); ?>">
                <?php echo loanForm::label('employer1Country', 'col-md-12 '); ?>
                <?php echo loanForm::select(
                    'employer1Country',
                    true,
                    $tabIndex++,
                    Strings::showField('employer1Country', 'incomeInfo'),
                    glCountryArray::$options
                ); ?>
            </div>
            <div class=" col-md-6 <?php echo loanForm::showField('employedByOtherParty'); ?>">
                <div class="checkbox-inline pt-10">
                    <?php echo loanForm::checkbox(
                        'employedByOtherParty',
                        $allowToEdit,
                        $tabIndex++,
                        1,
                        $employedByOtherParty == 1
                    ); ?>
                </div>
            </div>
        </div>

        <div class="form-group row">
            <div class=" col-md-6 mb-4 <?php echo loanForm::showField('ownerOrSelfEmpoyed'); ?>">
                <div class="checkbox-inline pt-10">
                    <?php echo loanForm::checkbox(
                        'ownerOrSelfEmpoyed',
                        $allowToEdit,
                        $tabIndex++,
                        1,
                        $ownerOrSelfEmpoyed == 1
                    ); ?>
                </div>
            </div>
        </div>

        <div class="form-group row">
            <div class="col-md-12 <?php echo loanForm::showField('employmentHistory'); ?>">
                <?php echo loanForm::label('employmentHistory', null, null, null, null, 'Please explain if employment history is less than 2 years'); ?>
                <?php echo loanForm::textarea(
                    'employmentHistory',
                    true,
                    $tabIndex++,
                    Strings::showField('employmentHistory', 'incomeInfo')
                ); ?>
            </div>
        </div>

        <div class="form-group row">
            <div class="col-md-12 <?php echo loanForm::showField('employer1Phone'); ?>">
                <?php echo loanForm::label('employer1Phone', null, null, null, null, 'Employer Phone'); ?>
                <?php echo loanForm::phone(
                    'employer1Phone',
                    true,
                    $tabIndex++,
                    $employer1Phone
                ); ?>
            </div>
        </div>
        <div class="form-group row <?php echo loanForm::showField('contactAtWork1'); ?>">
            <div class="col-md-6">
                <?php echo loanForm::label('contactAtWork1', null, null, null, null, 'May we contact you at work?'); ?>
            </div>

            <div class="col-md-6">
                <?php echo loanForm::switch(
                    'contactAtWork1',
                    $allowToEdit,
                    $tabIndex++,
                    Strings::showField('contactAtWork1', 'incomeInfo'),
                    null,
                    'toggleSwitch(\'contactAtWork1_switch\', \'contactAtWork1\', \'1\', \'0\' )',
                ); ?>
            </div>
        </div>

        <?php if ($isLO == 1 || $isHMLO == 1) { ?>
            <div class="form-group row <?php echo loanForm::showField('borLineOfWorkProfession'); ?>">
                <div class="col-md-6">
                    <?php echo loanForm::label('borLineOfWorkProfession', null, null, null, null, 'Yrs employed in this line of work / profession'); ?>
                </div>
                <div class="col-md-6">
                    <?php echo loanForm::number(
                        'borLineOfWorkProfession',
                        true,
                        $tabIndex++,
                        Strings::showField('borLineOfWorkProfession', 'incomeInfo')

                    ); ?>
                </div>
            </div>

            <?php require_once __DIR__ . '/additionalEmploymentInfo.php'; ?>

        <?php } ?>

        <?php echo CustomField::RenderForTabSection(
            PageVariables::$PCID,
            tblFile::class,
            LMRequest::$LMRId,
            'BEI',
            $fileTab,
            $activeTab,
            LMRequest::myFileInfo()->getFileTypes(),
            LMRequest::myFileInfo()->getLoanPrograms()
        ); ?>
    </div>
</div>

<!-- borrowerEmploymentInfo.php -->
<?php

loanForm::popSectionID();
