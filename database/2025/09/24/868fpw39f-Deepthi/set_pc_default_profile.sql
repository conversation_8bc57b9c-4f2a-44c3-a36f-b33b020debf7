DELIMITER $$


DROP TRIGGER  `set_pc_default_profile`$$

CREATE
    TRIGGER `set_pc_default_profile` AFTER INSERT ON `tblProcessingCompany`
    FOR EACH ROW BEGIN
    DECLARE cnt BIGINT DEFAULT 0 ;
  DECLARE cnt2 BIGINT DEFAULT 0 ;
  DECLARE nn BIGINT DEFAULT 0 ;
    INSERT INTO tblPCPrimeStatus (
        PCID,
        modules,
        primaryStatus,
        dispOrder,
        allowOthersToUpdate,
        activeStatus,
        statusDesc,
        allowAgentToEditFile,
        allowLoanOfficerToEditFile,
        allowClientToEditFile,
        allowBOToEditFile,
        showFSInCP,
        showFSInDash,
        PSID_X
    )
    SELECT
        NEW.PCID,
        modules,
        primaryStatus,
        dispOrder,
        allowOthersToUpdate,
        activeStatus,
        statusDesc,
        allowAgentToEditFile,
        allowLoanOfficerToEditFile,
        allowClientToEditFile,
        allowBOToEditFile,
        show<PERSON>InCP,
        showFSInDash,
        ID
    FROM
        `tblPrimaryStatusMaster`
    WHERE activeStatus = 1 ;
    CALL map_default_modules_statuses_for_pc (NEW.PCID) ;
    INSERT INTO tblPCOnboardingSectionsChecklist (
        checklistName,
        sectionID,
        PCID,
        checklistID
    )
    SELECT
        checklistName,
        sectionID,
        NEW.PCID,
        checklistID
    FROM
        tblOnboardingSectionsChecklist ;
    INSERT INTO tblPCWorkflow (
        PCID,
        WFName,
        recordDate,
        WFOrder,
        dStatus,
        showWorkFlowborrower,
        WFID_X
    )
    SELECT
        NEW.PCID,
        WFName,
        NOW(),
        WFOrder,
        dStatus,
        showWorkFlowborrower,
        WFID
    FROM
        tblWorkflowMaster ;
    INSERT INTO tblPCWorkflowSteps (
        WFID,
        steps,
        recordDate,
        dispOrder,
        dStatus,
        DESCRIPTION,
        WFSID_X
    )
    SELECT
        wf.WFID,
        wfs.steps,
        NOW(),
        wfs.dispOrder,
        wfs.dStatus,
        wfs.description,
        wfs.WFSID
    FROM
        tblWorkflowStepsMaster wfs
            INNER JOIN tblPCWorkflow wf
                       ON wf.WFID_X = wfs.WFID
    WHERE wf.PCID = NEW.PCID ;
    INSERT INTO tblPCWFServiceType (WFID, WFServiceType)
    SELECT
        wf.WFID,
        wfstm.WFServiceType
    FROM
        tblWFServiceTypeMaster wfstm
            INNER JOIN tblPCWorkflow wf
                       ON wf.WFID_X = wfstm.WFID
    WHERE wf.WFID_X > 0
      AND wf.PCID = NEW.PCID ;
    INSERT INTO tblPCFileSubstatusCategory (
        PCID,
        category,
        activeStatus,
        PSCID_X
    )
    SELECT
        NEW.PCID,
        category,
        activeStatus,
        PSCID
    FROM
        tblPCFileSubstatusCategoryMaster ;
    INSERT INTO tblPCFileSubstatus (
        PCID,
        substatus,
        dispOrder,
        activeStatus,
        tempFID,
        categoryId,
        PFSID_X
    )
    SELECT
        NEW.PCID,
        substatus,
        dispOrder,
        tsm.activeStatus,
        tempFID,
        PSCID,
        PFSID
    FROM
        tblPCFileSubstatusMaster tsm
            INNER JOIN tblPCFileSubstatusCategory tsc
                       ON tsm.categoryId = tsc.PSCID_X
    WHERE tsc.PCID = NEW.PCID ;
    INSERT INTO tblPCFileSubstatusModules (
        substatusId,
        moduleCode,
        displayOrder
    )
    SELECT
        ts.PFSID,
        moduleCode,
        displayOrder
    FROM
        tblPCFileSubstatusModulesMaster tsm
            INNER JOIN tblPCFileSubstatus ts
                       ON tsm.substatusId = ts.PFSID_X
    WHERE ts.PCID = NEW.PCID ;
    INSERT INTO tblPCChecklistModules (
        PCID,
        docName,
        dStatus,
        serviceType,
        moduleType,
        createdDate,
        displayOrder,
        MFCID,
        PCMID_X,
        description,
        categoryId,
        coBorrowerRelatedReqdoc,
        rehabRelatedReqdoc,
        noCrossCollRelatedReqdoc,
        usCitizenRelatedReqdoc,
        refDocName,
        refDocUrl
    )
    SELECT
        NEW.PCID,
        docName,
        dStatus,
        serviceType,
        moduleType,
        NOW(),
        displayOrder,
        MFCID,
        PCMID ,
        description,
        categoryId,
        coBorrowerRelatedReqdoc,
        rehabRelatedReqdoc,
        noCrossCollRelatedReqdoc,
        usCitizenRelatedReqdoc,
        refDocName,
        refDocUrl
    FROM
        tblPCChecklistModules
    WHERE PCID = 0 ;
    INSERT INTO tblPCChecklistRequiredBy (
        PCMID,
        requiredBy,
        UID,
        URole,
        recordDate,
        activeStatus
    )
    SELECT
        tc.PCMID,
        requiredBy,
        UID,
        URole,
        recordDate,
        activeStatus
    FROM
        tblPCChecklistRequiredBy tcm
            INNER JOIN tblPCChecklistModules tc
                       ON tc.PCMID_X = tcm.PCMID
    WHERE PCID = NEW.PCID ;
    INSERT INTO tblPCChecklistPropertyState (PCMID, propertyState)
    SELECT
        tc.PCMID,
        propertyState
    FROM
        tblPCChecklistPropertyState tps
            INNER JOIN tblPCChecklistModules tc
                       ON tc.PCMID_X = tps.PCMID
    WHERE PCID = NEW.PCID ;
    INSERT INTO tblPCChecklistBorrowerCreditScore (PCMID, borrowerCreditScore)
    SELECT
        tc.PCMID,
        borrowerCreditScore
    FROM
        tblPCChecklistBorrowerCreditScore tps
            INNER JOIN tblPCChecklistModules tc
                       ON tc.PCMID_X = tps.PCMID
    WHERE PCID = NEW.PCID ;
    INSERT INTO tblPCChecklistBorrowerOcc (PCMID, borrowerOccupancy)
    SELECT
        tc.PCMID,
        borrowerOccupancy
    FROM
        tblPCChecklistBorrowerOcc tps
            INNER JOIN tblPCChecklistModules tc
                       ON tc.PCMID_X = tps.PCMID
    WHERE PCID = NEW.PCID ;
    INSERT INTO tblPCChecklistEntityState (PCMID, entityState)
    SELECT
        tc.PCMID,
        entityState
    FROM
        tblPCChecklistEntityState tps
            INNER JOIN tblPCChecklistModules tc
                       ON tc.PCMID_X = tps.PCMID
    WHERE PCID = NEW.PCID ;
    INSERT INTO tblPCChecklistEntityType (PCMID, entityType)
    SELECT
        tc.PCMID,
        entityType
    FROM
        tblPCChecklistEntityType tps
            INNER JOIN tblPCChecklistModules tc
                       ON tc.PCMID_X = tps.PCMID
    WHERE PCID = NEW.PCID ;
    INSERT INTO tblPCChecklistPropertyType (PCMID, propertyType)
    SELECT
        tc.PCMID,
        propertyType
    FROM
        tblPCChecklistPropertyType tps
            INNER JOIN tblPCChecklistModules tc
                       ON tc.PCMID_X = tps.PCMID
    WHERE PCID = NEW.PCID ;
    INSERT INTO tblPCChecklistTransactionType (PCMID, transactionType)
    SELECT
        tc.PCMID,
        transactionType
    FROM
        tblPCChecklistTransactionType tps
            INNER JOIN tblPCChecklistModules tc
                       ON tc.PCMID_X = tps.PCMID
    WHERE PCID = NEW.PCID ;

    INSERT INTO tblPCChecklistBorrowerType(PCMID, borrowerType)
    SELECT
        tc.PCMID,
        borrowerType
    FROM
        tblPCChecklistBorrowerType tbt
            INNER JOIN tblPCChecklistModules tc
                       ON tc.PCMID_X = tbt.PCMID
    WHERE PCID = NEW.PCID ;

    INSERT INTO tblPCFileTabsOrder (
        tabName,
        PCID,
        displayOrder,
        recordDate
    )
    SELECT
        tabName,
        NEW.PCID,
        displayOrder,
        recordDate
    FROM
        tblPCFileTabsOrder
    WHERE PCID = 1652 ;
    INSERT INTO tblPCShowFileTabs (
        PCID,
        tabName,
        showTabs,
        activeStatus,
        recordDate
    )
    SELECT
        NEW.PCID,
        tabName,
        showTabs,
        activeStatus,
        recordDate
    FROM
        tblPCShowFileTabs
    WHERE PCID = 1652 ;
    IF (NEW.showStartLoanNumber)
  THEN SET cnt =
  (SELECT
    lastLoanNumber
  FROM
    tblLoanNumbers
  WHERE PCID = new.PCID) ;
  SET cnt2 =
  (SELECT
    loanNumberPrefix
  FROM
    tblProcessingCompany
  WHERE PCID = new.PCID) ;
  IF (cnt > cnt2)
  THEN SET nn = cnt ;
    ELSE SET nn = cnt2 ;
END IF ;
IF (cnt > 0)
  THEN
UPDATE
    `tblLoanNumbers`
SET
    `lastLoanNumber` = nn
WHERE `PCID` = NEW.PCID ;
ELSE
  INSERT INTO tblLoanNumbers (PCID, lastLoanNumber)
  VALUES
    (NEW.PCID, nn) ;
END IF ;
END IF ;
END;
$$

DELIMITER ;