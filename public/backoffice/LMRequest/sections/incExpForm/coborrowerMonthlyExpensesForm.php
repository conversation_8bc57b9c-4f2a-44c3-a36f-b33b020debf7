<?php

use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\CustomField;
use models\lendingwise\tblFile;
use models\PageVariables;
use models\standard\Currency;

global $tabIndex, $otherMortgage2, $otherMortgageBalance2, $unsecuredLoans2, $unsecuredLoanBalance2, $creditCards2, $creditCardsBalance2, $studentLoans2;
global $studentLoansBalance2, $childSupportOrAlimonyMonthly2, $childSupportOrAlimonyMonthlyBalance2, $other2, $otherBalance2;
global $coTotalHouseHoldExpenses, $totalHouseHoldExpenses;

loanForm::pushSectionID('CBME');

?>
<!-- coborrowerMonthlyExpensesForm.php -->
<div class="col-md-12">
    <div class="card card-custom coborrowerMonthlyExpenses">
        <div class="card-header card-header-tabs-line bg-gray-100  ">
            <?php echo loanForm::toggleSectionV2('coborrowerMonthlyExpenses', true, true, '', 'Co-Borrower Monthly Expenses'); ?>
        </div>
        <div class="card-body coborrowerMonthlyExpenses_body">
            <div class="form-group row">
                <table class="table table-hover table-bordered table-condensed table-sm table-vertical-center">
                    <tr class="bg-secondary">
                        <td></td>
                        <td class="font-weight-bolder">
                            Monthly Payment
                        </td>
                        <td class="font-weight-bolder">
                            Balance Owed
                        </td>
                    </tr>
                    <tr style="height:26px;">
                        <td colspan="3">&nbsp;</td>
                    </tr>
                    <tr style="height:26px;">
                        <td colspan="3">&nbsp;</td>
                    </tr>
                    <tr style="height:26px;">
                        <td colspan="3">&nbsp;</td>
                    </tr>
                    <tr style="height:26px;">
                        <td colspan="3">&nbsp;</td>
                    </tr>
                    <tr class="<?php echo loanForm::showField('otherMortgage2'); ?>">
                        <td style="width:40%">
                            <?php echo loanForm::label('otherMortgage2', null, null, null, null, 'Existing mortgages and Taxes/Insurance'); ?>
                        </td>

                        <td>
                            <?php echo loanForm::currency(
                                'otherMortgage2',
                                true,
                                $tabIndex++,
                                $otherMortgage2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                                    '',
                            ); ?>
                        </td>
                        <td>
                            <?php echo loanForm::currency(
                                'otherMortgageBalance2',
                                true,
                                $tabIndex++,
                                $otherMortgageBalance2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                                    '',
                            ); ?>
                        </td>
                    </tr>
                    <tr class="even <?php echo loanForm::showField('unsecuredLoans2'); ?>">
                        <td class="font-weight-bold">Installment Loans</td>

                        <td>
                            <?php echo loanForm::currency(
                                'unsecuredLoans2',
                                true,
                                $tabIndex++,
                                $unsecuredLoans2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                                    '',
                            ); ?>
                        </td>
                        <td>
                            <?php echo loanForm::currency(
                                'unsecuredLoanBalance2',
                                true,
                                $tabIndex++,
                                $unsecuredLoanBalance2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                                    '',
                            ); ?>
                        </td>

                    </tr>
                    <tr class="<?php echo loanForm::showField('creditCards2'); ?>">
                        <td class="font-weight-bold">Credit Cards</td>

                        <td>
                            <?php echo loanForm::currency(
                                'creditCards2',
                                true,
                                $tabIndex++,
                                $creditCards2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                                    '',
                            ); ?>
                        </td>
                        <td>
                            <?php echo loanForm::currency(
                                'creditCardsBalance2',
                                true,
                                $tabIndex++,
                                $creditCardsBalance2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                                    '',
                            ); ?>
                        </td>

                    </tr>
                    <tr class="even <?php echo loanForm::showField('studentLoans2'); ?>">
                        <td class="font-weight-bold">Student Loans</td>

                        <td>
                            <?php echo loanForm::currency(
                                'studentLoans2',
                                true,
                                $tabIndex++,
                                $studentLoans2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                                    '',
                            ); ?>
                        </td>
                        <td>
                            <?php echo loanForm::currency(
                                'studentLoansBalance2',
                                true,
                                $tabIndex++,
                                $studentLoansBalance2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                                    '',
                            ); ?>
                        </td>

                    </tr>
                    <tr class="<?php echo loanForm::showField('childSupportOrAlimonyMonthly2'); ?>">
                        <td class="font-weight-bold">Alimony/Child Support</td>

                        <td>
                            <?php echo loanForm::currency(
                                'childSupportOrAlimonyMonthly2',
                                true,
                                $tabIndex++,
                                $childSupportOrAlimonyMonthly2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                                    '',
                            ); ?>
                        </td>
                        <td>
                            <?php echo loanForm::currency(
                                'childSupportOrAlimonyMonthlyBalance2',
                                true,
                                $tabIndex++,
                                $childSupportOrAlimonyMonthlyBalance2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                                    '',
                            ); ?>
                        </td>
                    </tr>
                    <tr class="even <?php echo loanForm::showField('other2'); ?>">
                        <td class="font-weight-bold">
                            Other (Job Related Expenses: Child Care, Union Dues, etc...)
                        </td>

                        <td>
                            <?php echo loanForm::currency(
                                'other2',
                                true,
                                $tabIndex++,
                                $other2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                                    '',
                            ); ?>
                        </td>
                        <td>
                            <?php echo loanForm::currency(
                                'otherBalance2',
                                true,
                                $tabIndex++,
                                $otherBalance2,
                                null,
                                'calculateCoBorrowerTotalHouseHoldExpenses(this.value);',
                                    '',
                            ); ?>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3">&nbsp;</td>
                    </tr>
                    <tr>
                        <td style="vertical-align:middle;" class="font-weight-bolder">
                            Total Household Expenses
                        </td>
                        <td class="font-weight-bolder">
                            $
                            <span id="coTotalHouseHoldExpenses"><?php echo Currency::formatDollarAmountWithDecimal($coTotalHouseHoldExpenses) ?></span>
                            <br> (Co-Borrower)
                        </td>
                        <td class="font-weight-bolder">
                            $
                            <span id="subTotalHouseHoldExpenses"><?php echo Currency::formatDollarAmountWithDecimal($totalHouseHoldExpenses) ?></span>
                            <br> (Borr + Cobor)
                        </td>
                    </tr>
                </table>
            </div>

            <?php echo CustomField::RenderForTabSection(
                PageVariables::$PCID,
                tblFile::class,
                LMRequest::$LMRId,
                'CBME',
                $fileTab,
                $activeTab,
                LMRequest::myFileInfo()->getFileTypes(),
                LMRequest::myFileInfo()->getLoanPrograms()
            ); ?>
        </div>
    </div>
</div>

<!-- coborrowerMonthlyExpensesForm.php -->

<?php

loanForm::popSectionID();