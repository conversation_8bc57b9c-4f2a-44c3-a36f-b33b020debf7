<?php

namespace models\Controllers\LMRequest;

use models\types\strongType;

class assetsLiabilities extends strongType
{

    /**
     * Determines the JavaScript function to execute based on the field and the isLO parameter.
     *
     * @param string $field The field name to evaluate.
     * @param int $isLO A flag indicating whether the field relates to a loan officer (1 for yes, 0 for no).
     * @return string The JavaScript function calls to be executed based on the provided parameters.
     */
    public static function getOnChange(string $field, int $isLO): string
    {
        // Condition 1 fields
        $condition1Fields = [
            'assetAccount',
            'assetStocks',
            'assetNonMarketableSecurities',
            'assetLifeInsurance',
            'assetORE',
            'assetOther',
        ];
        // Condition 2 fields
        $condition2Fields = [
            'assetAccountOwd',
            'assetStocksOwed',
            'assetNonMarketableSecuritiesOwd',
            'assetLifeInsuranceOwed',
            'notesPayableToBanksOthersOwed',
            'installmentAccountOwed',
            'revolvingDebtOwed',
            'unpaidPayableTaxesOwed',
            'assetSecNotesOwd',
            'assetUnsecNotesOwd',
            'assetAcctPayableOwd',
            'assetMarginOwd',
            'otherLiabilitiesOwed',
        ];
        // Condition 3 fields
        $condition3Fields = [
            'assetCars',
            'automobilesOwned3x',
        ];
        // Condition 4 fields
        $condition4Fields = [
            'assetCarsOwed'
        ];


        if (in_array($field, $condition1Fields)) {
            return $isLO == 1 ? 'calculateTotalSumOfAll(this.value);'
                : 'calculateTotalAssets(this.value);';
        }
        if (in_array($field, $condition2Fields)) {
            return $isLO == 1 ? 'calculateTotalSumOfAll(this.value);'
                : "calculateTotalAssetsOwed(this.value,'loanModForm');";
        }
        if (in_array($field, $condition3Fields)) {
            return $isLO == 1 ? 'calculateTotalAutomobiles(this.value); calculateTotalSumOfAll(this.value);'
                : 'calculateTotalAssets(this.value);';
        }
        if (in_array($field, $condition4Fields)) {
            return $isLO == 1 ? 'calculateTotalAutomobiles(this.value); calculateTotalSumOfAll(this.value);'
                : "calculateTotalAssetsOwed(this.value,'loanModForm');";
        }
        // Default
        return '';
    }
}