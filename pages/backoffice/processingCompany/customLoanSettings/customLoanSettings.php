<?php

namespace pages\backoffice\processingCompany\customLoanSettings;

use models\cypher;
use models\lendingwise\db\tblProcessingCompany_db;
use models\lendingwise\tblProcessingCompany;
use models\portals\BackofficePage;
use models\Request;
use models\standard\HTTP;

class customLoanSettings extends BackofficePage
{

    public static function Post()
    {
        $PCID = (int)(cypher::myDecryption(Request::GetClean('customLoanPCID')) ?? null);

        if (!is_numeric($PCID)) {
            HTTP::ExitJSON(['error' => 'Invalid ID'], HTTP::HTTP_STATUS_FORBIDDEN);
        }

        $tblProcessingCompany = tblProcessingCompany::Get([
            tblProcessingCompany_db::COLUMN_PCID => $PCID,
        ]);

        $tblProcessingCompany->allowToImportFeesCostOnInternalLoanChange = Request::GetClean('allowToImportFeesCostOnInternalLoanChange') ? 1 : 0;
        $tblProcessingCompany->Save();
        HTTP::Redirect();
    }


}