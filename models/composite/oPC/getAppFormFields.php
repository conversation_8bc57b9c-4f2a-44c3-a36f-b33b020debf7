<?php

namespace models\composite\oPC;

use models\Database2;
use models\myFileInfo\branchModuleInfo;
use models\standard\Strings;
use models\types\strongType;

/**
 *
 */
class getAppFormFields extends strongType
{
    /**
     * @param $inArray
     * @return array
     */
    public static function getReport($inArray): array
    {
        $assignedPCID = '';
        $fileType = '';
        $filterQry = '';
        $myOpt = '';
        $orQry = ' ';
        $sectionsArr = '';

        $fieldsArray = [];
        $responseArray = [];
        $sectionTooltipArray = [];
        $subSectionHeadingArray = [];
        $PCSectionWithHeaders = [];
        $PCSubSectionsWithHeaders = [];
        $PCSubSectionsWithHeadersTooltip = [];
        $PCSectionTooltips = [];
        $fTArray = [];

        if (array_key_exists('assignedPCID', $inArray)) $assignedPCID = trim($inArray['assignedPCID']);
        if (array_key_exists('fileType', $inArray)) $fileType = trim($inArray['fileType']);
        if (array_key_exists('fTArray', $inArray)) $fTArray = $inArray['fTArray'];
        if (array_key_exists('myOpt', $inArray)) $myOpt = trim($inArray['myOpt']);

        $activeTab = $inArray['activeTab'] ?? '';
        $incrementer = 1;
        $params = [];
        foreach ($fTArray as $eachfT) {

            if (is_object($eachfT)) {
                /* @var branchModuleInfo $eachfT */
                $filterQry .= $orQry . " FIND_IN_SET( :moduleCode" . $incrementer . ", tf.fileType) > 0  ";
                $params['moduleCode' . $incrementer] = trim($eachfT->moduleCode);
           } else {
                $filterQry .= $orQry . " FIND_IN_SET(:moduleCode" . $incrementer . ", tf.fileType) > 0  ";
                $params['moduleCode' . $incrementer] = trim($eachfT['moduleCode']);
            }
            $incrementer++;
            $orQry = 'OR ';
        }
        //    SP_GetPCServiceType_new
        if ($fileType != '') {        //  Webform filter query
            $filterQry = " FIND_IN_SET(:moduleCode" . $incrementer . ", tf.fileType) > 0  ";
            $params['moduleCode' . $incrementer] = trim($fileType);
        }

        $sql = SP_getAppFormFields_New_dee::getReport($filterQry, $myOpt, $activeTab);

        $params['PCID'] = $assignedPCID;
        $params['fileType'] = $filterQry;
        $params['myOpt'] = $myOpt;
        $params['activeTab'] = $activeTab;
        $dbResponseArray = Database2::getInstance()->multiQueryData($sql, 'myOpt', $params);

        /* Get Fields Start */
        $fieldsArray = array_key_exists('fields', $dbResponseArray) ? $dbResponseArray['fields'] : [];

        foreach ($fieldsArray as $i => $field) {
            $fieldsArray[$i]['fieldLabel'] = stripslashes(Strings::replaceProcessString($field['fieldLabel']));
            $responseArray[$fieldsArray[$i]['sectionID']][$fieldsArray[$i]['fieldName']] = $fieldsArray[$i];
        }
        /* Get Fields End */

        /* Get section headings Start */
        if (array_key_exists('sh', $dbResponseArray)) $sectionsArr = $dbResponseArray['sh'];
        if (array_key_exists('sTooltip', $dbResponseArray)) $sectionTooltipArray = $dbResponseArray['sTooltip'];
        if (array_key_exists('PCSubSectionHeadings', $dbResponseArray)) $subSectionHeadingArray = $dbResponseArray['PCSubSectionHeadings'];


        foreach ($sectionsArr as $eachSection) {
            $PCSectionWithHeaders[$eachSection['sectionID']] = $eachSection['sectionHeading'];
            if (!isset($responseArray[$eachSection['sectionID']])) {
                $responseArray[$eachSection['sectionID']] = [];
            }
        }
        foreach ($sectionTooltipArray as $eachSection) {
            $PCSectionTooltips[$eachSection['sectionID']] = $eachSection['sectionTooltip'];
        }
        foreach ($subSectionHeadingArray as $eachSubSection) {
            $PCSubSectionsWithHeaders[$eachSubSection['sectionID'] . '_' . $eachSubSection['fieldName']] = $eachSubSection['fieldLabel'];
            $PCSubSectionsWithHeadersTooltip[$eachSubSection['sectionID'] . '_' . $eachSubSection['fieldName']] = $eachSubSection['toolTip'];
        }
        $responseArray['sh'] = $PCSectionWithHeaders;
        $responseArray['sTooltip'] = $PCSectionTooltips;
        $responseArray['PCSubSectionHeadings'] = $PCSubSectionsWithHeaders;
        $responseArray['PCSubSectionHeadingsToolTip'] = $PCSubSectionsWithHeadersTooltip;

        /* Get section Names End */
        return $responseArray;
    }
}
