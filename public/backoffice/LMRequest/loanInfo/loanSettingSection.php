<?php
global $fileTab, $HMLOPCAmortizationValPCLoanTerms, $isHMLO, $fldEditOpt, $lockedSections, $isEF, $reqForLoanProUnderwriting,
       $minRate, $maxRate, $includeCCF, $maxLTV, $minLoanAmount, $maxLoanAmount, $maxARV, $LGMaxLTC, $minMidFico, $maxMidFico, $minPropertyForFixFlop,
       $maxPropertyForFixFlop, $minPropertyForGrndConst, $maxPropertyForGrndConst,
       $maxPoints, $minPoints, $downPaymentPercent, $HMLOPCElgibleState, $propertyState, $stateArray, $fileRecordedDate,
       $ARVCALRELEASEDATE, $paydownamount, $guideLineMinSeasoningBusinessBankruptcyVal, $guideLineMinSeasoningForeclosureVal,
       $guideLineMinSeasoningPersonalBankruptcyVal, $ft,
       $guideLineMinTimeVal, $guideLineMinTimeVal, $minDSCR, $loanGuideLineId, $fileInfo, $lockedFile, $lockedSectionTxt,$HMLOLoanInfoSectionsDisp;

use models\constants\accrualTypes;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glFrlDesLoanAmt;
use models\constants\gl\glHMLOAmortization;
use models\constants\gl\glHMLOLoanTerms;
use models\constants\gl\globalBankruptcyCat;
use models\constants\gl\globalBusinessBankruptcyCat;
use models\constants\gl\globalMinTimeInBusinessCat;
use models\constants\gl\glPCID;
use models\constants\gl\glRateLockPeriod;
use models\constants\gl\gltypeOfHMLOLoanRequesting;
use models\constants\gl\glUserGroup;
use models\constants\typeOfHMLOLoanRequesting;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\backoffice\LoanInfo;
use models\Controllers\loanForm;
use models\cypher;
use models\HMLOLoanTermsCalculation;
use models\lendingwise\tblFileHMLOPropInfo;
use models\lendingwise\tblLMRClientType;
use models\PageVariables;
use models\paymentBased;
use models\servicing\LoanTerms;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Strings;
use pages\backoffice\loan\web_form\HMLO\classes\HMLOController;


$myFileInfo = [];
if (array_key_exists(LMRequest::$LMRId, $fileInfo)) {
    $myFileInfo = $fileInfo[LMRequest::$LMRId];
}


if (array_key_exists('fileHMLONewLoanInfo', $myFileInfo)) $fileHMLONewLoanInfo = $myFileInfo['fileHMLONewLoanInfo'];
if (array_key_exists('fileHMLOPropertyInfo', $myFileInfo)) $fileHMLOPropertyInfo = $myFileInfo['fileHMLOPropertyInfo'];
if (array_key_exists('paydownInfo', $myFileInfo)) $filepaydownInfo = $myFileInfo['paydownInfo'];

LoanInfo::$isHMLO = $isHMLO;
if (LMRequest::$LMRId) {
    LoanInfo::init(LMRequest::$LMRId, LMRequest::$PCID, PageVariables::$userNumber);
} else {
    LoanInfo::$tabIndex = 1;
    LoanInfo::$purposeOfLoanDisplayCls = 'd-none';
    LoanInfo::$isLoanPaymentAmt = paymentBased::TOTAL_LOAN_AMOUNT;
    LoanInfo::$lien1Terms = LoanTerms::INTEREST_ONLY;
}

if (!PageVariables::$publicUser) {
    HMLOLoanTermsCalculation::Init(
        isset($URLPOSTING), // 1
        $myFileInfo, // 2
        Strings::replaceCommaValues(LoanInfo::$insImpoundsMonth), // 3
        Strings::replaceCommaValues(LoanInfo::$taxImpoundsFee), // 4
        LoanInfo::$isBorBorrowedDownPayment, // 5
        LoanInfo::$propertyNeedRehab, // 6
        LoanInfo::$haveBorSquareFootage, // 7
        LoanInfo::$additionalPropertyRestrictions, // 8
        LoanInfo::$exitStrategy, // 9
        LoanInfo::$acceptedPurchase, // 10
        LoanInfo::$haveCurrentLoanBal, // 11
        LoanInfo::$doYouHaveInvoiceToFactor, // 12
        Strings::Numeric(LoanInfo::$maxAmtToPutDown), // 13
        Strings::replaceCommaValues(LoanInfo::$closingCostFinanced), // 14
        Strings::replaceCommaValues(LoanInfo::$payOffMortgage1), // 15
        Strings::replaceCommaValues(LoanInfo::$payOffMortgage2), // 16
        Strings::replaceCommaValues(LoanInfo::$payOffOutstandingTaxes), // 17
        Strings::replaceCommaValues(LoanInfo::$payOffOtherOutstandingAmounts), // 18
        Strings::replaceCommaValues(LoanInfo::$cashOutAmt), // 19
        $filepaydownInfo, // 20
        LoanInfo::$lien1Terms, // 21
        LoanInfo::$purchaseCloseDate, // 22
        Strings::replaceCommaValues(LoanInfo::$taxes1), // 23
        LoanInfo::$isBlanketLoan, // 24
        LoanInfo::$isThisGroundUpConstruction, // 25
        LoanInfo::$interestChargedFromDate, // 26
        LoanInfo::$interestChargedEndDate, // 27
        Strings::replaceCommaValues(LoanInfo::$taxImpoundsMonth), // 28
        Strings::replaceCommaValues(LoanInfo::$taxImpoundsMonthAmt), // 29
        Strings::replaceCommaValues(LoanInfo::$insImpoundsMonthAmt), // 30
        Strings::replaceCommaValues(LoanInfo::$insImpoundsFee), // 31
        Strings::replaceCommaValues(LoanInfo::$paymentReserves), // 32
        Strings::replaceCommaValues(LoanInfo::$requiredConstruction), // 33
        Strings::replaceCommaValues(LoanInfo::$contingencyReserve), // 34
        $fileHMLOPropertyInfo, // 35
        $fileHMLONewLoanInfo, // 36
        intval(LMRequest::$LMRId), // 37
        LMRequest::$activeTab,// 38
        Strings::replaceCommaValues(LoanInfo::$percentageTotalLoan)
    );
}

$LSTSecArr = BaseHTML::sectionAccess2(['sId' => 'LST', 'opt' => $fileTab]);
$ischk = count($LSTSecArr = BaseHTML::sectionAccess2(['sId' => 'LST', 'opt' => $fileTab, 'activeTab' => LMRequest::$activeTab])) > 0 ? 'checked' : '';


if (PageVariables::$publicUser == 1) {
    $ischk = LMRequest::$LMRId > 0 && $ischk != '' ? 'checked' : '';
}

$glFrlDesLoanAmt = glFrlDesLoanAmt::$glFrlDesLoanAmt;
$glHMLOLoanTerms = glHMLOLoanTerms::$glHMLOLoanTerms;
if (!LMRequest::$LMRId) {
    LoanInfo::$fileMC[] = $ft;
}
$glHMLOAmortization = glHMLOAmortization::getForFile(LoanInfo::$fileMC, $HMLOPCAmortizationValPCLoanTerms);
$glRateLockPeriod = glRateLockPeriod::$glRateLockPeriod;
loanForm::pushSectionID('LT');
?>

<input type="hidden" id="minRate" name="minRate" value="<?php echo $minRate ?>"/>
<input type="hidden" id="maxRate" name="maxRate" value="<?php echo $maxRate ?>"/>
<input type="hidden" id="includeCCF" name="includeCCF" value="<?php echo $includeCCF ?>"/>
<input type="hidden" id="PCID" name="PCID" value="<?php echo LMRequest::$PCID ?>"/>
<input type="hidden" id="LGMaxLTV" name="LGMaxLTV" value="<?php echo $maxLTV ?>"/>
<input type="hidden" id="LGMinLoanAmount" name="LGMinLoanAmount" value="<?php echo $minLoanAmount ?>"/>
<input type="hidden" id="LGMaxLoanAmount" name="LGMaxLoanAmount" value="<?php echo $maxLoanAmount ?>"/>
<input type="hidden" id="LGMaxARV" name="LGMaxARV" value="<?php echo $maxARV ?>"/>
<input type="hidden" id="LGMaxLTC" name="LGMaxLTC" value="<?php echo $LGMaxLTC ?>"/>
<input type="hidden" id="LGMinMidfico" name="LGMinMidfico" value="<?php echo $minMidFico ?>"/>
<input type="hidden" id="LGMaxMidfico" name="LGMaxMidfico" value="<?php echo $maxMidFico ?>"/>
<input type="hidden" id="LGMinFixFlop" name="LGMinFixFlop" value="<?php echo $minPropertyForFixFlop ?>"/>
<input type="hidden" id="LGMaxFixFlop" name="LGMaxFixFlop" value="<?php echo $maxPropertyForFixFlop ?>"/>
<input type="hidden" id="LGMinGround" name="LGMinGround" value="<?php echo $minPropertyForGrndConst ?>"/>
<input type="hidden" id="LGMaxGround" name="LGMaxGround" value="<?php echo $maxPropertyForGrndConst ?>"/>
<input type="hidden" id="LGMaxOrgPoints" name="LGMaxOrgPoints" value="<?php echo $maxPoints ?>"/>
<input type="hidden" id="LGMinOrgPoints" name="LGMinOrgPoints" value="<?php echo $minPoints ?>"/>
<input type="hidden" id="LGDownPaymentPerc" name="LGDownPaymentPerc" value="<?php echo $downPaymentPercent ?>"/>
<input type="hidden" id="LGElgibleState" value="<?php echo $HMLOPCElgibleState; ?>">
<input type="hidden" id="filePropertyState" value="<?php echo $propertyState; ?>">
<input type="hidden" id="filePropertyStateFull"
       value="<?php echo Strings::getStateFullName($stateArray, $propertyState);; ?>">
<input type="hidden" id="activetab" name="activetab" value="<?php echo LMRequest::$activeTab ?>"/>
<input type="hidden" name="allowFormSubmit" id="allowFormSubmit" value="0">
<input type="hidden" name="setRehabDefaultVal" id="setRehabDefaultVal" value="">
<input type="hidden" name="fileRecordedDate" id="fileRecordedDate" value="<?php echo $fileRecordedDate ?>">
<input type="hidden" name="ARVCALRELEASEDATE" id="ARVCALRELEASEDATE" value="<?php echo $ARVCALRELEASEDATE ?>">
<input type="hidden" id="paydownamount" value="<?php echo $paydownamount; ?>">

<input type="hidden" id="MinSeasoningBusinessBankruptcyVal"
       value="<?php echo $guideLineMinSeasoningBusinessBankruptcyVal; ?>">
<input type="hidden" id="MinSeasoningForeclosureVal" value="<?php echo $guideLineMinSeasoningForeclosureVal; ?>">
<input type="hidden" id="MinSeasoningPersonalBankruptcyVal"
       value="<?php echo $guideLineMinSeasoningPersonalBankruptcyVal; ?>">
<input type="hidden" id="minTimeVal" value="<?php echo $guideLineMinTimeVal; ?>">

<input type="hidden" id="MinSeasoningBusinessBankruptcyValText"
       value="<?php echo globalBusinessBankruptcyCat::$globalBusinessBankruptcyCat[$guideLineMinSeasoningBusinessBankruptcyVal]; ?>">
<input type="hidden" id="MinSeasoningForeclosureValText"
       value="<?php echo globalBankruptcyCat::$globalBankruptcyCat[$guideLineMinSeasoningForeclosureVal]; ?>">
<input type="hidden" id="MinSeasoningPersonalBankruptcyValText"
       value="<?php echo globalBankruptcyCat::$globalBankruptcyCat[$guideLineMinSeasoningPersonalBankruptcyVal]; ?>">
<input type="hidden" id="minTimeValText"
       value="<?php echo globalMinTimeInBusinessCat::$globalMinTimeInBusinessCat[$guideLineMinTimeVal]; ?>">
<input type="hidden" id="minDSCR" value="<?php echo $minDSCR ?>"/>
<input type="hidden" id="loanGuideLineId" value="<?php echo $loanGuideLineId ?>"/>

<div class="card card-custom HMLOLoanInfoSections loanSettingsCard LST <?php if (trim($ischk) == 'checked') {
    echo 'secShow';
} else {
    echo 'secHide';
} ?>" id="loanSettingsCard" style="<?php echo $HMLOLoanInfoSectionsDisp; ?>">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label"><?php echo BaseHTML::getSectionHeading('LST'); ?> </h3>
            <?php if (trim(BaseHTML::getSectionTooltip('LST')) != '') { ?>&nbsp;
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="<?php echo BaseHTML::getSectionTooltip('LST'); ?>"></i>
            <?php } ?>
        </div>
        <div class="card-toolbar">
            <?php if ((in_array(PageVariables::$userRole, ['Super']) || PageVariables::$allowToLockLoanFile) && (PageVariables::$userRole != 'Client')) { ?>

                <div id="loan_info_lock" class="d-flex flex-row bd-highlight mb-3">
                    <div class="p-2 font-weight-bold align-self-center"><i
                                class="fa fa-info-circle text-primary tooltipClass mr-2"
                                title="Locking the loan terms prohibits anyone from editing the terms including all employees, branch, broker/loan officer, & borrowers via their portal or webforms. Only users with permission to lock/unlock loans will be able to unlock the loan terms and make edits."></i>
                        Lock Loan File:
                    </div>
                    <input type="hidden" name="lockTabOpt" value="HMLI">
                    <input type="hidden" name="isFilelocked" id="isFilelocked" value="">
                    <input type="hidden" name="lockSectionOpt"
                           value="Loan Terms,Fees & Costs,Required Reserves,Cash-to-Close">
                    <div class="p-2 font-weight-bold align-self-center">
                                                        <span class="switch switch-icon">
                                    <label class="font-weight-bold">
                                        <input
                                                class="form-control" <?php if ($lockedFile == '1') { ?> checked="checked" <?php } ?>
                                               id="lockLoanFileId" type="checkbox"
                                                onchange="toggleSwitch('lockLoanFileId','lockLoanFileHidden','1','0' );">
                                        <input type="hidden" name="fileLock" id="lockLoanFileHidden"
                                               value="<?php echo $lockedFile ?>">
                                        <span></span>
                                    </label>
                                </span>
                    </div>
                </div>
            <?php }
            echo $lockedSectionTxt;
            ?>
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none cursor-pointer"
                  data-card-tool="toggle"
                  data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none cursor-pointer"
                  data-card-tool="reload"
                  data-toggle="tooltip" data-placement="top" title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </span>
            <span
                    class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer"
                    data-card-tool="toggle"
                    data-section="loanSettingsCard"
                    data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
        </div>
    </div>
    <div class="card-body loanSettingsCard_body ">
        <div class="row">

            <div class="col-md-12"><!-- Row 1 start -->
                <div class="row">
                    <?php
                    if (!PageVariables::$publicUser) { // Pivotal Task # : 153830484.
                        $adminSectionArray = BaseHTML::sectionAccess(['sId' => 'Admin', 'opt' => $fileTab]);
                        if (count($adminSectionArray) > 0) {
                            $loanprogramlabel = '';
                            $loanprogramlabel = BaseHTML::fieldAccess(['fNm' => 'LMRClientType', 'sArr' => $adminSectionArray, 'opt' => 'L']);
                            if (PageVariables::$userGroup == 'Super'
                                || PageVariables::$userGroup == 'Employee'
                                || PageVariables::$userGroup == 'Agent'
                                || PageVariables::$userGroup == 'Branch') {
                                ?>
                                <div class="row1SeparatorCls col-md-3 LMRClientType_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'LMRClientType', 'sArr' => $adminSectionArray, 'opt' => 'D']); ?>">
                                    <div class="row form-group">
                                        <label class="col-md-12 font-weight-bold"
                                            <?php if (LMRequest::$activeTab == 'LI' || LMRequest::$activeTab == 'QAPP') { ?>
                                                for="LMRClientType_mirror"
                                            <?php } else { ?>
                                                for="LMRClientType"
                                            <?php } ?>><?php echo BaseHTML::fieldAccess(['fNm' => 'LMRClientType', 'sArr' => $adminSectionArray, 'opt' => 'L']); ?>
                                            <i class="fa fa-info-circle text-primary tooltipClass" data-html="true"
                                               title="<?php echo nl2br(htmlentities(LoanInfo::$loanPgmDetails)); ?>"></i>
                                            <?php echo loanForm::changeLog(
                                                LMRequest::File()->getTblLMRClientType_by_LMRID()->CTID,
                                                'ClientType',
                                                tblLMRClientType::class,
                                                'Loan Program',
                                            ); ?>
                                        </label>
                                        <div class="col-md-12">
                                            <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                                                <div id="service_container">
                                                    <select
                                                        <?php if (LMRequest::$activeTab == 'LI' || LMRequest::$activeTab == 'QAPP') { ?>
                                                            name="LMRClientType_mirror[]"
                                                            id="LMRClientType_mirror"
                                                        <?php } else { ?>
                                                            name="LMRClientType"
                                                            id="LMRClientType"
                                                        <?php } ?>
                                                            onchange="
                                                            <?php if (LMRequest::$activeTab == 'LI' || LMRequest::$activeTab == 'QAPP') { ?>
                                                                    populateDualFieldForLP(this.value, 'LMRClientType');
                                                            <?php } ?>
                                                                    formControl.controlFormFields('',
                                                                    '<?php echo LMRequest::File()->getTblFileModules_by_fileID()->moduleCode; ?>', this.id,
                                                                    'loanProgram'
                                                                    );
                                                                    populatePCBasicLoanInfo(
                                                                    'loanModForm',
                                                                    this.value,
                                                                    '<?php echo LMRequest::$PCID; ?>',
                                                                    '<?php echo LMRequest::File()->getTblFileModules_by_fileID()->moduleCode; ?>',
                                                                    '<?php echo LMRequest::$activeTab; ?>'
                                                                    );
                                                                    getPCMinMaxLoanGuidelines('loanModForm', '<?php echo LMRequest::$PCID; ?>');
                                                                    showAndHideLandFieldsNew(this.value);
                                                            <?php if (LMRequest::$PCID == glPCID::PCID_PROD_CV3) { ?>
                                                                    loanInfoV2Form.showHidePropertyRehabCv3(this.value);
                                                                    loanInfoV2Form.hideInitialLoanAmountCV3();
                                                            <?php } ?>"
                                                            tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                            class="<?php echo BaseHTML::fieldAccess(['fNm' => 'LMRClientType', 'sArr' => $adminSectionArray, 'opt' => 'M']); ?> form-control input-sm" <?php echo BaseHTML::fieldAccess(['fNm' => 'LMRClientType', 'sArr' => $adminSectionArray, 'opt' => 'I']); ?>>
                                                        <option value="">- Select -</option>
                                                        <?php
                                                        if (LMRequest::$LMRId > 0) {
                                                            $serviceCnt = 0;
                                                            if (count(LoanInfo::$servicesRequested) > 0) $serviceCnt = count(LoanInfo::$servicesRequested);
                                                            for ($j = 0; $j < $serviceCnt; $j++) {
                                                                $LMRClientTypeCode = '';
                                                                $sOpt = '';
                                                                $LMRClientType = '';
                                                                $LMRClientTypeCode = trim(LoanInfo::$servicesRequested[$j]['LMRClientType']);
                                                                $LMRClientType = trim(LoanInfo::$servicesRequested[$j]['serviceType']);
                                                                $chk = '';
                                                                $chk = Strings::isKeyChecked(LoanInfo::$LMRClientTypeInfo, 'ClientType', $LMRClientTypeCode);
                                                                if (trim($chk) == 'checked') $chk = 'selected ';
                                                                $displayOption = '';
                                                                if ($LMRClientTypeCode == 'TBD' && LoanInfo::$LMRClientTypeInfo[0]->ClientType != 'TBD') {
                                                                    $displayOption = "style = 'display:none;' ";
                                                                }
                                                                if (LoanInfo::$servicesRequested[$j]['internalLoanProgram'] == 0) {
                                                                    echo '<option ' . $chk . ' ' . $displayOption . " value=\"$LMRClientTypeCode\">$LMRClientType</option>";
                                                                }
                                                            }
                                                        }
                                                        if (in_array('TBD', LoanInfo::$fileLP ?? []) && LMRequest::$LMRId > 0) { ?>
                                                            <!--                                                                <option selected-->
                                                            <!--                                                                        value="--><?php //echo 'TBD'; ?><!--">--><?php //echo 'TBD'; ?><!--</option>-->
                                                        <?php }
                                                        ?>
                                                    </select>
                                                </div>
                                                <?php
                                            } else {
                                                $selectServices = '';
                                                $j = 0;
                                                for ($i = 0; $i < count(LoanInfo::$LMRClientTypeInfo ?? []); $i++) {
                                                    if (!empty(HMLOController::$glLMRClientTypeData) && array_key_exists(LoanInfo::$LMRClientTypeInfo[$i]->ClientType, HMLOController::$glLMRClientTypeData)) {
                                                        if ($j > 0) $selectServices .= ', ';
                                                        $selectServices .= trim(HMLOController::$glLMRClientTypeData[LoanInfo::$LMRClientTypeInfo[$i]->ClientType]);
                                                        $j++;
                                                    }
                                                }
                                                if (in_array('TBD', LoanInfo::$fileLP ?? []) && LMRequest::$LMRId > 0) {
                                                    $selectServices = 'TBD';
                                                }
                                                echo '<b>' . $selectServices . '</b>';
                                            }
                                            ?>
                                        </div>
                                    </div>
                                </div>
                                <?php
                            }
                        }
                        ?>


                        <?php
                        if (in_array(PageVariables::$userGroup, ['Super', 'Employee'])
                                || (PageVariables::$userGroup == 'Branch' && PageVariables::$allowToAccessInternalLoanProgram == 1)
                                || (PageVariables::$userGroup == 'Agent' && PageVariables::$allowToAccessInternalLoanProgram == 1)
                        ) {
                            loanForm::popSectionID();
                            loanForm::pushSectionID('Admin');
                            ?>
                            <div class="col-md-3 internalLoanProgram_disp <?php echo loanForm::showField('internalLoanProgram'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label2(
                                            'internalLoanProgram',
                                            'col-md-12'
                                    ); ?>
                                    <div class="col-md-12">
                                        <?php if (LMRequest::$allowToEdit) { ?>
                                            <input type="hidden"
                                                   name="LMRInternalLoanProgramShadow"
                                                   value="">
                                            <input type="hidden"
                                                   id="allowToImportFeesCostOnInternalLoanChange"
                                                   value="<?= LMRequest::File()->getTblProcessingCompany_by_FPCID()->allowToImportFeesCostOnInternalLoanChange ?>">
                                            <select data-placeholder="Select Internal Loan Program"
                                                    class="loanTermsInternalLoanProgram chzn-select <?php echo BaseHTML::fieldAccess(['fNm' => 'internalLoanProgram', 'sArr' => $adminSectionArray, 'opt' => 'M']); ?>"
                                                    id="LMRInternalLoanProgram"
                                                    name="LMRInternalLoanProgram[]"
                                                    multiple="">
                                                <option></option>
                                                <?php
                                                foreach (LMRequest::$internalLoanProgramList as $program) {
                                                    $chk = '';
                                                    $LMRClientTypeCode = trim($program['LMRClientType'] ?? '');
                                                    $LMRClientType     = trim($program['serviceType'] ?? '');

                                                    if (($program['internalLoanProgram'] ?? '') == '1') {
                                                        // **exception** PCID = 4326 (BD Capital)
                                                        if (LMRequest::$PCID == glPCID::PCID_BD_CAPTIAL && LMRequest::$LMRId == 0) {
                                                            if ($LMRClientType == 'To Be Determined') {
                                                                $chk = 'selected';
                                                            }
                                                        } else if (in_array($LMRClientTypeCode, $myFileInfo['LMRInternalLoanprograms'] ?? [])) {
                                                                $chk = 'selected';
                                                        }
                                                        ?>
                                                        <option <?php echo $chk; ?>
                                                                value="<?php echo $LMRClientTypeCode; ?>">
                                                            <?php echo $LMRClientType; ?>
                                                        </option>
                                                        <?php
                                                    }
                                                }
                                                ?>
                                            </select>
                                        <?php } else {
                                            $selectInternalServices = [];
                                            foreach ($myFileInfo['LMRInternalLoanprograms'] as $internalProgram) {
                                                $addKeyVal = array_search(
                                                        $internalProgram,
                                                        array_column(LMRequest::$internalLoanProgramList, 'LMRClientType')
                                                );
                                                if (is_numeric($addKeyVal)) {
                                                    $selectInternalServices[] = trim(LMRequest::$internalLoanProgramList[$addKeyVal]['serviceType']);
                                                }
                                            }
                                            ?>
                                            <h5 style="width:380px;"><?php echo implode(', ', $selectInternalServices); ?></h5>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>



                        <?php
                        loanForm::popSectionID();
                        loanForm::pushSectionID('LST');
                        ?>
                        <div class="row1SeparatorCls col-md-3 loanLoanNumber_disp <?php echo loanForm::showField('loanLoanNumber'); ?>">
                            <div class="row form-group">
                                <?php
                                echo loanForm::label('loanLoanNumber', 'col-md-12 font-weight-bold label_highlight');

                                ?>
                                <div class="col-md-12">
                                    <?php if (PageVariables::$showStartLoanNumber == 1 && !glCustomJobForProcessingCompany::generateFileIDAsLoanNumber(LMRequest::$PCID)) { ?>
                                        <div class="input-group">
                                            <input
                                                    class="form-control  <?php echo BaseHTML::fieldAccess(['fNm' => 'loanNumber', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                                    type="text"
                                                <?php if ($fileTab == 'FA' || $fileTab == 'QA') { ?>
                                                    name="loanLoanNumber" id="loanLoanNumber" <?php } else { ?>name="loanNumber" id="loanNumber" <?php } ?>
                                                    placeholder="Loan Number"
                                                <?php if (($fileTab == 'FA' || $fileTab == 'QA') &&
                                                    (trim(Strings::isKeyChecked(LoanInfo::$fileModuleInfo, 'moduleCode', 'LM')) == 'checked'
                                                        || trim(Strings::isKeyChecked(LoanInfo::$fileModuleInfo, 'moduleCode', 'SS')) == 'checked')) { ?>
                                                    onchange="mirrorLoanNumber(this.id)" <?php } ?>
                                                    value="<?php echo htmlentities(Strings::showField('loanNumber', 'LMRInfo')); ?>"
                                                    maxlength="45"
                                                    size="25" autocomplete="off"
                                                    tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                    readonly>
                                            <?php if (!Strings::showField('loanNumber', 'LMRInfo')) { ?>
                                                <div class="input-group-append">
                                                    <span class="input-group-text" id="getLoanNo">
                                                        <a style="text-decoration:none;"
                                                           class="fa fa-refresh"
                                                            <?php if ($fileTab == 'FA' || $fileTab == 'QA') { ?>
                                                                onclick="getAvailableLoanNo('<?php echo cypher::myEncryption(LMRequest::$PCID) ?>','loanLoanNumber');"
                                                            <?php } else { ?>
                                                                onclick="getAvailableLoanNo('<?php echo cypher::myEncryption(LMRequest::$PCID) ?>','loanNumber');"
                                                            <?php } ?>
                                                           title="Click to auto create loan number.">
                                                            <i class="tooltipClass flaticon2-reload text-success"></i>
                                                        </a>
                                                    </span>
                                                </div>
                                            <?php } ?>
                                        </div>
                                    <?php } else if (LMRequest::$allowToEdit) { ?>
                                        <input type="text"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'loanLoanNumber', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?> "
                                            <?php if ($fileTab == 'FA' || $fileTab == 'QA') { ?> onchange="mirrorLoanNumber(this.id)" name="loanLoanNumber" id="loanLoanNumber" <?php } else { ?>name="loanNumber" id="loanNumber" <?php } ?>
                                               tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                               value="<?php echo htmlspecialchars(Strings::showField('loanNumber', 'LMRInfo')); ?>"
                                               maxlength="50"
                                               autocomplete="off"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'loanLoanNumber', 'sArr' => $LSTSecArr, 'opt' => 'I']);
                                            if (glCustomJobForProcessingCompany::generateFileIDAsLoanNumber(LMRequest::$PCID)) {
                                                echo 'readonly';
                                            }
                                            ?> >
                                    <?php } else { ?>
                                        <b><?php echo Strings::showField('loanNumber', 'LMRInfo'); ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <?php
                        if (PageVariables::$userRole != glUserGroup::CLIENT) { ?>
                            <div class="row1SeparatorCls col-md-3 HMLOLender_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'HMLOLender', 'sArr' => $LSTSecArr, 'opt' => 'D']); ?>">
                                <div class="row form-group">
                                    <?php
                                    echo loanForm::label('HMLOLender', 'col-md-12 ');
                                    ?>
                                    <div class="col-md-12">
                                        <?php if (LMRequest::$allowToEdit) { ?>
                                            <input name="HMLOLender" id="HMLOLender"
                                                   type="text"
                                                   tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                   maxlength="75"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'HMLOLender', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                                   value="<?php echo htmlspecialchars(Strings::showField('HMLOLender', 'fileHMLONewLoanInfo')); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'HMLOLender', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?>>
                                        <?php } else { ?>
                                            <h5><?php echo Strings::showField('HMLOLender', 'fileHMLONewLoanInfo') ?></h5><?php } ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <?php
                        loanForm::popSectionID();
                    }

                    ?>
                </div>
            </div><!-- Row 1 end -->
            <div class="col-md-12 row1SeparatorCls separator separator-dashed my-2 separator12"
                 id="row1SeparatorDiv"></div>
            <?php loanForm::pushSectionID('LST'); ?>
            <div class="col-md-12"><!-- Row 2 Start -->
                <div class="row">
                    <div class="row2SeparatorCls  col-md-3  desiredLoanAmount_disp <?php echo loanForm::showField('desiredLoanAmount'); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('desiredLoanAmount', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) {
                                    $fldAcc = '';
                                    if (in_array(LMRequest::$PCID, $glFrlDesLoanAmt)) {
                                        if (PageVariables::$userRole != 'Manager') {
                                            $fldAcc = ' readOnly ';
                                        }
                                    }
                                    ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'desiredLoanAmount', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                               name="desiredLoanAmount" id="desiredLoanAmount"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->fileHMLONewLoanInfo()->desiredLoanAmount) ?>"
                                               onblur="currencyConverter(this, this.value);"
                                               placeholder="0.00"
                                               TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>"
                                               autocomplete="off" <?php echo $fldAcc . ' ' . BaseHTML::fieldAccess(['fNm' => 'desiredLoanAmount', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else { ?>
                                    <b>$ <?php echo Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->fileHMLONewLoanInfo()->desiredLoanAmount) ?></b>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                    <!-- Desired Interest Rate Range -->
                    <div class="row2SeparatorCls col-md-3 desiredInterestRateRange_disp <?php echo loanForm::showField('desiredInterestRateRange'); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('desiredInterestRateRange', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <input type="text"
                                               name="desiredInterestRateRangeFrom"
                                               id="desiredInterestRateRangeFrom"
                                               placeholder="0.000"
                                               class="input-sm form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'desiredInterestRateRange', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                               onblur="currencyConverter(this, this.value, 3);"
                                               value="<?php echo number_format(Strings::replaceCommaValues(Strings::showField('desiredInterestRateRangeFrom', 'fileHMLONewLoanInfo')), 3); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'desiredInterestRateRange', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?>>
                                        <div class="input-group-append">
                                                    <span class="input-group-text">
                                                        %
                                                    </span>
                                        </div>
                                        <input type="text"
                                               name="desiredInterestRateRangeTo"
                                               id="desiredInterestRateRangeTo"
                                               placeholder="0.000"
                                               onblur="currencyConverter(this, this.value, 3);"
                                               class="input-sm form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'desiredInterestRateRange', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                               value="<?php echo number_format(Strings::replaceCommaValues(Strings::showField('desiredInterestRateRangeTo', 'fileHMLONewLoanInfo')), 3); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'desiredInterestRateRange', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?> >
                                        <div class="input-group-append">
                                                    <span class="input-group-text">
                                                        %
                                                    </span>
                                        </div>
                                    </div>
                                <?php } else { ?>
                                    <b> <?php echo number_format(Strings::replaceCommaValues(Strings::showField('desiredInterestRateRangeFrom', 'fileHMLONewLoanInfo')), 3) . ' % '; ?>
                                        -
                                        <?php echo number_format(Strings::replaceCommaValues(Strings::showField('desiredInterestRateRangeTo', 'fileHMLONewLoanInfo')), 3) . ' % '; ?></b>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                    <!-- Desired Interest Rate Range -->
                    <div class="row2SeparatorCls col-md-3 loanTerm_disp <?php echo loanForm::showField('loanTerm'); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label(
                                'loanTerm',
                                'col-md-12 ',
                                '',
                                loanForm::changeLog(
                                    LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->HMLOPID,
                                    'loanTerm',
                                    tblFileHMLOPropInfo::class
                                )
                            ); ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <?php if (trim(LoanInfo::$loanTermCRBStatus) == 'disabled' && $_GET['tabOpt'] == 'HMLI') { ?>
                                        <input type="hidden"
                                               name="loanTermCRB"
                                               id="loanTermCRB"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'loanTerm', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?>
                                               class="<?php echo BaseHTML::fieldAccess(['fNm' => 'loanTerm', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                               value="<?php echo LoanInfo::$loanTerm; ?>">
                                    <?php } ?>
                                    <select
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'loanTerm', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                            name="loanTerm"
                                            id="loanTerm"
                                            onchange="monthlyInterestRate(this.value, 0,0)"
                                            TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'loanTerm', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?>
                                        <?php echo LoanInfo::$loanTermCRBStatus; ?>>
                                        <option value=""> - Select -</option>
                                        <?php
                                        foreach ($glHMLOLoanTerms as $eachLoanTerm) {
                                            echo "<option value=\"" . $eachLoanTerm . "\" " . Arrays::isSelected($eachLoanTerm, LoanInfo::$loanTerm) . '>' . $eachLoanTerm . '</option>';
                                        }
                                        ?>
                                    </select>
                                <?php } else { ?>
                                    <input type="hidden" name="loanTerm" id="loanTerm"
                                           value="<?php echo Strings::showField('loanTerm', 'fileHMLOPropertyInfo') ?>">
                                    <?php echo '<b>' . Strings::showField('loanTerm', 'fileHMLOPropertyInfo') . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                    <div class="row2SeparatorCls col-md-3 typeOfHMLOLoanRequesting_disp <?php echo loanForm::showField('typeOfHMLOLoanRequesting'); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label(
                                'typeOfHMLOLoanRequesting',
                                'col-md-12 ',
                                '',
                                loanForm::changeLog(
                                    LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->HMLOPID,
                                    'typeOfHMLOLoanRequesting',
                                    tblFileHMLOPropInfo::class
                                )
                            ); ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <select name="typeOfHMLOLoanRequesting"
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'typeOfHMLOLoanRequesting', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                            id="typeOfHMLOLoanRequesting"
                                            onchange="getPCMinMaxLoanGuidelines('loanModForm', '<?php echo LMRequest::$PCID ?>'); showAndHideCommercialFields(this.value);calculateCapRate();"
                                            tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'typeOfHMLOLoanRequesting', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?>>
                                        <option value=""> - Select -</option>
                                        <?php
                                        if (sizeof($gltypeOfHMLOLoanRequesting ?? []) == 0) {
                                            $gltypeOfHMLOLoanRequesting = gltypeOfHMLOLoanRequesting::$gltypeOfHMLOLoanRequesting;
                                        }
                                        if (in_array('EF', LoanInfo::$fileMC) || in_array('loc', LoanInfo::$fileMC)) {
                                            $gltypeOfHMLOLoanRequesting[] = 'Equipment Financing';
                                        }
                                        if (!$gltypeOfHMLOLoanRequesting) {
                                            $gltypeOfHMLOLoanRequesting = [];
                                        }
                                        sort($gltypeOfHMLOLoanRequesting);
                                        for ($i = 0; $i < count($gltypeOfHMLOLoanRequesting); $i++) {
                                            $sOpt = '';
                                            $typeOfLoan = '';
                                            $typeOfLoan = trim($gltypeOfHMLOLoanRequesting[$i]);
                                            $sOpt = Arrays::isSelected($typeOfLoan, LoanInfo::$typeOfHMLOLoanRequesting);
                                            echo "<option value=\"" . $typeOfLoan . "\" " . $sOpt . '>' . $typeOfLoan . '</option>';
                                        }
                                        ?>
                                    </select>
                                <?php } else { ?>
                                    <b><?php echo Strings::showField('typeOfHMLOLoanRequesting', 'fileHMLOPropertyInfo') ?></b><?php } ?>
                            </div>
                        </div>
                    </div>

                    <div class="row2SeparatorCls col-md-3 useOfFundsDiv useOfFunds_disp
                    <?php echo loanForm::showField('useOfFundsLT');
                    if (!in_array(HMLOLoanTermsCalculation::$typeOfHMLOLoanRequesting, [
                            typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE,
                            typeOfHMLOLoanRequesting::DELAYED_PURCHASE,
                            typeOfHMLOLoanRequesting::COMMERCIAL_CASH_OUT_REFINANCE,
                            typeOfHMLOLoanRequesting::LINE_OF_CREDIT
                    ])) {
                        echo ' d-none ';
                    } ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label(
                                    'useOfFundsLT',
                                    'col-md-12 ',
                                    '',
                                    loanForm::changeLog(
                                            LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->HMLOPID,
                                            'useOfFunds',
                                            tblFileHMLOPropInfo::class
                                    )
                            ); ?>
                            <div class="col-md-12">
                                <?php
                                if (glCustomJobForProcessingCompany::isPC_CV3(LMRequest::$PCID)) {
                                    $useFundsMaxLength = loanForm::getFieldLength('useOfFunds', 'tblFileHMLOPropInfo');
                                } else {
                                    $useFundsMaxLength = 5000;
                                }
                                echo loanForm::textarea(
                                        'useOfFundsLT',
                                        LMRequest::$allowToEdit,
                                        LoanInfo::$tabIndex++,
                                        LoanInfo::$useOfFunds,
                                        'validateMaxLength',
                                        'assignValueToUseOfFundsMirrorfield(this.value,this.id);',
                                        '',
                                        $useFundsMaxLength,
                                        'mirror'
                                ); ?>
                            </div>
                        </div>
                    </div>

                    <?php
                    if (LMRequest::$allowToEdit) {
                        if (LoanInfo::$amortizationType == '') {
                            LoanInfo::$amortizationType = 'Fixed';
                        }
                        ?>
                        <div class="row2SeparatorCls  col-md-3 amortizationType_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'amortizationType', 'sArr' => $LSTSecArr, 'opt' => 'D']); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label(
                                    'amortizationType',
                                    'col-md-12 ',
                                    '',); ?>
                                <div class="col-md-12">
                                    <div class="radio-inline">
                                        <label class="radio radio-solid "
                                               for="amortizationTypeFixed"><input
                                                    type="radio"
                                                    name="amortizationType"
                                                    id="amortizationTypeFixed"
                                                    class="form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'amortizationType', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                                    value="Fixed" <?php echo Strings::isChecked('Fixed', LoanInfo::$amortizationType); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'amortizationType', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?>><span></span>Fixed
                                            Rate</label>
                                        <label class="radio radio-solid " for="amortizationTypeAdjust">
                                            <input type="radio"
                                                   name="amortizationType"
                                                   id="amortizationTypeAdjust"
                                                   class="form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'amortizationType', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                                   value="Adjustable" <?php echo Strings::isChecked('Adjustable', LoanInfo::$amortizationType); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'amortizationType', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?>><span></span>Adjustable
                                            Rate</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                    ?>

                    <div class="row2SeparatorCls col-md-3 lien1Terms_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Terms', 'sArr' => $LSTSecArr, 'opt' => 'D']); ?>">
                        <div class="row form-group">
                            <label class="col-md-12 font-weight-bold"
                                   for="lien1Terms"><?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Terms', 'sArr' => $LSTSecArr, 'opt' => 'L']); ?>
                                <?php echo loanForm::changeLog(
                                    LMRequest::$LMRId,
                                    'lien1Terms',
                                    \models\lendingwise\tblFile::class,
                                    'Amortization'
                                ); ?>
                            </label>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <select
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Terms', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                            name="lien1Terms" id="lien1Terms"
                                            tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php if (LoanInfo::$isLoanPaymentAmt == 'SMP' ||
                                        in_array(LMRequest::$PCID, glCustomJobForProcessingCompany::$glCustomTabDealSizerCommercial)) {
                                        echo 'disabled';
                                    } ?>
                                            onchange="updateLoanDetail();" <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Terms', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?>>
                                        <option value=""> - Select -</option>
                                        <?php

                                        foreach ($glHMLOAmortization as $i => $amort) {
                                            $sOpt = Arrays::isSelected($amort, LoanInfo::$lien1Terms);
                                            echo '<option value="' . $amort . '"' . $sOpt . '>' . $amort . '</option>';
                                        }
                                        ?>
                                    </select>
                                <?php } else {
                                    echo "<span class=\"H5\">" . LoanInfo::$lien1Terms . ' &nbsp;&nbsp;&nbsp; ' . LoanInfo::$amortizationType . '</span>';
                                } ?>
                            </div>
                        </div>
                    </div>

                </div>
            </div><!-- Row 2 End -->
            <div class="col-md-12 row2SeparatorCls separator separator-dashed my-2 separator12"
                 id="row2SeparatorDiv"></div>

            <div class="col-md-12"><!-- Row 3 Start -->
                <div class="row">
                    <?php if (!PageVariables::$publicUser) { ?>
                        <!-- Rate Lock fields added on Mar 15, 2024 sc-48955 -->
                        <div class="row3SeparatorCls col-md-3 <?php echo loanForm::showField('rateLockPeriod'); ?>">
                            <div class="row form-group">
                                <?php
                                echo loanForm::label(
                                    'rateLockPeriod',
                                    'col-md-12 ',
                                    '',
                                    loanForm::changeLog(
                                        LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                        'rateLockPeriod',
                                        \models\lendingwise\tblFileHMLONewLoanInfo::class,
                                        'Rate Lock Period'
                                    ),
                                );
                                ?>
                                <div class="col-md-12">
                                    <?php if (LMRequest::$allowToEdit) { ?>
                                        <select
                                                name="rateLockPeriod"
                                                id="rateLockPeriod"
                                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'rateLockPeriod', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?> chzn-select"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'rateLockPeriod', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?>
                                                data-placeholder="Please Select <?php echo loanForm::getFieldLabel('rateLockPeriod'); ?>"
                                                onchange="loanTerms.hideOrShowRateLockFields(this.value);">
                                            <option value=""></option>
                                            <?php foreach ($glRateLockPeriod as $eachRateLockPeriod) { ?>
                                                <option value="<?php echo $eachRateLockPeriod; ?>" <?php echo Arrays::isSelected($eachRateLockPeriod, LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockPeriod); ?>>
                                                    <?php echo trim($eachRateLockPeriod); ?>
                                                </option>
                                            <?php } ?>
                                        </select>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <div class="row3SeparatorCls rateLockFields col-md-3 <?php echo loanForm::showField('rateLockExtension'); ?>">
                            <div class="row form-group">
                                <?php
                                echo loanForm::label(
                                    'rateLockExtension',
                                    'col-md-12 ',
                                    '',
                                    loanForm::changeLog(
                                        LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                        'rateLockExtension',
                                        \models\lendingwise\tblFileHMLONewLoanInfo::class,
                                        'Rate Lock Extension'
                                    ),
                                );
                                ?>
                                <div class="col-md-12">
                                    <?php if (LMRequest::$allowToEdit) { ?>
                                        <select
                                                name="rateLockExtension"
                                                id="rateLockExtension"
                                                class="form-control input-sm"
                                                onchange="showOrHideDiv('rateLockExtension', 'rateLockNotesDiv')">
                                            <option value=""> Select</option>
                                            <option value="Yes" <?php echo Arrays::isSelected('Yes', LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockExtension); ?>>
                                                Yes
                                            </option>
                                            <option value="No" <?php echo Arrays::isSelected('No', LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockExtension); ?>>
                                                No
                                            </option>
                                        </select>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <div id="rateLockNotesDiv" class="row3SeparatorCls rateLockFields  col-md-3
                            <?php echo loanForm::showField('rateLockNotes'); ?>
                            <?php echo BaseHTML::parentFieldAccess(['fNm' => 'rateLockExtension', 'sArr' => $LSTSecArr, 'pv' => LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockExtension, 'av' => 'Yes']); ?> ">
                            <div class="row form-group">
                                <?php
                                echo loanForm::label(
                                    'rateLockNotes',
                                    'col-md-12 ',
                                    '',
                                    loanForm::changeLog(
                                        LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                        'rateLockNotes',
                                        \models\lendingwise\tblFileHMLONewLoanInfo::class,
                                        'Rate Lock Notes'
                                    ),
                                );
                                ?>
                                <div class="col-md-12">
                                    <?php if (LMRequest::$allowToEdit) { ?>
                                        <textarea
                                                name="rateLockNotes"
                                                id="rateLockNotes"
                                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'rateLockNotes', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'rateLockNotes', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?>
                                        ><?php echo LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockNotes; ?></textarea>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                    <!--// Rate Lock fields added on Mar 15, 2024 sc-48955 //-->
                </div>
            </div><!-- Row 3 End -->
            <div class="col-md-12 row3SeparatorCls separator separator-dashed my-2 separator12"
                 id="row3SeparatorDiv"></div>

            <div class="col-md-12"><!-- Row 4 Start -->
                <div class="row">
                    <div class="row4SeparatorCls col-md-3 lien1Rate_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Rate', 'sArr' => $LSTSecArr, 'opt' => 'D']); ?>">
                        <div class="row form-group">
                            <label class="col-md-12 font-weight-bold"
                                   for="lien1Rate"><?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Rate', 'sArr' => $LSTSecArr, 'opt' => 'L']); ?>
                                <?php echo loanForm::changeLog(
                                    LMRequest::$LMRId,
                                    'lien1Rate',
                                    \models\lendingwise\tblFile::class,
                                    'Interest Rate'
                                ); ?>
                            </label>
                            <div class="col-md-12">
                                <?php
                                if (LMRequest::$allowToEdit) {
                                    ?>
                                    <div class="input-group">
                                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Rate', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                               type="text"
                                               placeholder="0.0"
                                               name="lien1Rate"
                                               id="lien1Rate"
                                            <?php if (isset($lien1Rate)) { ?>
                                                value="<?php echo number_format(Strings::replaceCommaValues(Strings::showField('lien1Rate', 'LMRInfo')), 3) ?>"
                                            <?php } else { ?>
                                                value="<?php echo number_format(Strings::replaceCommaValues(Strings::showField('lien1Rate', 'LMRInfo')), 3) ?>"
                                            <?php } ?>
                                               autocomplete="off"
                                               tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                               onchange="populateCostOfCaptial('loanModForm', this.value); updateLoanDetail(); validateMinMaxLoanGuidelines();"
                                            <?php echo LoanInfo::$editIR; ?>
                                            <?php echo glCustomJobForProcessingCompany::readOnlyFieldLoanInfo(LMRequest::$PCID, LMRequest::$activeTab); ?>
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Rate', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?>
                                            <?php if (Strings::showField('lien1Rate', 'LMRInfo') > 0 && $fldEditOpt == 2) echo ' readonly '; ?> >
                                        <div class="input-group-append">
                                            <span class="input-group-text">%</span>
                                        </div>
                                    </div>
                                    <div class="note InRateRange"></div>
                                <?php } else {
                                    echo '<b>' . Strings::showField('lien1Rate', 'LMRInfo') . '&nbsp;%</b>';
                                } ?>
                            </div>
                        </div>
                    </div>

                    <?php if (!PageVariables::$publicUser && PageVariables::$userRole != 'Client') { ?>

                        <div class="row4SeparatorCls col-md-3 costOfCapital_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'costOfCapital', 'sArr' => $LSTSecArr, 'opt' => 'D']); ?>">
                            <div class="row form-group">
                                <?php
                                echo loanForm::label('costOfCapital', 'col-md-12 ');
                                ?>
                                <div class="col-md-12">
                                    <?php
                                    if (LMRequest::$allowToEdit) { ?>
                                        <div class="input-group">

                                            <input
                                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'costOfCapital', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                                    type="text"
                                                    placeholder="0.0"
                                                    name="costOfCapital"
                                                    id="costOfCapital"
                                                    value="<?php echo number_format(Strings::replaceCommaValues(Strings::showField('costOfCapital', 'fileHMLONewLoanInfo')), 3) ?>"
                                                    autocomplete="off"
                                                    tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                    onchange="populateYieldSpread('loanModForm', this.value);calculateHMLOInterestRate('loanModForm', 'lien1Rate');validateMinMaxLoanGuidelines();" <?php echo LoanInfo::$editIR; ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'costOfCapital', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?> <?php if (Strings::showField('lien1Rate', 'LMRInfo') > 0 && $fldEditOpt == 2) echo ' readonly '; ?> >
                                            <div class="input-group-append">
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                    <?php } else {
                                        echo '<b>' . Strings::showField('costOfCapital', 'fileHMLONewLoanInfo') . '&nbsp;%</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <div class=" row4SeparatorCls col-md-3 yieldSpread_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'yieldSpread', 'sArr' => $LSTSecArr, 'opt' => 'D']); ?>">
                            <div class="row form-group">
                                <?php
                                echo loanForm::label('yieldSpread', 'col-md-12 ');
                                ?>
                                <div class="col-md-12">
                                    <?php
                                    if (LMRequest::$allowToEdit) { ?>
                                        <div class="input-group">

                                            <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'yieldSpread', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                                   type="text"
                                                   name="yieldSpread"
                                                   id="yieldSpread"
                                                   readonly="readonly"
                                                   placeholder="0.0"
                                                   value="<?php echo number_format(Strings::replaceCommaValues(Strings::showField('yieldSpread', 'fileHMLONewLoanInfo')), 3) ?>"
                                                   autocomplete="off" tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                   onchange="calculateHMLOInterestRate('loanModForm', 'lien1Rate');" <?php echo LoanInfo::$editIR; ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'yieldSpread', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?> <?php if (Strings::showField('lien1Rate', 'LMRInfo') > 0 && $fldEditOpt == 2) echo ' readonly '; ?> >
                                            <div class="input-group-append">
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                    <?php } else {
                                        echo '<b>' . Strings::showField('yieldSpread', 'fileHMLONewLoanInfo') . '&nbsp;%</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <div class="row4SeparatorCls col-md-3 spread_disp <?php echo loanForm::showField('spread'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('spread', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php
                                    if (LMRequest::$allowToEdit) {
                                        ?>
                                        <div class="input-group">
                                            <input
                                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'spread', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                                    type="number"
                                                    name="spread"
                                                    id="spread"
                                                    value="<?php echo Currency::formatDollarAmountWithDecimalZerosLimit(Strings::showField('spread', 'fileHMLOPropertyInfo'), 8); ?>"
                                                    autocomplete="off"
                                                    tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'spread', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?> >
                                            <div class="input-group-append">
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                    <?php } else {
                                        echo '<b>' . Strings::showField('spread', 'fileHMLOPropertyInfo') . '&nbsp;%</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div><!-- Row 4 End -->
            <div class="col-md-12 row4SeparatorCls separator separator-dashed my-2 separator12"
                 id="row4SeparatorDiv"></div>

            <div class="col-md-12"><!-- Row 5 Start -->
                <div class="row">
                    <div class="row5SeparatorCls col-md-3 isTaxesInsEscrowed_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'isTaxesInsEscrowed', 'sArr' => $LSTSecArr, 'opt' => 'D']); ?>">
                        <div class="row form-group">
                            <?php
                            echo loanForm::label('isTaxesInsEscrowed', 'col-md-12 ');
                            ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="radio-inline">
                                        <label class="radio radio-solid " for="isTaxesInsEscrowedYes">
                                            <input type="radio"
                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isTaxesInsEscrowed', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                                   name="isTaxesInsEscrowed" id="isTaxesInsEscrowedYes" value="Yes"
                                                   tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                <?php echo Strings::isChecked('Yes', Strings::showField('isTaxesInsEscrowed', 'fileHMLONewLoanInfo')); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'isTaxesInsEscrowed', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?>><span></span>Yes
                                        </label>
                                        <label class="radio radio-solid " for="isTaxesInsEscrowedNo">
                                            <input type="radio" name="isTaxesInsEscrowed" id="isTaxesInsEscrowedNo"
                                                   value="No"
                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isTaxesInsEscrowed', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                                   tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                <?php echo Strings::isChecked('No', Strings::showField('isTaxesInsEscrowed', 'fileHMLONewLoanInfo')); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'isTaxesInsEscrowed', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?>><span></span>No
                                        </label></div>
                                <?php } else { ?>
                                    <b><?php echo Strings::showField('isTaxesInsEscrowed', 'fileHMLONewLoanInfo') ?></b>
                                <?php } ?>
                            </div>
                        </div>
                    </div>

                    <div class="row5SeparatorCls col-md-3 isTaxesInsEscrowedDispOpt taxes1_disp
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'taxes1', 'sArr' => $LSTSecArr, 'opt' => 'D']); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('taxes1', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php
                                if (LMRequest::$allowToEdit) {
                                    ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'taxes1', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                               name="taxes1" id="taxes1"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('taxes1', 'incomeInfo')) ?>"
                                               autocomplete="off"
                                               onblur="currencyConverter(this, this.value);calculateHMLORealEstateTaxes(this.value); updateLoanDetail();"
                                               placeholder="0.00"
                                               tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php if (LoanInfo::$typeOfHMLOLoanRequesting == 'Blanket Loan') {
                                            echo 'readonly';
                                        } ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'taxes1', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?> />
                                    </div>
                                <?php } else {
                                    echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(Strings::showField('taxes1', 'incomeInfo')) . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                    <div class="row5SeparatorCls col-md-3 isTaxesInsEscrowedDispOpt insurance1_disp
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'insurance1', 'sArr' => $LSTSecArr, 'opt' => 'D']); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('insurance1', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'insurance1', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?> annualPremiumClass"
                                               name="annualPremium" id="annualPremium"
                                               placeholder="0.00"
                                               onblur="currencyConverter(this, this.value);updateLoanDetail();mirrorField.mirrorFieldValues('annualPremium','spcf_annualPremium');"
                                               placeholder="0.00"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('annualPremium', 'fileHMLOPropertyInfo')) ?>"
                                               autocomplete="off"
                                               tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'insurance1', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else {
                                    echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(Strings::showField('annualPremium', 'fileHMLOPropertyInfo')) . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!-- Row 5 End -->

            <div class="col-md-12 row5SeparatorCls separator separator-dashed my-2 separator12"
                 id="row5SeparatorDiv"></div>

            <div class="col-md-12"><!-- Row 6 Start -->
                <div class="row">
                    <div class="row6SeparatorCls col-md-3 isLoanPaymentAmt_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'isLoanPaymentAmt', 'sArr' => $LSTSecArr, 'opt' => 'D']); ?>">
                        <div class="row form-group">
                            <label class="col-md-12 font-weight-bold"
                                   for="isLoanPaymentAmtILA"><?php echo BaseHTML::fieldAccess(['fNm' => 'isLoanPaymentAmt', 'sArr' => $LSTSecArr, 'opt' => 'L']); ?></label>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="radio-list">
                                        <label id="isIntialLoanAmountDisp"
                                               class="radio radio-solid disableClick" for="isLoanPaymentAmtILA">
                                            <div class="radio enableClick">
                                                <input type="radio"
                                                       name="isLoanPaymentAmt"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isLoanPaymentAmt', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                                       id="isLoanPaymentAmtILA"
                                                       value="ILA"
                                                       tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                       onclick="updatePaymentTooltip('ILA');updateLoanDetail();"
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'isLoanPaymentAmt', 'sArr' => $LSTSecArr, 'opt' => 'i']); ?>
                                                    <?php echo Strings::isChecked('ILA', LoanInfo::$isLoanPaymentAmt); ?>>
                                                <span></span>
                                                <div id="isLoTxt"><?php echo HMLOLoanTermsCalculation::$isLoTxt; ?></div>
                                            </div>
                                        </label>
                                        <label class="radio radio-solid disableClick" for="isLoanPaymentAmtTLA">
                                            <div class="radio enableClick">
                                                <input type="radio"
                                                       name="isLoanPaymentAmt"
                                                       id="isLoanPaymentAmtTLA"
                                                       value="TLA"
                                                       tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                       onclick="updatePaymentTooltip('TLA');updateLoanDetail(); " <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Payment', 'sArr' => $LSTSecArr, 'opt' => 'i']); ?> <?php echo Strings::isChecked('TLA', LoanInfo::$isLoanPaymentAmt); ?>>
                                                <span></span>
                                                <div>Total Loan Amount</div>
                                            </div>
                                        </label>
                                        <?php
                                        if (!in_array(LMRequest::$PCID, glCustomJobForProcessingCompany::$glCustomHidePaymentBasedField)) { ?>
                                            <label class="radio radio-solid disableClick" for="isLoanPaymentAmtSMP">
                                                <div class="radio enableClick">
                                                    <input type="radio"
                                                           name="isLoanPaymentAmt"
                                                           id="isLoanPaymentAmtSMP"
                                                           value="SMP"
                                                           tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                           onclick="updatePaymentTooltip('SMP');updateLoanDetail(); "
                                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Payment', 'sArr' => $LSTSecArr, 'opt' => 'i']); ?> <?php echo Strings::isChecked('SMP', LoanInfo::$isLoanPaymentAmt); ?>>
                                                    <span></span>
                                                    <div>Set Manual Payment</div>
                                                </div>
                                            </label>
                                        <?php } ?>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo LoanInfo::$isLoanPaymentAmt ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>

                    <div class="row6SeparatorCls col-md-3 accrualType_disp <?php echo loanForm::showField('accrualType'); ?>">
                        <?php echo loanForm::label(
                            'accrualType',
                            'col-md-12 ',
                            '',
                            loanForm::changeLog(
                                LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->HMLOPID,
                                'accrualType',
                                tblFileHMLOPropInfo::class,
                                'Accrual Type'
                            )
                        ); ?>
                        <div class="col-md-12">
                            <?php if (LMRequest::$allowToEdit) { ?>
                                <select name="accrualType"
                                        class="form-control input-sm accrualTypeClass <?php echo BaseHTML::fieldAccess(['fNm' => 'accrualType', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                        id="accrualType"
                                        onchange="updateLoanDetail();"
                                        tabindex="<?php echo LoanInfo::$tabIndex++; ?>">
                                    <?php foreach (accrualTypes::$accrualTypes as $k => $v) { ?>
                                        <option value="<?php echo $k; ?>" <?php if (LoanInfo::$accrualType == $k) echo ' selected '; ?> >
                                            <?php echo $v; ?>
                                        </option>
                                    <?php } ?>
                                </select>
                            <?php } else {
                                echo '<h7>' . (accrualTypes::$accrualTypes[LoanInfo::$accrualType] ?? '') . '</h7>';
                            } ?>
                        </div>
                    </div>

                    <div class="row6SeparatorCls col-md-3 lien1Payment_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Payment', 'sArr' => $LSTSecArr, 'opt' => 'D']); ?>">
                        <div class="row form-group">
                            <label class="col-md-12 font-weight-bold"
                                   for="lien1Payment"><?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Payment', 'sArr' => $LSTSecArr, 'opt' => 'L']); ?></label>
                            <div class="col-md-12">
                                <?php
                                if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) {
                                    ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text" <?php echo(LoanInfo::$isLoanPaymentAmt != 'SMP' ? ' readonly ' : ''); ?>
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Payment', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                               name="lien1Payment"
                                               id="lien1Payment"
                                               onblur="currencyConverter(this, this.value);updateLoanDetail();monthlyInterestRate(0, -this.value,0)"
                                               placeholder="0.00"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$totalMonthlyPayment) ?>"
                                               autocomplete="off"
                                               tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Payment', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?>/>
                                        <div class="input-group-append tooltipClass"
                                             id="totalMonthlyPaymentTooltip"
                                             data-html="true"
                                             data-formula="<?php echo HMLOLoanTermsCalculation::$totalMonthlyPaymentTooltip; ?>"
                                             title="<?php echo HMLOLoanTermsCalculation::$totalMonthlyPaymentTooltipWithValues ? (HMLOLoanTermsCalculation::$totalMonthlyPaymentTooltip . '<hr>' . HMLOLoanTermsCalculation::$totalMonthlyPaymentTooltipWithValues) : HMLOLoanTermsCalculation::$totalMonthlyPaymentTooltip; ?>">
                                                  <span class="input-group-text">
                                                      <i class="fa fa-info-circle text-primary "></i>
                                                  </span>
                                        </div>
                                    </div>
                                <?php } else {
                                    echo '<b>$ ' . HMLOLoanTermsCalculation::$totalMonthlyPayment . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>

                    <?php if (!PageVariables::$publicUser) { ?>
                        <div class="row6SeparatorCls col-md-3 isTaxesInsEscrowedDispOpt netMonthlyPayment_disp
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'netMonthlyPayment', 'sArr' => $LSTSecArr, 'opt' => 'D']); ?>">
                            <div class="row form-group">
                                <label class="col-md-12 font-weight-bold"
                                       for="netMonthlyPayment"><?php echo BaseHTML::fieldAccess(['fNm' => 'netMonthlyPayment', 'sArr' => $LSTSecArr, 'opt' => 'L']); ?>
                                    <i class="fa fa-info-circle tooltipClass text-primary ml-2"
                                       data-html="true"
                                       id="netMonthlyPaymentTooltip"
                                       data-formula="Monthly Payment (PITIA) = PITIA = Monthly Payment + (Annual Property Tax + Annual Insurance Policy Premium + HOA Fees) / 12."
                                       title="Monthly Payment (PITIA) = PITIA = Monthly Payment + (Annual Property Tax + Annual Insurance Policy Premium + HOA Fees) / 12. <hr> <?php echo HMLOLoanTermsCalculation::$netMonthlyPaymentTooltip ?>  "></i>
                                </label>
                                <div class="col-md-12 h4">
                                    $
                                    <span id="netMonthlyPayment"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$netMonthlyPayment); ?></span>
                                </div>
                            </div>
                        </div>
                    <?php } ?>

                </div>
            </div><!-- Row 65 End -->
            <div class="col-md-12 row6SeparatorCls separator separator-dashed my-2 separator12"
                 id="row6SeparatorDiv"></div>

            <div class="col-md-12"><!-- Row 7 Start -->
                <div class="row">
                    <div class="row7SeparatorCls col-md-3 transactionalTdFields commercialFieldsTD transactionalTdFieldsNC costBasis_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'costBasis', 'sArr' => $LSTSecArr, 'opt' => 'D']); ?>"
                         style="<?php echo HMLOLoanTermsCalculation::$commercialFieldsTDNCDispOpt; ?><?php echo HMLOLoanTermsCalculation::$transCommercialFieldsDispOpt; ?>">
                        <div class="row form-group">
                            <label class="col-md-12 font-weight-bold"
                                   for="costBasis"><?php echo BaseHTML::fieldAccess(['fNm' => 'costBasis', 'sArr' => $LSTSecArr, 'opt' => 'L']); ?></label>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'costBasis', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                               name="costBasis"
                                               id="costBasis"
                                               onblur="currencyConverter(this, this.value);calculateDownPaymentByPercentage();validateMinMaxLoanGuidelines(); calculateCapRate();"
                                               onkeyup='return restrictAlphabetsLoanTerms(this)'
                                               placeholder="0.00"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$costBasis); ?>"
                                               tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'costBasis', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else {
                                    echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$costBasis) . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                    <div class="row7SeparatorCls col-md-3 homeValue_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'homeValue', 'sArr' => $LSTSecArr, 'opt' => 'D']); ?>">
                        <div class="row form-group">
                            <label class="col-md-12 font-weight-bold"
                                   for="homeValue"><?php echo BaseHTML::fieldAccess(['fNm' => 'homeValue', 'sArr' => $LSTSecArr, 'opt' => 'L']); ?></label>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text" name="homeValue"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'homeValue', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                               id="homeValue"
                                               onchange="updateLoanDetail(); calculateCORefiLoanAmtByLTVPercentage(); currencyConverter(this, this.value);calculateCapRate();"
                                               onkeyup='return restrictAlphabetsLoanTerms(this)'
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$homeValue) ?>"
                                               TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>"
                                            <?php echo glCustomJobForProcessingCompany::readOnlyFieldLoanInfo(LMRequest::$PCID, LMRequest::$activeTab); ?>
                                               placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'homeValue', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?>>
                                    </div>
                                <?php } else {
                                    echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$homeValue) . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                    <div class="row7SeparatorCls col-md-3 aggregateDSCR_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'aggregateDSCR', 'sArr' => $LSTSecArr, 'opt' => 'D']); ?>">
                        <div class="row form-group">
                            <label class="col-md-12 font-weight-bold"
                                   for="aggregateDSCR"><?php echo BaseHTML::fieldAccess(['fNm' => 'aggregateDSCR', 'sArr' => $LSTSecArr, 'opt' => 'L']); ?></label>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <input type="text" name="aggregateDSCR"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'aggregateDSCR', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                               id="aggregateDSCR"
                                               onkeyup='return restrictAlphabetsLoanTermsDecimal(this,3)'
                                               value="<?php echo HMLOLoanTermsCalculation::$aggregateDSCR ?>"
                                               TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>"
                                               placeholder="0.000" <?php echo BaseHTML::fieldAccess(['fNm' => 'aggregateDSCR', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?>>
                                        <div class="input-group-append">
                                            <span class="input-group-text">%</span>
                                        </div>
                                    </div>
                                <?php } else {
                                    echo '<b>' . HMLOLoanTermsCalculation::$aggregateDSCR . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                    <div class="row7SeparatorCls col-md-3 transactionalFields resalePrice_disp <?php echo loanForm::showField('resalePrice'); ?>"
                         style="<?php echo HMLOLoanTermsCalculation::$transactionalFieldsDispOpt; ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('resalePrice', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'resalePrice', 'sArr' => $LSTSecArr, 'opt' => 'M']); ?>"
                                               name="resalePrice" id="resalePrice"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$resalePrice) ?>"
                                               TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>"
                                               onblur="currencyConverter(this, this.value); "
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'resalePrice', 'sArr' => $LSTSecArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else {
                                    echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(LoanInfo::$resalePrice) . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!-- Row 7 End -->
            <div class="col-md-12 row7SeparatorCls separator separator-dashed my-2 separator12"
                 id="row7SeparatorDiv"></div>
            <?php loanForm::popSectionID(); ?>
        </div>
    </div>
</div>
<script>

    $('select.jsexamplebasicmultiple').select2();
    let refinanceList = $.parseJSON('<?php echo(json_encode(typeOfHMLOLoanRequesting::REFINANCE_LIST)); ?>');
    $(document).ready(function () {
        validateMinMaxLoanGuidelines('No');
        var typeOfloanRequest = "<?php echo LoanInfo::$typeOfHMLOLoanRequesting; ?>";
        if (typeOfloanRequest === 'Purchase') {
            $('.cashOutDiv,.LOCTotalLoanAmt_disp,.totalCashOut_disp').css("display", "none");
            $('.commercialFieldsTD').css("display", "block");
            $('.commercialFieldsTDNew').css("display", ""); //needs to override the previous style fix for sc31816
        }

        $(document).on('blur', '#noOfPropertiesAcquiring', function () {
            var noOfPropertiesAcquiring = $(this).val();
            if ((!$.isNumeric(noOfPropertiesAcquiring) || noOfPropertiesAcquiring < 2) && noOfPropertiesAcquiring != '') { // the entered value should be a number and greater than 1
                alert('The value should be a number and greater than 1');
                $(this).val('');
                $(this).focus();
            } else if (noOfPropertiesAcquiring > 100) {
                toastrNotification('The value should not exceed 100', 'error');
                $(this).val('');
                $(this).focus();
            }
        });

        $('#rateIndex').chosen({'create_option': true, 'persistent_create_option': true, 'skip_no_results': true});

        $('.annualPremiumClass').bind('keypress keyup blur', function () {
            if ($('.annualPremiumClass').length > 1) {
                $('.annualPremiumClass').val($(this).val());
            }
        });


    });

    $(function () {
        for(let ind = 1; ind <= 7 ; ind++){
            if ($('.row'+ind+'SeparatorCls.secShow').length > 0) { //show titlessssss
                $("#row"+ind+"SeparatorDiv").show();
            } else { //hide title
                $("#row"+ind+"SeparatorDiv").hide();
            }
        }
    });

    function mirrornoOfPropertiesAcquiring() {
        $('#noOfPropertiesAcquiring_mirror').val($('#noOfPropertiesAcquiring').val());
        if ($('#noOfPropertiesAcquiring').val() > 100) {
            $('#noOfPropertiesAcquiring_mirror').val('');
            return false;
        }
        LMRId = "<?php echo LMRequest::$LMRId;?>";

        if ($('#webFormView').val() == 'wizard') {
            $('#btnSave').click();
            location.reload();
        } else if (LMRId > 0) {
            var formIdToSubmit = $('#loanModForm');
            ajaxUrl = $(formIdToSubmit).attr('action');
            formData = $(formIdToSubmit).serialize();
            formData = new FormData($('#loanModForm')[0]);

            var ajaxRequest = $.ajax({
                url: ajaxUrl,
                type: "POST",
                data: formData,
                processData: false,
                contentType: false,
                beforeSend: function () {
                    //  BlockDiv('employeeCreateDiv');
                },
                complete: function () {
                    //UnBlockDiv('employeeCreateDiv');
                },
                success: function (response, status, xhr) {
                    location.reload();
                },
                error: function (jqXhr, textStatus, errorMessage) {
                    toastrNotification(errorMessage, 'error');
                }
            });
        } else {
            if ($('#noOfPropertiesAcquiring_mirror').val() > 1 &&
                ($('.proploc.secShow').length > 0 || $('.propchar.secShow').length > 0 || $('.propdetails.secShow').length > 0
                    || $('.propaccess.secShow').length > 0 || $('.rentRollSecFields.secShow').length > 0)) {
                if (!($('#noOfPropertiesAcquiring_mirror_disp').is(":visible"))) {
                    $('#addSubpropDiv').show();
                }
            } else {
                $('#addSubpropDiv').hide();
            }
        }
    }

    function restrictAlphabets(el, evt, afterDecimal = 3) {
        var beforeDecimal = 10;

        $('#' + el.id).on('input', function () {
            this.value = this.value
                .replace(/[^\d.]/g, '')
                .replace(new RegExp("(^[\\d]{" + beforeDecimal + "})[\\d]", "g"), '$1')
                .replace(/(\..*)\./g, '$1')
                .replace(new RegExp("(\\.[\\d]{" + afterDecimal + "}).", "g"), '$1');
        })
    }

    function showAndHideGroupUpFields(fieldValue) {
        if (fieldValue === 'Yes') {
            $('.groundUpFields').css("display", "block");
        } else {
            $('.groundUpFields').css("display", "none");
        }
        loanCalculation.calculateGrossProfit();
    }

    let FPCID = parseInt($('#FPCID').val());
    let PCID_CRB = parseInt(<?php echo glPCID::PCID_CRB; ?>);
</script>
<script src="<?php echo CONST_SITE_URL; ?>/backoffice/LMRequest/js/crbCustomLoanTermCalculation.js?<?php echo CONST_JS_VERSION; ?>"
        type="text/javascript"></script>
<script src="/backoffice/LMRequest/loanInfoV2/js/loanInfoV2Form.js?<?php echo CONST_JS_VERSION; ?>"></script>
<script src="<?php echo CONST_SITE_URL; ?>assets/js/cashFlow.js?<?php echo CONST_JS_VERSION; ?>"></script>



