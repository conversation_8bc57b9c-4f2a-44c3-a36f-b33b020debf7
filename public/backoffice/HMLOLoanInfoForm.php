<?php
global $URLPOSTING, $activeTab, $borrowerEmail, $docArray, $userTimeZone, $oldFPCID, $LMRId,
       $PCID, $propertyValuationDocInfo, $userGroup, $SID,
       $isHMLOSelOpt, $assetId, $paymentReserves, $lien1Payment, $totalInsurance, $cliType, $isHOALien, $QAID, $allowToSeeBillingSectionForFile,
       $borrowerActiveSectionDisp, $publicUser, $emailOpt, $allowToEdit, $webFormFOpt, $loanOfficerId,
       $executiveId, $HMLORealEstateTaxes, $oBroker, $fieldsInfo, $tabIndex, $REBroker, $REBrokerYesBtn,
       $agentNumber, $brokerFName, $brokerLName, $brokerEmail, $LmbInfo, $brokerCompany, $brokerPhone, $RELoanofficer, $RELoanofficerYesBtn,
       $loanofficerFName, $loanofficerLName, $loanofficerEmail, $loanofficerCompany, $LoanofficerPhone, $LMRClientTypeDisplay,
       $PCBasicLoanTabLMRIDsExists, $ft, $servicesRequested, $LMRClientTypeInfo, $fileLP, $loanInfoLPSectionDisp, $myFileInfo,
       $propDetailsProcess, $branchHAInfoArray, $leadSource, $hereAbout, $referringParty, $hideThisField, $userRole,
       $isBlanketLoanDiv, $isBlanketLoan, $isBlanketLoanDispOpt,
       $noOfPropertiesAcquiring, $HMLOLoanInfoSectionsDisp, $HMLOTAC, $HMLOTACQA, $wfOpt, $aud, $allowCaptcha,
       $isPLO, $loanPgmDetails, $UType;

use models\composite\oBroker\getBranchBrokerList;
use models\composite\oBroker\listAllAgentsLoanOfficer;
use models\composite\oChecklist\getChecklistNameID;
use models\composite\oFile\getFileInfo\PCInfo;
use models\composite\oLockFile\isFileLockedLastVal;
use models\composite\oPC\getPCInternalServiceType;
use models\composite\oUserAccess\getAutomationControlAccess;
use models\constants\gl\glAgentLabelChanges;
use models\constants\gl\glAllowHMLOPCToEditBrokerINLV;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glFirstRehabLending;
use models\constants\gl\globalSBALoanProductsCat;
use models\constants\gl\glPropDetailsProcess;
use models\constants\gl\glUserGroup;
use models\constants\gl\glUserRole;
use models\constants\GpropertyTypeNumbArray;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\backoffice\LoanInfo;
use models\Controllers\Base\generateWebformLinks;
use models\Controllers\LMRequest\mortgageNotes;
use models\Controllers\LMRequest\WebForm;
use models\Controllers\loanForm;
use models\Controllers\backoffice\LoanStagesController;
use models\CustomField;
use models\cypher;
use models\lendingwise\tblFile;
use models\PageVariables;
use models\Request;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Dates;
use models\standard\Strings;

$globalSBALoanProductsCat = globalSBALoanProductsCat::$globalSBALoanProductsCat;
$glPropDetailsProcess =  glPropDetailsProcess::getPropDetailsProcess($PCID);
$glFirstRehabLending = glFirstRehabLending::$glFirstRehabLending;
$glAgentLabelChanges = glAgentLabelChanges::$glAgentLabelChanges;
$nameOfSignaturedPerson = '';
$coBroSignaturedPersonName = '';
if ($URLPOSTING == 1) {
    require 'webformInitVars.php';
} else {
    require 'HMLOLoanInfoVars.php';
}
global $fileTab;
if ($activeTab == 'QAPP') $fileTab = 'QA';
if ($activeTab == 'LI') $fileTab = 'FA';

$borrowerEmailLink = '';

$recordDate = Strings::showField('recordDate', 'LMRInfo');

if (isset($_REQUEST['borrowerEmail'])) $borrowerEmailLink = htmlspecialchars(Request::GetClean('borrowerEmail'));

if ($borrowerEmailLink != '') {
    $borrowerEmail = $borrowerEmailLink;
} else {
    if (!$borrowerEmail) {
        $borrowerEmail = Strings::showField('borrowerEmail', 'LMRInfo');
    }

}
$coBorrowerLink = '';
$BorrowerLink = '';
if ($publicUser) {
    generateWebformLinks::init($LMRId);
    if ($wfOpt == 'QA') {
        $coBorrowerLink = generateWebformLinks::$quickAppLinkCoBorrower;
        $BorrowerLink = generateWebformLinks::$quickAppLinkBorrower;
    } else if ($wfOpt == 'FA') {
        $coBorrowerLink = generateWebformLinks::$fullAppLinkCoBorrower;
        $BorrowerLink = generateWebformLinks::$fullAppLinkBorrower;
    }
}

/**
 * Desc : Proof of sale (HUD)
 * Date : 09 Mar, 2017
 */

$docCnt = 1;
$proofOfSale1 = '';
$proofOfSale2 = '';
$proofOfSale3 = '';
$proofOfSale4 = '';
$proofOfSale5 = '';
$proofOfSale6 = '';
$proofOfSale7 = '';
$proofOfSale8 = '';
$proofOfSale9 = '';
$proofOfSale10 = '';
$proofOfSale11 = '';
$proofOfSale12 = '';
$proofOfSale13 = '';
$proofOfSale14 = '';
$proofOfSale15 = '';
$hudDocN7 = '';
$hudDocN8 = '';
$hudDocN9 = '';
$hudDocN10 = '';
$hudDocN11 = '';
$hudDocN12 = '';
$hudDocN13 = '';
$hudDocN14 = '';
$hudDocN15 = '';
$fileUpdLimit = 5;
for ($doc = 0; $doc < count($docArray ?? []); $doc++) {
    $tempDocArray = [];
    $docCategoryArray = [];
    $flatNotes = '';
    $uploadDocUrl = '';
    $docName = '';
    $displayDocName = '';
    $docId = 0;
    $myUploadedBy = '';
    $myUploadedRole = '';
    $docCategory = '';

    $docName = trim($docArray[$doc]['docName']);
    $displayDocName = trim($docArray[$doc]['displayDocName']);
    $uploadedDate = trim($docArray[$doc]['uploadedDate']);
    $userId = trim($docArray[$doc]['uploadedBy']);
    $userType = trim($docArray[$doc]['uploadingUserType']);
    $docCategory = trim($docArray[$doc]['docCategory']);
    $docId = trim($docArray[$doc]['docID']);
    $fileType = trim($docArray[$doc]['fileType']);

    $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
    $ipArray['outputZone'] = $userTimeZone;
    $ipArray['inputTime'] = $uploadedDate;
    $uploadedDate = Dates::timeZoneConversion($ipArray);

    $docCategoryArray = explode('-', $docCategory);

    if (count($docCategoryArray ?? []) > 0) {
        if ($docCategoryArray[0] == 'Proof of sale (HUD)') {

            if (Dates::IsEmpty($uploadedDate)) {
                $uploadedDate = '';
            } else {
                $uploadedDate = Dates::formatDateWithRE($uploadedDate, 'YMD_HMS', 'm/d/Y h:i A');
            }

            if ($displayDocName == '' || $displayDocName == NULL) {
                $displayDocName = $docName;
            }

            $tempRecDate = str_replace('-', '', $recordDate);
            $folderName = $oldFPCID . '/' . date('Y', strtotime($tempRecDate)) . '/' . date('m', strtotime($tempRecDate)) . '/' . date('d', strtotime($tempRecDate));
            $fileValue = $LMRId;

            $fP = $folderName . '/' . $fileValue . '/upload';
            $uploadDocUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($docName) . '&fd=' . cypher::myEncryption(CONST_PATH_LMR_FILE_DOCS . $fP) . '&opt=enc';
            if (isset($docCategoryArray[1])) ${'proofOfSale' . $docCategoryArray[1]} = $uploadDocUrl;
            if (isset($docCategoryArray[1])) ${'hudDocN' . $docCategoryArray[1]} = $displayDocName;
        }
    }
}
if (in_array($PCID, $glFirstRehabLending)) {
    unset($glPropDetailsProcess[2]);
    $glPropDetailsProcess = array_values($glPropDetailsProcess);
}

$titlereportDoc = '';
$pc = 1;

if (count($propertyValuationDocInfo ?? []) > 0) {
    $keysArray = [];
    $keysArray = array_keys($propertyValuationDocInfo);
    for ($i = 0; $i < count($keysArray ?? []); $i++) {
        $docCategory = '';
        $uploadDocUrl = '';
        $docName = '';
        $displayDocName = '';
        $fileType = '';
        $docID = '';
        $uploadedBy = '';
        $docCategory = trim($keysArray[$i]);
        $docName = trim($propertyValuationDocInfo[$docCategory]['docName']);
        $displayDocName = trim($propertyValuationDocInfo[$docCategory]['displayDocName']);
        $fileType = trim($propertyValuationDocInfo[$docCategory]['fileType']);
        $docID = trim($propertyValuationDocInfo[$docCategory]['docID']);
        $uploadedBy = trim($propertyValuationDocInfo[$docCategory]['uploadedBy']);

        $tempRecDate = str_replace('-', '', $recordDate);
        $folderName = $oldFPCID . '/' . date('Y', strtotime($tempRecDate)) . '/' . date('m', strtotime($tempRecDate)) . '/' . date('d', strtotime($tempRecDate));
        $fileValue = $LMRId;

        $fP = $folderName . '/' . $fileValue . '/property';
        ${strtolower(str_replace(' ', '', $docCategory)) . 'Doc'} = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($docName) . '&fd=' . cypher::myEncryption(CONST_PATH_LMR_FILE_DOCS . $fP) . '&opt=enc';
        ${strtolower(str_replace(' ', '', $docCategory)) . 'DocName'} = substr($displayDocName, 0, 18) . '.' . $fileType;
    }
    $proInsCnt = $pc;
}
if ($userGroup == 'Client') {
    $disableFld = '';
}
?>

<input type="hidden" name="SID" value="<?php echo $SID; ?>">
<input type="hidden" name="UGroup" value="<?php echo $userGroup; ?>">
<input type="hidden" name="isHMLOOpt" id="isHMLOOpt" value="<?php echo $isHMLOSelOpt; ?>">
<input type="hidden" name="assetId" value="<?php echo $assetId; ?>">
<input type="hidden" name="paymentReserves" value="<?php echo $paymentReserves; ?>">
<input type="hidden" name="HMLORealEstateTaxes" id="HMLORealEstateTaxes" value="<?php echo $HMLORealEstateTaxes; ?>">
<input type="hidden" name="totalInsurance" value="<?php echo $totalInsurance; ?>">
<input type="hidden" name="mortgage1MonthlyPayment" value="<?php echo $lien1Payment; ?>">
<input type="hidden" name="cliType" id="cliType" value="<?php echo $cliType; ?>">
<input type="hidden" name="recordDate" id="recordDate"
       value="<?php echo Strings::showField('recordDate', 'LMRInfo') ?>"/>
<input type="hidden" name="isHOALien" id="isHOALien" value="<?php echo $isHOALien ?>"/>
<input type="hidden" name="fileUpdLimit" id="fileUpdLimit" value="<?php echo $fileUpdLimit ?>"/>
<input type="hidden" name="QAID" id="QAID" value="<?php echo $QAID; ?>"/>
<input type="hidden" name="RUID" value="<?php echo Strings::showField('RUID', 'billingUrlInfo') ?>">
<input type="hidden" name="allowToSeeBillingSectionForFile" id="allowToSeeBillingSectionForFile"
       value="<?php echo $allowToSeeBillingSectionForFile ?>">
<input type="hidden" name="inPro" id="inPro" value="<?php if ($borrowerActiveSectionDisp == '') {
    echo 'hide';
} ?>">
<input type="hidden" name="AE" value="<?php echo cypher::myEncryption($allowToEdit) ?>">
<input type="hidden" name="RD" value="<?php echo cypher::myEncryption($recordDate) ?>">


<?php
$lastUpdatedParam = '';
$fileRow = '';
$triggerRule = 'No';
if ($LMRId == 0) { //automation for new file
    $isFssUpdated = 'Yes';
    $fileRow = 'Insert';
    $triggerRule = 'Yes';
} else {
    $isFssUpdated = 'No';
    $fileRow = 'Update';
}
if ($publicUser) {
    $lastUpdatedParam = 'PFS';
    $triggerRule = 'Yes';
}
$userAutomationControlAccess = getAutomationControlAccess::getReport(PageVariables::$userNumber);
if ($userGroup != 'Employee') {
    $userAutomationControlAccess = 0;
}
?>
<!-- automation -->
<input type="hidden" name="isFssUpdated" id="isFssUpdated" value="<?php echo $isFssUpdated; ?>">
<input type="hidden" name="lastUpdatedParam" id="lastUpdatedParam" value="<?php echo $lastUpdatedParam; ?>">
<input type="hidden" name="triggerRule" id="triggerRule" value="<?php echo $triggerRule; ?>">
<input type="hidden" name="userAutomationControlAccess" id="userAutomationControlAccess"
       value="<?php echo $userAutomationControlAccess; ?>">
<input type="hidden" name="fileRow" id="fileRow" value="<?php echo $fileRow; ?>">
<?php if ($publicUser) { ?>
    <input type="hidden" name="previousModStatusId"
           value="<?php echo Strings::showField('primeStatusId', 'ResponseInfo') ?>">
<?php } ?>
<!-- // automation // -->
<?php
if ($publicUser != 1) { // Not allow this section for public users
    require 'fileAdminInfo.php';
} else { ?>
    <input type="hidden" name="selectedPC" id="selectedPC" value="<?php echo $PCID; ?>">

    <?php
    if ($PCID == 4326 && $LMRId == 0) { // **exception** PCID = 4326 (BD Capital)
        $internalLoanProgramList = [];
        $ip = [
            'PCID'       => $PCID,
            'keyNeeded'  => 'n',
            'moduleCode' => 'HMLO',
        ];
        $internalLoanProgramList = getPCInternalServiceType::getReport($ip);
        $serviceCnt = 0;
        if (count($internalLoanProgramList ?? []) > 0) $serviceCnt = count($internalLoanProgramList ?? []);
        for ($a = 0; $a < $serviceCnt; $a++) {
            $LMRClientTypeCode = '';
            $LMRClientType = '';
            $LMRClientTypeCode = trim($internalLoanProgramList[$a]['LMRClientType']);
            $LMRClientType = trim($internalLoanProgramList[$a]['serviceType']);
            if ($internalLoanProgramList[$a]['internalLoanProgram'] == '1' && $LMRClientType == 'To Be Determined') { ?>
                <input type="hidden" name="LMRInternalLoanProgram[]" class="" value="<?php echo $LMRClientTypeCode; ?>">
                <?php
            }
        }
    }
}

/**
 *
 * Customization for PC = Lendterra -->  Allow to edit the broker info in the webform on Mar 16, 2018.
 *
 * Added the Testing Purpose PC Dave = 820, Awata = 2, Lendterra = 3126 PC's
 * Ticket ID : 156022288
 **/
$allowUserToEditBroker = 0;
if (glAllowHMLOPCToEditBrokerINLV::is($PCID) && $emailOpt == 'Email' && $LMRId > 0 && $allowToEdit) {
    $allowUserToEditBroker = 1;
}

if ($publicUser == 1 && ($webFormFOpt != 'agent' || $loanOfficerId > 0)) { // Agent Section Only shows Branch Web Form Only.
    $preferredLoanOfficerBrokerInfoArray = $preferredBrokerInfoArray = $preferredLoanofficerInfoArray = [];
    $agentSectionDiv = 'display: block;';

    if ($loanOfficerId > 0) {
        //echo $loanOfficerId;
        $brokerList = [];
        $ip['externalBroker'] = 0;
        $ip['loanOfficerId'] = $loanOfficerId;
        $ip['userGroup'] = 'Agent';
        $ip['_publicUser'] = $publicUser;
        $ip['PCID'] = $PCID;
        if ($loanOfficerId > 0) {
            $brokerList = listAllAgentsLoanOfficer::getReport($ip);
        }
        foreach ($brokerList as $brokeachKey => $eachBrokerList) {
            $brokerListNewArray[$brokeachKey] = $eachBrokerList;
            $brokerListNewArray[$brokeachKey]['brokerFName'] = $eachBrokerList['bName'];
            $brokerListNewArray[$brokeachKey]['brokerLName'] = $eachBrokerList['bLName'];
        }
        $preferredLoanOfficerBrokerInfoArray = $brokerListNewArray;
    } else {
        $inArray = ['executiveId' => $executiveId, 'fOpt' => $webFormFOpt, '_publicUser' => $publicUser];
        $preferredLoanOfficerBrokerInfoArray = getBranchBrokerList::getReport($inArray);
    }

    foreach ($preferredLoanOfficerBrokerInfoArray as $eachLBuser) {
        if ($eachLBuser['externalBroker'] == '0') {
            $preferredBrokerInfoArray[] = $eachLBuser;
        }
        if ($eachLBuser['externalBroker'] == '1') {
            $preferredLoanofficerInfoArray[] = $eachLBuser;
        }
    }

    /*
     * “Agent/Broker Info” as a label “Loan Officer/Mortgage Broker Info” - Dayton Capital Partners, LLC
     * Ticket ID : 156333030
     * Mar 28, 2018
     * Make it as global on Mar 30, 2018
    */
    $agentLabel = 'Agent/Broker';
    if (in_array($PCID, $glAgentLabelChanges)) {
        $agentLabel = 'Loan Officer/Broker';
    }
//$PCquickAppFieldsInfo = array();
    if (($emailOpt != 'Email' || $allowUserToEditBroker == 1)) {
        $secArr = BaseHTML::sectionAccess2(['sId' => 'ABI', 'opt' => $fileTab]);
        loanForm::pushSectionID('ABI');

        ?>
        <!-- HMLOLoanInfoForm.php -->
        <div class="card card-custom ABI ABICard <?php if (count(Arrays::getValueFromArray('ABI', $fieldsInfo)) <= 0) {
            echo 'secHide';
        } ?>">
            <div class="card-header card-header-tabs-line bg-gray-100  ">
                <div class="card-title">
                    <h3 class="card-label">
                        <?php echo BaseHTML::getSectionHeading('ABI'); ?>
                    </h3>
                    <?php if (trim(BaseHTML::getSectionTooltip('ABI')) != '') { ?>&nbsp;
                        <i class="popoverClass fas fa-info-circle text-primary "
                           data-html="true"
                           data-content="<?php echo BaseHTML::getSectionTooltip('ABI'); ?>"></i>
                    <?php } ?>
                </div>
                <div class="card-toolbar ">
                    <a href="javascript:void(0);"
                       class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                       data-card-tool="toggle"
                       data-section="ABICard"
                       data-toggle="tooltip" data-placement="top" title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </a>
                    <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none" data-card-tool="reload"
                       data-toggle="tooltip" data-placement="top" title="Reload Card">
                        <i class="ki ki-reload icon-nm"></i>
                    </a>
                    <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
                       data-toggle="tooltip" data-placement="top" title="Remove Card">
                        <i class="ki ki-close icon-nm"></i>
                    </a>
                </div>
            </div>


            <div class="card-body ABICard_body">
                <!--  Broker -->
                <?php if (!$agentNumber) { ?>
                    <div id="REBrokerQuestion"
                         class="row form-group bg-gray-100 py-2 REBrokerLODisp REBroker_disp <?php echo loanForm::showField('REBroker'); ?><?php if ($PCID == 2853) {
                             //echo 'secHide';
                         } //2853 is express, 3363 stage lw demo 2, 3580 is lendingwise-dave in live ?>"
                    >
                        <?php echo loanForm::label('REBroker', 'col-md-6 '); ?>
                        <div class="col-md-6">
                            <?php
                            if ($LMRId == 0 || $allowUserToEditBroker == 1) { ?>
                                <input type="radio"
                                       class="radiobtn mandatory"
                                       name="REBroker"
                                       id="REBroker"
                                       value="Yes"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                    <?php echo Strings::isChecked('Yes', $REBroker) . ' ' . $REBrokerYesBtn; ?>
                                       onclick="showAndHideBrokerInfo(this.value, 'BrokerInfoDiv');"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'REBroker', 'sArr' => $secArr, 'opt' => 'I']); ?>>Yes
                                <input type="radio"
                                       name="REBroker"
                                       id="REBroker"
                                       value="No"
                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $REBroker); ?>
                                       onclick="showAndHideBrokerInfo(this.value, 'BrokerInfoDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'REBroker', 'sArr' => $secArr, 'opt' => 'I']); ?>>No

                                <?php
                            } else {
                                echo '<b>' . $REBroker . '</b>';
                            }
                            ?>
                        </div>
                    </div>
                    <div id="BrokerInfoDiv"
                         class="  brokerSection  <?php echo BaseHTML::parentFieldAccess(['fNm' => 'REBroker', 'sArr' => $secArr, 'pv' => $REBroker, 'av' => 'Yes']); ?>">
                        <!-- Broker Info Section Start -->
                        <div class="row">
                            <?php if (count($preferredBrokerInfoArray ?? []) > 0) { ?>
                                <div class=" col-md-6 LMRBroker_disp <?php echo loanForm::showField('LMRBroker'); ?>">
                                    <div class="row form-group">
                                        <?php echo loanForm::label('LMRBroker', 'col-md-5 '); ?>
                                        <div class="col-md-7">
                                            <?php
                                            if (($LMRId == 0 || $allowUserToEditBroker == 1)) {
                                                ?>
                                                <select class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'LMRBroker', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                        name="LMRBroker" id="LMRBroker"
                                                        tabindex="<?php echo $tabIndex++; ?>"
                                                        onchange="updateBrokerNo(this.value, 'loanModForm');populateBrokerInfo('loanModForm', this.value, 'DD');" <?php echo BaseHTML::fieldAccess(['fNm' => 'LMRBroker', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                    <option value=""> - Select / New -</option>
                                                    <?php
                                                    for ($pd = 0; $pd < count($preferredBrokerInfoArray ?? []); $pd++) {
                                                        $tempPrefArray = [];
                                                        $selLMRBrokerNo = 0;
                                                        $selBrokerFName = '';
                                                        $selBrokerLName = '';
                                                        $selLMRBrokerName = '';
                                                        $sOpt = '';
                                                        $tempPrefArray = $preferredBrokerInfoArray[$pd];
                                                        $selLMRBrokerNo = cypher::myEncryption($tempPrefArray['brokerNumber']);
                                                        $selBrokerFName = $tempPrefArray['brokerFName'];
                                                        $selBrokerLName = $tempPrefArray['brokerLName'];
                                                        $selLMRBrokerName = $selBrokerFName . ' ' . $selBrokerLName;
                                                        if ($selLMRBrokerNo == cypher::myEncryption($agentNumber)) $sOpt = 'selected';
                                                        ?>
                                                        <option value="<?php echo $selLMRBrokerNo; ?>" <?php echo $sOpt ?> ><?php echo $selLMRBrokerName ?></option>
                                                    <?php } ?>
                                                </select>
                                            <?php } else {
                                                echo '<b>' . $brokerFName . ' ' . $brokerLName . '</b>';
                                            } ?>
                                        </div>
                                    </div>
                                </div>
                            <?php }
                            ?>
                            <div class=" col-md-6 <?php echo loanForm::showField('REBrokerEmail'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('REBrokerEmail', 'col-md-5 '); ?>
                                    <div class="col-md-7">
                                        <?php
                                        if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                            ?>
                                            <input type="email"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerEmail', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="REBrokerEmail" id="REBrokerEmail"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo $brokerEmail; ?>" autocomplete="off"
                                                   onblur="checkREBrokerEmailExist('loanModForm');" <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerEmail', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                        <?php } else {
                                            echo '<b>' . $brokerEmail . '</b>';
                                        } ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div style="<?php echo $LmbInfo; ?>" class="LmbInfo row ">

                            <div class=" col-md-6 REBrokerFirstName_disp <?php echo loanForm::showField('REBrokerFirstName'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('REBrokerFirstName', 'col-md-5 '); ?>
                                    <div class="col-md-7">
                                        <?php
                                        if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                            ?>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerFirstName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="REBrokerFirstName" id="REBrokerFirstName"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo $brokerFName; ?>"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerFirstName', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <?php
                                        } else {
                                            echo '<b>' . $brokerFName . '</b>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 REBrokerLastName_disp <?php echo loanForm::showField('REBrokerLastName'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('REBrokerLastName', 'col-md-5 '); ?>
                                    <div class="col-md-7">
                                        <?php
                                        if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                            ?>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerLastName', 'sArr' => $secArr, 'opt' => 'M']); ?>>"
                                                   name="REBrokerLastName" id="REBrokerLastName"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo $brokerLName; ?>"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerLastName', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <?php
                                        } else {
                                            echo '<b>' . $brokerLName . '</b>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-6 REBrokerCompany_disp <?php echo loanForm::showField('REBrokerCompany'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('REBrokerCompany', 'col-md-5 '); ?>
                                    <div class="col-md-7">
                                        <?php
                                        if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                            ?>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerCompany', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="REBrokerCompany" id="REBrokerCompany"
                                                   tabindex="<?php echo $tabIndex++; ?>" class="form-control input-sm"
                                                   value="<?php echo $brokerCompany; ?>"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerCompany', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <?php } else {
                                            echo '<b>' . $brokerCompany . '</b>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 brokerPhone_disp <?php echo loanForm::showField('brokerPhone'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('brokerPhone', 'col-md-5 '); ?>
                                    <div class="col-md-7 brokerSection">
                                        <?php
                                        if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                            ?>
                                            <input type="text"
                                                   class="form-control input-sm mask_phone <?php echo BaseHTML::fieldAccess(['fNm' => 'brokerPhone', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="brokerPhone" id="brokerPhone"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo $brokerPhone; ?>" autocomplete="off"
                                                   placeholder="(___) ___ - ____ Ext ____" <?php echo BaseHTML::fieldAccess(['fNm' => 'brokerPhone', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <?php
                                        } else {
                                            echo '<b>' . Strings::formatPhoneNumber($brokerPhone) . '</b>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                <?php } ?>
                <!--  End of Broker-->


                <?php if ($loanOfficerId == '0') { ?>
                    <!--  Loan Officer -->
                    <div id="RELoanofficerQuestion"
                         class="row form-group  bg-gray-100 py-2  REBrokerLODisp RELoanofficer_disp <?php echo loanForm::showField('RELoanofficer'); ?><?php if ($PCID == 2853) {
                             echo 'secHide';
                         } //2853 is express, 3363 stage lw demo 2, 3580 is lendingwise-dave in live ?>"
                    >
                        <?php echo loanForm::label('RELoanofficer', 'col-md-6 '); ?>
                        <div class="col-md-6">
                            <?php if ($LMRId == 0 || $allowUserToEditBroker == 1) { ?>
                                <input type="radio"
                                       class="radiobtn <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficer', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       name="RELoanofficer"
                                       id="RELoanofficer"
                                       value="Yes"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                    <?php echo Strings::isChecked('Yes', $RELoanofficer) . ' ' . $RELoanofficerYesBtn; ?>
                                       onclick="showAndHideLoanofficerInfo(this.value, 'LoanofficerInfoDiv');"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficer', 'sArr' => $secArr, 'opt' => 'I']); ?>>Yes
                                <input type="radio"
                                       class="radiobtn <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficer', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       name="RELoanofficer"
                                       id="RELoanofficer"
                                       value="No"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                    <?php echo Strings::isChecked('No', $RELoanofficer); ?>
                                       onclick="showAndHideLoanofficerInfo(this.value, 'LoanofficerInfoDiv');"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficer', 'sArr' => $secArr, 'opt' => 'I']); ?>>No
                                <?php
                            } else {
                                if ($RELoanofficer == '') {
                                    $RELoanofficer = 'No';
                                }
                                echo '<b>' . $RELoanofficer . '</b>';
                            }
                            ?>
                        </div>
                    </div>

                    <div id="LoanofficerInfoDiv"
                         class="loanofficerSection  <?php echo BaseHTML::parentFieldAccess(['fNm' => 'RELoanofficer', 'sArr' => $secArr, 'pv' => $RELoanofficer, 'av' => 'Yes']); ?>">
                        <!-- Loan Officer Section Start -->

                        <div class="row">
                            <?php
                            if (count($preferredLoanofficerInfoArray ?? []) > 0) { ?>
                                <div class=" col-md-6 LMRLoanofficer_disp <?php echo loanForm::showField('LMRLoanofficer'); ?>">
                                    <div class="row form-group">
                                        <?php echo loanForm::label('LMRLoanofficer', 'col-md-5 '); ?>
                                        <div class="col-md-7">
                                            <?php if (($LMRId == 0 || $allowUserToEditBroker == 1)) { ?>
                                                <select class="form-control input-sm disableLoanofficerfields <?php echo BaseHTML::fieldAccess(['fNm' => 'LMRLoanofficer', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                        name="LMRLoanofficer"
                                                        id="LMRLoanofficer"
                                                        tabindex="<?php echo $tabIndex++; ?>"
                                                        onchange="updateLoanofficerNo(this.value, 'loanModForm');populateLoanofficerInfo('loanModForm', this.value, 'DD');" <?php echo BaseHTML::fieldAccess(['fNm' => 'LMRLoanofficer', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                    <option value=""> - Select / New -</option>
                                                    <?php
                                                    for ($pd = 0; $pd < count($preferredLoanofficerInfoArray ?? []); $pd++) {
                                                        $tempPrefArray = [];
                                                        $selLMRLoanofficerNo = 0;
                                                        $selBrokerFName = '';
                                                        $selBrokerLName = '';
                                                        $selLMRLoanofficerName = '';
                                                        $sOpt = '';
                                                        $tempPrefArray = $preferredLoanofficerInfoArray[$pd];
                                                        $selLMRLoanofficerNo = cypher::myEncryption($tempPrefArray['brokerNumber']);
                                                        $selBrokerFName = $tempPrefArray['brokerFName'];
                                                        $selBrokerLName = $tempPrefArray['brokerLName'];
                                                        $selLMRLoanofficerName = $selBrokerFName . ' ' . $selBrokerLName;
                                                        if ($selLMRLoanofficerNo == cypher::myEncryption($agentNumber)) $sOpt = 'selected';
                                                        ?>
                                                        <option value="<?php echo $selLMRLoanofficerNo; ?>" <?php echo $sOpt ?> ><?php echo $selLMRLoanofficerName ?></option>
                                                    <?php } ?>
                                                </select>
                                            <?php } else {
                                                echo '<b>' . $loanofficerFName . ' ' . $loanofficerLName . '</b>';
                                            } ?>
                                        </div>
                                    </div>
                                </div>
                            <?php }
                            ?>
                            <div class=" col-md-6 <?php echo loanForm::showField('RELoanofficerEmail'); ?>">
                                <div class="form-group row">
                                    <?php echo loanForm::label('RELoanofficerEmail', 'col-md-5 '); ?>
                                    <div class="col-md-7">
                                        <?php if ($LMRId == 0 || $allowUserToEditBroker == 1) { ?>
                                            <input type="email"
                                                   class="form-control input-sm disableLoanofficerfields <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerEmail', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="RELoanofficerEmail" id="RELoanofficerEmail"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo $loanofficerEmail; ?>" autocomplete="off"
                                                   onblur="checkLoanofficerEmailExist('loanModForm');" <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerEmail', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                        <?php } else {
                                            echo '<b>' . $loanofficerEmail . '</b>';
                                        } ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div style="<?php echo $LmbInfo; ?>" class="LmbInfo row ">
                            <div class="col-md-6 RELoanofficerFirstName_disp <?php echo loanForm::showField('RELoanofficerFirstName'); ?>">
                                <div class="form-group row">
                                    <?php echo loanForm::label('RELoanofficerFirstName', 'col-md-5 '); ?>
                                    <div class="col-md-7">
                                        <?php if ($LMRId == 0 || $allowUserToEditBroker == 1) { ?>
                                            <input type="text"
                                                   class="form-control disableLoanofficerfields input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerFirstName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="RELoanofficerFirstName" id="RELoanofficerFirstName"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo $loanofficerFName; ?>"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerFirstName', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <?php
                                        } else {
                                            echo '<b>' . $loanofficerFName . '</b>';
                                        } ?>
                                    </div>
                                </div>
                            </div>

                            <div class=" col-md-6 RELoanofficerLastName_disp <?php echo loanForm::showField('RELoanofficerLastName'); ?>">
                                <div class="form-group row">
                                    <?php echo loanForm::label('RELoanofficerLastName', 'col-md-5 '); ?>
                                    <div class="col-md-7">
                                        <?php if ($LMRId == 0 || $allowUserToEditBroker == 1) { ?>
                                            <input type="text"
                                                   class="form-control disableLoanofficerfields input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerLastName', 'sArr' => $secArr, 'opt' => 'M']); ?>>"
                                                   name="RELoanofficerLastName" id="RELoanofficerLastName"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo $loanofficerLName; ?>"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerLastName', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <?php
                                        } else {
                                            echo '<b>' . $brokerLName . '</b>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>

                            <div class=" col-md-6 RELoanofficerCompany_disp <?php echo loanForm::showField('RELoanofficerCompany'); ?>">
                                <div class="form-group row">
                                    <?php echo loanForm::label('RELoanofficerCompany', 'col-md-5 '); ?>
                                    <div class="col-md-7">
                                        <?php
                                        if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                            ?>
                                            <input type="text"
                                                   class="form-control disableLoanofficerfields input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerCompany', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="RELoanofficerCompany" id="RELoanofficerCompany"
                                                   tabindex="<?php echo $tabIndex++; ?>" class="form-control input-sm"
                                                   value="<?php echo $loanofficerCompany; ?>"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerCompany', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <?php } else {
                                            echo '<b>' . $loanofficerCompany . '</b>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 LoanofficerPhone_disp <?php echo loanForm::showField('LoanofficerPhone'); ?>">
                                <div class="form-group row">
                                    <?php echo loanForm::label('LoanofficerPhone', 'col-md-5 '); ?>
                                    <div class="col-md-7 loanofficerSection">
                                        <?php if ($LMRId == 0 || $allowUserToEditBroker == 1) { ?>
                                            <input type="text"
                                                   class="form-control disableLoanofficerfields input-sm mask_phone <?php echo BaseHTML::fieldAccess(['fNm' => 'LoanofficerPhone', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="LoanofficerPhone" id="LoanofficerPhone"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo $LoanofficerPhone; ?>" autocomplete="off"
                                                   placeholder="(___) ___ - ____ Ext ____"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'LoanofficerPhone', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <?php
                                        } else {
                                            echo '<b>' . Strings::formatPhoneNumber($LoanofficerPhone) . '</b>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php //} ?>
                    <!-- end of Loan Officer -->
                <?php } ?>

                <?php echo CustomField::RenderForTabSection(
                    PageVariables::$PCID,
                    tblFile::class,
                    LMRequest::$LMRId,
                    'ABI',
                    $fileTab,
                    $activeTab,
                    LMRequest::myFileInfo()->getFileTypes(),
                    LMRequest::myFileInfo()->getLoanPrograms()
                ); ?>
            </div>
        </div>
        <?php

    }
}  // Agent Section Only shows Branch Web Form Only.
?>
<?php
if ($publicUser == 1) {
    if (count($secArr = BaseHTML::sectionAccess2(['sId' => 'Admin', 'opt' => $fileTab])) > 0) {
        loanForm::pushSectionID('Admin');

        ?>
        <div style="<?php echo $LMRClientTypeDisplay; ?>" class="card card-custom lmrClientTypeDisp">
            <div class="card-body">
                <div class=" row">
                    <div class="col-md-6">
                        <div class="form-group row ">
                            <?php echo loanForm::label('LMRClientType', 'col-md-4 '); ?>
                            <div class="col-md-1 align-self-center">
                                <i id="loanprogramtooltip"
                                   data-html="true"
                                   class="fa fa-info-circle text-primary tooltipClass <?php if ($LMRId == 0) {
                                       echo 'hide';
                                   } ?>"
                                   title=""></i>
                            </div>
                            <div class="col-md-7">
                                <select class="form-control mandatory input-sm"
                                        data-placeholder=""
                                        name="LMRClientType[]"
                                        id="LMRClientType"
                                        tabindex="<?php echo $tabIndex++; ?>"
                                        onchange="formControl.controlFormFields('fileModule', '',this.id,'loanProgram');
                                                fixAdditionalLoanProgChosen(this.value);
                                                showAndHideLandFieldsNew(this.value);
                                                getPCMinMaxLoanGuidelines('loanModForm', '<?php echo $PCID ?>');
                                        <?php if (!in_array($LMRId, $PCBasicLoanTabLMRIDsExists)) { ?>
                                                populatePCBasicLoanInfo('loanModForm', this.value, '<?php echo $PCID ?>', '<?php echo $ft ?>');
                                        <?php } ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'LMRClientType', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                    <option value="">- Select -</option>
                                    <?php
                                    $serviceCnt = 0;
                                    if (count($servicesRequested ?? []) > 0) $serviceCnt = count($servicesRequested);
                                    for ($j = 0; $j < $serviceCnt; $j++) {
                                        $LMRClientTypeCode = '';
                                        $sOpt = '';
                                        $LMRClientType = '';
                                        $chk = '';
                                        $LMRClientTypeCode = trim($servicesRequested[$j]['LMRClientType']);
                                        $LMRClientType = trim($servicesRequested[$j]['serviceType']);
                                        $chk = Strings::isKeyChecked($LMRClientTypeInfo, 'ClientType', $LMRClientTypeCode);
                                        if (trim($chk) == 'checked') $chk = 'selected ';
                                        $displayOption = '';
                                        if ($LMRClientTypeCode == 'TBD' && $LMRClientTypeInfo[0]['ClientType'] != 'TBD') {
                                            $displayOption = "style = 'display:none;' ";
                                        }
                                        if ($servicesRequested[$j]['internalLoanProgram'] == 0) { ?>
                                            <option <?php echo $chk; ?> <?php echo $displayOption; ?>
                                                    value="<?php echo $LMRClientTypeCode; ?>"><?php echo $LMRClientType; ?></option>
                                        <?php }
                                    }
                                    if (in_array('TBD', $fileLP) && $LMRId > 0) { ?>
                                        <!--                                        <option selected-->
                                        <!--                                                value="--><?php //echo 'TBD'; ?><!--">--><?php //echo 'TBD'; ?><!--</option>-->
                                    <?php } ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!--    sbaLoanProduct_disp <?php /*echo BaseHTML::fieldAccess(array('fNm' => 'sbaLoanProduct', 'sArr' => $secArr, 'opt' => 'D')); */ ?>"

                -->
                <div class=" row sbaLoanProduct_disp <?php echo loanForm::showField('sbaLoanProduct'); ?>">
                    <div class="col-md-6">
                        <div class="form-group row ">
                            <?php echo loanForm::label('sbaLoanProduct', 'col-md-5 '); ?>
                            <div class="col-md-7">
                                <select data-placeholder="Select SBA Loan Product"
                                        name="sbaLoanProduct"
                                        id="sbaLoanProduct"
                                        tabindex="<?php echo $tabIndex++; ?>"
                                        class="chzn-select form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'sbaLoanProduct', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        data-placeholder="Select SBA Loan Product">
                                    <option></option>
                                    <?php
                                    if (count($HMLOPCBasicSBALoanProductInfoArray ?? []) > 0) {
                                        foreach ($HMLOPCBasicSBALoanProductInfoArray as $eachSBALoanProductID) { ?>
                                            <option value="<?php echo $eachSBALoanProductID; ?>" <?php if (Strings::showField('sbaLoanProduct', 'ResponseInfo') == $eachSBALoanProductID) {
                                                echo 'selected';
                                            } ?>><?php echo $globalSBALoanProductsCat[$eachSBALoanProductID]; ?></option>
                                        <?php }
                                    } else {
                                        foreach ($globalSBALoanProductsCat as $eachSBALoanProductID => $eachSBALoanProductVal) { ?>
                                            <option value="<?php echo $eachSBALoanProductID; ?>" <?php if (Strings::showField('sbaLoanProduct', 'ResponseInfo') == $eachSBALoanProductID) {
                                                echo 'selected';
                                            } ?>><?php echo $eachSBALoanProductVal; ?></option>
                                        <?php }
                                    } ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="loanInfoLPSection row <?php echo loanForm::showField('LMRClientType'); ?> "
                     style="<?php echo $loanInfoLPSectionDisp ?>">
                    <div class=" col-md-6 additionalLoanProgram_disp <?php echo loanForm::showField('additionalLoanProgram'); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('additionalLoanProgram', 'col-md-5 '); ?>
                            <div class="col-md-7">
                                <select class=" chzn-select <?php echo BaseHTML::fieldAccess(['fNm' => 'additionalLoanProgram', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        data-placeholder="Select Additional Loan Programs"
                                        id="LMRadditionalLoanProgram"
                                        onchange="formControl.controlFormFields('fileModule', '','LMRClientType','')"
                                        name="LMRadditionalLoanProgram[]"
                                        multiple="">
                                    <?php
                                    $serviceCnt = 0;
                                    if (count($servicesRequested ?? []) > 0) $serviceCnt = count($servicesRequested);
                                    for ($j = 0; $j < $serviceCnt; $j++) {
                                        $LMRClientTypeCode = '';
                                        $sOpt = '';
                                        $LMRClientType = '';
                                        $LMRClientTypeCode = trim($servicesRequested[$j]['LMRClientType']);
                                        $LMRClientType = trim($servicesRequested[$j]['serviceType']);
                                        $chk = '';
                                        if (in_array($LMRClientTypeCode, $myFileInfo['LMRadditionalLoanprograms'] ?? [])) {
                                            $chk = 'selected ';
                                        }
                                        $displayOption = '';
                                        if ($LMRClientTypeCode == 'TBD') {
                                            $displayOption = "style = 'display:none;' ";
                                        }
                                        if ($servicesRequested[$j]['internalLoanProgram'] == 0) { ?>
                                            <option <?php echo $chk; ?> <?php echo $displayOption; ?>
                                                    value="<?php echo $LMRClientTypeCode; ?>"><?php echo $LMRClientType; ?></option>
                                            <?php
                                        }
                                    } ?>
                                </select></div>
                        </div>
                    </div>

                    <div class=" col-md-6 propDetailsProcess_disp <?php echo loanForm::showField('propDetailsProcess'); ?> ">
                        <div class="row form-group">
                            <?php echo loanForm::label('propDetailsProcess', 'col-md-5 '); ?>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <select class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propDetailsProcess', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            name="propDetailsProcess" id="propDetailsProcess"
                                            tabindex="<?php echo $tabIndex++; ?>"
                                            onchange="populatePropertyDetails(this.value);" <?php echo BaseHTML::fieldAccess(['fNm' => 'propDetailsProcess', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <option value=''> - Select -</option>
                                        <?php
                                        for ($i = 0; $i < count($glPropDetailsProcess ?? []); $i++) {
                                            $sOpt = '';
                                            $propDetals = '';
                                            $propDetals = trim($glPropDetailsProcess[$i]);
                                            $sOpt = Arrays::isSelected($propDetals, $propDetailsProcess);
                                            echo "<option value=\"" . $propDetals . "\" " . $sOpt . '>' . $propDetals . '</option>';
                                        }
                                        ?>
                                    </select>
                                <?php } else {
                                    echo '<b>' . $propDetailsProcess . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 leadSource_disp <?php echo loanForm::showField('leadSource'); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('leadSource', 'col-md-5 '); ?>
                            <div class="col-md-7">
                                <select class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'leadSource', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        name="branchLeadSource" id="leadSource" TABINDEX="<?php echo $tabIndex++ ?>"
                                        onchange="checkRef(this.value);" <?php echo BaseHTML::fieldAccess(['fNm' => 'leadSource', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <option value="">- Select -</option>
                                    <?php
                                    for ($bh = 0; $bh < count($branchHAInfoArray ?? []); $bh++) {
                                        $branchHearAbout = '';
                                        $sOpt = '';
                                        $branchHearAbout = $branchHAInfoArray[$bh]['branchHearAbout'];
                                        $sOpt = Arrays::isSelected($branchHearAbout, $leadSource);
                                        ?>
                                        <option <?php echo $sOpt; ?>
                                                value="<?php echo $branchHearAbout; ?>"><?php echo $branchHearAbout; ?></option>
                                        <?php
                                    }
                                    ?>
                                    <option <?php echo Arrays::isSelected('Other', $leadSource) ?> value="Other">Other
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class=" col-md-6 <?php echo BaseHTML::parentFieldAccess(['fNm' => 'leadSource', 'sArr' => $secArr, 'pv' => $leadSource, 'av' => 'Other']); ?>"
                         id="refDiv">
                <textarea class="form-control input-sm " name="hereAbout" id="hereAbout"
                          tabindex="<?php echo $tabIndex++; ?>"
                          placeholder="Let us know how you heard about us."><?php echo $hereAbout; ?></textarea>
                    </div>


                    <div class="col-md-6 referringParty_disp <?php echo loanForm::showField('referringParty'); ?>">
                        <div class="row form-group">
                            <label class="col-md-5 font-weight-bold"
                                   for="referringParty"><?php echo BaseHTML::fieldAccess(['fNm' => 'referringParty', 'sArr' => $secArr, 'opt' => 'L']); ?></label>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'referringParty', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           type="text" name="referringParty" id="referringParty"
                                           value="<?php echo $referringParty; ?>" autocomplete="off"
                                           TABINDEX="<?php echo $tabIndex++ ?>"
                                           maxlength="45" <?php echo BaseHTML::fieldAccess(['fNm' => 'referringParty', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                                <?php } else { ?>
                                    <h5><?php echo $referringParty; ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
} // Public User...
?>

<?php
if (glCustomJobForProcessingCompany::showMortgageNotes($PCID) && glUserRole::USER_ROLE_CLIENT != $userRole && !$publicUser) {
    mortgageNotes::$fileTab = $fileTab;
    mortgageNotes::$HMLOLoanInfoSectionsDisp = $HMLOLoanInfoSectionsDisp;
    mortgageNotes::$allowToEdit = $allowToEdit;

    require_once 'LMRequest/sections/mortgageNotes.php';
}
?>

<?php echo LoanStagesController::forLoanFile($LMRId)->renderStagesHtml(); ?>

<!--begin: Wizard Step 1-->
<?php require 'borrowerInfo.php'; ?>
<?php require 'coborInformation.php'; ?>
<?php require 'businessEntitySection.php'; ?>
<?php
if ($hideThisField) {
    require 'salesMethod.php';
}
?>
<?php
if ($hideThisField) {
    require 'feeScheduleSection.php';
}
?>
<?php require 'equipmentInformationSection.php'; ?>
<?php require 'additionalGuarantorsSection.php'; ?>
<?php
if ($hideThisField) {
    require 'governmentInfo.php';
}
?>
<!--end: Wizard Step 1-->

<!--begin: Wizard Step 2-->
<?php
/* Borrower Background Section Start */
require 'borrowerBackground.php';

/* Co-Borrower Background Section Start */
require 'coborrowerBackground.php';

/* Borrower Experience Section */
require 'borrowerExperience.php';

/* Co-Borrower Experience Section End */
require 'coBorrowerExperience.php';

/* Property Management Section Start */
require 'propertyManagementSection.php';

include_once 'sbaBackgroundAdditionalQuestions.php';
?>
<!--end: Wizard Step 2-->


<!--begin: Wizard Step 3-->
<?php
/* Assets Section Start */
require 'assetsForm.php';
if ($hideThisField) {
    require 'giftsOrGrantsSection.php';
}
require 'FinancialAccountsAndSecurities.php';
require 'contingentLiabilitiesSection.php';
require 'scheduleRealEstate.php';

require 'incExpWFForm.php';
require 'liabilitiesSection.php';
require 'creditorsLiabilitiesSection.php';
require_once 'LMRequest/sections/partnerShipsForm.php';
require 'otherNewMortgageLoansSection.php';
?>
<!--end: Wizard Step 3-->


<!--begin: Wizard Step 4-->
<?php
if (!isset($lockedSections)) {
    $lockedSections = [];

    if ($allowToEdit) {
        $lockInfo = (PageVariables::$userRole == 'Super' ? [] : (isFileLockedLastVal::getReport(['LMRId' => $LMRId]) ?? [])); //            // Superuser or file owner can access unlocked sections

        if (!empty($lockInfo)) {
            $lockedFile = $lockInfo['locked'] ? 1 : 0;
            $lockedSections = $lockedFile ? (str_contains($lockInfo['lockedSection'] ?? '', ',')
                ? explode(',', $lockInfo['lockedSection'])
                : [$lockInfo['lockedSection'] ?? '']) : [];


            $lockStatus = $lockInfo['locked'] ? 'Locked' : 'Unlocked';
            $iconClass = $lockInfo['locked'] ? 'fa-lock text-danger' : 'fa-unlock';

            $lockedSectionTxt =
                '<span class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2"
  title="' . $lockStatus . ' By: ' . $lockInfo['lockedBy'] . ' (' . $lockInfo['lockedUserRole'] . ')">
    <i class="fa ' . $iconClass . '  icon-md"></i>
</span>';

            if (($lockInfo['lockedUID'] ?? null) == PageVariables::$userNumber) {
                $lockedSections = [];
            }
        }
    }
}
$ft = Request::GetClean('ft') ?? 'HMLO';
LoanInfo::$disabledInputForClient = 1;
if (PageVariables::$userGroup == glUserGroup::CLIENT) LoanInfo::$disabledInputForClient = 0;
require 'LMRequest/loanInfo/loanSettingSection.php';
require 'LMRequest/loanInfo/importantDatesSection.php';
require 'LMRequest/loanInfo/rehabSection.php';
require 'LMRequest/loanInfo/loanTermsSection.php';
require 'LMRequest/loanInfo/additionalLoanSettingsSection.php';
require_once 'LMRequest/sections/refinanceMortgageForm.php';
//require 'HMLOLoanTermsForm.php';
?>
<?php
if (count($secArr = BaseHTML::sectionAccess2(['sId' => 'ACF', 'opt' => $fileTab])) > 0) { // Get Active Fields only...
    loanForm::pushSectionID('ACF');

    if (count(Arrays::getValueFromArray('ACF', $fieldsInfo)) > 0) {
        ?>
        <div class="col-md-12 <?php echo loanForm::showField('guidelinewarning'); ?>"
             id="divGuidelinesErrorMsg"></div>
        <?php
    }
}
?>
<!-- HMLOLoanInfoForm.php -->
<input type="hidden" id="subjectPropertyJson"
       value='<?php echo json_encode(GpropertyTypeNumbArray::$GpropertyTypeNumbArray); ?>'>
<input type="hidden" id="subjectStateJson" value='<?php echo json_encode($stateArray); ?>'>

<?php include_once 'propDetailsSection.php'; ?>

<?php
//}
require 'subjectPropertyCashFlow.php';
if ($hideThisField) {
    require 'sellerInfo.php';
    //require 'propertyValuationSection.php';
    require 'HOAInformationSection.php';
}
require 'estimatedProjectCost.php';
include('collateralSection.php');
require 'equipmentFinancingForm.php';
if ($hideThisField) {
    require 'picturesOfProperty.php';
}
?>
<div id="propertyPictures"></div>
<?php
require 'lien1MortgageScenario.php';
require 'lien2MortgageScenario.php';
?>
<!--end: Wizard Step 4-->


<!--begin: Wizard Step 5-->
<?php
require 'foreclosure.php';
require 'loanWorkoutQA.php';
require 'LMRequest/loanInfo/additionalQuestions.php';
//require 'additionalQuestions.php';
if ($hideThisField) {
    require 'titleInfoSection.php';
}
if ($hideThisField) {
    require 'attorneyInfo.php';
}
if ($hideThisField) {
    require 'escrowInfo.php';
    require 'generalContractor.php';
    require 'financialAdvisor.php';
    require 'accountant.php';
}
?>
<?php
if ($hideThisField) {
    require 'insuranceInfo.php';
}
if ($activeTab == 'PI' || $activeTab == 'QAPP' || $activeTab == 'LI') {
    if ($hideThisField) {
        require 'insuranceDetails.php';
    }
} ?>

<?php
if ($hideThisField) {
    require 'creditMemoSection.php';
    require_once 'LMRequest/sections/paymentInfoForm.php';
}
?>
<!--end: Wizard Step 5-->

<?php
if (!(glCustomJobForProcessingCompany::showMortgageNotes($PCID) && glUserRole::USER_ROLE_CLIENT != $userRole && !$publicUser)) {
    mortgageNotes::$fileTab = $fileTab;
    mortgageNotes::$HMLOLoanInfoSectionsDisp = $HMLOLoanInfoSectionsDisp;
    mortgageNotes::$allowToEdit = $allowToEdit;

    require_once 'LMRequest/sections/mortgageNotes.php';
}

if (!$publicUser && (LMRequest::$activeTab == 'LI' || LMRequest::$activeTab == 'QAPP')) {
    LMRequest::$termsConditionsText = (LMRequest::$activeTab == 'LI' ? $HMLOTAC : $HMLOTACQA);
    require_once 'LMRequest/sections/termsAndConditions.php';
}


WebForm::$isCoborrower = (bool)$isCoBorrower;
WebForm::getSignatures($LMRId, $fileTab);

if ($publicUser == 1) {
    WebForm::$publicUser = $publicUser;
    WebForm::$allowToEdit = $allowToEdit;
    WebForm::$activeTab = $activeTab;
    WebForm::$PCID = $PCID;
    WebForm::$URLLink = $URLLink;
    WebForm::$secArr = $secArr;
    WebForm::$LMRId = $LMRId;
    WebForm::$hideThisField = $hideThisField;
    WebForm::$UType = $UType;
    WebForm::$BorrowerLink = $BorrowerLink;
    WebForm::$coBorrowerLink = $coBorrowerLink;
    WebForm::$isMultiStepWebForm = false;
    WebForm::$opt = $wfOpt;
    WebForm::$termsConditionsText = (WebForm::$activeTab == 'LI' ? $HMLOTAC : $HMLOTACQA);

    require_once __DIR__ . '/../webForm/sections/termsAndConditions.php';

    if ($hideThisField) {
        require_once __DIR__ . '/../webForm/sections/showSignature.php';
    }
    require_once __DIR__ . '/../webForm/sections/signaturePad.php';

    if ($aud == 1) { //value assigned in HMLOWebForm.php
        $ip['fileID'] = $LMRId;
        $docCheckListName = getChecklistNameID::getReport($ip);
        if (isset($myFileInfo['docArray'])) $tempDocInfoArray = $myFileInfo['docArray'];
        /* Upload docs section */
        for ($i = 0; $i < count($tempDocInfoArray ?? []); $i++) {
            $userId = 0;
            $userType = '';
            $userId = $tempDocInfoArray[$i]['uploadedBy'];
            $userType = $tempDocInfoArray[$i]['uploadingUserType'];
            if ($userType == 'Processor' || $userType == 'Employee') {
                $empIdArray[] = $userId;
            }
            if ($userType == 'LMR Executive' || $userType == 'Branch') {
                $branchIdArray[] = $userId;
            }
            if ($userType == 'Broker' || $userType == 'Agent') {
                $agentIdArray[] = $userId;
            }
            if ($userType == 'Client' && $userId > 0) {
                $clientIdArray[] = $userId;
            }
            if ($tempDocInfoArray[$i]['docCategory'] == 'Checklist Items') {
                $tempDocInfoArray[$i]['docChecklistName'] = $docCheckListName[$tempDocInfoArray[$i]['docID']];
            } elseif (!in_array($tempDocInfoArray[$i]['docCategory'], ['Appraisal1', 'Appraisal2', 'BPO1', 'BPO2', 'BPO3', 'Title Report', 'Property Insurance Coverage1', 'Property Insurance Coverage2', 'Property Insurance Coverage3', 'Property Insurance Coverage4', 'Property Insurance Coverage5', 'Property Insurance Coverage', 'AVM1', 'AVM2', 'AVM3'])) {
                $tempDocInfoArray[$i]['docChecklistName'] = $tempDocInfoArray[$i]['docCategory']; //"Other new";
                $tempDocInfoArray[$i]['docCategory'] = 'Other';
            }
        }

        require 'getAllFileDocsInfoFromRemote.php';
        /* Upload docs end */
        ?>
        <!-- Upload Documents List section start-->
        <div class="block-content row">
            <div class="text-on-pannel text-primary">Uploaded Files, Documents &amp; Binder</div>
            <div class="col-md-12">
                <?php require 'fileUploadDocList.php'; ?>
            </div>
        </div>
        <!-- Upload Documents List section end -->
        <?php
    }
}

if ($LMRId > 0 && $publicUser != 1) {
    require_once __DIR__ . '/../webForm/sections/showSignature.php';
    require_once __DIR__ . '/../webForm/sections/creditAuthCheckbox.php';
}
if ($LMRId &&
    glCustomJobForProcessingCompany::showApplicationReceivedDate(LMRequest::$PCID) &&
    ((!$publicUser && $fileTab == 'FA') || ($publicUser && $fileTab == 'FA' && Request::isset('pdf')))
) {
    require_once __DIR__ . '/../webForm/sections/applicationReceivedDate.php';
}
?>

<div class="card card-custom">
    <div class="card-body">
        <?php
        if ($publicUser == 1) {
            if ($allowToEdit) {
                if ($allowCaptcha == 1 && !RECAPTCHA_DISABLE) { ?>
                    <!-- CAPTCHA-reCAPTCHA -->
                    <div class="row justify-content-center recaptchaCode">
                        <div class="col-md-4 ">
                            <script src="https://www.google.com/recaptcha/api.js" async defer></script>
                            <div class="g-recaptcha"
                                 data-sitekey="<?php echo PCInfo::getCaptchaKey($PCID, 'siteKey'); ?>"></div>
                            <p id="human_valid" class="text-danger hidden">Please verify you are human!</p>
                        </div>
                    </div>
                    <!--//CAPTCHA-reCAPTCHA//-->
                <?php } ?>
                <div class="row hideForPDFGeneration mt-2">
                    <script type="text/javascript">
                        $(function () {
                            $('[data-toggle="tooltip"]').tooltip();
                        });
                    </script>
                    <?php
                    if ($wfOpt == 'FA') {
                        echo '<div class="col-md-12 align-center text-center" >
                        <input
                            type="submit"
                            value="Submit"
                            name="btnSave"
                            id="btnSave"
                            onclick="$(\'#submitType\').val(\'Submit\');"
                            class="btn btn-primary right"
                            tabindex="' . $tabIndex++ . '">';
                        if (!glCustomJobForProcessingCompany::isPC_GIF($PCID)) {
                            echo '<input
                            type="submit"
                            value="Save & Finish Later"
                            name="btnSave"
                            id="btnSaveAndFinish"
                            class="btn btn-primary left"
                            onclick="$(\'#submitType\').val(\'Save & Finish Later\');"
                            tabindex="' . $tabIndex++ . '"
                            style="margin-left: 40px;"
                            data-toggle="tooltip"
                            data-placement="left"
                            title="By clicking here, you will be saving the data on this page. You can continue completing this form later, by visiting the same URL address.">';
                        }
                        echo '<input type="hidden" name="submitType" id="submitType" value=""></div>';
                    } else {
                        echo '<div class="col-md-12 align-center text-center">
                        <input
                            type="submit"
                            value="Submit"
                            name="btnSave"
                            id="btnSave"
                            onclick="$(\'#submitType\').val(\'Submit\');"
                            class="btn btn-primary"
                            tabindex="' . $tabIndex++ . '">
                            </div>
                        <input type="hidden" name="submitType" id="submitType" value="">';
                    }
                    ?>
                </div>
                <?php
            }
        } else { ?>
            <div class="row  justify-content-center">
                <input type="submit" class="btn btn-primary btnSave" name="btnSave" id="saveBtn" value="Save"
                       tabindex="<?php echo $tabIndex++; ?>">
                <input type="submit" class="btn btn-primary btnSave ml-1" name="btnSave" id="saveNextBtn"
                       value="Save & Next"
                       tabindex="<?php echo $tabIndex++; ?>"
                       onclick="if(this.disabled===false) {return true;} else {return false;}">
                <script type="text/javascript">
                    /*
                        allowToEditFileContacts('titleAttorneyContactCls', 'titleAttorneyPrimContactCls', 'Edit'); // Allow to Edit file contacts..
                        allowToEditFileContacts('insContactCls', 'insPrimContactCls', 'Edit'); // Allow to Edit file contacts.
                    */

                    $(document).ready(function () {
                        $("#loanModForm").submit(function (e) {
                            e.preventDefault();
                            $('#submitStatus').val('1');
                            $('.btnSave').attr('disabled', 'disabled');
                            var encPCID = $('#encryptedPCID').val();
                            var loanNumber = $('#loanNumber').val();
                            var fid = $('#encryptedLId').val();
                            var finalData = false;
                            chznFldVal = $('#LMRClientType').val();
                            chznPrimaryStatusVal = $('#primaryStatus').val();
                            if (chznFldVal == '' || chznFldVal == 0) {
                                $('.btnSave').prop('disabled', false);
                                $('#submitStatus').val('0');
                                toastrNotification("Please Select What kind of program are you looking for?", 'error');
                                return false;

                            } else if (chznPrimaryStatusVal == '' || chznPrimaryStatusVal == 0) {
                                $('.btnSave').prop('disabled', false);
                                $('#submitStatus').val('0');
                                toastrNotification("Please Select Primary Client File Status", 'error');
                                return false;
                            } else if (!LMRequest.validateDates()) {
                                $('.btnSave').prop('disabled', false);
                                return false;
                            } else {
                                $.ajax({
                                    type: 'POST',
                                    url: siteSSLUrl + "JQFiles/checkLoanNoExist.php",
                                    data: jQuery.param({'encPCID': encPCID, 'loanNumber': loanNumber, 'fid': fid}),
                                    contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
                                    success: function (status) {
                                        if (status == 1) {
                                            $('.btnSave').prop('disabled', false);
                                            $('#submitStatus').val('0');
                                            toastrNotification("Loan Number already exists on another file! Please try assigning a new number.", 'error');
                                            return false;
                                        } else {
                                            //validate the Credit Memo Section
                                            let memCatLen = $('.memoCategory').length;
                                            let fstMemDescLen = $('#memoDescription_1').length;
                                            let validateMemo = 0; // no validation if no desc for credit memo 1
                                            if (fstMemDescLen > 0) { // check desc
                                                let fstMemDescVal = $('#memoDescription_1').val();
                                                if (fstMemDescVal != '') {
                                                    let validateMemo = 1; // validate the Credit Memo Cat
                                                }
                                            }
                                            if (memCatLen > 0 && validateMemo) { // we have fields to check
                                                let exists = false;
                                                $(".memoCategory option:selected").each(function (i) {
                                                    var selVal = $(this).val();
                                                    if (selVal == '') {
                                                        exists = true;
                                                        $('.btnSave').prop('disabled', false);
                                                        $('#submitStatus').val('0');
                                                        return false;
                                                    }
                                                });
                                                if (exists) {
                                                    var selCatMsg = "Please select a category";
                                                    toastrNotification(selCatMsg, 'error');
                                                    $([document.documentElement, document.body]).animate({
                                                        scrollTop: $(".memoCategory").offset().top
                                                    }, 500);
                                                    $('.btnSave').prop('disabled', false);
                                                    $('#submitStatus').val('0');
                                                    return false;
                                                }
                                            }
                                            //validate the Credit Memo Section
                                            if (validateClientInfoForm() && validateMinMaxLoanGuidelines()) {
                                                $('.btnSave').attr('disabled', 'disabled');
                                                $('#submitStatus').val('1');
                                                //check if PC has automation feature enabled
                                                let allowAutomation = parseInt($('#allowAutomation').val());
                                                let lastUpdatedParam = $('#lastUpdatedParam').val();
                                                let lmrid = parseInt($('#fLMRId').val());
                                                let OSID = $('#OSID').val();
                                                let primaryStatus = $('#primaryStatus').val();
                                                let isUpdated = $('#isFssUpdated').val();
                                                let userAutomationControlAccess = parseInt($('#userAutomationControlAccess').val());
                                                let dataChanged = $('#dataChanged').val();
                                                let fileRow = $('#fileRow').val();
                                                if (allowAutomation === 1
                                                    && lastUpdatedParam !== ''
                                                    && lmrid > 0
                                                    && userAutomationControlAccess === 1) {
                                                    //check if PFS, FSS is updated
                                                    let lastUpdatedFss = $('#lastUpdatedFss').val();
                                                    let fileTypesTxt = $('#fileTypesTxt').val();
                                                    let activeTab = $('#activeTab').val();
                                                    let encPCID = $('#encryptedPCID').val();

                                                    //allow automation rule repeat
                                                    //code to check for user conformation
                                                    if (OSID !== primaryStatus || isUpdated === 'Yes' || dataChanged === 'Yes') {
                                                        $.ajax({
                                                            url: siteSSLUrl + 'backoffice/HMLOLoanInfoSave.php',
                                                            type: 'POST',
                                                            dataType: 'json',
                                                            data: {
                                                                encryptedPCID: encPCID,
                                                                LMRID: lmrid,
                                                                fileTypesTxt: fileTypesTxt,
                                                                OSID: OSID,
                                                                primaryStatus: primaryStatus,
                                                                isFssUpdated: isUpdated,
                                                                lastUpdatedParam: lastUpdatedParam,
                                                                lastUpdatedFss: lastUpdatedFss,
                                                                activeTab: activeTab,
                                                                allowRepeat: 'check',
                                                                triggerRule: 'No',
                                                                dataChanged: dataChanged,
                                                                fileRow: fileRow,
                                                            },
                                                            beforeSend: function () {
                                                                //console.log(this.data);
                                                            },
                                                            success: function (resp) {
                                                                if (resp.length === 0) {
                                                                    document.getElementById("loanModForm").submit();
                                                                }
                                                                if (resp) {
                                                                    previewAutomatedEventPopup(resp, fid, lastUpdatedParam, activeTab);
                                                                } else {
                                                                    let msg = 'These automated action(s) were already triggered and cannot be triggered again.';
                                                                    toastrNotification(msg, 'success');
                                                                }
                                                            },
                                                            error: function () {

                                                            }
                                                        });
                                                    } else {
                                                        document.getElementById("loanModForm").submit();
                                                    }
                                                } else {
                                                    if ((OSID !== primaryStatus || lastUpdatedParam !== '') && !userAutomationControlAccess) {
                                                        $('#manual').val('0');
                                                        $('#triggerRule').val('Yes');
                                                    }
                                                    document.getElementById("loanModForm").submit();
                                                }
                                            } else {
                                                $('#submitStatus').val('0');
                                            }
                                        }
                                    }
                                });
                            }
                        });
                    });
                </script>
            </div>
        <?php } ?>

        <?php if ($isPLO == 0 && $publicUser == 1) { ?>
            <div class="col-md-12 no-padding no-margin">
                <div class="no-padding no-margin text-right">
        <span class="col-md-12 no-padding no-margin"
              style="letter-spacing: 1px; font-size: 14px; font-weight: 600; color: #666; font-family: 'Lato', sans-serif;text-align: right;padding-bottom:2px;">
             Webform Fueled By:<br>
        </span>
                    <a href="https://www.lendingwise.com/" target="_blank">
                        <img src="<?php echo CONST_SITE_URL; ?>assets/images/logonew.svg"
                             alt="Webform Fueled By LendingWise"
                             style="width: 180px;">
                    </a>
                </div>
            </div>
        <?php } ?>
    </div>
</div>

<?php if ($allowUserToEditBroker == 1) { ?>
    <script type="text/javascript">
        jQuery(".agentInfoCls").each(function () {
            $(this).addClass('disabledKeyFields');
        });
    </script>
    <?php
}
if ($borrowerEmailLink != '') { ?>
    <script type="text/javascript">
        populateClientBackgroundEntityInfo('loanModForm', '<?php echo $borrowerEmail ?>', '<?php echo $PCID ?>');
    </script>
<?php } ?>

<script>
    $(function () {
        let publicUser = <?php echo $publicUser ?>;
        let LMRId = <?php echo $LMRId; ?>;
        if (!publicUser && !LMRId) {
            let branchList = <?php echo json_encode($branchList); ?>;
            let PCID = <?php echo $PCID ?>;
            let moduleCode = '<?php echo LMRequest::$moduleCode; ?>';

            if (Object.keys(branchList).length === 1) {
                getModules('loanModForm', PCID, moduleCode);
                getServiceTypes('loanModForm');
                getBranchAgents($('#branchId').val());
            }
        }
        $('#loanprogramtooltip').prop('title', atob("<?php echo base64_encode($loanPgmDetails); ?>"));
        validateMinMaxLoanGuidelines('No');
        fixAdditionalLoanProgChosen(document.getElementById("LMRClientType").value);

        setTimeout(function () {
            formControl.formFields = <?php echo json_encode($fieldsInfo); ?>;
            formControl.fileTab = "<?php echo htmlspecialchars($fileTab); ?>";
            formControl.publicUser = "<?php echo intval($publicUser); ?>";
            formControl.userGroup = "<?php echo $userGroup; ?>";
            formControl.activeTab = '<?php echo htmlspecialchars($activeTab); ?>';
        }, 100);
    });

    function fixAdditionalLoanProgChosen(removeMe = '') {
        if (removeMe) {
            $("#LMRadditionalLoanProgram option[value='" + removeMe + "']").attr('disabled', 'disabled').siblings().removeAttr('disabled');
            $("#LMRadditionalLoanProgram").trigger('chosen:updated');
        }
    }
    <?php
    if ($PCID == 4026) {  //open broker div custom code for tenet (4026), express cap (2853)-removed... (and stage-lendingwise-demo2 - 3363, 3580 is lendingwise-dave in live)
    ?>
    $(document).ready(function () {
        $("#REBroker").prop("checked", true);
        showAndHideBrokerInfo('Yes', 'BrokerInfoDiv');
    });
    <?php
    }
    ?>
    <?php
    if ($PCID == 2853) {  //open lo div custom code for express cap (2853) (and stage-lendingwise-demo2 - 3363, but not for tenet)
    ?>
    $(document).ready(function () {
        $("#RELoanofficer").prop("checked", true);
        showAndHideLoanofficerInfo('Yes', 'LoanofficerInfoDiv');
    });
    <?php
    }
    ?>

    /* remove mandatory for Mailing Address in webforms */
    function removeMandatory() {
        if ($("#isPresentAdd").hasClass("mandatory")) { // Returns true if the class exist.
            $('#isPresentAdd').removeClass('mandatory');
        }
    }

    function buildurlMulti(newhref, count) {
        var newPropCity = document.getElementById('propertyCity' + count).value;
        var newPropState = document.getElementById('propertyState' + count).value;
        var newPropZip = document.getElementById('propertyZip' + count).value;
        var newaddress = document.getElementById('propertyAddress' + count).value;
        newstring = 'http://www.zillow.com/search/RealEstateSearch.htm?citystatezip=' + newaddress + ' ' + newPropCity + ' ' + newPropState + ' ' + newPropZip;
        newhref.href = newstring;
    }

    function customDev() {
        <?php
        if ($PCID == 2853) {
            echo "document.getElementById(\"REBrokerQuestion\").style.display=\"none\";";
            //ch12431 - force the radio and quesiton div to hidden after changing loan program
        }
        ?>
    }

    if ($('.REBrokerLODisp.secShow').length > 0) { //show title
        $(".ABICard").show();
    } else { //hide title
        $(".ABICard").hide();
    }
</script>
<!-- HMLOLoanInfoForm.php -->
