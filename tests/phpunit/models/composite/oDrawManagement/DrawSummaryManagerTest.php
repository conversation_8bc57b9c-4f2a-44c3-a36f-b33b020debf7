<?php

namespace tests\models\composite\oDrawManagement;

use models\composite\oDrawManagement\DrawSummaryManager;
use models\composite\oDrawManagement\DrawRequestManager;
use PHPUnit\Framework\TestCase;

/**
 * Tests the DrawSummaryManager class.
 *
 * @covers \models\composite\oDrawManagement\DrawSummaryManager::__construct
 * @covers \models\composite\oDrawManagement\DrawSummaryManager::initialize
 * @covers \models\composite\oDrawManagement\DrawSummaryManager::getFormattedTotalDrawsFunded
 * @covers \models\composite\oDrawManagement\DrawSummaryManager::getFormattedHoldbackRemaining
 * @covers \models\composite\oDrawManagement\DrawSummaryManager::getAllData
 */
class DrawSummaryManagerTest extends TestCase
{
    /**
     * Tests the constructor with valid LMRId.
     */
    public function testConstructorWithValidLMRId(): void
    {
        try {
            $manager = new DrawSummaryManager(123);
            $this->assertInstanceOf(DrawSummaryManager::class, $manager);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the constructor with DrawRequestManager.
     */
    public function testConstructorWithDrawRequestManager(): void
    {
        try {
            $drawRequestManager = new DrawRequestManager(123);
            $manager = new DrawSummaryManager(123, $drawRequestManager);
            $this->assertInstanceOf(DrawSummaryManager::class, $manager);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the initialize static method.
     */
    public function testInitialize(): void
    {
        try {
            $manager = DrawSummaryManager::initialize(123);
            $this->assertInstanceOf(DrawSummaryManager::class, $manager);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the initialize static method with DrawRequestManager.
     */
    public function testInitializeWithDrawRequestManager(): void
    {
        try {
            $drawRequestManager = new DrawRequestManager(123);
            $manager = DrawSummaryManager::initialize(123, $drawRequestManager);
            $this->assertInstanceOf(DrawSummaryManager::class, $manager);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the getFormattedTotalDrawsFunded static method.
     */
    public function testGetFormattedTotalDrawsFunded(): void
    {

        DrawSummaryManager::$totalDrawsFunded = 12345.67;

        $result = DrawSummaryManager::getFormattedTotalDrawsFunded();

        $this->assertIsString($result);
        $this->assertEquals('12,346', $result);
    }

    /**
     * Tests the getFormattedTotalDrawsFunded with zero value.
     */
    public function testGetFormattedTotalDrawsFundedZero(): void
    {

        DrawSummaryManager::$totalDrawsFunded = 0;

        $result = DrawSummaryManager::getFormattedTotalDrawsFunded();

        $this->assertIsString($result);
        $this->assertEquals('0', $result);
    }

    /**
     * Tests the getFormattedHoldbackRemaining static method.
     */
    public function testGetFormattedHoldbackRemaining(): void
    {

        DrawSummaryManager::$holdbackRemaining = 98765.43;

        $result = DrawSummaryManager::getFormattedHoldbackRemaining();

        $this->assertIsString($result);
        $this->assertEquals('98,765', $result);
    }

    /**
     * Tests the getFormattedHoldbackRemaining with zero value.
     */
    public function testGetFormattedHoldbackRemainingZero(): void
    {

        DrawSummaryManager::$holdbackRemaining = 0;

        $result = DrawSummaryManager::getFormattedHoldbackRemaining();

        $this->assertIsString($result);
        $this->assertEquals('0', $result);
    }

    /**
     * Tests the getAllData static method.
     */
    public function testGetAllData(): void
    {

        DrawSummaryManager::$address = '123 Test St';
        DrawSummaryManager::$city = 'Test City';
        DrawSummaryManager::$state = 'TS';
        DrawSummaryManager::$zip = '12345';
        DrawSummaryManager::$initialLoan = 100000;
        DrawSummaryManager::$rehabCostFinanced = 50000;
        DrawSummaryManager::$totalLoanAmount = 150000;
        DrawSummaryManager::$currentLoanBalance = 140000;
        DrawSummaryManager::$rehabCost = 55000;
        DrawSummaryManager::$arv = '200000';
        DrawSummaryManager::$totalDrawsFunded = 25000;
        DrawSummaryManager::$holdbackRemaining = 25000;
        DrawSummaryManager::$closingDate = '01/01/2023';
        DrawSummaryManager::$maturityDate = '01/01/2024';
        DrawSummaryManager::$dateOfLastDraw = '06/01/2023';
        DrawSummaryManager::$dateOfCurrentDraw = '07/01/2023';

        $result = DrawSummaryManager::getAllData();

        $this->assertIsArray($result);
        $this->assertEquals('123 Test St', $result['address']);
        $this->assertEquals('Test City', $result['city']);
        $this->assertEquals('TS', $result['state']);
        $this->assertEquals('12345', $result['zip']);
        $this->assertEquals(100000, $result['initialLoan']);
        $this->assertEquals(50000, $result['rehabCostFinanced']);
        $this->assertEquals(150000, $result['totalLoanAmount']);
        $this->assertEquals(140000, $result['currentLoanBalance']);
        $this->assertEquals(55000, $result['rehabCost']);
        $this->assertEquals('200000', $result['arv']);
        $this->assertEquals(25000, $result['totalDrawsFunded']);
        $this->assertEquals(25000, $result['holdbackRemaining']);
        $this->assertEquals('01/01/2023', $result['closingDate']);
        $this->assertEquals('01/01/2024', $result['maturityDate']);
        $this->assertEquals('06/01/2023', $result['dateOfLastDraw']);
        $this->assertEquals('07/01/2023', $result['dateOfCurrentDraw']);
    }

    /**
     * Tests the getAllData method returns all expected keys.
     */
    public function testGetAllDataContainsAllKeys(): void
    {
        $result = DrawSummaryManager::getAllData();

        $expectedKeys = [
            'address', 'city', 'state', 'zip', 'initialLoan', 'rehabCostFinanced',
            'totalLoanAmount', 'currentLoanBalance', 'rehabCost', 'arv',
            'totalDrawsFunded', 'holdbackRemaining', 'closingDate', 'maturityDate',
            'dateOfLastDraw', 'dateOfCurrentDraw'
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $result);
        }

        $this->assertCount(16, $result);
    }

    /**
     * Tests static properties initialization.
     */
    public function testStaticPropertiesInitialization(): void
    {
        DrawSummaryManager::$address = null;
        DrawSummaryManager::$city = null;
        DrawSummaryManager::$state = null;
        DrawSummaryManager::$zip = null;
        DrawSummaryManager::$initialLoan = 0;
        DrawSummaryManager::$rehabCostFinanced = 0;
        DrawSummaryManager::$totalLoanAmount = 0;
        DrawSummaryManager::$currentLoanBalance = 0;
        DrawSummaryManager::$rehabCost = 0;
        DrawSummaryManager::$arv = null;
        DrawSummaryManager::$totalDrawsFunded = 0;
        DrawSummaryManager::$holdbackRemaining = 0;
        DrawSummaryManager::$closingDate = null;
        DrawSummaryManager::$maturityDate = null;
        DrawSummaryManager::$dateOfLastDraw = null;
        DrawSummaryManager::$dateOfCurrentDraw = null;

        $this->assertNull(DrawSummaryManager::$address);
        $this->assertNull(DrawSummaryManager::$city);
        $this->assertNull(DrawSummaryManager::$state);
        $this->assertNull(DrawSummaryManager::$zip);
        $this->assertEquals(0, DrawSummaryManager::$initialLoan);
        $this->assertEquals(0, DrawSummaryManager::$rehabCostFinanced);
        $this->assertEquals(0, DrawSummaryManager::$totalLoanAmount);
        $this->assertEquals(0, DrawSummaryManager::$currentLoanBalance);
        $this->assertEquals(0, DrawSummaryManager::$rehabCost);
        $this->assertNull(DrawSummaryManager::$arv);
        $this->assertEquals(0, DrawSummaryManager::$totalDrawsFunded);
        $this->assertEquals(0, DrawSummaryManager::$holdbackRemaining);
        $this->assertNull(DrawSummaryManager::$closingDate);
        $this->assertNull(DrawSummaryManager::$maturityDate);
        $this->assertNull(DrawSummaryManager::$dateOfLastDraw);
        $this->assertNull(DrawSummaryManager::$dateOfCurrentDraw);
    }

    /**
     * Tests getFormattedTotalDrawsFunded with large numbers.
     */
    public function testGetFormattedTotalDrawsFundedLargeNumber(): void
    {
        DrawSummaryManager::$totalDrawsFunded = 1234567.89;

        $result = DrawSummaryManager::getFormattedTotalDrawsFunded();

        $this->assertEquals('1,234,568', $result);
    }

    /**
     * Tests getFormattedHoldbackRemaining with large numbers.
     */
    public function testGetFormattedHoldbackRemainingLargeNumber(): void
    {
        DrawSummaryManager::$holdbackRemaining = 9876543.21;

        $result = DrawSummaryManager::getFormattedHoldbackRemaining();

        $this->assertEquals('9,876,543', $result);
    }

    /**
     * Tests constructor with zero LMRId.
     */
    public function testConstructorWithZeroLMRId(): void
    {
        try {
            $manager = new DrawSummaryManager(0);
            $this->assertInstanceOf(DrawSummaryManager::class, $manager);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests initialize with zero LMRId.
     */
    public function testInitializeWithZeroLMRId(): void
    {
        try {
            $manager = DrawSummaryManager::initialize(0);
            $this->assertInstanceOf(DrawSummaryManager::class, $manager);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests constructor with large LMRId.
     */
    public function testConstructorWithLargeLMRId(): void
    {
        try {
            $largeLMRId = PHP_INT_MAX;
            $manager = new DrawSummaryManager($largeLMRId);
            $this->assertInstanceOf(DrawSummaryManager::class, $manager);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }
}
