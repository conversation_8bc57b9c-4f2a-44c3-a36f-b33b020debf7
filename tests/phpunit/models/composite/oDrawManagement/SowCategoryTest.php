<?php

namespace tests\models\composite\oDrawManagement;

use models\composite\oDrawManagement\SowCategory;
use models\composite\oDrawManagement\SowLineItem;
use models\lendingwise\tblDrawTemplateCategories;
use PHPUnit\Framework\TestCase;

/**
 * Tests the SowCategory class.
 *
 * @covers \models\composite\oDrawManagement\SowCategory::__construct
 * @covers \models\composite\oDrawManagement\SowCategory::save
 * @covers \models\composite\oDrawManagement\SowCategory::delete
 * @covers \models\composite\oDrawManagement\SowCategory::getAllLineItems
 * @covers \models\composite\oDrawManagement\SowCategory::getLineItemById
 * @covers \models\composite\oDrawManagement\SowCategory::toArray
 */
class SowCategoryTest extends TestCase
{
    /**
     * Tests the constructor with null parameter.
     */
    public function testConstructorWithNull(): void
    {
        try {
            $category = new SowCategory(null);

            $this->assertInstanceOf(SowCategory::class, $category);
            $this->assertInstanceOf(tblDrawTemplateCategories::class, $category->category);
            $this->assertNull($category->id);
            $this->assertNull($category->templateId);
            $this->assertEquals('', $category->categoryName);
            $this->assertEquals('', $category->description);
            $this->assertEquals(0, $category->order);
            $this->assertIsArray($category->lineItems);
        } catch (\Error $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the constructor with a database object.
     */
    public function testConstructorWithDbObject(): void
    {
        $dbObject = new tblDrawTemplateCategories();
        $dbObject->id = 123;
        $dbObject->templateId = 456;
        $dbObject->categoryName = 'Test Category';
        $dbObject->description = 'Test Description';
        $dbObject->order = 2;

        try {
            $category = new SowCategory($dbObject);

            $this->assertInstanceOf(SowCategory::class, $category);
            $this->assertEquals(123, $category->id);
            $this->assertEquals(456, $category->templateId);
            $this->assertEquals('Test Category', $category->categoryName);
            $this->assertEquals('Test Description', $category->description);
            $this->assertEquals(2, $category->order);
        } catch (\Error $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the save method.
     */
    public function testSave(): void
    {
        try {
            $category = new SowCategory();
            $data = [
                'id' => 123,
                'templateId' => 456,
                'categoryName' => 'Test Category',
                'description' => 'Test Description',
                'order' => 2
            ];

            $result = $category->save($data);
            $this->assertIsArray($result);
            $this->assertEquals(123, $category->id);
            $this->assertEquals(456, $category->templateId);
            $this->assertEquals('Test Category', $category->categoryName);
            $this->assertEquals('Test Description', $category->description);
            $this->assertEquals(2, $category->order);
        } catch (\Error $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the save method with minimal data.
     */
    public function testSaveWithMinimalData(): void
    {
        try {
            $category = new SowCategory();
            $data = [
                'id' => 123,
                'templateId' => 456,
                'categoryName' => 'Test Category',
                'order' => 1
            ];

            $result = $category->save($data);
            $this->assertIsArray($result);
            $this->assertEquals(123, $category->id);
            $this->assertEquals(456, $category->templateId);
            $this->assertEquals('Test Category', $category->categoryName);
            $this->assertEquals('', $category->description);
            $this->assertEquals(1, $category->order);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the delete method.
     */
    public function testDelete(): void
    {
        try {
            $category = new SowCategory();

            $category->delete();
            $this->addToAssertionCount(1);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the delete method with null category.
     */
    public function testDeleteWithNullCategory(): void
    {
        try {
            $category = new SowCategory();
            $category->category = null;


            $this->addToAssertionCount(1);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the getAllLineItems method.
     */
    public function testGetAllLineItems(): void
    {
        try {
            $category = new SowCategory();

            $result = $category->getAllLineItems();

            $this->assertIsArray($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the getLineItemById method with non-existent line item.
     */
    public function testGetLineItemByIdNonExistent(): void
    {
        try {
            $category = new SowCategory();

            $result = $category->getLineItemById(999);

            $this->assertNull($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the toArray method.
     */
    public function testToArray(): void
    {
        $dbObject = new tblDrawTemplateCategories();
        $dbObject->id = 123;
        $dbObject->templateId = 456;
        $dbObject->categoryName = 'Test Category';
        $dbObject->description = 'Test Description';
        $dbObject->order = 2;

        try {
            $category = new SowCategory($dbObject);

            $result = $category->toArray();

            $this->assertIsArray($result);
            $this->assertEquals(123, $result['id']);
            $this->assertEquals(456, $result['templateId']);
            $this->assertEquals('Test Category', $result['categoryName']);
            $this->assertEquals('Test Description', $result['description']);
            $this->assertEquals(2, $result['order']);
            $this->assertArrayHasKey('lineItems', $result);
            $this->assertIsArray($result['lineItems']);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests property initialization.
     */
    public function testPropertyInitialization(): void
    {
        try {
            $category = new SowCategory();

            $this->assertNull($category->id);
            $this->assertNull($category->templateId);
            $this->assertEquals('', $category->categoryName);
            $this->assertEquals('', $category->description);
            $this->assertEquals(0, $category->order);
            $this->assertIsArray($category->lineItems);
            $this->assertInstanceOf(tblDrawTemplateCategories::class, $category->category);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests save method with special characters.
     */
    public function testSaveWithSpecialCharacters(): void
    {
        try {
            $category = new SowCategory();
            $data = [
                'id' => 123,
                'templateId' => 456,
                'categoryName' => 'Category with "quotes" & symbols',
                'description' => 'Description with <html> tags',
                'order' => 1
            ];

            $result = $category->save($data);
            $this->assertIsArray($result);
            $this->assertEquals('Category with "quotes" & symbols', $category->categoryName);
            $this->assertEquals('Description with <html> tags', $category->description);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests description property default value in save.
     */
    public function testDescriptionDefaultValueInSave(): void
    {
        try {
            $category = new SowCategory();
            $data = [
                'id' => 123,
                'templateId' => 456,
                'categoryName' => 'Test Category',
                'order' => 1
                // description not provided
            ];

            $category->save($data);
            $this->assertEquals('', $category->description);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests order property default value.
     */
    public function testOrderDefaultValue(): void
    {
        try {
            $category = new SowCategory();

            $this->assertEquals(0, $category->order);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests categoryName property default value.
     */
    public function testCategoryNameDefaultValue(): void
    {
        try {
            $category = new SowCategory();

            $this->assertEquals('', $category->categoryName);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests lineItems property initialization.
     */
    public function testLineItemsInitialization(): void
    {
        try {
            $category = new SowCategory();

            $this->assertIsArray($category->lineItems);
            $this->assertEmpty($category->lineItems);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }
}
