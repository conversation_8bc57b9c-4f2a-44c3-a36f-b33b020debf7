<?php
global $fileTab, $fieldsInfo, $coBorDisp, $allowToEdit, $coBorrowerFName,
       $tabIndex, $hideThisField, $coBorrowerLName, $coBorrowerEmail,
       $coBPhoneNumber, $coBCellNumber, $coBorrowerFax,
       $coBServiceProvider, $coBorMailingAddrAsPresent,
       $coBPresentAddress, $coBPresentCity, $stateArray, $coBPresentState,
       $coBPresentZip, $tabIndexNo, $coBPresentPropType,
       $presentPropLengthTimeCoBor, $coBResidedPresentAddr, $coBorPreviousState,
       $coBFormerPropType, $coBMailingPropType, $coBorrowerDOB, $coborrowerPOB,
       $coBSsnNumber, $coBorDriverLicenseState, $coBorDriverLicenseNumber,
       $maritalStatusCoBor, $marriedToBor, $coBorrowerCitizenship, $coBorEquifaxScore,
       $midFicoScoreCoBor, $coBorTransunionScore, $coBorExperianScore,
       $coBorCreditScoreRange, $fileMC, $PCID;

use models\composite\oFile\getFileInfo\propertyCountyInfo;
use models\constants\gl\glCreditScore;
use models\constants\gl\glDate;
use models\constants\gl\glHMLOCreditScoreRange;
use models\constants\gl\glPCID;
use models\constants\gl\glPropTypeArray;
use models\constants\SMSServiceProviderArray;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\CustomField;
use models\lendingwise\tblFile;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Dates;
use models\standard\Strings;

$glPropTypeArray = glPropTypeArray::$glPropTypeArray;
$SMSServiceProviderArray = SMSServiceProviderArray::$SMSServiceProviderArray;
$glHMLOCreditScoreRange = glHMLOCreditScoreRange::$glHMLOCreditScoreRange;
$secArr = BaseHTML::sectionAccess2(['sId' => 'CBI', 'opt' => $fileTab]); // Get Active Fields only...
loanForm::pushSectionID('CBI');

$cBorPresentCountyInfo = propertyCountyInfo::getReport(LMRequest::$activeTab, $coBPresentState);
$cBorPreviousCountyInfo = propertyCountyInfo::getReport(LMRequest::$activeTab, $coBorPreviousState);

$countyArray = [];
foreach ($cBorPresentCountyInfo as $countyKey => $countyValue) {
    $countyArray[$countyValue['countyName']] = $countyValue['countyName'];
}

$previousCountyArray = [];
foreach ($cBorPreviousCountyInfo as $countyKey => $countyValue) {
    $previousCountyArray[$countyValue['countyName']] = $countyValue['countyName'];
}

?>
<!-- coborInformation.php -->
<div class="card card-custom HMLOLoanInfoSections coBorrowerSections coBorrowerSectionsCard CBI <?php if (count(Arrays::getValueFromArray('CBI', $fieldsInfo)) <= 0) {
    echo 'secHide';
} ?>" style="<?php echo $coBorDisp; ?>">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                <?php echo BaseHTML::getSectionHeading('CBI'); ?>
            </h3>
            <?php if (trim(BaseHTML::getSectionTooltip('CBI')) != '') { ?>&nbsp;
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="<?php echo BaseHTML::getSectionTooltip('CBI'); ?>"></i>
            <?php } ?>
        </div>
        <div class="card-toolbar ">
            <span class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                  data-card-tool="toggle"
                  data-section="coBorrowerSectionsCard"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none"
                  data-card-tool="reload"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </span>
            <span class="btn btn-icon btn-sm btn-hover-light-primary d-none"
                  data-card-tool="remove"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="Remove Card">
                <i class="ki ki-close icon-nm"></i>
            </span>
        </div>
    </div>
    <div class="card-body coBorrowerSectionsCard_body">
        <div class="row">
            <div class="form-group row col-md-6 <?php echo loanForm::showField('coBorrowerFName'); ?> ">
                <?php echo loanForm::label('coBorrowerFName', 'col-md-5 '); ?>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerFName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="text"
                               name="coBorrowerFName"
                               id="coBorrowerFName"
                               value="<?php echo htmlentities($coBorrowerFName); ?>"
                               maxlength="30"
                               autocomplete="off"
                               tabindex="<?php echo $tabIndex++ ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerFName', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                    <?php } else { ?>
                        <h5><?php echo $coBorrowerFName; ?></h5>
                    <?php } ?>
                </div>
            </div>
            <div class="form-group row col-md-6 <?php echo loanForm::showField('coBorrowerMName'); ?> ">
                <?php echo loanForm::label('coBorrowerMName', 'col-md-5 '); ?>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerMName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="text"
                               name="coBorrowerMName"
                               id="coBorrowerMName"
                               value="<?php echo htmlentities(LMRequest::myFileInfo()->file2Info()->coBorrowerMName); ?>"
                               maxlength="30"
                               autocomplete="off"
                               tabindex="<?php echo $tabIndex++ ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerMName', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                    <?php } else { ?>
                        <h5><?php echo htmlentities(LMRequest::myFileInfo()->file2Info()->coBorrowerMName); ?></h5>
                    <?php } ?>
                </div>
            </div>
            <?php if ($hideThisField) { ?>
                <div class="form-group row col-md-6 <?php echo loanForm::showField('coBorrowerLName'); ?> ">
                    <?php echo loanForm::label('coBorrowerLName', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="text"
                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerLName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="coBorrowerLName"
                                   id="coBorrowerLName"
                                   value="<?php echo htmlentities($coBorrowerLName); ?>"
                                   autocomplete="off"
                                   tabindex="<?php echo $tabIndex++ ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerLName', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        <?php } else { ?>
                            <h5><?php echo $coBorrowerLName; ?></h5>
                        <?php } ?>
                    </div>
                </div>
            <?php } ?>
            <?php if ($hideThisField) { ?>
                <div class="form-group row col-md-6 <?php echo loanForm::showField('coBorrowerEmail'); ?>">
                    <?php echo loanForm::label('coBorrowerEmail', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="email"
                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerEmail', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="coBorrowerEmail"
                                   id="coBorrowerEmail"
                                   tabindex="<?php echo $tabIndex++; ?>"
                                   maxlength="75"
                                   size="40"
                                   value="<?php echo $coBorrowerEmail ?>"
                                   onblur="fieldValidation(this.id,this.name);"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerEmail', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        <?php } else { ?>
                            <h5><?php echo $coBorrowerEmail; ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <div id="showClientEmailExists" class="left"></div>
                <div id="divLo1" class="left pad2" style="display:none;">
                    Please Wait... <img src="<?php echo CONST_SITE_URL; ?>assets/images/ajax-loader.gif" alt="">
                </div>
            <?php } ?>

            <?php if ($hideThisField) { ?>
                <!--phone num start -->
                <div class="form-group row col-md-6  <?php echo loanForm::showField('coBPhoneNumber'); ?>">
                    <?php echo loanForm::label('coBPhoneNumber', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control input-sm mask_cellnew <?php echo BaseHTML::fieldAccess(['fNm' => 'coBPhoneNumber', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="coBPhoneNumber"
                                   id="coBPhoneNumber"
                                   tabindex="<?php echo $tabIndex++; ?>"
                                   value="<?php echo Strings::formatPhoneNumber($coBPhoneNumber); ?>"
                                   autocomplete="off"
                                   type="text"
                                   placeholder="(___) ___ - ____"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'coBPhoneNumber', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                        <?php } else { ?>
                            <h5><?php echo Strings::formatPhoneNumber($coBPhoneNumber) ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <!--phone num end -->
            <?php } ?>

            <div class="clearfix"></div>
            <?php if ($hideThisField) { ?>
                <!--cell num start -->
                <div class="form-group row col-md-6 <?php echo loanForm::showField('coBCellNumber'); ?> ">
                    <?php echo loanForm::label('coBCellNumber', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control input-sm mask_cellnew
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'coBCellNumber', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="coBCellNumber"
                                   id="coBCellNumber"
                                   tabindex="<?php echo $tabIndex++; ?>"
                                   value="<?php echo Strings::formatCellNumber($coBCellNumber); ?>"
                                   autocomplete="off"
                                   type="text"
                                   placeholder="(___) ___ - ____"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'coBCellNumber', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                        <?php } else { ?>
                            <h5><?php echo Strings::formatCellNumber($coBCellNumber) ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <!--cell num end -->
            <?php } ?>

            <?php if ($hideThisField) { ?>
                <!--Fax start -->
                <div class="form-group row col-md-6 <?php echo loanForm::showField('coBFax'); ?> ">
                    <?php echo loanForm::label('coBFax', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control input-sm mask_cellnew <?php echo BaseHTML::fieldAccess(['fNm' => 'coBFax', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="coBFax"
                                   id="coBFax"
                                   tabindex="<?php echo $tabIndex++; ?>"
                                   value="<?php echo Strings::formatCellNumber($coBorrowerFax); ?>"
                                   autocomplete="off"
                                   type="text"
                                   placeholder="(___) ___ - ____"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'coBFax', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                        <?php } else { ?>
                            <h5><?php echo Strings::formatCellNumber($coBorrowerFax) ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <!--Fax end -->
            <?php } ?>

            <?php if ($hideThisField) { ?>
                <!--coBor Service Provider start-->
                <div class="form-group row col-md-6 <?php echo loanForm::showField('coBServiceProvider'); ?>">
                    <?php echo loanForm::label('coBServiceProvider', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <select class="form-control input-sm
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'coBServiceProvider', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    name="coBServiceProvider"
                                    id="coBServiceProvider"
                                    tabindex="<?php echo $tabIndex++; ?>"
                                    autocomplete="off"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'coBServiceProvider', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                <option value=''> - Select -</option>
                                <?php
                                $SMSServiceProviderKeyArray = [];
                                $SMSServiceProviderKeyArray = array_keys($SMSServiceProviderArray);
                                for ($j = 0; $j < count($SMSServiceProviderKeyArray); $j++) {
                                    $sOpt = '';
                                    $servicePr = '';
                                    $servicePr = trim($SMSServiceProviderKeyArray[$j]);
                                    $sOpt = Arrays::isSelected($servicePr, $coBServiceProvider);
                                    echo "<option value=\"" . trim($servicePr) . "\" " . $sOpt . '>' . trim($SMSServiceProviderArray[$servicePr]) . '</option>';
                                }
                                ?>
                            </select>
                        <?php } else { ?>
                            <h5><?php echo $coBServiceProvider; ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <!--coBor Service Provider end-->
            <?php } ?>


            <!--coBor Mailing Addr As Present start-->
            <div class="form-group row col-md-6  <?php echo loanForm::showField('coBorMailingAddrAsPresent'); ?> ">
                <?php echo loanForm::label('coBorMailingAddrAsPresent', 'col-md-5 '); ?>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <div class="switch switch-icon">
                            <label class="font-weight-bold">
                                <input class="form-control" <?php if ($coBorMailingAddrAsPresent == '1') { ?> checked="checked" <?php } ?>
                                       id="iscoborrowerPreAdd" type="checkbox"
                                       onchange="toggleSwitch('iscoborrowerPreAdd', 'coBorMailingAddrAsPresent', '1', '0' );">
                                <input type="hidden" name="coBorMailingAddrAsPresent" id="coBorMailingAddrAsPresent"
                                       value="<?php echo $coBorMailingAddrAsPresent; ?>">
                                <span></span>
                            </label>
                        </div>
                    <?php } else {
                        if ($coBorMailingAddrAsPresent == '1') {
                            echo '<h5>Yes</h5>';
                        } else {
                            echo '<h5>No</h5>';
                        }
                    } ?>
                </div>
            </div>

            <!--coBor Mailing Addr As Present end-->


            <?php if ($hideThisField) { ?>
                <!--coBor Present Address start-->
                <div class="form-group row col-md-6 <?php echo loanForm::showField('coBPresentAddress'); ?>">
                    <script>
                        $(document).ready(function() {
                            $('#coBPresentAddress').on('input', function() {
                                address_lookup.InitLegacy($(this));
                            });
                        });
                    </script>
                    <?php echo loanForm::label('coBPresentAddress', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="text"
                                   class="form-control input-sm
                                   <?php echo BaseHTML::fieldAccess(['fNm' => 'coBPresentAddress', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="coBPresentAddress"
                                   id="coBPresentAddress"
                                   data-address="coBPresentAddress"
                                   data-city="coBPresentCity"
                                   data-state="coBPresentState"
                                   data-zip="coBPresentZip"
                                   data-unit="coBorrowerUnit"
                                   data-county="coBorrowerCounty"
                                   tabindex="<?php echo $tabIndex++; ?>"
                                   value="<?php echo htmlentities($coBPresentAddress); ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'coBPresentAddress', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                        <?php } else { ?><h5><?php echo $coBPresentAddress ?></h5><?php } ?>
                    </div>
                </div>
                <!--coBor Present Address end-->
            <?php } ?>
            <?php if ($hideThisField) { ?>
                <div class="form-group row col-md-6  <?php echo loanForm::showField('coBorrowerUnit'); ?>">
                    <?php echo loanForm::label('coBorrowerUnit', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="text"
                                   class="form-control input-sm
                               <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerUnit', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="coBorrowerUnit"
                                   id="coBorrowerUnit"
                                   tabindex="<?php echo $tabIndex++; ?>"
                                   value="<?php echo LMRequest::myFileInfo()->file2Info()->coBorrowerUnit; ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerUnit', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                        <?php } else { ?>
                            <h5><?php echo LMRequest::myFileInfo()->file2Info()->coBorrowerUnit; ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <!--coBor Present City start-->
                <div class="form-group row col-md-6  <?php echo loanForm::showField('coBPresentCity'); ?>">
                    <?php echo loanForm::label('coBPresentCity', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="text"
                                   class="form-control input-sm
                               <?php echo BaseHTML::fieldAccess(['fNm' => 'coBPresentCity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="coBPresentCity"
                                   id="coBPresentCity"
                                   tabindex="<?php echo $tabIndex++; ?>"
                                   value="<?php echo $coBPresentCity ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'coBPresentCity', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                        <?php } else { ?>
                            <h5><?php echo $coBPresentCity ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <!--coBor Present City end-->
            <?php } ?>
            <?php if ($hideThisField) { ?>
                <!--coBor Present State start-->
                <div class="form-group row col-md-6 <?php echo loanForm::showField('coBPresentState'); ?> ">
                    <?php echo loanForm::label('coBPresentState', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <select class="form-control input-sm
                        <?php echo BaseHTML::fieldAccess(['fNm' => 'coBPresentState', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    name="coBPresentState"
                                    id="coBPresentState"
                                    onchange="populateStateCounty('loanModForm', 'coBPresentState', 'coBorrowerCounty')"
                                    tabindex="<?php echo $tabIndex++; ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'coBPresentState', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                <option value=''> - Select -</option>
                                <?php
                                for ($j = 0; $j < count($stateArray ?? []); $j++) {
                                    $sOpt = '';
                                    $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $coBPresentState);
                                    echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                                }
                                ?>
                            </select>
                        <?php } else { ?>
                            <h5><?php echo $coBPresentState ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <!--coBor Present State end-->
            <?php } ?>
            <?php if ($hideThisField) { ?>
                <!--coBor Present Zip start-->
                <div class="form-group row col-md-6 <?php echo loanForm::showField('coBPresentZip'); ?> ">
                    <?php echo loanForm::label('coBPresentZip', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control zipCode  input-sm
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'coBPresentZip', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="text"
                                   name="coBPresentZip"
                                   id="coBPresentZip"
                                   tabindex="<?php echo $tabIndex++; ?>"
                                   value="<?php echo $coBPresentZip ?>"
                                   autocomplete="off"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'coBPresentZip', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                        <?php } else { ?>
                            <h5><?php echo $coBPresentZip ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <!--coBor Present Zip end-->

                <div class="form-group row col-md-6 <?php echo loanForm::showField('coBorrowerCounty'); ?>">
                        <?php echo loanForm::label('coBorrowerCounty', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php echo loanForm::select(
                                'coBorrowerCounty',
                                $allowToEdit,
                                $tabIndex++,
                                LMRequest::myFileInfo()->file2Info()->coBorrowerCounty,
                                $countyArray,
                                '',
                                ' input-sm ',
                                '- Select -',
                            ); ?>
                        </div>
                </div>
            <?php } ?>
            <!--coBor Present Prop Type start-->
            <div class="form-group row col-md-6 <?php echo loanForm::showField('coBPresentPropType'); ?> ">
                <?php echo loanForm::label('coBPresentPropType', 'col-md-5 '); ?>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <select name="coBPresentPropType"
                                id="coBPresentPropType"
                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'coBPresentPropType', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                tabindex="<?php echo $tabIndexNo++; ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'coBPresentPropType', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                            <option value=""> - Select -</option>
                            <?php
                            for ($i = 0; $i < count($glPropTypeArray ?? []); $i++) {
                                $sOpt = '';
                                $glPropType = '';
                                $glPropType = trim($glPropTypeArray[$i]);
                                $sOpt = Arrays::isSelected($glPropType, $coBPresentPropType);
                                echo "<option value=\"" . $glPropType . "\" " . $sOpt . '>' . $glPropType . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php echo $coBPresentPropType; ?></h5>
                    <?php } ?>
                </div>
            </div>
            <!--coBor Present Prop Type end-->

            <div class="form-group row col-md-6 <?php echo loanForm::showField('cobor_guarantee'); ?>">
                <?php echo loanForm::label('cobor_guarantee', 'col-md-6 '); ?>
                <div class="col-md-6">
                    <?php if ($allowToEdit) { ?>
                        <div class="radio-inline">
                            <label class="radio radio-solid font-weight-bold" for="cobor_guarantee_radio1">
                                <input type="radio"
                                       name="cobor_guarantee"
                                       id="cobor_guarantee_radio1"
                                       value="Yes"
                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'cobor_guarantee', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       tabindex="<?php echo $tabIndexNo++; ?>"
                                    <?php echo Strings::isChecked('Yes', Strings::showField('cobor_guarantee', 'file2Info')); ?>
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'cobor_guarantee', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                <span></span>Yes
                            </label>
                            <label class="radio radio-solid font-weight-bold" for="cobor_guarantee_radio2">
                                <input type="radio"
                                       name="cobor_guarantee"
                                       id="cobor_guarantee_radio2"
                                       value="No"
                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'cobor_guarantee', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       tabindex="<?php echo $tabIndexNo++; ?>"
                                    <?php echo Strings::isChecked('No', Strings::showField('cobor_guarantee', 'file2Info')); ?>
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'cobor_guarantee', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                <span></span>No
                            </label>
                            <label class="radio radio-solid font-weight-bold" for="cobor_guarantee_radio3">
                                <input type="radio"
                                       name="cobor_guarantee"
                                       id="cobor_guarantee_radio3"
                                       value="NA"
                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'cobor_guarantee', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       tabindex="<?php echo $tabIndexNo++; ?>"
                                    <?php echo Strings::isChecked('NA', Strings::showField('cobor_guarantee', 'file2Info')); ?>
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'cobor_guarantee', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                <span></span>NA
                            </label>
                        </div>
                    <?php } else { ?>
                        <h5><?php echo Strings::showField('cobor_guarantee', 'file2Info'); ?></h5>
                    <?php } ?>
                </div>
            </div>


            <!-- Length of Time at address start -->
            <div class="form-group row col-md-6  <?php echo loanForm::showField('presentPropLengthTimeCoBor'); ?> ">
                <?php echo loanForm::label('presentPropLengthTimeCoBor', 'col-md-5 '); ?>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input type="text"
                               name="presentPropLengthTimeCoBor"
                               id="presentPropLengthTimeCoBor"
                               class="form-control input-sm
                               <?php echo BaseHTML::fieldAccess(['fNm' => 'presentPropLengthTimeCoBor', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               value="<?php echo $presentPropLengthTimeCoBor; ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'presentPropLengthTimeCoBor', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                    <?php } else { ?>
                        <h5><?php echo $presentPropLengthTimeCoBor; ?></h5>
                    <?php } ?>
                </div>
            </div>
            <!-- Length of Time at address end -->

            <!--coBor Resided Present Addr start-->
            <div class="form-group row col-md-12 <?php echo loanForm::showField('coBResidedPresentAddr'); ?>">
                <?php echo loanForm::label('coBResidedPresentAddr', 'col-md-6 '); ?>
                <div class="col-md-6">
                    <?php if ($allowToEdit) { ?>
                        <div class="radio-inline">
                            <label class="radio radio-solid font-weight-bold" for="coBResidedPresentAddr_radio1">
                                <input type="radio"
                                       name="coBResidedPresentAddr"
                                       id="coBResidedPresentAddr_radio1"
                                       value="Yes"
                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'coBResidedPresentAddr', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       tabindex="<?php echo $tabIndexNo++; ?>"
                                    <?php echo Strings::isChecked('Yes', $coBResidedPresentAddr); ?>
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'coBResidedPresentAddr', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                       onclick="showFormerAddrDiv(this.value, 'CoBor');hideAndShowAcceptPurchaseAgreement(this.value, 'coBResidedPresentAddrDispOpt'); ">
                                <span></span>Yes
                            </label>
                            <label class="radio radio-solid font-weight-bold" for="coBResidedPresentAddr_radio2">
                                <input type="radio"
                                       name="coBResidedPresentAddr"
                                       id="coBResidedPresentAddr_radio2"
                                       value="No"
                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'coBResidedPresentAddr', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       tabindex="<?php echo $tabIndexNo++; ?>"
                                    <?php echo Strings::isChecked('No', $coBResidedPresentAddr); ?>
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'coBResidedPresentAddr', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                       onclick="showFormerAddrDiv(this.value, 'CoBor');hideAndShowAcceptPurchaseAgreement(this.value, 'coBResidedPresentAddrDispOpt');">
                                <span></span>No
                            </label>
                        </div>
                    <?php } else { ?>
                        <h5><?php echo $coBResidedPresentAddr; ?></h5>
                    <?php } ?>
                </div>
            </div>
            <!--coBor Resided Present Addr end-->


            <!--coBor Resided Present Addr related fields start-->
            <div class="coBResidedPresentAddrDispOpt col-md-12  <?php echo BaseHTML::parentFieldAccess(['fNm' => 'coBResidedPresentAddr', 'sArr' => $secArr, 'pv' => $coBResidedPresentAddr, 'av' => 'Yes']); ?>">
                <script>
                    $(document).ready(function() {
                        $('#coBorPreviousAddress').on('input', function() {
                            address_lookup.InitLegacy($(this));
                        });
                    });
                </script>
                <div class="row">
                    <?php if ($hideThisField) { ?>
                        <div class="form-group row col-md-6 <?php echo loanForm::showField('coBorPreviousAddress'); ?>">
                            <?php echo loanForm::label('coBorPreviousAddress', 'col-md-5 '); ?>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input type="text"
                                           class="form-control input-sm
                                       <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorPreviousAddress', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="coBorPreviousAddress"
                                           id="coBorPreviousAddress"
                                           data-address="coBorPreviousAddress"
                                           data-city="coBorPreviousCity"
                                           data-state="coBorPreviousState"
                                           data-zip="coBorPreviousZip"
                                           data-unit="coBorrowerPreviousUnit"
                                           data-county="coBorrowerPreviousCounty"
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           value="<?php echo htmlentities(Strings::showField('coBorPreviousAddress', 'LMRInfo')); ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorPreviousAddress', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('coBorPreviousAddress', 'LMRInfo') ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                        <div class="form-group row col-md-6 <?php echo loanForm::showField('coBorrowerPreviousUnit'); ?>">
                            <?php echo loanForm::label('coBorrowerPreviousUnit', 'col-md-5 '); ?>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input type="text"
                                           class="form-control input-sm
                                       <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerPreviousUnit', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="coBorrowerPreviousUnit"
                                           id="coBorrowerPreviousUnit"
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           value="<?php echo LMRequest::myFileInfo()->file2Info()->coBorrowerPreviousUnit ; ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerPreviousUnit', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('coBorrowerPreviousUnit', 'LMRInfo') ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    <?php } ?>
                    <?php if ($hideThisField) { ?>
                        <div class="form-group row col-md-6 <?php echo loanForm::showField('coBorPreviousCity'); ?>">
                            <?php echo loanForm::label('coBorPreviousCity', 'col-md-5 '); ?>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input type="text"
                                           class="form-control input-sm
                                       <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorPreviousCity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="coBorPreviousCity"
                                           id="coBorPreviousCity"
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           value="<?php echo htmlentities(Strings::showField('coBorPreviousCity', 'LMRInfo')); ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorPreviousCity', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('coBorPreviousCity', 'LMRInfo') ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    <?php } ?>
                    <?php if ($hideThisField) { ?>
                        <div class="form-group row col-md-6 <?php echo loanForm::showField('coBorPreviousState'); ?>">
                            <?php echo loanForm::label('coBorPreviousState', 'col-md-5 '); ?>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <select class="form-control input-sm
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorPreviousState', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            name="coBorPreviousState"
                                            id="coBorPreviousState"
                                            onchange="populateStateCounty('loanModForm', 'coBorPreviousState', 'coBorrowerPreviousCounty')"
                                            tabindex="<?php echo $tabIndex++; ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorPreviousState', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <option value=''> - Select -</option>
                                        <?php
                                        for ($j = 0; $j < count($stateArray); $j++) {
                                            $sOpt = '';
                                            $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $coBorPreviousState);
                                            echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                                        } ?>
                                    </select>
                                <?php } else { ?>
                                    <h5><?php echo $coBorPreviousState; ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    <?php } ?>
                    <div class="form-group row col-md-6 <?php echo loanForm::showField('coBorPreviousZip'); ?>">
                        <?php echo loanForm::label('coBorPreviousZip', 'col-md-5 '); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control zipCode  input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorPreviousZip', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       type="text"
                                       name="coBorPreviousZip"
                                       id="coBorPreviousZip"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo Strings::showField('coBorPreviousZip', 'LMRInfo') ?>"
                                       autocomplete="off"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorPreviousZip', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                            <?php } else { ?>
                                <h5><?php echo Strings::showField('coBorPreviousZip', 'LMRInfo') ?></h5>
                            <?php } ?>
                        </div>
                    </div>

                    <div class="form-group row col-md-6 <?php echo loanForm::showField('coBorrowerPreviousCounty'); ?>">
                        <?php echo loanForm::label('coBorrowerPreviousCounty', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php echo loanForm::select(
                                'coBorrowerPreviousCounty',
                                $allowToEdit,
                                $tabIndex++,
                                LMRequest::myFileInfo()->file2Info()->coBorrowerPreviousCounty,
                                $previousCountyArray,
                                '',
                                ' input-sm ',
                                '- Select -',
                            ); ?>
                        </div>
                    </div>


                    <div class="form-group row col-md-6 <?php echo loanForm::showField('coBNoOfYrAtPrevAddr'); ?>">
                        <?php echo loanForm::label('coBNoOfYrAtPrevAddr', 'col-md-5 '); ?>
                        <div class="col-md-7 ">
                            <?php if ($allowToEdit) { ?>
                                <input type="text"
                                       class="form-control  input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'coBNoOfYrAtPrevAddr', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       name="coBNoOfYrAtPrevAddr" id="coBNoOfYrAtPrevAddr"
                                       tabindex="<?php echo $tabIndexNo++; ?>"
                                       value="<?php echo Strings::showField('coBNoOfYrAtPrevAddr', 'fileLoanOriginationInfo'); ?>"
                                       size="20" maxlength="30"
                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'coBNoOfYrAtPrevAddr', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                            <?php } else { ?>
                                <h5><?php echo Strings::showField('coBNoOfYrAtPrevAddr', 'fileLoanOriginationInfo'); ?></h5>
                            <?php } ?>
                        </div>
                    </div>

                    <div class="form-group row col-md-6 <?php echo loanForm::showField('coBFormerPropType'); ?>">
                        <?php echo loanForm::label('coBFormerPropType', 'col-md-5 '); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <select class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'coBFormerPropType', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        name="coBFormerPropType"
                                        id="coBFormerPropType"
                                        tabindex="<?php echo $tabIndexNo++; ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'coBFormerPropType', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <option value=""> - Select -</option>
                                    <?php
                                    for ($i = 0; $i < count($glPropTypeArray ?? []); $i++) {
                                        $sOpt = '';
                                        $glPropType = '';
                                        $glPropType = trim($glPropTypeArray[$i]);
                                        $sOpt = Arrays::isSelected($glPropType, $coBFormerPropType);
                                        echo "<option value=\"" . $glPropType . "\" " . $sOpt . '>' . $glPropType . '</option>';
                                    } ?>
                                </select>
                            <?php } else { ?>
                                <h5><?php echo $coBFormerPropType; ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            </div>
            <!--coBor Resided Present Addr related fields start-->


            <div class="form-group row col-md-6 <?php echo loanForm::showField('coBMailingPropType'); ?>">
                <?php echo loanForm::label('coBMailingPropType', 'col-md-5 '); ?>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <select name="coBMailingPropType"
                                id="coBMailingPropType"
                                class="form-control input-sm
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'coBMailingPropType', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                tabindex="<?php echo $tabIndexNo++; ?>">
                            <option value=""> - Select -</option>
                            <?php
                            for ($i = 0; $i < count($glPropTypeArray); $i++) {
                                $sOpt = '';
                                $glPropType = '';
                                $glPropType = trim($glPropTypeArray[$i]);
                                $sOpt = Arrays::isSelected($glPropType, $coBMailingPropType);
                                echo "<option value=\"" . $glPropType . "\" " . $sOpt . '>' . $glPropType . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php echo $coBMailingPropType; ?></h5>
                    <?php } ?>
                </div>
            </div>
            <?php if ($hideThisField) { ?>
                <!-- Co-Borrower Mailing Address-Start -->
                <?php require 'coBorMailingAddress.php'; ?>
                <!-- Co-Borrower Mailing Address-End -->
            <?php } ?>

            <div class="form-group row col-lg-12 m-0 mb-4 px-0" id="copersonalInfotitle">
                <label class="bg-secondary  py-2  col-lg-12"><b><?php echo BaseHTML::getSubSectionHeading('CBI', 'coborrowerPersonalInfoSubSection'); ?></b></label>
            </div>
            <?php if ($hideThisField) { ?>
                <!--DOB start -->
                <div class="copersonalInfo row form-group col-md-6 <?php echo loanForm::showField('coBorrowerDOB'); ?> ">
                    <?php echo loanForm::label('coBorrowerDOB', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend coBorrowerDOB">
                                    <span class="input-group-text">
                                    <i class="fa fa-calendar text-primary"></i>
                                    </span>
                                </div>
                                <input class="form-control input-sm
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerDOB', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                       name="coBorrowerDOB"
                                       id="coBorrowerDOB"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo $coBorrowerDOB ?>"
                                       data-date-dob-start-date="<?php echo glDate::getMinRequirementDate(); ?>"
                                       data-date-dob-end-date="<?php echo glDate::getMaxRequirementDate(); ?>"
                                       autocomplete="off"
                                       maxlength="10"
                                       placeholder="MM/DD/YYYY"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerDOB', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                            </div>
                        <?php } else { ?>
                            <h5><?php echo $coBorrowerDOB; ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <!--DOB end -->
                <!--Co Borrower Place Of Birth start -->
                <div class="copersonalInfo row form-group col-md-6 <?php echo loanForm::showField('coborrowerPOB'); ?> ">
                    <?php echo loanForm::label('coborrowerPOB', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control input-sm
                        <?php echo BaseHTML::fieldAccess(['fNm' => 'coborrowerPOB', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="coborrowerPOB"
                                   id="coborrowerPOB"
                                   type="text"
                                   tabindex="<?php echo $tabIndex++; ?>"
                                   value="<?php echo $coborrowerPOB; ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'coborrowerPOB', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                        <?php } else { ?>
                            <h5><?php echo $coborrowerPOB; ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <!--Co Borrower Place Of Birth end -->
            <?php } ?>
            <?php if ($hideThisField) { ?>
                <!--ssn start -->
                <div class="copersonalInfo row form-group col-md-6 <?php echo loanForm::showField('coBSsnNumber'); ?> ">
                    <?php echo loanForm::label('coBSsnNumber', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control input-sm mask_ssn
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'coBSsnNumber', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="coBSsnNumber"
                                   id="coBSsnNumber"
                                   type="text"
                                   tabindex="<?php echo $tabIndex++; ?>"
                                   placeholder="___ - __ - ____"
                                   value="<?php echo Strings::formatSSNNumber($coBSsnNumber); ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'coBSsnNumber', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                        <?php } else { ?>
                            <h5><?php echo Strings::formatSSNNumber($coBSsnNumber) ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <!--ssn end -->
            <?php } ?>
            <?php if ($hideThisField) { ?>
                <!--coBor Driver LicenseState start-->
                <div class="copersonalInfo row form-group col-md-6 <?php echo loanForm::showField('coBorDriverLicenseState'); ?> ">
                    <?php echo loanForm::label('coBorDriverLicenseState', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <select class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorDriverLicenseState', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    name="coBorDriverLicenseState"
                                    id="coBorDriverLicenseState"
                                    tabindex="<?php echo $tabIndex++; ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorDriverLicenseState', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                <option value=''> - Select -</option>
                                <?php
                                for ($j = 0; $j < count($stateArray); $j++) {
                                    $sOpt = '';
                                    $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $coBorDriverLicenseState);
                                    echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                                }
                                ?>
                            </select>
                        <?php } else { ?>
                            <h5><?php echo $coBorDriverLicenseState ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <!--coBor Driver LicenseState end-->
            <?php } ?>
            <?php if ($hideThisField) { ?>
                <!--coBor Driver License Num start-->
                <div class="copersonalInfo row form-group col-md-6 <?php echo loanForm::showField('coBorDriverLicenseNumber'); ?> ">
                    <?php echo loanForm::label('coBorDriverLicenseNumber', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorDriverLicenseNumber', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="text"
                                   name="coBorDriverLicenseNumber"
                                   maxlength="20"
                                   id="coBorDriverLicenseNumber"
                                   value="<?php echo htmlentities($coBorDriverLicenseNumber); ?>"
                                   tabindex="<?php echo $tabIndex++; ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorDriverLicenseNumber', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                        <?php } else { ?>
                            <h5><?php echo $coBorDriverLicenseNumber ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <!--coBor Driver License Num end-->
            <?php } ?>

            <div class="copersonalInfo row form-group col-md-6 <?php echo loanForm::showField('coborLicenseIssuance'); ?> ">
                <?php echo loanForm::label('coborLicenseIssuance', 'col-md-5 '); ?>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend coborLicenseIssuance">
                                    <span class="input-group-text">
                                    <i class="fa fa-calendar text-primary"></i>
                                    </span>
                            </div>
                            <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'coborLicenseIssuance', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass "
                                   name="coborLicenseIssuance"
                                   id="coborLicenseIssuance"
                                   tabindex="<?php echo $tabIndex++; ?>"
                                   value="<?php echo Dates::formatDateWithRE(Strings::showField('coborLicenseIssuance', 'file2Info'), 'YMD', 'm/d/Y') ?>"
                                   autocomplete="off"
                                   maxlength="10"
                                   placeholder="MM/DD/YYYY"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'coborLicenseIssuance', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    <?php } else { ?>
                        <h5><?php echo Dates::formatDateWithRE(Strings::showField('coborLicenseIssuance', 'file2Info'), 'YMD', 'm/d/Y'); ?></h5>
                    <?php } ?>
                </div>
            </div>

            <div class="copersonalInfo row form-group col-md-6 <?php echo loanForm::showField('coborLicenseExpiration'); ?> ">
                <?php echo loanForm::label('coborLicenseExpiration', 'col-md-5 '); ?>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend coborLicenseExpiration">
                                    <span class="input-group-text">
                                    <i class="fa fa-calendar text-primary"></i>
                                    </span>
                            </div>
                            <input class="form-control input-sm
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'coborLicenseExpiration', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                   name="coborLicenseExpiration"
                                   id="coborLicenseExpiration"
                                   tabindex="<?php echo $tabIndex++; ?>"
                                   value="<?php echo Dates::formatDateWithRE(Strings::showField('coborLicenseExpiration', 'file2Info'), 'YMD', 'm/d/Y') ?>"
                                   autocomplete="off"
                                   maxlength="10"
                                   placeholder="MM/DD/YYYY"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'coborLicenseExpiration', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    <?php } else { ?>
                        <h5><?php echo Dates::formatDateWithRE(Strings::showField('coborLicenseExpiration', 'file2Info'), 'YMD', 'm/d/Y'); ?></h5>
                    <?php } ?>
                </div>
            </div>
            <?php if ($hideThisField) { ?>
                <!--coBor Marital Status start-->
                <div class="copersonalInfo row form-group col-md-6 <?php echo loanForm::showField('maritalStatusCoBor'); ?>">
                    <?php echo loanForm::label('maritalStatusCoBor', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold" for="maritalStatusCoBor_1">
                                    <input type="radio" name="maritalStatusCoBor" id="maritalStatusCoBor_1"
                                           value="Unmarried" <?php echo Strings::isChecked('Unmarried', $maritalStatusCoBor); ?>
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'maritalStatusCoBor', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'maritalStatusCoBor', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>
                                    Unmarried
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="maritalStatusCoBor_2">
                                    <input type="radio" name="maritalStatusCoBor" id="maritalStatusCoBor_2"
                                           value="Married" <?php echo Strings::isChecked('Married', $maritalStatusCoBor); ?>
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'maritalStatusCoBor', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'maritalStatusCoBor', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>
                                    Married
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="maritalStatusCoBor_3">

                                    <input type="radio" name="maritalStatusCoBor" id="maritalStatusCoBor_3"
                                           value="Separated" <?php echo Strings::isChecked('Separated', $maritalStatusCoBor); ?>
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'maritalStatusCoBor', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'maritalStatusCoBor', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>
                                    Separated
                                </label>
                            </div>
                        <?php } else { ?>
                            <h5><?php echo $maritalStatusCoBor; ?></h5>
                        <?php } ?>
                    </div>
                </div>

                <div class="copersonalInfo row form-group col-md-6 <?php echo loanForm::showField('marriedToBor'); ?> ">
                    <?php echo loanForm::label('marriedToBor', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold"
                                       for="marriedToBor_1">
                                    <input type="radio"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'marriedToBor', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="marriedToBor"
                                           id="marriedToBor_1"
                                           value="Yes"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $marriedToBor); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'marriedToBor', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span>Yes
                                </label>
                                <label class="radio radio-solid font-weight-bold"
                                       for="marriedToBor_2">
                                    <input type="radio"
                                           name="marriedToBor"
                                           id="marriedToBor_2"
                                           value="No"
                                           tabindex="<?php echo $tabIndex++; ?>"
                                        <?php echo Strings::isChecked('No', $marriedToBor); ?>
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'marriedToBor', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <span></span>No
                                </label>
                            </div>
                        <?php } else { ?>
                            <h5><?php echo $marriedToBor; ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <!--coBor Marital Status end-->
            <?php } ?>
            <!--coBor Citizenship start-->
            <div class="copersonalInfo row form-group col-md-6 <?php echo loanForm::showField('coBorrowerCitizenship'); ?>">
                <?php echo loanForm::label('coBorrowerCitizenship', 'col-md-5 '); ?>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <div class="radio-inline">
                            <label class="radio radio-solid font-weight-bold"
                                   for="coBorrowerCitizenship_1">
                                <input type="radio" name="coBorrowerCitizenship"
                                       id="coBorrowerCitizenship_1"
                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerCitizenship', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       value="U.S. Citizen"
                                       onclick="assignValueTOAreYouUSCitizenCoBor(this.value)"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerCitizenship', 'sArr' => $secArr, 'opt' => 'I']); ?> <?php echo Strings::isChecked('U.S. Citizen', $coBorrowerCitizenship); ?> >
                                <span></span> U.S. Citizen
                            </label>
                            <label class="radio radio-solid font-weight-bold" for="coBorrowerCitizenship_2">
                                <input type="radio"
                                       name="coBorrowerCitizenship"
                                       id="coBorrowerCitizenship_2"
                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerCitizenship', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       value="Perm Resident Alien"
                                       onclick="assignValueTOAreYouUSCitizenCoBor(this.value)"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerCitizenship', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                    <?php echo Strings::isChecked('Perm Resident Alien', $coBorrowerCitizenship); ?> >
                                <span></span>
                                Perm Resident
                            </label>
                            <label class="radio radio-solid font-weight-bold"
                                   for="coBorrowerCitizenship_3">
                                <input type="radio"
                                       name="coBorrowerCitizenship"
                                       id="coBorrowerCitizenship_3"
                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerCitizenship', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       value="Non-Perm Resident Alien"
                                       onclick="assignValueTOAreYouUSCitizenCoBor(this.value)"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerCitizenship', 'sArr' => $secArr, 'opt' => 'I']); ?> <?php echo Strings::isChecked('Non-Perm Resident Alien', $coBorrowerCitizenship); ?> ><span></span>
                                Non-Perm Resident
                            </label>
                            <label class="radio radio-solid font-weight-bold"
                                   for="coBorrowerCitizenship_4">
                                <input type="radio"
                                       name="coBorrowerCitizenship"
                                       id="coBorrowerCitizenship_4"
                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerCitizenship', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       value="Foreign National"
                                       onclick="assignValueTOAreYouUSCitizenCoBor(this.value)"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerCitizenship', 'sArr' => $secArr, 'opt' => 'I']); ?> <?php echo Strings::isChecked('Foreign National', $coBorrowerCitizenship); ?> ><span></span>
                                Foreign National
                            </label>
                        </div>
                    <?php } else { ?>
                        <h5><?php echo $coBorrowerCitizenship; ?></h5>
                    <?php } ?>
                </div>
            </div>
            <!--coBor Citizenship end-->

            <!--coBor Equifax Score start-->
            <div class="copersonalInfo row form-group col-md-6 <?php echo loanForm::showField('coBorEquifaxScore'); ?> ">
                <?php echo loanForm::label('coBorEquifaxScore', 'col-md-5 '); ?>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorEquifaxScore', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="text"
                               name="coBorEquifaxScore"
                               id="coBorEquifaxScore"
                               value="<?php echo $coBorEquifaxScore; ?>"
                               tabindex="<?php echo $tabIndex++; ?>"
                               placeholder="<?php echo glCreditScore::getRange(); ?>"
                               inputmode="numeric"
                               maxlength="<?php echo glCreditScore::getMaxLength(); ?>"
                               onkeypress="return onlyNumbers(event)"
                               onblur="validateCreditScoreRange(this)"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorEquifaxScore', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                    <?php } else { ?>
                        <h5><?php echo $coBorEquifaxScore ?></h5>
                    <?php } ?>
                </div>
            </div>
            <!--coBor Equifax Score end-->

            <?php if ($hideThisField) { ?>
                <!--coBor Mid Fico Score start-->
                <div class="copersonalInfo row form-group col-md-6 <?php echo loanForm::showField('midFicoScoreCoBor'); ?> ">
                    <?php echo loanForm::label('midFicoScoreCoBor', 'col-md-5 '); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'midFicoScoreCoBor', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="text"
                                   name="midFicoScoreCoBor"
                                   id="midFicoScoreCoBor"
                                   value="<?php echo $midFicoScoreCoBor; ?>"
                                   tabindex="<?php echo $tabIndex++; ?>"
                                   placeholder="<?php echo glCreditScore::getRange(); ?>"
                                   inputmode="numeric"
                                   maxlength="<?php echo glCreditScore::getMaxLength(); ?>"
                                   onkeypress="return onlyNumbers(event)"
                                   onblur="validateCreditScoreRange(this)"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'midFicoScoreCoBor', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                        <?php } else { ?>
                            <h5><?php echo $midFicoScoreCoBor; ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <!--coBor Mid Fico Score end-->
            <?php } ?>

            <!--coBor Transunion Score start-->
            <div class="copersonalInfo row form-group col-md-6 <?php echo loanForm::showField('coBorTransunionScore'); ?> ">
                <?php echo loanForm::label('coBorTransunionScore', 'col-md-5 '); ?>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorTransunionScore', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="text"
                               name="coBorTransunionScore"
                               id="coBorTransunionScore"
                               value="<?php echo $coBorTransunionScore; ?>"
                               tabindex="<?php echo $tabIndex++; ?>"
                               placeholder="<?php echo glCreditScore::getRange(); ?>"
                               inputmode="numeric"
                               maxlength="<?php echo glCreditScore::getMaxLength(); ?>"
                               onkeypress="return onlyNumbers(event)"
                               onblur="validateCreditScoreRange(this)"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorTransunionScore', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                    <?php } else { ?>
                        <h5><?php echo $coBorTransunionScore ?></h5>
                    <?php } ?>
                </div>
            </div>
            <!--coBor Transunion Score end-->

            <!--coBor Experian Score start-->
            <div class="copersonalInfo row form-group col-md-6 <?php echo loanForm::showField('coBorExperianScore'); ?> ">
                <?php echo loanForm::label('coBorExperianScore', 'col-md-5 '); ?>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorExperianScore', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="text"
                               name="coBorExperianScore"
                               id="coBorExperianScore"
                               value="<?php echo $coBorExperianScore; ?>"
                               tabindex="<?php echo $tabIndex++; ?>"
                               placeholder="<?php echo glCreditScore::getRange(); ?>"
                               inputmode="numeric"
                               maxlength="<?php echo glCreditScore::getMaxLength(); ?>"
                               onkeypress="return onlyNumbers(event)"
                               onblur="validateCreditScoreRange(this)"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorExperianScore', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                    <?php } else { ?>
                        <h5><?php echo $coBorExperianScore ?></h5>
                    <?php } ?>
                </div>
            </div>
            <!--coBor Experian Score end-->

            <!--credit score range start -->
            <div class="copersonalInfo row form-group col-md-6 <?php echo loanForm::showField('coBorCreditScoreRange'); ?> ">
                <?php echo loanForm::label('coBorCreditScoreRange', 'col-md-5 '); ?>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <select
                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorCreditScoreRange', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                name="coBorCreditScoreRange"
                                id="coBorCreditScoreRange"
                                tabindex="<?php echo $tabIndex++; ?>"
                                autocomplete="off"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorCreditScoreRange', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                            <option value=''> - Select -</option>
                            <?php
                            for ($i = 0; $i < count($glHMLOCreditScoreRange ?? []); $i++) {
                                $sOpt = '';
                                $glCreditScoreRange = '';
                                $glcoBorCreditScoreRange = trim($glHMLOCreditScoreRange[$i]);
                                $sOpt = Arrays::isSelected($glcoBorCreditScoreRange, $coBorCreditScoreRange);
                                echo "<option value=\"" . $glcoBorCreditScoreRange . "\" " . $sOpt . '>' . $glcoBorCreditScoreRange . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php echo $coBorCreditScoreRange; ?></h5>
                    <?php } ?>
                </div>
            </div>
            <!--credit score range end -->

        </div>

        <?php echo CustomField::RenderForTabSection(
            PageVariables::$PCID,
            tblFile::class,
            LMRequest::$LMRId,
            'CBI',
            $fileTab,
            $activeTab,
            LMRequest::myFileInfo()->getFileTypes(),
            LMRequest::myFileInfo()->getLoanPrograms()
        ); ?>
    </div>
</div>
<script>
    $(function () {
        if ($('.copersonalInfo.secShow').length > 0) { //show title
            $("#copersonalInfotitle").show();
        } else { //hide title
            $("#copersonalInfotitle").hide();
        }

        //borrower to co-borrower address
        $(document).on('change', '#iscoborrowerPreAdd', function () {
            //alert(this.checked);

            var cls = $(this).attr('class');
            if (this.checked) { //copy the values from borrower address
                $('#coBPresentAddress').val($('#presentAddress').val());
                $('#coBPresentCity').val($('#presentCity').val());
                $('#coBPresentState').val($('#presentState').val());
                populateStateCounty('loanModForm', 'coBPresentState', 'coBorrowerCounty')
                $('#coBPresentZip').val($('#presentZip').val());
                $('#coBPresentPropType').val($('#borPresentPropType').val());
                $('#coBorrowerUnit').val($('#presentUnit').val());
                $('#coBorrowerCounty').val($('#presentCounty').val());
            } else { //empty the values to add new values
                $('#coBPresentAddress').val('');
                $('#coBPresentCity').val('');
                $('#coBPresentState').val('');
                $('#coBPresentZip').val('');
                $('#coBPresentPropType').val('');
                $('#coBorrowerUnit').val('');
                $('#coBorrowerCounty').val('');
            }
        });
    });
    function assignValueTOAreYouUSCitizenCoBor(val)
    {
        if(val == 'U.S. Citizen')
        {
            $("#isCoBorUSCitizenYes").prop("checked", true);
            $("#isCoBorUSCitizenNo").prop("checked", false);
            hideAndShowSection('Yes', 'No', 'coborOriginAndVisaTR');
        }
        else if(val != '')
        {
            $("#isCoBorUSCitizenNo").prop("checked", true);
            $("#isCoBorUSCitizenYes").prop("checked", false);
            hideAndShowSection('No', 'No', 'coborOriginAndVisaTR');
        }
    }
</script>
<!-- coborInformation.php -->
