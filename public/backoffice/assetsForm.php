<!-- Assets Section Start -->
<?php
//global variables
global $selClientId, $fileTab, $fieldsInfo, $HMLOLoanInfoSectionsDisp, $allowToEdit, $assetCash, $tabIndex, $vestedInterest;
global $assetCheckingAccounts, $assetSavingMoneyMarket, $networthOfBusinessOwned, $isLO, $assetAccount, $assetAccountOwd, $assetStocks;
global $assetStocksOwed, $assetNonMarketableSecurities, $assetNonMarketableSecuritiesOwd, $assetIRAAccounts, $assetIRAAccountsOwed, $assetESPOAccounts;
global $assetTotalRetirementValue, $assetAvailabilityLinesCredit, $assetAvailabilityLinesCreditOwed, $assetHome, $assetHomeOwed, $assetSR;
global $assetESPOAccountsOwed, $assetLifeInsurance, $assetLifeInsuranceOwed, $assetTotalCashBankAcc, $assetSROwed, $assetORE, $assetOREOwed;
global $assetOther, $otherAmtOwed, $otherAssets, $notesPayableToBanksOthersOwed, $installmentAccountOwed, $revolvingDebtOwed;
global $otherLiabilityDetails, $unpaidPayableTaxesDesc, $totalAssets, $totalAssetsOwed,
       $totalAssetsNetValue, $isClientProfile, $showSOREDispOpt, $encCID;
global $assetCars, $assetCarsOwed, $automobilesOwned3x, $automobilesOwned3x1,
       $unpaidPayableTaxesOwed, $otherLiabilitiesOwed, $otherDescription, $LMRId, $fileLOScheduleRealInfo, $assetSecNotesOwd,
       $assetUnsecNotesOwd, $assetAcctPayableOwd, $assetMarginOwd, $fileMC;

use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\assetsLiabilities;
use models\Controllers\loanForm;
use models\CustomField;
use models\lendingwise\tblFile;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;

$secArr = BaseHTML::sectionAccess2(['sId' => 'Assets', 'opt' => $fileTab]);  // Section Start...
loanForm::pushSectionID('Assets');

$isLO = LMRequest::File()->LMRId ? LMRequest::myFileInfo()->isLO() : $isLO;
?>
<!-- assetsForm.php -->
<div
    class="card card-custom  HMLOLoanInfoSections isClientInfo Assets AssetsCard <?php if (count(Arrays::getValueFromArray('Assets', $fieldsInfo)) <= 0) {
        echo 'secHide';
    } ?>" style="<?php echo $HMLOLoanInfoSectionsDisp; ?>">

    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <?php echo loanForm::toggleSectionV2(
            'Assets',
            true,
            true,
        ); ?>
    </div>
    <div class="card-body assets AssetsCard_body">
        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <div class="row">
                        <div class="col-4 font-weight-boldest">Type</div>
                        <div class="col-4 font-weight-boldest">Estimated Value ($)</div>
                        <div class="col-4 font-weight-boldest">Amount Owed ($)</div>
                    </div>
                </div>
                <div class="form-group <?php echo loanForm::showField('assetCash'); ?>">
                    <div class="row">
                        <div class="col-md-4">
                            <?php echo loanForm::label2('assetCash'); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                'assetCash',
                                $allowToEdit,
                                $tabIndex++,
                                $assetCash,
                                null,
                                'calculateTotalAssets(this.value);',
                                    '',
                            ); ?>
                        </div>
                        <div class="col-4">
                        </div>
                    </div>
                </div>

                <div class="form-group  <?php echo loanForm::showField('vestedInterest'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label2('vestedInterest'); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                'vestedInterest',
                                $allowToEdit,
                                $tabIndex++,
                                $vestedInterest,
                                null,
                                'calculateTotalSumOfAll(this.value);calculateTotalAssets(this.value);',
                                    '',
                            ); ?>
                        </div>
                        <div class="col-4">
                        </div>
                    </div>
                </div>

                <div class="form-group  <?php echo loanForm::showField('assetCheckingAccounts'); ?> ">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetCheckingAccounts'); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                'assetCheckingAccounts',
                                    $allowToEdit,
                                    $tabIndex,
                                    $assetCheckingAccounts,
                                    '',
                                    '',
                                    'calculateTotalAssets(this.value);',
                            );?>
                        </div>
                        <div class="col-4">
                        </div>
                    </div>
                </div>

                <div class="form-group assetSavingMoneyMarket_disp <?php echo loanForm::showField('assetSavingMoneyMarket'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetSavingMoneyMarket'); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'assetSavingMoneyMarket',
                                    $allowToEdit,
                                    $tabIndex,
                                    $assetSavingMoneyMarket,
                                    '',
                                    '',
                                    'calculateTotalAssets(this.value);',
                            );?>
                        </div>
                        <div class="col-4">
                        </div>
                    </div>
                </div>

                <div class="form-group networthOfBusinessOwned_disp <?php echo loanForm::showField('networthOfBusinessOwned'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('networthOfBusinessOwned'); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'networthOfBusinessOwned',
                                    $allowToEdit,
                                    $tabIndex,
                                    $networthOfBusinessOwned,
                                    '',
                                    '',
                                    'calculateTotalAssets(this.value);',
                            );?>
                        </div>
                        <div class="col-4">
                        </div>
                    </div>
                </div>

                <!-- Accounts & Notes Receivable -->
                <div class="form-group assetAccount_disp <?php echo loanForm::showField('assetAccount'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetAccount', ''); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'assetAccount',
                                    $allowToEdit,
                                    $tabIndex,
                                    $assetAccount,
                                    '',
                                    '',
                                    assetsLiabilities::getOnChange('assetAccount', intval($isLO)),
                            );?>
                        </div>
                        <div class="col-4">
                            <div class="assetAccountOwd_disp <?php echo loanForm::showField('assetAccountOwd'); ?>">
                                <?php echo loanForm::label('assetAccountOwd', 'hidden'); ?>
                                <?php echo loanForm::currency(
                                        'assetAccountOwd',
                                        $allowToEdit,
                                        $tabIndex,
                                        $assetAccountOwd,
                                        '',
                                        '',
                                        assetsLiabilities::getOnChange('assetAccountOwd', intval($isLO)),
                                );?>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- // Accounts & Notes Receivable // -->

                <!-- Assets Stocks-->
                <div class="form-group assetStocks_disp <?php echo loanForm::showField('assetStocks'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetStocks', ''); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'assetStocks',
                                    $allowToEdit,
                                    $tabIndex,
                                    $assetStocks,
                                    '',
                                    '',
                                    assetsLiabilities::getOnChange('assetStocks', intval($isLO)),
                            );?>
                        </div>
                        <div class="col-4">
                            <div class="assetStocksOwed_disp <?php echo loanForm::showField('assetStocksOwed'); ?>">
                                <?php echo loanForm::label('assetStocksOwed', 'hidden'); ?>
                                <?php echo loanForm::currency(
                                        'assetStocksOwed',
                                        $allowToEdit,
                                        $tabIndex,
                                        $assetStocksOwed,
                                        '',
                                        '',
                                        assetsLiabilities::getOnChange('assetStocksOwed', intval($isLO)),
                                );?>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- // Assets Stocks // -->

                <!-- Non-Marketable Securities -->
                <div class="form-group assetNonMarketableSecurities_disp <?php echo loanForm::showField('assetNonMarketableSecurities'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetNonMarketableSecurities', ''); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'assetNonMarketableSecurities',
                                    $allowToEdit,
                                    $tabIndex,
                                    $assetNonMarketableSecurities,
                                    '',
                                    '',
                                    assetsLiabilities::getOnChange('assetNonMarketableSecurities', intval($isLO)),
                            );?>
                        </div>
                        <div class="col-4">
                            <div class="assetNonMarketableSecuritiesOwd_disp <?php echo loanForm::showField('assetNonMarketableSecuritiesOwd'); ?>">
                                <?php echo loanForm::label('assetNonMarketableSecuritiesOwd', 'hidden'); ?>
                                <?php echo loanForm::currency(
                                        'assetNonMarketableSecuritiesOwd',
                                        $allowToEdit,
                                        $tabIndex,
                                        $assetNonMarketableSecuritiesOwd,
                                        '',
                                        '',
                                        assetsLiabilities::getOnChange('assetNonMarketableSecuritiesOwd', intval($isLO)),
                                );?>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- // Non Marketable Securities // -->

                <div class="form-group assetIRAAccounts_disp <?php echo loanForm::showField('assetIRAAccounts'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetIRAAccounts', ''); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'assetIRAAccounts',
                                    $allowToEdit,
                                    $tabIndex,
                                    $assetIRAAccounts,
                                    '',
                                    '',
                                    'calculateTotalAssets(this.value);',
                            );?>
                        </div>
                        <div class="col-4">
                            <div class="assetIRAAccountsOwed_disp <?php echo loanForm::showField('assetIRAAccountsOwed'); ?>">
                                <?php echo loanForm::label('assetIRAAccountsOwed', 'hidden'); ?>
                                <?php echo loanForm::currency(
                                        'assetIRAAccountsOwed',
                                        $allowToEdit,
                                        $tabIndex,
                                        $assetIRAAccountsOwed,
                                        '',
                                        '',
                                        "calculateTotalAssetsOwed(this.value, 'loanModForm');",
                                );?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group assetESPOAccounts_disp <?php echo loanForm::showField('assetESPOAccounts'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetESPOAccounts', ''); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'assetESPOAccounts',
                                    $allowToEdit,
                                    $tabIndex,
                                    $assetESPOAccounts,
                                    '',
                                    '',
                                    'calculateTotalAssets(this.value);',
                            );?>
                        </div>
                        <div class="col-4">
                            <div class="assetESPOAccountsOwed_disp <?php echo loanForm::showField('assetESPOAccountsOwed'); ?> ">
                                <?php echo loanForm::label('assetESPOAccountsOwed', 'hidden'); ?>
                                <?php echo loanForm::currency(
                                        'assetESPOAccountsOwed',
                                        $allowToEdit,
                                        $tabIndex,
                                        $assetESPOAccountsOwed,
                                        '',
                                        '',
                                        "calculateTotalAssetsOwed(this.value, 'loanModForm');",
                                );?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group assetLifeInsurance_disp <?php echo loanForm::showField('assetLifeInsurance'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetLifeInsurance', ''); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'assetLifeInsurance',
                                    $allowToEdit,
                                    $tabIndex,
                                    $assetLifeInsurance,
                                    '',
                                    '',
                                    assetsLiabilities::getOnChange('assetLifeInsurance', intval($isLO)),
                            );?>
                        </div>
                        <div class="col-4">
                            <div class="assetLifeInsuranceOwed_disp assetLifeInsuranceOwed <?php echo loanForm::showField('assetLifeInsuranceOwed'); ?> ">
                                <?php echo loanForm::label('assetLifeInsuranceOwed', 'hidden'); ?>
                                <?php echo loanForm::currency(
                                        'assetLifeInsuranceOwed',
                                        $allowToEdit,
                                        $tabIndex,
                                        $assetLifeInsuranceOwed,
                                        '',
                                        '',
                                        assetsLiabilities::getOnChange('assetLifeInsuranceOwed', intval($isLO)),
                                );?>
                            </div>
                        </div>
                    </div>
                </div>

                <div
                    class="form-group assetTotalCashBankAcc_disp <?php echo loanForm::showField('assetTotalCashBankAcc'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetTotalCashBankAcc', ''); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'assetTotalCashBankAcc',
                                    $allowToEdit,
                                    $tabIndex,
                                    $assetTotalCashBankAcc,
                                    '',
                                    '',
                                    'calculateTotalAssets(this.value);',
                            );?>
                        </div>
                        <div class="col-4">
                        </div>
                    </div>
                </div>

                <div class="form-group assetTotalRetirementValue_disp <?php echo loanForm::showField('assetTotalRetirementValue'); ?> ">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetTotalRetirementValue', ''); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'assetTotalRetirementValue',
                                    $allowToEdit,
                                    $tabIndex,
                                    $assetTotalRetirementValue,
                                    '',
                                    '',
                                    'calculateTotalAssets(this.value);',
                            );?>
                        </div>
                        <div class="col-4">
                        </div>
                    </div>
                </div>

                <div class="form-group assetAvailabilityLinesCredit_disp <?php echo loanForm::showField('assetAvailabilityLinesCredit'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetAvailabilityLinesCredit', ''); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'assetAvailabilityLinesCredit',
                                    $allowToEdit,
                                    $tabIndex,
                                    $assetAvailabilityLinesCredit,
                                    '',
                                    '',
                                    'calculateTotalAssets(this.value);',
                            );?>
                        </div>
                        <div class="col-4">
                            <div class="assetAvailabilityLinesCreditOwed_disp <?php echo loanForm::showField('assetAvailabilityLinesCreditOwed'); ?>">
                                <?php echo loanForm::label('assetAvailabilityLinesCreditOwed', 'hidden'); ?>
                                <?php echo loanForm::currency(
                                        'assetAvailabilityLinesCreditOwed',
                                        $allowToEdit,
                                        $tabIndex,
                                        $assetAvailabilityLinesCreditOwed,
                                        '',
                                        '',
                                        "calculateTotalAssetsOwed(this.value, 'loanModForm');",
                                );?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group assetHome_disp <?php echo loanForm::showField('assetHome'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetHome', ''); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'assetHome',
                                    $allowToEdit,
                                    $tabIndex,
                                    $assetHome,
                                    '',
                                    '',
                                    'calculateTotalAssets(this.value);',
                            );?>
                        </div>
                        <div class="col-4">
                            <div class="assetHomeOwed_disp <?php echo loanForm::showField('assetHomeOwed'); ?>">
                                <?php echo loanForm::label('assetHomeOwed', 'hidden'); ?>
                                <?php echo loanForm::currency(
                                        'assetHomeOwed',
                                        $allowToEdit,
                                        $tabIndex,
                                        $assetHomeOwed,
                                        '',
                                        '',
                                        "calculateTotalAssetsOwed(this.value, 'loanModForm');",
                                );?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group assetSR_disp <?php echo loanForm::showField('assetSR'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetSR', ''); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'assetSR',
                                    $allowToEdit,
                                    $tabIndex,
                                    $assetSR,
                                    '',
                                    '',
                                    'calculateTotalAssets(this.value);',
                            );?>
                        </div>
                        <div class="col-4">
                            <div class="assetSROwed_disp <?php echo loanForm::showField('assetSROwed'); ?>">
                                <?php echo loanForm::label('assetSROwed', 'hidden'); ?>
                                <?php echo loanForm::currency(
                                        'assetSROwed',
                                        $allowToEdit,
                                        $tabIndex,
                                        $assetSROwed,
                                        '',
                                        '',
                                        "calculateTotalAssetsOwed(this.value, 'loanModForm');",
                                );?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group SORESection assetORE_disp <?php echo loanForm::showField('assetORE'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetORE', ''); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'assetORE',
                                    $allowToEdit,
                                    $tabIndex,
                                    $assetORE,
                                    '',
                                    '',
                                    assetsLiabilities::getOnChange('assetORE', intval($isLO)),
                            );?>
                        </div>
                        <div class="col-4">
                            <div class="assetOREOwed_disp <?php echo loanForm::showField('assetOREOwed'); ?>">
                                <?php echo loanForm::label('assetOREOwed', 'hidden'); ?>
                                <?php echo loanForm::currency(
                                        'assetOREOwed',
                                        $allowToEdit,
                                        $tabIndex,
                                        $assetOREOwed,
                                        '',
                                        '',
                                        "calculateTotalAssetsOwed(this.value,'loanModForm');",
                                );?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group assetCars_disp <?php echo loanForm::showField('assetCars'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetCars', ''); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'assetCars',
                                    $allowToEdit,
                                    $tabIndex,
                                    $assetCars,
                                    '',
                                    '',
                                    assetsLiabilities::getOnChange('assetCars', intval($isLO)),
                            );?>
                        </div>
                        <div class="col-4">
                            <div class="assetCarsOwed_disp <?php echo loanForm::showField('assetCarsOwed'); ?> ">
                                <?php echo loanForm::label('assetCarsOwed', 'hidden'); ?>
                                <?php echo loanForm::currency(
                                        'assetCarsOwed',
                                        $allowToEdit,
                                        $tabIndex,
                                        $assetCarsOwed,
                                        '',
                                        '',
                                        assetsLiabilities::getOnChange('assetCarsOwed', intval($isLO)),
                                );?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group automobilesOwned3x_disp <?php echo loanForm::showField('automobilesOwned3x'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('automobilesOwned3x', ''); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'automobilesOwned3x',
                                    $allowToEdit,
                                    $tabIndex,
                                    $automobilesOwned3x,
                                    '',
                                    '',
                                    assetsLiabilities::getOnChange('automobilesOwned3x', intval($isLO)),
                            );?>
                        </div>
                        <div class="col-4">
                            <div class="automobilesOwned3x1_disp <?php echo loanForm::showField('automobilesOwned3x1'); ?> ">
                                <?php echo loanForm::label('automobilesOwned3x1', 'hidden'); ?>
                                <?php echo loanForm::currency(
                                        'automobilesOwned3x1',
                                        $allowToEdit,
                                        $tabIndex,
                                        $automobilesOwned3x,
                                        '',
                                        '',
                                        "calculateTotalAssetsOwed(this.value,'loanModForm');",
                                );?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group assetOther_disp <?php echo loanForm::showField('assetOther'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetOther', ''); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'assetOther',
                                    $allowToEdit,
                                    $tabIndex,
                                    $assetOther,
                                    '',
                                    '',
                                    assetsLiabilities::getOnChange('assetOther', intval($isLO)),
                            );?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'otherAmtOwed',
                                    $allowToEdit,
                                    $tabIndex,
                                    $otherAmtOwed,
                                    '',
                                    '',
                                    "calculateTotalAssetsOwed(this.value, 'loanModForm');",
                            );?>
                        </div>
                    </div>
                </div>

                <div class="form-group otherAssets_disp <?php echo loanForm::showField('otherAssets'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('otherAssets', ''); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'otherAssets',
                                    $allowToEdit,
                                    $tabIndex,
                                    $otherAssets,
                                    '',
                                    '',
                                    'calculateTotalAssets(this.value);calculateTotalSumOfAll(this.value);',
                            );?>
                        </div>
                        <div class="col-4">
                        </div>
                    </div>
                </div>
                <!-- Notes Payable to Banks & Others -->
                <div
                    class="form-group notesPayableToBanksOthers_disp <?php echo loanForm::showField('notesPayableToBanksOthers'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('notesPayableToBanksOthers', '', '', '', 'Owed'); ?>
                        </div>
                        <div class="col-4">
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'notesPayableToBanksOthersOwed',
                                    $allowToEdit,
                                    $tabIndex,
                                    $notesPayableToBanksOthersOwed,
                                    '',
                                    '',
                                    assetsLiabilities::getOnChange('notesPayableToBanksOthersOwed', intval($isLO)),
                            );?>
                        </div>
                    </div>
                </div>
                <!-- // Notes Payable to Banks & Others // -->
                <!-- Installment Account(s) -->
                <div class="form-group installmentAccount_disp <?php echo loanForm::showField('installmentAccount'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('installmentAccount', '', '', '', 'Owed'); ?>
                        </div>
                        <div class="col-4">
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'installmentAccountOwed',
                                    $allowToEdit,
                                    $tabIndex,
                                    $installmentAccountOwed,
                                    '',
                                    '',
                                    assetsLiabilities::getOnChange('installmentAccountOwed', intval($isLO)),
                            );?>
                        </div>
                    </div>
                </div>
                <!-- // Installment Account(s) // -->
                <!-- Revolving Debt -->
                <div class="form-group revolvingDebt_disp <?php echo loanForm::showField('revolvingDebt'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('revolvingDebt', '', '', '', 'Owed'); ?>
                        </div>
                        <div class="col-4">
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'revolvingDebtOwed',
                                    $allowToEdit,
                                    $tabIndex,
                                    $revolvingDebtOwed,
                                    '',
                                    '',
                                    assetsLiabilities::getOnChange('revolvingDebtOwed', intval($isLO)),
                            );?>
                        </div>
                    </div>
                </div>
                <!-- // Revolving Debt // -->

                <!-- Unpaid/Payable Taxes -->
                <div class="form-group unpaidPayableTaxes_disp <?php echo loanForm::showField('unpaidPayableTaxes'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('unpaidPayableTaxes', '', '', '', 'Owed'); ?>
                        </div>
                        <div class="col-4">
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'unpaidPayableTaxesOwed',
                                    $allowToEdit,
                                    $tabIndex,
                                    $unpaidPayableTaxesOwed,
                                    '',
                                    '',
                                    assetsLiabilities::getOnChange('unpaidPayableTaxesOwed', intval($isLO)),
                            );?>
                        </div>
                    </div>
                </div>
                <!-- // Unpaid/Payable Taxes // -->


                <div class="form-group assetSecNotesOwd_disp <?php echo loanForm::showField('assetSecNotesOwd'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetSecNotesOwd', ''); ?>
                        </div>
                        <div class="col-4">
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'assetSecNotesOwd',
                                    $allowToEdit,
                                    $tabIndex,
                                    $assetSecNotesOwd,
                                    '',
                                    '',
                                    assetsLiabilities::getOnChange('assetSecNotesOwd', intval($isLO)),
                            );?>
                        </div>
                    </div>
                </div>


                <div class="form-group assetUnsecNotesOwd_disp <?php echo loanForm::showField('assetUnsecNotesOwd'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetUnsecNotesOwd', ''); ?>
                        </div>
                        <div class="col-4">
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'assetUnsecNotesOwd',
                                    $allowToEdit,
                                    $tabIndex,
                                    $assetUnsecNotesOwd,
                                    '',
                                    '',
                                    assetsLiabilities::getOnChange('assetUnsecNotesOwd', intval($isLO)),
                            );?>
                        </div>
                    </div>
                </div>

                <div class="form-group assetAcctPayableOwd_disp <?php echo loanForm::showField('assetAcctPayableOwd'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetAcctPayableOwd', ''); ?>
                        </div>
                        <div class="col-4">
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'assetAcctPayableOwd',
                                    $allowToEdit,
                                    $tabIndex,
                                    $assetAcctPayableOwd,
                                    '',
                                    '',
                                    assetsLiabilities::getOnChange('assetAcctPayableOwd', intval($isLO)),
                            );?>
                        </div>
                    </div>
                </div>
                <div class="form-group assetMarginOwd_disp <?php echo loanForm::showField('assetMarginOwd'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetMarginOwd', ''); ?>
                        </div>
                        <div class="col-4">
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                    'assetMarginOwd',
                                    $allowToEdit,
                                    $tabIndex,
                                    $assetMarginOwd,
                                    '',
                                    '',
                                    assetsLiabilities::getOnChange('assetMarginOwd', intval($isLO)),
                            );?>
                        </div>
                    </div>
                </div>


                <!-- Other Liabilities -->
                <div class="form-group otherLiabilitiesOwed_disp <?php echo loanForm::showField('otherLiabilitiesOwed'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('otherLiabilitiesOwed', ''); ?>
                        </div>
                        <div class="col-4">&nbsp;</div>
                        <div class="col-4">
                            <div class="otherLiabilitiesOwed_disp <?php echo loanForm::showField('otherLiabilitiesOwed'); ?>">
                                <?php echo loanForm::currency(
                                        'otherLiabilitiesOwed',
                                        $allowToEdit,
                                        $tabIndex,
                                        $otherLiabilitiesOwed,
                                        '',
                                        '',
                                        assetsLiabilities::getOnChange('otherLiabilitiesOwed', intval($isLO)),
                                );?>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- // Other Liabilities // -->

                <!-- Other Assets Details -->
                <div class="form-group otherDesc_disp <?php echo loanForm::showField('otherDesc'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('otherDesc', ''); ?>
                        </div>
                        <div class="col-8">
                            <?php if ($allowToEdit) { ?>
                                <textarea
                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'otherDesc', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    name="otherDesc" id="otherDesc"
                                    tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'otherDesc', 'sArr' => $secArr, 'opt' => 'I']); ?>><?php echo $otherDescription ?></textarea>
                            <?php } else { ?>
                                <label><?php echo $otherDescription; ?></label>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <!-- // Other Assets Details // -->
                <!-- Other Liability Details Desc -->
                <div class="form-group otherLiabilityDetails_disp <?php echo loanForm::showField('otherLiabilityDetails'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('otherLiabilityDetails', ''); ?>
                        </div>
                        <div class="col-8">
                            <?php if ($allowToEdit) { ?>
                                <textarea
                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'otherLiabilityDetails', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    name="otherLiabilityDetails" id="otherLiabilityDetails"
                                    tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'otherLiabilityDetails', 'sArr' => $secArr, 'opt' => 'I']); ?>><?php echo $otherLiabilityDetails; ?></textarea>
                            <?php } else { ?>
                                <label><?php echo $otherLiabilityDetails; ?></label>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <!-- // Other Liability Details Desc // -->
                <!-- Unpaid Payable Taxes Desc -->
                <div class="form-group unpaidPayableTaxesDesc_disp <?php echo loanForm::showField('unpaidPayableTaxesDesc'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('unpaidPayableTaxesDesc', ''); ?>
                        </div>
                        <div class="col-8">
                            <?php if ($allowToEdit) { ?>
                                <textarea
                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'unpaidPayableTaxesDesc', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    name="unpaidPayableTaxesDesc" id="unpaidPayableTaxesDesc"
                                    tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'unpaidPayableTaxesDesc', 'sArr' => $secArr, 'opt' => 'I']); ?>><?php echo $unpaidPayableTaxesDesc; ?></textarea>
                            <?php } else { ?>
                                <label><?php echo $unpaidPayableTaxesDesc; ?></label>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <!-- // Unpaid Payable Taxes Desc // -->

                <div class="form-group py-2 bg-success-o-40">
                    <div class="row">
                        <div class="col-4 font-weight-boldest text-right"><h4>Total: </h4></div>
                        <div class="col-4 font-weight-boldest">
                            <h4>$
                                <span
                                    id="totalAssets"><?php echo Currency::formatDollarAmountWithDecimal($totalAssets); ?></span>
                            </h4>
                        </div>
                        <div class="col-4 font-weight-boldest">
                            <h4>$
                                <span
                                    id="totalAssetsOwed"><?php echo Currency::formatDollarAmountWithDecimal($totalAssetsOwed); ?></span>
                            </h4>
                        </div>
                    </div>
                </div>
                <div class="form-group py-2 bg-primary-o-40">
                    <div class="row">
                        <div class="col-4 font-weight-boldest"><h4 class="pl-2">Total Est. Value</h4></div>
                        <div class="col-4 font-weight-boldest"><h4>- Total Amt Owed</h4></div>
                        <div class="col-4 font-weight-boldest"><h4>= Total Net Worth</h4></div>
                    </div>
                </div>
                <div class="form-group py-2 bg-warning-o-40">
                    <div class="row">
                        <div class="col-4 font-weight-boldest">
                            <h4 class="pl-2">$ <span
                                    id="totalAssetsDisp"><?php echo Currency::formatDollarAmountWithDecimal($totalAssets); ?></span>
                            </h4>
                        </div>
                        <div class="col-4 font-weight-boldest">
                            <h4>- $ <span
                                    id="totalAssetsOwedDisp"><?php echo Currency::formatDollarAmountWithDecimal($totalAssetsOwed); ?></span>
                            </h4>
                        </div>
                        <div class="col-4 font-weight-boldest">
                            <h4>= $ <span
                                    id="totalAssetNetValue"><?php echo Currency::formatDollarAmountWithDecimal($totalAssetsNetValue); ?></span>
                            </h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php echo CustomField::RenderForTabSection(
            PageVariables::$PCID,
            tblFile::class,
            LMRequest::$LMRId,
            'Assets',
            $fileTab,
            LMRequest::$activeTab,
            LMRequest::myFileInfo()->getFileTypes(),
            LMRequest::myFileInfo()->getLoanPrograms()
        ); ?>

    </div>
</div>
<!-- Assets Section End -->
<!-- assetsForm.php -->
