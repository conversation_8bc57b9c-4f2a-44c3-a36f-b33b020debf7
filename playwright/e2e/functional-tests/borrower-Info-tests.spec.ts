import { test, expect } from "@playwright/test";
import { AppManager } from "../../support/AppManager/PageObjectManager";

test.describe.configure({ mode: "serial" });

let loanFile: string ="/backoffice/LMRequest.php?eId=c20e03e74919f6c3&lId=fcae6d51bee930af&rId=5e5e77841cd042ac&op=a72f9e967052513d&tabOpt=CI&supp=VIPhelp";
let loanFile1: string ="/backoffice/LMRequest.php?eId=c20e03e74919f6c3&lId=791dd0d1871d0329&rId=0d269c4e3d97619c&tabOpt=CI&op=a72f9e967052513d&supp=VIPhelp";

let app: AppManager;

test.describe("Validate borrower info tab", () => {
  test.beforeEach(async ({ page }) => {
    app = new AppManager(page);
  });
  test("Validate admin info section", async ({ page }) => {
    await app.borrowerInfoTab.openLoanFile(loanFile);
    app.utilities.logStep("Check the loan card should be visible");
    await app.adminInfoSection.checkLoanFileCardVisibility();
    app.utilities.logStep("Click on borrower info tab");
    await app.adminInfoSection.clickOnBorrowerInfoTab();
    app.utilities.logStep("Clear all borrower input fields values");
    await app.borrowerInfoSection.clearAllBorrowerFields();
    app.utilities.logStep("Click on save button");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep(
      "Validate all values clear for borrower info section"
    );
    await app.borrowerInfoSection.validateAllBorrowerFieldsCleared();
    app.utilities.logStep("Check visibility of admin info card");
    await app.adminInfoSection.checkAdminInfoHeadingVisibility();
    app.utilities.logStep(
      "Check visibility of admin info sections input fields"
    );
    app.utilities.logStep("Select branch from dropdown");
    await app.adminInfoSection.selectBranch(
      app.data.borrowerInfo.branch.noBranch
    );
    await app.adminInfoSection.selectBranch(
      app.data.borrowerInfo.branch.branchName
    );
    app.utilities.logStep("Select Broker/Partner from dropdown");
    await app.adminInfoSection.selectBrokerPartner(
      app.data.borrowerInfo.brokerPartner.nobrokerPartner
    );
    await app.adminInfoSection.selectBrokerPartner(
      app.data.borrowerInfo.brokerPartner.brokerName
    );
    app.utilities.logStep("Select the loan Officer from dropdown");
    await app.adminInfoSection.selectLoanOfficer(
      app.data.borrowerInfo.loanOfficer.noLoanOfficer
    );
    await app.adminInfoSection.selectLoanOfficer(
      app.data.borrowerInfo.loanOfficer.loanOfficerName
    );
    await app.adminInfoSection.selectLoanOfficer(
      app.data.borrowerInfo.loanOfficer.loanOfficerName2
    );
    app.utilities.logStep("Select the file type from dropdown");
    await app.adminInfoSection.addFileType(
      app.data.borrowerInfo.fileType.fileTypeName
    );
    //await app.borrowerInfoTab.addFileType(app.data.borrowerInfo.fileType.fileTypeName2);
    app.utilities.logStep("Select the loan program from dropdown");
    await app.adminInfoSection.selectLoanProgram(
      app.data.borrowerInfo.loanProgram.loanProgramName2
    );
    app.utilities.logStep("Select the SBA Loan product from dropdown");
    await app.adminInfoSection.SBALoanproduct(
      app.data.borrowerInfo.sbaLoanProduct.sbaLoanProductName
    );
    app.utilities.logStep(
      "Select the Additional Desired Loan programs from dropdown"
    );
    await app.adminInfoSection.selectAdditionalDesiredLoanPrograms(
      app.data.borrowerInfo.additionalDesiredLoanPrograms
        .additionalDesiredLoanProgramsName2
    );
    app.utilities.logStep(
      "Select the Select Internal Loan Program from dropdown"
    );
    await app.adminInfoSection.selectInternalLoanProgram(
      app.data.borrowerInfo.internalLoanProgram.internalLoanProgramName2
    );
    app.utilities.logStep(
      "Select value for Where are you in the process? from dropdown"
    );
    await app.adminInfoSection.selectWhereAreYouInTheProcess(
      app.data.borrowerInfo.whereAreYouInTheProcess.whereAreYouInTheProcessName2
    );
    app.utilities.logStep(
      "Select value for Select Primary Client File Status from dropdown"
    );
    await app.adminInfoSection.selectPrimaryClientFileStatus(
      app.data.borrowerInfo.primaryClientFileStatus.primaryClientFileStatusName
    );
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep("Select File sub Status from dropdown");
    await app.adminInfoSection.selectFileSubStatus(
      app.data.borrowerInfo.fileSubStatus.fileSubStatusName2
    );
    app.utilities.logStep("Loan number validation is pending");
    // await app.borrowerInfoTab.validateLoanNumber(app.data.borrowerInfo.loanNumber.loanNumberValue);
    app.utilities.logStep("Select lead resource from dropdown");
    await app.adminInfoSection.selectLeadResource();
    app.utilities.logStep("Fill the project name");
    await app.adminInfoSection.fillProjectName(
      app.data.borrowerInfo.projectName.projectNameValue
    );
    app.utilities.logStep("Select Recived Date from calendar");
    // get the created data of loan file
    let createdDate = await app.adminInfoSection.getCreatedDateOfLoanFile();
    console.log("Loan Created Date: ", createdDate);
    await app.adminInfoSection.fillCalendarField(
      createdDate,
      app.data.borrowerInfo.calenderFeildReceived.index,
      app.data.borrowerInfo.calenderFeildReceived.date,
      "Received_Date"
    );
    app.utilities.logStep(
      "Select calender field borrower callback from calendar"
    );
    console.log("Loan Created Date: ", createdDate);
    await app.adminInfoSection.fillCalendarField(
      createdDate,
      app.data.borrowerInfo.calenderFeildBorrowerCallback.index,
      app.data.borrowerInfo.calenderFeildBorrowerCallback.date,
      "Borrower_Call_back"
    );
    app.utilities.logStep("Select calender welcome call date from calendar");
    await app.adminInfoSection.fillCalendarField(
      createdDate,
      app.data.borrowerInfo.welcomeCallDate.index,
      app.data.borrowerInfo.welcomeCallDate.date,
      "Welcome_Call_Date"
    );
    app.utilities.logStep(
      "Select calender actual - closing date from calendar"
    );
    await app.adminInfoSection.fillCalendarField(
      createdDate,
      app.data.borrowerInfo.actualClosingDate.index,
      app.data.borrowerInfo.actualClosingDate.date,
      "Actual_Closing_Date"
    );
    app.utilities.logStep("Select calender target closing date from calendar");
    await app.adminInfoSection.fillCalendarField(
      createdDate,
      app.data.borrowerInfo.targetClosingDate.index,
      app.data.borrowerInfo.targetClosingDate.date,
      "Target_Closing_Date"
    );
    app.utilities.logStep("Select calender hearing date from calendar");
    await app.adminInfoSection.fillCalendarField(
      createdDate,
      app.data.borrowerInfo.hearingDate.index,
      app.data.borrowerInfo.hearingDate.date,
      "Hearing_Date"
    );
    app.utilities.logStep("Add 3rd Party File Id");
    await app.adminInfoSection.add3rdPartyFileId(
      app.data.borrowerInfo.thirdPartyId.id
    );
    app.utilities.logStep("Select calender disclosure sent date from calendar");
    await app.adminInfoSection.fillCalendarField(
      createdDate,
      app.data.borrowerInfo.loanDocumentDate.index,
      app.data.borrowerInfo.loanDocumentDate.date,
      "Loan_Document_Date"
    );
    app.utilities.logStep("Select calender loan document date from calendar");
    await app.adminInfoSection.fillCalendarField(
      createdDate,
      app.data.borrowerInfo.disclosureSentDate.index,
      app.data.borrowerInfo.disclosureSentDate.date,
      "Disclosure_Sent_Date"
    );
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep("Validate added all details for admin info section");
    await app.adminInfoSection.validateAllDetailsAddedToInputFieldForAdminInfoSection(
      app.data.borrowerInfo.branch.branchName,
      app.data.borrowerInfo.brokerPartner.brokerName,
      app.data.borrowerInfo.loanOfficer.loanOfficerName2,
      app.data.borrowerInfo.fileType.fileTypeName,
      app.data.borrowerInfo.loanProgram.loanProgramName2,
      app.data.borrowerInfo.sbaLoanProduct.sbaLoanProductName,
      app.data.borrowerInfo.additionalDesiredLoanPrograms
        .additionalDesiredLoanProgramsName2,
      app.data.borrowerInfo.internalLoanProgram.internalLoanProgramName2,
      app.data.borrowerInfo.whereAreYouInTheProcess
        .whereAreYouInTheProcessName2,
      app.data.borrowerInfo.primaryClientFileStatus.primaryClientFileStatusName,
      app.data.borrowerInfo.fileSubStatus.fileSubStatusName2,
      app.data.borrowerInfo.projectName.projectNameValue,
      app.data.borrowerInfo.calenderFeildReceived.date,
      app.data.borrowerInfo.calenderFeildBorrowerCallback.date,
      app.data.borrowerInfo.welcomeCallDate.date,
      app.data.borrowerInfo.actualClosingDate.date,
      app.data.borrowerInfo.targetClosingDate.date,
      app.data.borrowerInfo.hearingDate.date,
      app.data.borrowerInfo.thirdPartyId.id,
      app.data.borrowerInfo.loanDocumentDate.date,
      app.data.borrowerInfo.disclosureSentDate.date
    );
    app.utilities.logStep("Check visibility of admin info card");
    await app.adminInfoSection.checkAdminInfoHeadingVisibility();
    app.utilities.logStep("DeSelect Broker/Partner");
    await app.adminInfoSection.selectBrokerPartner(
      app.data.borrowerInfo.brokerPartner.nobrokerPartner
    );
    app.utilities.logStep("DeSelect the Loan Officer");
    await app.adminInfoSection.selectLoanOfficer(
      app.data.borrowerInfo.loanOfficer.noLoanOfficer
    );
    app.utilities.logStep("DeSelect the SBA Loan product");
    await app.adminInfoSection.DeSelectSBALoanproduct();
    app.utilities.logStep("Deselect Additional Desired Loan Programs");
    await app.adminInfoSection.DeselectAdditionalDesiredLoanPrograms();
    app.utilities.logStep("DeSelect Internal Loan Program");
    await app.adminInfoSection.deSelectInternalLoanProgram();
    app.utilities.logStep("DeSelect Where are you in the process?");
    await app.adminInfoSection.selectWhereAreYouInTheProcess(
      app.data.borrowerInfo.whereAreYouInTheProcess.noWhereAreYouInTheProcess
    );
    app.utilities.logStep("DeSelect File Sub Status");
    await app.adminInfoSection.deSelectFileSubStatus();
    app.utilities.logStep("DeSelect LeadSource");
    await app.adminInfoSection.deSelectLeadSource();
    app.utilities.logStep("DeSelect Project Name ");
    await app.adminInfoSection.fillProjectName(
      app.data.borrowerInfo.projectName.noProjectName
    );
    app.utilities.logStep("DeSelect Broker/Referring Party");
    await app.adminInfoSection.deSelectBrokerReferringParty();
    app.utilities.logStep("DeSelect Received Date");
    await app.adminInfoSection.deSelectCalenderfeild(
      app.data.borrowerInfo.calenderFeildReceived.index,
      "Received_Date"
    );
    app.utilities.logStep("DeSelect Borrower Call back Date");
    await app.adminInfoSection.deSelectCalenderfeild(
      app.data.borrowerInfo.calenderFeildBorrowerCallback.index,
      "Borrower_Call_back"
    );
    app.utilities.logStep("DeSelect Welcome Call Date");
    await app.adminInfoSection.deSelectCalenderfeild(
      app.data.borrowerInfo.welcomeCallDate.index,
      "Welcome_Call_Date"
    );
    app.utilities.logStep("DeSelect Actual - Closing Date");
    await app.adminInfoSection.deSelectCalenderfeild(
      app.data.borrowerInfo.actualClosingDate.index,
      "Actual_Closing_Date"
    );
    app.utilities.logStep("DeSelect Target Closing Date");
    await app.adminInfoSection.deSelectCalenderfeild(
      app.data.borrowerInfo.targetClosingDate.index,
      "Target_Closing_Date"
    );
    app.utilities.logStep("DeSelect Hearing Date");
    await app.adminInfoSection.deSelectCalenderfeild(
      app.data.borrowerInfo.hearingDate.index,
      "Hearing_Date"
    );
    app.utilities.logStep("DeSelect 3rd Party File Id");
    await app.adminInfoSection.add3rdPartyFileId(
      app.data.borrowerInfo.thirdPartyId.noid
    );
    app.utilities.logStep("DeSelect Loan Document Date");
    await app.adminInfoSection.deSelectCalenderfeild(
      app.data.borrowerInfo.loanDocumentDate.index,
      "Loan_Document_Date"
    );
    app.utilities.logStep("DeSelect Disclosure Sent Date");
    await app.adminInfoSection.deSelectCalenderfeild(
      app.data.borrowerInfo.disclosureSentDate.index,
      "Disclosure_Sent_Date"
    );
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep("Validate removed details for admin info section");
    await app.adminInfoSection.validateAllRemovedDetailsAddedToInputFieldForAdminInfoSection(
      app.data.borrowerInfo.branch.branchName,
      app.data.borrowerInfo.brokerPartner.nobrokerPartner,
      app.data.borrowerInfo.loanOfficer.noLoanOfficer,
      app.data.borrowerInfo.fileType.fileTypeName,
      app.data.borrowerInfo.loanProgram.loanProgramName2,
      app.data.borrowerInfo.sbaLoanProduct.noSbaLoanProduct,
      app.data.borrowerInfo.additionalDesiredLoanPrograms
        .noAdditionalDesiredLoanPrograms,
      app.data.borrowerInfo.internalLoanProgram.noInternalLoanProgram,
      app.data.borrowerInfo.whereAreYouInTheProcess.noWhereAreYouInTheProcess,
      app.data.borrowerInfo.primaryClientFileStatus.primaryClientFileStatusName,
      app.data.borrowerInfo.fileSubStatus.noFileSubStatus,
      app.data.borrowerInfo.projectName.noProjectName,
      app.data.borrowerInfo.calenderFeildReceived.nodate,
      app.data.borrowerInfo.calenderFeildBorrowerCallback.nodate,
      app.data.borrowerInfo.welcomeCallDate.nodate,
      app.data.borrowerInfo.actualClosingDate.nodate,
      app.data.borrowerInfo.targetClosingDate.nodate,
      app.data.borrowerInfo.hearingDate.nodate,
      app.data.borrowerInfo.thirdPartyId.noid,
      app.data.borrowerInfo.loanDocumentDate.nodate,
      app.data.borrowerInfo.disclosureSentDate.nodate
    );
  });
  test("Validate borrower info section", async () => {
    app.utilities.logStep(
      "Check personal info fields in borrower info section"
    );
    await app.borrowerInfoTab.openLoanFile(loanFile);
    await app.borrowerInfoSection.enterBorrowerMiddleName(
      app.borrowerInfoData.middleName
    );
    await app.borrowerInfoSection.validateBorrowerEmailIsInactive();
    await app.borrowerInfoSection.enterBorrowerSecondaryEmail(
      app.borrowerInfoData.secondaryEmail
    );
    await app.borrowerInfoSection.enterHomePhone(
      app.borrowerInfoData.homePhone
    );
    await app.borrowerInfoSection.enterCellPhone(
      app.borrowerInfoData.cellPhone
    );
    await app.borrowerInfoSection.enterBorrowerFax(app.borrowerInfoData.fax);

    app.utilities.logStep("Check Contact info fields in borrower info section");
    await app.borrowerInfoSection.selectServiceProvider(
      app.borrowerInfoData.serviceProvider
    );
    await app.borrowerInfoSection.isThereCoBorrower();
    // await app.borrowerInfoSection.clickCoBorrowerButton();
    await app.borrowerInfoSection.enterAltPhoneNumber(
      app.borrowerInfoData.alternatePhone
    );
    await app.borrowerInfoSection.enterWorkNumber(
      app.borrowerInfoData.workNumber
    );

    app.utilities.logStep("Check Address info fields in borrower info section");
    await app.borrowerInfoSection.enterPresentAddress(
      app.borrowerInfoData.presentAddress
    );
    await app.borrowerInfoSection.enterPresentUnit(
      app.borrowerInfoData.presentUnit
    );
    await app.borrowerInfoSection.enterPresentCity(
      app.borrowerInfoData.presentCity
    );
    await app.borrowerInfoSection.selectPresentState(
      app.borrowerInfoData.presentState
    );
    await app.borrowerInfoSection.enterPresentZip(
      app.borrowerInfoData.presentZip
    );
    await app.borrowerInfoSection.selectPresentCounty(
      app.borrowerInfoData.presentCounty
    );
    await app.borrowerInfoSection.selectPresentCountry(
      app.borrowerInfoData.presentCountry
    );

    app.utilities.logStep(
      "Check Property info fields in borrower info section"
    );
    await app.borrowerInfoSection.enterPresentPropLengthTime(
      app.borrowerInfoData.presentPropLengthYears
    );
    await app.borrowerInfoSection.enterPresentPropLengthMonths(
      app.borrowerInfoData.presentPropLengthMonths
    );
    await app.borrowerInfoSection.selectRentOrOwn(
      app.borrowerInfoData.rentOrOwn
    );
    await app.borrowerInfoSection.enterCurrentRPM(
      app.borrowerInfoData.currentRPM
    );

    await app.borrowerInfoSection.selectBorResidedPresentAddr("Yes", {
      formerAddress: app.borrowerInfoData.formerAddress,
      formerUnit: app.borrowerInfoData.formerUnit,
      formerCity: app.borrowerInfoData.formerCity,
      formerState: app.borrowerInfoData.formerState,
      formerZip: app.borrowerInfoData.formerZip,
      formerCountry: app.borrowerInfoData.formerCountry,
      formerRentOrOwn: app.borrowerInfoData.formerRentOrOwn,
      lengthOfTimeYears: app.borrowerInfoData.lengthOfTimeYears,
      lengthOfTimeMonths: app.borrowerInfoData.lengthOfTimeMonths,
      rentPerMonth: app.borrowerInfoData.rentPerMonth,
    });
    await app.borrowerInfoSection.clickMailingAddressSameasCurrent();

    app.utilities.logStep(
      "Check Mailing Address section fields in borrower info section"
    );
    await app.borrowerInfoSection.checkMailingAddressInputVisible();
    await app.borrowerInfoSection.fillMailingAddress(
      app.borrowerInfoData.mailingAddress
    );
    await app.borrowerInfoSection.fillMailingUnit(
      app.borrowerInfoData.mailingUnit
    );
    await app.borrowerInfoSection.fillMailingCity(
      app.borrowerInfoData.mailingCity
    );
    await app.borrowerInfoSection.selectMailingState(
      app.borrowerInfoData.mailingState
    );
    await app.borrowerInfoSection.fillMailingZip(
      app.borrowerInfoData.mailingZip
    );
    await app.borrowerInfoSection.selectMailingCountry(
      app.borrowerInfoData.mailingCountry
    );

    app.utilities.logStep(
      "Check Mailing Address Prefill When SameAsCurrent address Checked"
    );
    await app.borrowerInfoSection.clickMailingAddressSameasCurrent();
    await app.borrowerInfoSection.verifyMailingAddressSameAsCurrent(
      app.borrowerInfoData.presentAddress,
      app.borrowerInfoData.presentUnit,
      app.borrowerInfoData.presentCity,
      app.borrowerInfoData.mailingState,
      app.borrowerInfoData.presentZip,
      app.borrowerInfoData.mailingCountry
    );

    app.utilities.logStep(
      "Check Alternate names section fields in borrower info section"
    );
    await app.borrowerInfoSection.checkAlternateNamesSectionVisible();
    await app.borrowerInfoSection.checkAlternateNamesSectionCountVisible();
    await app.borrowerInfoSection.fillAlternateFirstName(
      app.borrowerInfoData.alternateFirstName
    );
    await app.borrowerInfoSection.fillAlternateMiddleName(
      app.borrowerInfoData.alternateMiddleName
    );
    await app.borrowerInfoSection.fillAlternateLastName(
      app.borrowerInfoData.alternateLastName
    );
    await app.borrowerInfoSection.clickOnAddAlternateNameButton();
    await app.borrowerInfoSection.removeAlternateSection2AndVerify();

    app.utilities.logStep(
      "Check all the input fields present in personal info under borrower info"
    );

    await app.borrowerInfoSection.fillBorrowerDOB(app.borrowerInfoData.dob);
    await app.borrowerInfoSection.fillPlaceOfBirth(
      app.borrowerInfoData.placeOfBirth
    );
    await app.borrowerInfoSection.fillSSN(app.borrowerInfoData.ssn);
    await app.borrowerInfoSection.selectDriverLicenseState(
      app.borrowerInfoData.driverLicenseState
    );
    await app.borrowerInfoSection.fillDriverLicenseNumber(
      app.borrowerInfoData.driverLicenseNumber
    );
    await app.borrowerInfoSection.fillDriverLicenseIssuanceDate(
      app.borrowerInfoData.driverLicenseIssuanceDate
    );
    await app.borrowerInfoSection.fillDriverLicenseExpirationDate(
      app.borrowerInfoData.driverLicenseExpirationDate
    );
    app.utilities.logStep("Check marital statuses");
    if (app.borrowerInfoData.maritalStatusUnmarried) {
      await app.borrowerInfoSection.selectMaritalStatusUnmarried();
    }
    if (app.borrowerInfoData.maritalStatusMarried) {
      await app.borrowerInfoSection.selectMaritalStatusMarried();
      await app.borrowerInfoSection.fillMarriageDate(
        app.borrowerInfoData.marriageDate
      );
      await app.borrowerInfoSection.fillDivorceDate(
        app.borrowerInfoData.divorceDate
      );
      await app.borrowerInfoSection.fillSpouseName(
        app.borrowerInfoData.spouseName
      );
      await app.borrowerInfoSection.fillMaidenName(
        app.borrowerInfoData.maidenName
      );
    }
    if (app.borrowerInfoData.maritalStatusSeparated) {
      await app.borrowerInfoSection.selectMaritalStatusSeparated();
      await app.borrowerInfoSection.fillMarriageDate(
        app.borrowerInfoData.marriageDate
      );
      await app.borrowerInfoSection.fillDivorceDate(
        app.borrowerInfoData.divorceDate
      );
      await app.borrowerInfoSection.fillSpouseName(
        app.borrowerInfoData.spouseName
      );
      await app.borrowerInfoSection.fillMaidenName(
        app.borrowerInfoData.maidenName
      );
    }

    app.utilities.logStep("Check citizenships");
    if (app.borrowerInfoData.citizenshipUS)
      await app.borrowerInfoSection.selectCitizenshipUS();
    if (app.borrowerInfoData.citizenshipPermResident)
      await app.borrowerInfoSection.selectCitizenshipPermResident();
    if (app.borrowerInfoData.citizenshipNonPermResident)
      await app.borrowerInfoSection.selectCitizenshipNonPermResident();
    if (app.borrowerInfoData.citizenshipForeignNational)
      await app.borrowerInfoSection.selectCitizenshipForeignNational();

    app.utilities.logStep("Armed forces status");
    if (app.borrowerInfoData.servicingMemberNo)
      await app.borrowerInfoSection.selectServicingMemberNo();
    //   if (app.borrowerInfoData.servicingMemberYes)
    //     await app.borrowerInfoTab.selectServicingMemberYes();
    //   await app.borrowerInfoTab.selectAllServicingMemberInfoOptions();
    //   for (const option of app.borrowerInfoData.servicingMemberInfoOptions) {
    //     await app.borrowerInfoTab.selectServicingMemberInfoByText(option);
    //   }
    //   await app.borrowerInfoTab.fillServiceExpirationDate(
    //     app.borrowerInfoData.serviceExpirationDate
    //   );
    //   await app.borrowerInfoTab.clickOnserviceExpirationDate();

    app.utilities.logStep("Dependents, Scores, Authorization statuses");
    await app.borrowerInfoSection.fillAgesOfDependent(
      app.borrowerInfoData.agesOfDependent
    );
    await app.borrowerInfoSection.fillNumberOfDependents(
      app.borrowerInfoData.numberOfDependents
    );
    await app.borrowerInfoSection.fillMidFicoScore(
      app.borrowerInfoData.midFicoScore
    );
    await app.borrowerInfoSection.fillEquifaxScore(
      app.borrowerInfoData.equifaxScore
    );
    await app.borrowerInfoSection.fillTransunionScore(
      app.borrowerInfoData.transunionScore
    );
    await app.borrowerInfoSection.fillExperianScore(
      app.borrowerInfoData.experianScore
    );
    await app.borrowerInfoSection.selectCreditScoreRange(
      app.borrowerInfoData.creditScoreRange
    );
    await app.borrowerInfoSection.selectAllAuthorizationStatuses();
    for (const auth of app.borrowerInfoData.authorizationStatuses) {
      await app.borrowerInfoSection.selectAuthorizationStatusByText(auth);
    }
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();

    app.utilities.logStep(
      "Validate all the input fields present in borrower info section"
    );
    app.utilities.logStep(
      "validate all the data is correct after saving the form"
    );
    await app.borrowerInfoTab.openLoanFile(loanFile);
    await app.borrowerInfoSection.validateAllBorrowerInfoFields(
      app.borrowerInfoData
    );
  });
  test("Validate the borrower entity section", async () => {
    await app.borrowerInfoTab.openLoanFile(loanFile);
    await app.adminInfoSection.clickOnBorrowerInfoTab();
    await app.borrowerTypeEntityInfoSection.checkvisibility();
    app.utilities.logStep(
      "Validate the remove the selected option for borrower type entity"
    );
    await app.borrowerTypeEntityInfoSection.selectTheOptionForborroweType(
      app.borrowerEntityTypeData.borrower_type.noBorrower_type
    );
    app.utilities.logStep(
      "Validate the change of the selected option to Individual for borrower type entity and check corporate secretary input details"
    );
    await app.borrowerTypeEntityInfoSection.selectTheOptionForborroweType(
      app.borrowerEntityTypeData.borrower_type.individualBorrower.type
    );
    // await app.borrowerTypeEntityInfoSection.validateCorporateSecretaryNameInputField(app.borrowerEntityTypeData.borrower_type.individualBorrower.corporate_Secretary_Name);
    app.utilities.logStep(
      "Validate the change of the selected option to Entity for borrower type entity and check other fields assosiated with it"
    );
    await app.borrowerTypeEntityInfoSection.selectTheOptionForborroweType(
      app.borrowerEntityTypeData.borrower_type.entityBorrower.type
    );
    await app.borrowerTypeEntityInfoSection.validateEntityInputField(
      app.borrowerEntityTypeData.borrower_type.borrower_entity_data_type
        .data_One
    );
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep(
      "Remove and clear values for General Business Info for Borrower Type - Entity"
    );
    await app.borrowerTypeEntityInfoSection.clearEntityInputField();
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep(
      "Validate and fill the Business Address section for Borrower Type - Entity"
    );
    await app.borrowerTypeEntityInfoSection.fillBusinessAddressSection(
      app.borrowerEntityTypeData.borrower_type.borrower_entity_data_type
        .businessAddress
    );
    //Bug raised - entity half right section is not visible ,so we need to save the form here
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep(
      "Validate all data filled properly for Business Address section for Borrower Type - Entity"
    );
    await app.borrowerTypeEntityInfoSection.validateBusinessAddressSection(
      app.borrowerEntityTypeData.borrower_type.borrower_entity_data_type
        .businessAddress
    );
    app.utilities.logStep("Remove all detais to Business Address ");
    await app.borrowerTypeEntityInfoSection.resetBusinessAddressSection();
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep(
      "Validate and Fill the Business Details section for Borrower Type - Entity"
    );
    await app.borrowerTypeEntityInfoSection.fillBusinessDetailsSection(
      app.borrowerEntityTypeData.borrower_type.borrower_entity_data_type
        .businessDetails
    );
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep(
      "Validate all data filled properly for Business Details section for Borrower Type - Entity"
    );
    await app.borrowerTypeEntityInfoSection.validateBusinessDetailsSection(
      app.borrowerEntityTypeData.borrower_type.borrower_entity_data_type
        .businessDetails
    );
    app.utilities.logStep("Remove added details to Business Details");
    await app.borrowerTypeEntityInfoSection.removeBusinessDetailsSection();
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep(
      "Validate all details are removed and set to NO for Business Details section"
    );
    await app.borrowerTypeEntityInfoSection.validateResetBusinessDetailsSection();
    app.utilities.logStep(
      "Validate and fill Business Financials section for Borrower Type - Entity"
    );
    await app.borrowerTypeEntityInfoSection.fillBusinessFinancials(
      app.borrowerEntityTypeData.borrower_type.borrower_entity_data_type
        .businessFinancials
    );
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep(
      "Validate all details added succesfullly for Business Financials for Borrower Type - Entity"
    );
    await app.borrowerTypeEntityInfoSection.validateBusinessFinancials(
      app.borrowerEntityTypeData.borrower_type.borrower_entity_data_type
        .businessFinancials
    );
    app.utilities.logStep("Remove added details to Business Financials");
    await app.borrowerTypeEntityInfoSection.clearBusinessFinancials();
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep("Validate removed details for Business Financials");
    await app.borrowerTypeEntityInfoSection.validateClearBusinessFinancials();
    app.utilities.logStep(
      "Validate and fill Members/Officers section for Borrower Type - Entity"
    );
    await app.borrowerTypeEntityInfoSection.fillMemberOfficerDetails(
      app.borrowerEntityTypeData.borrower_type.borrower_entity_data_type
        .memberOfficer
    );
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep(
      "Validate all details are added succesfully for Members/Officers section for Borrower Type - Entity"
    );
    await app.borrowerTypeEntityInfoSection.validateMemberOfficerDetails(
      app.borrowerEntityTypeData.borrower_type.borrower_entity_data_type
        .memberOfficer
    );
    app.utilities.logStep("Remove added details to Members/Officers section");
    await app.borrowerTypeEntityInfoSection.clearMemberOfficerDetails();
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep(
      "Validate all details are removed and options set to NO"
    );
    await app.borrowerTypeEntityInfoSection.validateClearedMemberOfficerDetails();
    app.utilities.logStep(
      "Validate the change of the selected option to trust for borrower type entity and check other fields assosiated with it"
    );
    await app.borrowerTypeEntityInfoSection.selectTheOptionForborroweType(
      app.borrowerEntityTypeData.borrower_type.trustBorrower.type
    );
    await app.borrowerTypeEntityInfoSection.validateTrustInputField(
      app.borrowerEntityTypeData.borrower_type.trustBorrower
        .trust_type_values_noSelection
    );
    await app.borrowerTypeEntityInfoSection.validateTrustInputField(
      app.borrowerEntityTypeData.borrower_type.trustBorrower
        .trust_type_values_Revocable
    );
    await app.borrowerTypeEntityInfoSection.validateTrustInputField(
      app.borrowerEntityTypeData.borrower_type.trustBorrower
        .trust_type_values_Irrevocable
    );
    app.utilities.logStep(
      "Validate the change of the selected option to retirement for borrower entity and check other fields assosiated with it"
    );
    await app.borrowerTypeEntityInfoSection.selectTheOptionForborroweType(
      app.borrowerEntityTypeData.borrower_type.retirementEntityBorrower.type
    );
    await app.borrowerTypeEntityInfoSection.validateRetirementInputField(
      app.borrowerEntityTypeData.borrower_type.retirementEntityBorrower
        .retirement_type_values_noSelection
    );
    await app.borrowerTypeEntityInfoSection.validateRetirementInputField(
      app.borrowerEntityTypeData.borrower_type.retirementEntityBorrower
        .retirement_type_values_Self_Directed_IRA
    );
    await app.borrowerTypeEntityInfoSection.validateRetirementInputField(
      app.borrowerEntityTypeData.borrower_type.retirementEntityBorrower
        .retirement_type_values_Self_Directed_401K
    );
    await app.borrowerTypeEntityInfoSection.validateRetirementInputField(
      app.borrowerEntityTypeData.borrower_type.retirementEntityBorrower
        .retirement_type_values_IRA_LLC
    );
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();
  });
  test("Validate the borrower background section", async () => {
    await test.step("Open loan file", async () => {
      app.utilities.logStep("Open loan file (create)");
      await app.borrowerInfoTab.openLoanFile(loanFile);
    });

    await test.step("Fill borrower background (create)", async () => {
      app.utilities.logStep("Filling borrower background (create)");
      await app.borrowerBackground.fillAll(app.borrowerBackgroundCreateData);
    });

    await test.step("Save and validate persisted (create)", async () => {
      app.utilities.logStep("Saving borrower background (create)");
      await app.borrowerBackground.clickSave();

      // Re-open loan file to ensure persisted values loaded from server
      await app.borrowerInfoTab.openLoanFile(loanFile);

      // Validate all fields persisted exactly as expected
      await app.borrowerBackground.validateBorrowerBackgroundSaved(
        app.borrowerBackgroundCreateData
      );
    });

    await test.step("Apply update dataset", async () => {
      app.utilities.logStep("Applying update dataset");
      await app.borrowerBackground.fillAll(app.borrowerBackgroundUpdateData);
    });

    await test.step("Save and validate persisted (update)", async () => {
      app.utilities.logStep("Saving borrower background (update)");
      await app.borrowerBackground.clickSave();

      // Re-open loan file to ensure persisted values loaded
      await app.borrowerInfoTab.openLoanFile(loanFile);

      await app.borrowerBackground.validateBorrowerBackgroundSaved(
        app.borrowerBackgroundUpdateData
      );
    });

    await test.step("Apply delete dataset", async () => {
      app.utilities.logStep("Applying delete dataset (clears fields)");
      await app.borrowerBackground.fillAll(app.borrowerBackgroundDeleteData);
    });

    await test.step("Explicitly clear gated sections (safety)", async () => {
      app.utilities.logStep(
        "Explicitly clear gated sections (open->clear->close)"
      );
      // same explicit clears you used earlier to ensure hidden/gated values are cleared
      // example:
      await app.borrowerBackground.selectDeclaredBankrupt("Yes");
      await app.borrowerBackground.selectBankruptcyTypes([]);
      await app.borrowerBackground.fillBankruptcyExplanation(
        "Filed due to job loss in 2018, discharged in 2019."
      );
      await app.borrowerBackground.selectDeclaredBankrupt("No");
      // ... repeat for the other gated sections exactly as your cleanup routine ...
    });

    await test.step("Save and validate persisted (delete)", async () => {
      app.utilities.logStep("Saving borrower background (delete)");
      await app.borrowerBackground.clickSave();

      // // Re-open loan file to ensure persisted cleared values loaded
      // await app.borrowerInfoTab.openLoanFile(loanFile);

      // Validate the persisted state is cleared / default per delete dataset
      await app.borrowerBackground.validateBorrowerBackgroundSaved(
        app.borrowerBackgroundDeleteData
      );
    });
  });
  test("Validate SBA Questions section of borrower info tab", async () => {
    await app.borrowerInfoTab.openLoanFile(loanFile);
    app.utilities.logStep("Check the loan card should be visible");
    await app.adminInfoSection.checkLoanFileCardVisibility();
    app.utilities.logStep("Click on borrower info tab");
    await app.adminInfoSection.clickOnBorrowerInfoTab();
    app.utilities.logStep("Check visibility of SBA Questions card");
    await app.sbaquestionsSection.checksbaquestionsvisibility();
    app.utilities.logStep("Select Average Monthly Payroll of all employees? ");
    await app.sbaquestionsSection.selectAverageMonthlyPayrollOfAllEmployees(
      app.data.SBAQuetions.monthlyPayroll.amount
    );
    app.utilities.logStep("Select Purpose of the loan: ");
    await app.sbaquestionsSection.selectPurposeOfLoan();
    app.utilities.logStep("Select Detailed Explanation ");
    await app.sbaquestionsSection.selectDetailedExplanation(
      app.data.SBAQuetions.detailExplaination.explanation
    );
    app.utilities.logStep("Select Criminal Information");
    await app.sbaquestionsSection.selectcriminalinformation("Yes", {
      detailedexplaination1:
        app.data.SBAQuetions.criminalExplanation.explanation,
    });
    app.utilities.logStep("Select have you been arrested");
    await app.sbaquestionsSection.selecthaveyouarrested("Yes", {
      detailedexplaination2:
        app.data.SBAQuetions.arrestedExplanation.explanation,
    });
    app.utilities.logStep("Select Criminal Offence");
    await app.sbaquestionsSection.selectcriminaloffence("Yes", {
      detailedexplaination3:
        app.data.SBAQuetions.criminaloffenceExplanation.explanation,
    });
    app.utilities.logStep("Select Certified Development Company");
    await app.sbaquestionsSection.selectCertifiedDevelopmentCompany("Yes", {
      detailedexplaination4:
        app.data.SBAQuetions.certifiedDeveExplanationExplanation.explanation,
    });
    app.utilities.logStep("Select SBAs Franchise Directory");
    await app.sbaquestionsSection.selectSBAFranchiseDirectory("Yes", {
      detailedexplaination5:
        app.data.SBAQuetions.SBAFranchiseDirectory.explanation,
    });
    app.utilities.logStep("Select Small Business Applicant");
    await app.sbaquestionsSection.selectSmallBusinessApplicant("Yes", {
      detailedexplaination6:
        app.data.SBAQuetions.SmallBusinessApplicant.explanation,
    });
    app.utilities.logStep("Select Small Business Applicant Bankruptcy");
    await app.sbaquestionsSection.selectSmallBusinessApplicantBankruptcy(
      "Yes",
      app.data.SBAQuetions.SmallBusinessApplicantBankruptcy.optionToSelect,
      {
        detailedexplaination7:
          app.data.SBAQuetions.SmallBusinessApplicantBankruptcy.explanation,
      }
    );
    app.utilities.logStep(
      "Select Small Business Applicant and/or its Affiliates presently involved in any pending legal action?"
    );
    await app.sbaquestionsSection.selectsmallBusinessApplicantLegal("Yes", {
      detailedexplaination8:
        app.data.SBAQuetions.SmallBusinessApplicantLegal.explanation,
    });
    app.utilities.logStep("Select Debarred Suspended Federal");
    await app.sbaquestionsSection.selectdebarredSuspendedFederal("Yes", {
      detailedexplaination9:
        app.data.SBAQuetions.DebarredSuspendedFederal.explanation,
    });
    app.utilities.logStep("Select Child SupportEnforcement Services ");
    await app.sbaquestionsSection.selectchildSupportEnforcementServices("Yes", {
      detailedexplaination10:
        app.data.SBAQuetions.ChildSupportEnforcementServices.explanation,
    });
    app.utilities.logStep(
      "Select Has the Small Business Applicant and/or its Affiliates ever obtained a direct or guaranteed loan from SBA or any other Federal agency or been a guarantor on such a loan?"
    );
    await app.sbaquestionsSection.selectSbaLoanOption("Yes", {
      delinquent: "Yes",
      delinquentExplanation: app.data.SBAQuetions.SbaLoanOption.explanation1,
      defaultLoss: "Yes",
      defaultLossExplanation: app.data.SBAQuetions.SbaLoanOption.explanation2,
    });
    app.utilities.logStep("Select Small Business Applicant Export");
    await app.sbaquestionsSection.selectsmallBusinessApplicantExport("Yes", {
      amount: app.data.SBAQuetions.SmallBusinessApplicantExport.amount,
    });
    app.utilities.logStep("Select Small Business Applicant Packer");
    await app.sbaquestionsSection.selectsmallBusinessApplicantPacker("Yes", {
      detailedexplaination11:
        app.data.SBAQuetions.SmallBusinessApplicantPacker.explanation,
    });
    app.utilities.logStep("Select Small Business Applicant Revenue");
    await app.sbaquestionsSection.selectsmallBusinessApplicantRevenue("Yes", {
      detailedexplaination12:
        app.data.SBAQuetions.SmallBusinessApplicantRevenue.explanation,
    });
    app.utilities.logStep("Select No SBA emp HouseHold Member");
    await app.sbaquestionsSection.selectnoSBAempHouseHoldMember("Yes", {
      detailedexplaination13:
        app.data.SBAQuetions.NoSBAempHouseHoldMember.explanation,
    });
    app.utilities.logStep("Select No Former SBA emp Separated ");
    await app.sbaquestionsSection.selectnoFormerSBAempSeparated("Yes", {
      detailedexplaination14:
        app.data.SBAQuetions.NoFormerSBAempSeparated.explanation,
    });
    app.utilities.logStep("Select No Member Sole Proprietor ");
    await app.sbaquestionsSection.selectnoMemberSoleProprietor("Yes", {
      detailedexplaination15:
        app.data.SBAQuetions.NoMemberSoleProprietor.explanation,
    });
    app.utilities.logStep("Select No Gov Emp GS-13 ");
    await app.sbaquestionsSection.selectnoGovEmpGS13("Yes", {
      detailedexplaination16: app.data.SBAQuetions.NoGovEmpGS13.explanation,
    });
    app.utilities.logStep("Select No Member Small Buiness Advisory");
    await app.sbaquestionsSection.selectnoMemberSmallBuinessAdvisory("Yes", {
      detailedexplaination17:
        app.data.SBAQuetions.NoMemberSmallBuinessAdvisory.explanation,
    });
    app.utilities.logStep("Select Have Controlled Bankruptcy Protection");
    await app.sbaquestionsSection.selecthaveControlledBankruptcyProtection(
      "Yes",
      {
        detailedexplaination18:
          app.data.SBAQuetions.HaveControlledBankruptcyProtection.explanation,
      }
    );
    app.utilities.logStep("Select Applicants Payroll Calculation ");
    await app.sbaquestionsSection.selectapplicantsPayrollCalculation("Yes", {
      detailedexplaination19:
        app.data.SBAQuetions.ApplicantsPayrollCalculation.explanation,
    });
    app.utilities.logStep("Select Estimated Monthly Payroll");
    await app.sbaquestionsSection.selectestimatedMonthlyPayroll("Yes", {
      detailedexplaination20:
        app.data.SBAQuetions.EstimatedMonthlyPayroll.explanation,
    });
    app.utilities.logStep("Select Loss To The Government");
    await app.sbaquestionsSection.selectlossToTheGovernment("Yes", {
      detailedexplaination21:
        app.data.SBAQuetions.LossToTheGovernment.explanation,
    });
    app.utilities.logStep("Select Business Control Legal Action");
    await app.sbaquestionsSection.selectbusinessControlLegalAction("Yes", {
      detailedexplaination22:
        app.data.SBAQuetions.BusinessControlLegalAction.explanation,
    });
    app.utilities.logStep("Select Sba Economic Injury");
    await app.sbaquestionsSection.selectsbaEconomicInjury("Yes", {
      loanamount: app.data.SBAQuetions.SbaEconomicInjury.amount,
      detailedexplaination23:
        app.data.SBAQuetions.SbaEconomicInjury.explanation,
    });
    app.utilities.logStep("Select Have Ownership Affiliate");
    await app.sbaquestionsSection.selectHaveOwnershipAffiliate("Yes", {
      detailedexplaination24:
        app.data.SBAQuetions.HaveOwnershipAffiliate.explanation,
      businessinfo: app.data.SBAQuetions.HaveOwnershipAffiliate.businessinfo,
    });
    app.utilities.logStep("Select W-2 Employees Count");
    await app.sbaquestionsSection.selectw2Employeescount(
      app.data.SBAQuetions.W2Employeescount.count
    );
    app.utilities.logStep("Select W-2s issue in 2020");
    await app.sbaquestionsSection.selectw2issuesin2020(
      app.data.SBAQuetions.W2issuesin2020.count
    );
    app.utilities.logStep("Select When was your company started? ");
    await app.sbaquestionsSection.selectWhenWasCompanyStarted(
      app.data.SBAQuetions.Companystarted.value
    );
    app.utilities.logStep(
      "Select Did you operate under full or partial suspension ? "
    );
    await app.sbaquestionsSection.selectGovOrderSuspensionStatus(
      "Partial Suspension",
      {
        impactedExplanation: app.data.SBAQuetions.impactedExplanation.value,
        affectedQuarters: app.data.SBAQuetions.affectedQuarters1.value,
      }
    );
    app.utilities.logStep("Filling Gross Receipts ");
    await app.sbaquestionsSection.fillGrossReceipts1(
      app.data.SBAQuetions.grossReceipts1
    );
    app.utilities.logStep("Select Select Vendor Suspension Status");
    await app.sbaquestionsSection.selectVendorSuspensionStatus(
      "Partial Suspension",
      {
        vendorImpactDetails: {
          affectedQuarters: app.data.SBAQuetions.affectedQuarters2.value,
          impactExplanation: app.data.SBAQuetions.impactExplanation.explanation,
        },
      }
    );
    app.utilities.logStep("Filling Gross Receipts ");
    await app.sbaquestionsSection.fillGrossReceipts2(
      app.data.SBAQuetions.grossReceipts2
    );
    app.utilities.logStep("Select Quarter Experienced Loss in 2020 ");
    await app.sbaquestionsSection.selectquarterExperiencedLoss2020(
      app.data.SBAQuetions.QuarterExperiencedLoss2020.value
    );
    app.utilities.logStep("Select Quarter Experienced Loss in 2021 ");
    await app.sbaquestionsSection.selectquarterExperiencedLoss2021(
      app.data.SBAQuetions.QuarterExperiencedLoss2021.value
    );
    app.utilities.logStep("Fill PPP Draw Loan Amount ");
    await app.sbaquestionsSection.fillPPPDrawAmounts(
      app.data.SBAQuetions.pppLoanamount
    );
    app.utilities.logStep("Select PPP Draw Loan Forgiveness ");
    await app.sbaquestionsSection.fillPPPDrawForgiveness("Yes", "No");
    app.utilities.logStep(
      "Fill ;If you did have PPP loan forgiveness, what was the forgiven amount(s)?"
    );
    await app.sbaquestionsSection.havePPPLoanForgiven(
      app.data.SBAQuetions.ForgivenAmounts
    );
    app.utilities.logStep("Select Payroll Service Provider ");
    await app.sbaquestionsSection.selectpayrollServiceProvider(
      app.data.SBAQuetions.PayrollService.value
    );
    app.utilities.logStep("Select Employee Retention Credits ");
    await app.sbaquestionsSection.selectEmpRetentionCre("Yes", {
      whichquarterandcompany: app.data.SBAQuetions.EmpRetentionCre.details,
    });
    app.utilities.logStep("Select Employee Family Owner ");
    await app.sbaquestionsSection.selectemployeeFamilyOwner(
      app.data.SBAQuetions.EmployeeFamilyOwner.value
    );
    app.utilities.logStep("Select Have You Started Any Other Busniess ");
    await app.sbaquestionsSection.selectstartedotherbusiness(
      app.data.SBAQuetions.Startedotherbusiness.value
    );
    app.utilities.logStep("Select Do you have other Existing Busniess ");
    await app.sbaquestionsSection.selectotherExistingBusiness(
      app.data.SBAQuetions.OtherExistingBusiness.value
    );
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep(
      "Validate added all details for SBA Questions section"
    );
    await app.sbaquestionsSection.validateAllDetailsAddedToSBAQuestionsSection();
    app.utilities.logStep(
      "Validate and clear all values for SBA Questions section"
    );
    await app.sbaquestionsSection.setAllSBAQuestionsToNo();
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep("Validate removed details for SBA Questions section");
    await app.sbaquestionsSection.validateAllSBAQuestionsSetToNo();
  });
  test("Validate the Borrower Experience section", async () => {
    await test.step("Open loan form and navigate borrower experience", async () => {
      app.utilities.logStep("Open borrower experience section");
      await app.borrowerInfoTab.openLoanFile(loanFile1);
    });

    await test.step("Fill borrower experience (create)", async () => {
      app.utilities.logStep("Filling borrower experience property1(create)");
      await app.borrowerExperienceSection.fillBuyHoldExpProperty0(
        app.borrowerExperienceCreateData,
        app.borrowerExperienceCreateData.flips[0]
      );

      app.utilities.logStep("Filling borrower experience property2 (create)");
      await app.borrowerExperienceSection.fillBuyHoldExpProperty1(
        app.borrowerExperienceCreateData,
        app.borrowerExperienceCreateData.flips[1]
      );

      app.utilities.logStep("Filling borrower experience property3 (create)");
      await app.borrowerExperienceSection.fillBuyHoldExpProperty2(
        app.borrowerExperienceCreateData,
        app.borrowerExperienceCreateData.flips[2]
      );
    });

    await test.step("Fill borrower experience (create)", async () => {
      app.utilities.logStep(
        "Filling borrower experience ground up property1(create)"
      );
      await app.borrowerExperienceSection.fillGroundUpExpProperty0(
        app.borrowerExperienceCreateData,
        app.borrowerExperienceCreateData.flips[0]
      );

      app.utilities.logStep(
        "Filling borrower experience ground up property2 (create)"
      );
      await app.borrowerExperienceSection.fillGroundUpExpProperty1(
        app.borrowerExperienceCreateData,
        app.borrowerExperienceCreateData.flips[1]
      );

      app.utilities.logStep(
        "Filling borrower experience ground up property3 (create)"
      );
      await app.borrowerExperienceSection.fillGroundUpExpProperty2(
        app.borrowerExperienceCreateData,
        app.borrowerExperienceCreateData.flips[2]
      );
    });

    await test.step("Fill borrower experience (create)", async () => {
      app.utilities.logStep(
        "Filling borrower experience selling experience property1(create)"
      );
      await app.borrowerExperienceSection.fillSellingExpProperty0(
        app.borrowerExperienceCreateData,
        app.borrowerExperienceCreateData.sells[0]
      );

      app.utilities.logStep(
        "Filling borrower experience selling experience property2 (create)"
      );
      await app.borrowerExperienceSection.fillSellingExpProperty1(
        app.borrowerExperienceCreateData,
        app.borrowerExperienceCreateData.sells[1]
      );

      app.utilities.logStep(
        "Filling borrower experience selling experience property3 (create)"
      );
      await app.borrowerExperienceSection.fillSellingExpProperty2(
        app.borrowerExperienceCreateData,
        app.borrowerExperienceCreateData.sells[2]
      );
    });

    await test.step("Fill borrower experience other(create)", async () => {
      app.utilities.logStep(
        "Filling borrower experience other fileds (create)"
      );
      await app.borrowerExperienceSection.fillOtherDetails(
        app.borrowerExperienceCreateData
      );
    });

    await test.step("Fill Typical Transactions (create)", async () => {
      app.utilities.logStep("Filling Typical Transactions section (create)");
      await app.borrowerExperienceSection.fillTypicalTransactions(
        app.borrowerExperienceTTCreateData
      );
    });

    await test.step("Validate borrower experience (create)", async () => {
      app.utilities.logStep("Validating borrower experience after save");
      await app.borrowerExperienceSection.validateBorrowerExperienceCreate(
        app.borrowerExperienceCreateData
      );
    });

    await test.step("Validate Typical Transactions (create)", async () => {
      app.utilities.logStep("Validating Typical Transactions section (create)");
      await app.borrowerExperienceSection.validateTypicalTransactions(
        app.borrowerExperienceTTCreateData
      );
    });

    await test.step("Update borrower experience Fix&Flip (update)", async () => {
      app.utilities.logStep(
        "Update borrower experience Fix&Flip property1 (update)"
      );
      await app.borrowerExperienceSection.updateBuyHoldExpProperty0(
        app.borrowerExperienceUpdateData,
        app.borrowerExperienceUpdateData.flips[0]
      );

      app.utilities.logStep(
        "Filling borrower experience Fix&Flip property2 (update)"
      );
      await app.borrowerExperienceSection.updateBuyHoldExpProperty1(
        app.borrowerExperienceUpdateData,
        app.borrowerExperienceUpdateData.flips[1]
      );

      app.utilities.logStep(
        "Update borrower experience Fix&Flip property3 (update)"
      );
      await app.borrowerExperienceSection.updateBuyHoldExpProperty2(
        app.borrowerExperienceUpdateData,
        app.borrowerExperienceUpdateData.flips[2]
      );
    });

    await test.step("Update borrower experience ground up (update)", async () => {
      app.utilities.logStep("Update borrower experience ground up property 1");
      await app.borrowerExperienceSection.updateGroundUpExpProperty0(
        app.borrowerExperienceUpdateData,
        app.borrowerExperienceUpdateData.flips[0]
      );
      app.utilities.logStep(
        "Update borrower experience ground up property 2 (update)"
      );
      await app.borrowerExperienceSection.updateGroundUpExpProperty1(
        app.borrowerExperienceUpdateData,
        app.borrowerExperienceUpdateData.flips[1]
      );
      app.utilities.logStep(
        "Update borrower experience ground up property 3 (update)"
      );
      await app.borrowerExperienceSection.updateGroundUpExpProperty2(
        app.borrowerExperienceUpdateData,
        app.borrowerExperienceUpdateData.flips[2]
      );
    });

    await test.step("Update borrower selling experience  (update)", async () => {
      app.utilities.logStep(
        "Update borrower experience selling experience property0 (update)"
      );
      await app.borrowerExperienceSection.updateSellingExpProperty0(
        app.borrowerExperienceUpdateData,
        app.borrowerExperienceUpdateData.sells[0]
      );
      app.utilities.logStep(
        "Update borrower experience selling experience property1 (update)"
      );
      await app.borrowerExperienceSection.updateSellingExpProperty1(
        app.borrowerExperienceUpdateData,
        app.borrowerExperienceUpdateData.sells[1]
      );
      app.utilities.logStep(
        "Update borrower experience selling experience property2 (update)"
      );
      await app.borrowerExperienceSection.updateSellingExpProperty2(
        app.borrowerExperienceUpdateData,
        app.borrowerExperienceUpdateData.sells[2]
      );
    });

    await test.step("Update borrower experience other (update)", async () => {
      app.utilities.logStep("Update borrower experience other fields (update)");
      await app.borrowerExperienceSection.updateOtherDetails(
        app.borrowerExperienceUpdateData
      );
    });

    await test.step("Update Typical Transactions (create)", async () => {
      app.utilities.logStep("Update Typical Transactions section (create)");
      await app.borrowerExperienceSection.updateTypicalTransactions(
        app.borrowerExperienceTTUpdateData
      );
    }); //

    await test.step("Validate borrower experience (update)", async () => {
      app.utilities.logStep("Validating borrower experience after update");
      await app.borrowerExperienceSection.validateBorrowerExperienceUpdate(
        app.borrowerExperienceUpdateData
      );
    });

    await test.step("Validate Typical Transactions (update)", async () => {
      app.utilities.logStep("Validating Typical Transactions section (update)");
      await app.borrowerExperienceSection.validateUpdatedTypicalTransactions(
        app.borrowerExperienceTTUpdateData
      );
    });

    await test.step("Clear borrower experience (delete)", async () => {
      app.utilities.logStep("Clearing borrower experience (delete)");
      await app.borrowerExperienceSection.clearBorrowerExperienceFields();
    });

    await test.step("Delete Typical Transactions", async () => {
      app.utilities.logStep("Clearing Typical Transactions section (delete)");
      await app.borrowerExperienceSection.clearTypicalTransactions();
    });

    await test.step("Validate borrower experience (delete)", async () => {
      app.utilities.logStep("Validating borrower experience after delete");
      await app.borrowerExperienceSection.validateBorrowerExperienceCleared(
        app.borrowerExperienceDeleteData
      );
    });

    await test.step("Validate Typical Transactions (delete)", async () => {
      app.utilities.logStep("Validating Typical Transactions section (delete)");
      await app.borrowerExperienceSection.validateTypicalTransactionsCleared();
    });
  });
  test("Validate the Additional Guarantors section", async () => {
    await test.step("Open loan file (create)", async () => {
      app.utilities.logStep("Opening loan file for create guarantor");
      await app.borrowerInfoTab.openLoanFile(loanFile);
    });

    await test.step("Fill Additional Guarantor form (create)", async () => {
      app.utilities.logStep("Filling additional guarantor form (create)");
      await app.additionalGuarantors.createGuarantor(
        app.additionalGuarantorsData.create
      );
      // app.utilities.logStep("Filling additional notes");
      // await app.additionalGuarantors.fillAdditionalNotes(
      //   app.additionalGuarantorsData.create
      // );
    });

    await test.step("Save and validate persisted Additional Guarantor (create)", async () => {
      app.utilities.logStep("Saving additional guarantor (create)");
      await app.borrowerInfoTab.clickOnSaveButton();

      await app.borrowerInfoTab.openLoanFile(loanFile);
      await app.additionalGuarantors.validateGuarantorForm(
        app.additionalGuarantorsData.create
      );
    });

    await test.step("Open loan file (update)", async () => {
      app.utilities.logStep("Opening loan file for update guarantor");
      await app.borrowerInfoTab.openLoanFile(loanFile);
    });

    await test.step("Update Additional Guarantor form", async () => {
      app.utilities.logStep("Updating additional guarantor form");
      await app.additionalGuarantors.updateGuarantor(
        app.additionalGuarantorsData.update
      );
    });

    await test.step("Save and validate persisted Additional Guarantor (update)", async () => {
      app.utilities.logStep("Saving additional guarantor (update)");
      await app.borrowerInfoTab.clickOnSaveButton();

      await app.borrowerInfoTab.openLoanFile(loanFile);
      await app.additionalGuarantors.validateGuarantorForm(
        app.additionalGuarantorsData.update
      );
    });

    await test.step("Open loan file (delete)", async () => {
      app.utilities.logStep("Opening loan file for delete guarantor");
      await app.borrowerInfoTab.openLoanFile(loanFile);
    });

    await test.step("Delete Additional Guarantor", async () => {
      app.utilities.logStep("Deleting additional guarantor");
      await app.additionalGuarantors.deleteGuarantor();
    });

    await test.step("Save and validate Additional Guarantor removal", async () => {
      app.utilities.logStep("validate Save button is disabled after deletion");
      await app.additionalGuarantors.validateAllFieldsCleared();
    });
  });
  test("Validate the Co-borrower Information section", async () => {
    app.utilities.logStep("Navigate to Borrower Info And Check Tab Click");
    await app.borrowerInfoTab.openLoanFile(loanFile);
    app.utilities.logStep("Check the loan card should be visible");
    await app.adminInfoSection.checkLoanFileCardVisibility();
    app.utilities.logStep("Click on borrower info tab");
    await app.adminInfoSection.clickOnBorrowerInfoTab();
    app.utilities.logStep(
      "Navigate to Co-borrower Info section and add details to fields"
    );
    app.utilities.logStep("Check visibility of Co-Borrower Info card");
    await app.coBorrowerInfoSection.checkcoBorrowervisibility();
    app.utilities.logStep("Select First Name");
    await app.coBorrowerInfoSection.fillFname(app.coBorrowerInfoData.Fname);
    app.utilities.logStep("Select Middle Name");
    await app.coBorrowerInfoSection.fillMname(app.coBorrowerInfoData.Mname);
    app.utilities.logStep("Select Last Name");
    await app.coBorrowerInfoSection.fillLname(app.coBorrowerInfoData.Lname);
    app.utilities.logStep("Select Email Id");
    await app.coBorrowerInfoSection.fillemail(app.coBorrowerInfoData.email);
    app.utilities.logStep("Select Home No");
    await app.coBorrowerInfoSection.fillhomeno(app.coBorrowerInfoData.homeno);
    app.utilities.logStep("Select Cell No");
    await app.coBorrowerInfoSection.fillcellno(app.coBorrowerInfoData.cellno);
    app.utilities.logStep("Select Fax No");
    await app.coBorrowerInfoSection.fillfax(app.coBorrowerInfoData.faxno);
    app.utilities.logStep("Select Moile Service Provider");
    await app.coBorrowerInfoSection.selectMobileServiceProvider(
      app.coBorrowerInfoData.serviceprovider
    );
    app.utilities.logStep("Select Address ");
    await app.coBorrowerInfoSection.selectSameAsBorrowerAddress("off", {
      fill: app.coBorrowerInfoData.sameAsBorrower.extraData,
    });
    //await app.coBorrowerInfoSection.selectSameAsBorrowerAddress("on");
    app.utilities.logStep("Select Is the Co-Borrower Guaranteeing the loan? ");
    await app.coBorrowerInfoSection.selectcoborguaranteeloan("No");
    app.utilities.logStep("Select Length of time at address ");
    await app.coBorrowerInfoSection.selectlengthoftime(
      app.coBorrowerInfoData.lengthoftime
    );
    app.utilities.logStep(
      "Select Has the borrower resided at the present address for less than two years?"
    );
    await app.coBorrowerInfoSection.selectborresidedlessthan2("Yes", {
      fill: app.coBorrowerInfoData.borresidedlessthan2.extraData,
    });
    app.utilities.logStep("Select And Fill Mailing Adress");
    await app.coBorrowerInfoSection.selectMailingAddress("off", {
      fill: app.coBorrowerInfoData.bormailaddress.extraData,
    });
    app.utilities.logStep("Select Personal Info and Fill Date of Birth");
    await app.coBorrowerInfoSection.fillCalendarField(
      app.coBorrowerInfoData.DOB.date
    );
    app.utilities.logStep("Select Place of Birth");
    await app.coBorrowerInfoSection.selectPlaceOfBirth(
      app.coBorrowerInfoData.PlaceOfBirth.place
    );
    app.utilities.logStep("Select Social Security Number");
    await app.coBorrowerInfoSection.selectsocialsecurityno(
      app.coBorrowerInfoData.SecurityNo.no
    );
    app.utilities.logStep("Select Driver License State ");
    await app.coBorrowerInfoSection.selectdriverlicsencestate(
      app.coBorrowerInfoData.driverState.state
    );
    app.utilities.logStep("Select Driver License Number ");
    await app.coBorrowerInfoSection.selectdriverlicsenceno(
      app.coBorrowerInfoData.driverlicno.no
    );
    app.utilities.logStep("Select Driver License Issuance Date ");
    await app.coBorrowerInfoSection.filldriverlicissuedate(
      app.coBorrowerInfoData.Driverlicissuedate.date
    );
    app.utilities.logStep("Select Driver License Expiration Date ");
    await app.coBorrowerInfoSection.fillDriverLicenseExp(
      app.coBorrowerInfoData.DriverLicenseExpiration.date
    );
    app.utilities.logStep("Select Marital Status? ");
    await app.coBorrowerInfoSection.selectmaritialstatus("Unmarried");
    app.utilities.logStep("Select Married to Borrower? ");
    await app.coBorrowerInfoSection.selectmarriedtobor("Yes");
    app.utilities.logStep("Select Citizenship ");
    await app.coBorrowerInfoSection.selectcitizenship(" Perm Resident ");
    app.utilities.logStep("Select Equifax Score ");
    await app.coBorrowerInfoSection.selectequifaxscore(
      app.coBorrowerInfoData.equifax.no
    );
    app.utilities.logStep("Select Mid Fico Score ");
    await app.coBorrowerInfoSection.selectmidficoscore(
      app.coBorrowerInfoData.midfico.no
    );
    app.utilities.logStep("Select Transunion Score ");
    await app.coBorrowerInfoSection.selecttransunionscore(
      app.coBorrowerInfoData.transunion.no
    );
    app.utilities.logStep("Select Experian Score");
    await app.coBorrowerInfoSection.selectexperianscore(
      app.coBorrowerInfoData.experian.no
    );
    app.utilities.logStep("Select Credit Score Range");
    await app.coBorrowerInfoSection.selectcreditrange(
      app.coBorrowerInfoData.creditrange.value
    );
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep(
      "Navigate to Co-borrower Info section and Validate added details to fields"
    );
    app.utilities.logStep(
      "Validating added fields for Co-Borrower Info Section"
    );
    await app.coBorrowerInfoSection.validateCoBorrowerInformation(
      app.coBorrowerInfoData
    );
    app.utilities.logStep(
      "Navigate to Co-borrower Info section and Remove added details to fields"
    );
    await app.utilities.logStep(
      "Remove added fields for Co-Borrower Info Section"
    );
    await app.coBorrowerInfoSection.clearCoBorrowerInformation();
    app.utilities.logStep("Click on save button");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep(
      "Navigate to Co-Borrower Info Section and Validate All fields are cleared"
    );
    await app.coBorrowerInfoSection.validateCoBorrowerInformationCleared();
  });
  test("Validate the Co-Borrower Background Setion", async () => {
    app.utilities.logStep("Navigate to Co-Borrower Background section");
    app.utilities.logStep("Navigate to Borrower Info And Check Tab Click");
    await app.borrowerInfoTab.openLoanFile(loanFile);
    app.utilities.logStep("Check the loan card should be visible");
    await app.adminInfoSection.checkLoanFileCardVisibility();
    app.utilities.logStep("Click on borrower info tab");
    await app.adminInfoSection.clickOnBorrowerInfoTab();
    app.utilities.logStep(
      "Navigate to Co-borrower Background section and add details to fields"
    );
    app.utilities.logStep("Check visibility of Co-Borrower Background card");
    await app.coBorrowerBackgroundSection.coBorrowerBackVisibility();
    app.utilities.logStep("Select Are you a U.S. Citizen?");
    await app.coBorrowerBackgroundSection.selectUSCitizen(
      true, // false = NO, true = YES
      app.coBorrowerBackgroundData.origin, // only required if NO
      app.coBorrowerBackgroundData.visaStatus // only required if NO
    );
    app.utilities.logStep("Select Have you filed for bankruptcy?");
    await app.coBorrowerBackgroundSection.selectBankruptcy(
      true,
      app.coBorrowerBackgroundData.bankruptDetails
    );
    app.utilities.logStep("Select Do you have any judgments?");
    await app.coBorrowerBackgroundSection.selectJudgments(
      true,
      app.coBorrowerBackgroundData.judgmentsDetails
    );
    app.utilities.logStep("Select Do you have any tax liens?");
    await app.coBorrowerBackgroundSection.selectTaxLiens(
      true,
      app.coBorrowerBackgroundData.taxLiensDetails
    );
    app.utilities.logStep("Select Are you involved in any lawsuits?");
    await app.coBorrowerBackgroundSection.selectLawsuits(
      true,
      app.coBorrowerBackgroundData.lawsuitsDetails
    );
    app.utilities.logStep("Select Do you have any obligations?");
    await app.coBorrowerBackgroundSection.selectObligations(
      true,
      app.coBorrowerBackgroundData.obligationsDetails
    );
    app.utilities.logStep("Select Are you delinquent on any debts?");
    await app.coBorrowerBackgroundSection.selectDelinquent(
      true,
      app.coBorrowerBackgroundData.delinquentDetails
    );
    app.utilities.logStep("Select Have you ever been convicted of a felony?");
    await app.coBorrowerBackgroundSection.selectFelony(
      true,
      app.coBorrowerBackgroundData.felonyDetails
    );
    app.utilities.logStep("Select Are you in a civil union?");
    await app.coBorrowerBackgroundSection.selectCivilUnion(
      true,
      app.coBorrowerBackgroundData.civilUnionDetails
    );
    app.utilities.logStep("Enter Background Explanation");
    await app.coBorrowerBackgroundSection.enterBackgroundExplanation(
      app.coBorrowerBackgroundData.backgroundExplanation
    );
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep("Co-Borrower Background Section filled successfully");
    app.utilities.logStep(
      "Navigate to Co-borrower Background section and Validate added details to fields"
    );
    app.utilities.logStep("Check visibility of Co-Borrower Background card");
    await app.coBorrowerBackgroundSection.coBorrowerBackVisibility();
    app.utilities.logStep("Validating Co-Borrower Background section");
    await app.coBorrowerBackgroundSection.validateCoBorrowerBackground(
      app.coBorrowerBackgroundData
    );
    app.utilities.logStep(
      "All Co-Borrower Background fields validated Successfully"
    );
    app.utilities.logStep(
      "Navigate to Co-borrower Background section and Set all Co-Borrower Background fields to NO"
    );
    await app.coBorrowerBackgroundSection.selectNoForAllBackgroundFields();
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep(
      "Navigate to Co-borrower Background section and Validate Removed details to fields"
    );
    app.utilities.logStep("Check visibility of Co-Borrower Background card");
    await app.coBorrowerBackgroundSection.coBorrowerBackVisibility();
    app.utilities.logStep(
      "Validate Co-Borrower Background: US Citizen YES, others NO"
    );
    await app.coBorrowerBackgroundSection.validateBackgroundFieldsUSCitizenYesOthersNo();
  });
  test("Validate the Co-Borrower Experience Setion", async () => {
    app.utilities.logStep("Navigate to Borrower Info And Check Tab Click");
    await app.borrowerInfoTab.openLoanFile(loanFile);
    app.utilities.logStep("Check the loan card should be visible");
    await app.adminInfoSection.checkLoanFileCardVisibility();
    app.utilities.logStep("Click on borrower info tab");
    await app.adminInfoSection.clickOnBorrowerInfoTab();
    app.utilities.logStep(
      "Navigate to Co-borrower Experience Section and add details to fields"
    );
    await app.utilities.logStep("Filling Fix & Flip Experience");
    await app.coBorrowerExperienceSection.fillFixFlipExperience(
      app.coBorrowerExperienceData.fixFlipExperience
    );
    await app.utilities.logStep("Filling Ground Up Experience");
    await app.coBorrowerExperienceSection.fillGroundUpExperience(
      app.coBorrowerExperienceData.groundUpExperience
    );
    await app.utilities.logStep(
      "Filling Selling Without Construction Experience"
    );
    await app.coBorrowerExperienceSection.fillSellingWithoutConstruction(
      app.coBorrowerExperienceData.sellingWithoutConstruction
    );
    await app.utilities.logStep("Filling Projects in Progress");
    await app.coBorrowerExperienceSection.fillProjectsInProgress(
      app.coBorrowerExperienceData.projectsInProgress
    );
    await app.utilities.logStep("Filling Investment Properties");
    await app.coBorrowerExperienceSection.fillInvestmentProperties(
      app.coBorrowerExperienceData.investmentProperties
    );
    await app.utilities.logStep("Filling Investment Club Info");
    await app.coBorrowerExperienceSection.fillInvestmentClub(
      app.coBorrowerExperienceData.investmentClub
    );
    await app.utilities.logStep("Filling Professional Licenses");
    await app.coBorrowerExperienceSection.fillProfessionalLicenses(
      app.coBorrowerExperienceData.professionalLicenses
    );
    await app.utilities.logStep("Setting Liquid Reserves");
    await app.coBorrowerExperienceSection.setLiquidReserves(
      app.coBorrowerExperienceData.liquidReserves
    );
    await app.utilities.logStep("Setting Primary Investment Strategy");
    await app.coBorrowerExperienceSection.setPrimaryInvestmentStrategy(
      app.coBorrowerExperienceData.primaryStrategy
    );
    await app.utilities.logStep("Explaining Primary Investment Strategy");
    await app.coBorrowerExperienceSection.setPrimaryInvestmentStrategyExplain(
      app.coBorrowerExperienceData.primaryStrategyExplain
    );
    await app.utilities.logStep(
      "✅ Co-Borrower Experience Section filled successfully!"
    );
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep(
      "Navigate to Co-borrower Expereince section and Validate added details to fields"
    );
    app.utilities.logStep("Validating Co-Borrower Experience section");
    app.utilities.logStep("Check visibility of Co-Borrower Experience card");
    await app.coBorrowerExperienceSection.checkcoBorrowerexpvisibility();
    await app.coBorrowerExperienceSection.validateAllFields(
      app.coBorrowerExperienceData
    );
    app.utilities.logStep(
      "All Co-Borrower Experience fields validated Successfully"
    );
    app.utilities.logStep(
      "Navigate to Co-borrower Experience section and Remove added details to fields"
    );
    app.utilities.logStep("Set all Co-Borrower Experience fields to NO");
    await app.coBorrowerExperienceSection.setAllExperienceFieldsToNo();
    app.utilities.logStep("Save the added fields details");
    await app.borrowerInfoTab.clickOnSaveButton();
    app.utilities.logStep("All fields cleared and set to NO");
    app.utilities.logStep(
      "Navigate to Co-borrower Experience section and Validate Removed details to fields"
    );
    app.utilities.logStep("Check visibility of Co-Borrower Experience card");
    await app.coBorrowerExperienceSection.checkcoBorrowerexpvisibility();
    app.utilities.logStep(
      "Validate Co-Borrower Experience fields set to NO and are cleared"
    );
    await app.coBorrowerExperienceSection.validateAllExperienceFieldsNo();
    app.utilities.logStep("All fields Validated Successfully");
  });
  test("HMDA Borrower — CRUD)", async () => {
    await test.step("Open loan file (create)", async () => {
      app.utilities.logStep("Opening loan file");
      await app.borrowerInfoTab.openLoanFile(loanFile1);
    });

    await test.step("Fill HMDA Borrower section (create)", async () => {
      app.utilities.logStep("Filling HMDA Borrower section");
      await app.HMDABorrowerSection.fillHMDABorrower(app.HMDABorrowerCreate);
    });

    await test.step("Validate Filled HMDA Borrower section (create)", async () => {
      app.utilities.logStep("Validate Filled HMDA Borrower section");
      await app.HMDABorrowerSection.validateHMDABorrowerCreate(
        app.HMDABorrowerCreate
      );
    });

    await test.step("Update HMDA Borrower section (create)", async () => {
      app.utilities.logStep("Update HMDA Borrower section");
      await app.HMDABorrowerSection.updateHMDABorrower(app.HMDABorrowerUpdate);
    });

    await test.step("Validate Updated HMDA Borrower section (create)", async () => {
      app.utilities.logStep("Validate Updated HMDA Borrower section");
      await app.HMDABorrowerSection.validateHMDABorrowerUpdate(
        app.HMDABorrowerUpdate
      );
    });

    await test.step("Delete HMDA Borrower section (create)", async () => {
      app.utilities.logStep("Delete HMDA Borrower section");
      await app.HMDABorrowerSection.clearHMDABorrower(app.HMDABorrowerDelete);
    });

    await test.step("Validate Delete HMDA Borrower section (create)", async () => {
      app.utilities.logStep("Validate Delete HMDA Borrower section");
      await app.HMDABorrowerSection.validateHMDABorrowerDelete();
    });
  });
  test("HMDA Co - Borrower — CRUD)", async () => {
    await test.step("Open loan file (create)", async () => {
      app.utilities.logStep("Opening loan file");
      await app.borrowerInfoTab.openLoanFile(loanFile1);
    });

    await test.step("Fill HMDA Co - Borrower section (create)", async () => {
      app.utilities.logStep("Filling HMDA Borrower section");
      await app.HMDACoBorrowerSection.fillHMDACoBorrower(
        app.HMDABorrowerCreate
      );
    });

    await test.step("Validate Filled HMDA Co - Borrower section (create)", async () => {
      app.utilities.logStep("Validate Filled HMDA Borrower section");
      await app.HMDACoBorrowerSection.validateHMDACoBorrowerCreate(
        app.HMDABorrowerCreate
      );
    });

    await test.step("Update HMDA Co - Borrower section (create)", async () => {
      app.utilities.logStep("Update HMDA Co - Borrower section");
      await app.HMDACoBorrowerSection.updateHMDACoBorrower(
        app.HMDABorrowerUpdate
      );
    });

    await test.step("Validate Updated HMDA Co - Borrower section (create)", async () => {
      app.utilities.logStep("Validate Updated HMDA Co - Borrower section");
      await app.HMDACoBorrowerSection.validateHMDACoBorrowerUpdate(
        app.HMDABorrowerUpdate
      );
    });

    await test.step("Delete HMDA Co - Borrower section (create)", async () => {
      app.utilities.logStep("Delete HMDA Co - Borrower section");
      await app.HMDACoBorrowerSection.clearHMDACoBorrower(
        app.HMDABorrowerDelete
      );
    });

    await test.step("Validate Delete HMDA Co - Borrower section (create)", async () => {
      app.utilities.logStep("Validate Delete HMDA Co - Borrowerr section");
      await app.HMDACoBorrowerSection.validateHMDACoBorrowerDelete();
    });
  });
});
