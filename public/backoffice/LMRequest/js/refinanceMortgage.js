$(document).on('click', '.cloneRCMForm', function () {

    let cloneSection = $(this).attr('data-clone-section');
    let icrementSec = $(this).attr('data-increment-section');
    let _cs = $('.' + cloneSection);
    let mainSecLength = _cs.length;
    if (mainSecLength === 3) {
        toastrNotification('Maximum 3 Refinance Mortgages Allowed', 'error');
        return;
    }

    let clonedSection = _cs.last().clone();
    let clonedIDArray = clonedSection.attr('id').split('_');
    let clonedIDCount = parseInt(clonedIDArray[parseInt(clonedIDArray.length) - 1]);
    let newSectionCount = clonedIDCount + 1;

    _cs.find('.cloneRCMForm').addClass('d-none').end();

    clonedSection
        .attr('id', function (idx, attrVal) {
            try {
                cloneSectionDivArray = attrVal.split('_');
                if (cloneSectionDivArray.length === 2) {
                    return cloneSectionDivArray[0] + '_' + (parseInt(cloneSectionDivArray[1]) + 1);
                }
            } catch (e) {
            }
        })
        .find(":input")
        .attr('id', function (idx, attrVal) {
            try {
                cloneSectionDivArray = attrVal.split('_');
                if (cloneSectionDivArray.length === 2) {
                    return cloneSectionDivArray[0] + '_' + (parseInt(cloneSectionDivArray[1]) + 1);
                }
            } catch (e) {
            }
        })
        .attr('name', function (idx, attrVal) {
            try {
                return attrVal.replace('[' + clonedIDCount + ']', '[' + newSectionCount + ']'); // fix is here
            } catch (e) {
            }
        })
        .attr('type', function (idx, attrVal) {
            if (attrVal === 'radio') {
                $(this).prop("checked", false);
            } else {
                $(this).val('');
            }
        }).removeAttr('checked').prop("checked", false).end()
        .find('.chosen-container').remove().end()
        .find('.chzn-select').show().end()
        .find('.is-invalid').removeClass('is-invalid').end()
        .find('label').attr('for', function (idx, attrVal) {
        try {
            cloneSectionDivArray = attrVal.split('_');
            if (cloneSectionDivArray.length === 2) {
                return cloneSectionDivArray[0] + '_' + (parseInt(cloneSectionDivArray[1]) + 1);
            }
        } catch (e) {
        }
    }).end()
        // .find('.cloneRCMForm').addClass('d-none').end()
        .find('.' + icrementSec).html(newSectionCount).end()
        .find('.removeRCMForm').removeAttr('data-id').end()
        .find('.removeCloneButton').removeClass('d-none').end()
        .insertAfter('.' + cloneSection + ':last');

    $('.chzn-select').chosen({allow_single_deselect: true, search_contains: true});

    autoIncrement('icrementClass');

});

$(document).on('click', '.removeRCMForm', function () {
    let _rms = $(this);
    let id = _rms.attr('data-id');

    if (typeof id !== 'undefined' && id !== false) {
        $.confirm({
            icon: 'fa fa-warning',
            closeIcon: true,
            title: 'Confirm',
            content: "Are you sure you want to delete?",
            type: 'red',
            backgroundDismiss: true,
            buttons: {
                yes: function () {
                    HTTP.Post(siteSSLUrl + 'JQFiles/deleteRefinanceMortgage.php', {
                        id: id,
                    }, function (resObj) {
                        if (parseInt(resObj.code) === 100) {
                            toastrNotification(resObj.success, 'success');
                            removeRCMCloneSection(_rms);
                        } else {
                            toastrNotification(resObj.error, 'error');
                        }
                    });
                },
                cancel: function () {

                },
            }
        });
    } else {
        removeRCMCloneSection(_rms);
    }
});

function removeRCMCloneSection(_rms) {
    let cloneSection = _rms.attr('data-clone-section');
    let icrementClass = _rms.attr('data-increment-section');
    let _cs = $('.' + cloneSection);
    if (_cs.length > 1) {
        _rms.parents('.' + cloneSection).remove();
        autoIncrement(icrementClass);
    } else {
        $('.RCMCloneSection').find('input').val('');
    }
    $('.' + cloneSection).last().find('.cloneRCMForm').removeClass('d-none');
}

function autoIncrement(icrementClass) {
    incrementSecCnt = 1;
    jQuery('.' + icrementClass).each(function (i) {
        $(this).html(incrementSecCnt);
        incrementSecCnt++;
    });
}
