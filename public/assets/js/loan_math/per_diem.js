class per_diem {

    diemDays = null;
    totalEstPerDiem = null;
    totalDailyInterestCharge = null;
    monthsDays = null;
    interestChargedFromDate = null;
    interestChargedEndDate = null;
    accrualType = null;
    perDiemAccrualType = null;
    LoanTerms = null;
    currentLoanBalance = null;
    totalLoanBalance = null;
    interestRate = null;
    InclusivePerDiem = null;

    static ACCRUAL_TYPE_30_360 = '360';
    static ACCRUAL_TYPE_30_365 = '30/365';
    static ACCRUAL_TYPE_ACTUAL_360 = 'Actual/360';
    static ACCRUAL_TYPE_ACTUAL_365 = '365';

    static CONST_LOAN_TERMS_ILA = 'ILA';
    static CONST_LOAN_TERMS_TLA = 'TLA';

    static getDailyInterest(
        accrualType,
        interest,
        totalLoanAmount
    ) {
        interest /= 100;

        switch (accrualType) {
            case this.ACCRUAL_TYPE_30_360:
            case this.ACCRUAL_TYPE_ACTUAL_360:
                interest /= 360;
                return totalLoanAmount * interest;

            case this.ACCRUAL_TYPE_ACTUAL_365:
            case this.ACCRUAL_TYPE_30_365:
                interest /= 365;
                return totalLoanAmount * interest;
        }
        return 0.0;
    }

    static dailyPerDiemInterest(
        perDiemLoanAmount,
        lien1Rate,
        perDiemAccrualType
    ) {

        return per_diem.getDailyInterest(
            perDiemAccrualType,
            lien1Rate,
            perDiemLoanAmount
        );
    }

    static perDiemLoanAmount(
        LoanTerms,
        currentLoanBalance,
        totalLoanAmount
    ) {

        if (LoanTerms === this.CONST_LOAN_TERMS_ILA) {
            return currentLoanBalance;
        }

        if (LoanTerms === this.CONST_LOAN_TERMS_TLA) {
            return totalLoanAmount;
        }

        return 0;
    }


    /**
     *
     * @param interestChargedFromDate
     * @param interestChargedEndDate
     * @param accrualType
     * @param perDiemAccrualType
     * @param LoanTerms
     * @param currentLoanBalance
     * @param totalLoanBalance
     * @param interestRate
     * @param InclusivePerDiem
     * @returns {per_diem}
     */
    static calc(
        interestChargedFromDate,
        interestChargedEndDate,
        accrualType,
        perDiemAccrualType,
        LoanTerms,
        currentLoanBalance,
        totalLoanBalance,
        interestRate,
        InclusivePerDiem
    ) {

        console.log({
            class: 'per_diem',
            func: 'calc',
            interestChargedFromDate: interestChargedFromDate,
            interestChargedEndDate: interestChargedEndDate,
            accrualType: accrualType,
            perDiemAccrualType: perDiemAccrualType,
            LoanTerms: LoanTerms,
            currentLoanBalance: currentLoanBalance,
            totalLoanBalance: totalLoanBalance,
            interestRate: interestRate,
            InclusivePerDiem: InclusivePerDiem,
        });

        let totalDailyInterestCharge = per_diem.dailyPerDiemInterest(
            per_diem.perDiemLoanAmount(
                LoanTerms,
                currentLoanBalance,
                totalLoanBalance
            ),
            interestRate,
            perDiemAccrualType
        );

 /*       if (perDiemAccrualType) {
            accrualType = perDiemAccrualType;
        }*/


        let res = new this();
        res.totalDailyInterestCharge = totalDailyInterestCharge;
        res.interestChargedFromDate = interestChargedFromDate;
        res.interestChargedEndDate = interestChargedEndDate;
        res.accrualType = accrualType;
        res.perDiemAccrualType = perDiemAccrualType;
        res.LoanTerms = LoanTerms;
        res.currentLoanBalance = currentLoanBalance;
        res.totalLoanBalance = totalLoanBalance;
        res.interestRate = interestRate;
        res.InclusivePerDiem = InclusivePerDiem;

        /* Diem Days */
        if (!interestChargedFromDate || !interestChargedEndDate) {

            res.diemDays = '';
            res.totalEstPerDiem = 0;

            return res;
        }

        let monthsDays = Dates.monthsDaysDiff(
            interestChargedFromDate,
            interestChargedEndDate,
            InclusivePerDiem
        );


        // console.log({
        //     func: 'dateDiffDiemDays',
        //     interestChargedFromDate: interestChargedFromDate,
        //     interestChargedEndDate: interestChargedEndDate,
        //     totalDailyInterestCharge: totalDailyInterestCharge,
        //     accrualType: accrualType,
        //     monthsDays: monthsDays,
        // });


        let diemDays;
        let totalEstPerDiem;

        switch (perDiemAccrualType) {
            case this.ACCRUAL_TYPE_ACTUAL_365: // 365 is also actual
            case this.ACCRUAL_TYPE_ACTUAL_360:
                diemDays = Math.round(monthsDays.actualDays) + ' Day' + (monthsDays.actualDays !== 1 ? 's' : '');
                totalEstPerDiem = parseFloat(monthsDays.actualDays) * parseFloat(totalDailyInterestCharge);
                break;

            case this.ACCRUAL_TYPE_30_360:
            case this.ACCRUAL_TYPE_30_365:
                diemDays = ''; //monthsDays.months + ' Months / ' + monthsDays.days + ' Days';
                if (monthsDays.months) {
                    diemDays += monthsDays.months + ' Month' + (monthsDays.months !== 1 ? 's' : '');
                }

                if (monthsDays.days) {
                    if (monthsDays.months) {
                        diemDays += ' / ';
                    }
                    diemDays += monthsDays.days + ' Day' + (monthsDays.days !== 1 ? 's' : '');
                }

                if(!diemDays) {
                    diemDays = '0 Days';
                }

                totalEstPerDiem = parseFloat(monthsDays.months * 30 + monthsDays.days) * parseFloat(totalDailyInterestCharge);
                break;
        }


        res.diemDays = diemDays;
        res.totalEstPerDiem = totalEstPerDiem;
        res.monthsDays = monthsDays;

        return res;
    }
}