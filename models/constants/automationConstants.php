<?php

namespace models\constants;

use models\types\strongType;

class automationConstants extends strongType
{
    /**
     * @var array|string[]
     */
    public static array $automation_LoanFileStatus = [
        1 => 'Create',
        2 => 'Update',
    ];

    //Text Labels
    public static string $automation_LoanFileStatusTitle = 'Loan File';
    public static string $automation_PrimaryFileStatus = 'Primary File Status';
    public static string $automation_FileSubStatus = 'File Sub Status';


    //short codes
    public static string $automation_PFS = 'PFS';
    public static string $automation_FSS = 'FSS';
    public static string $automation_FCU = 'FCU';
    public static string $automation_Workflow = 'Workflow';
    public static string $automation_LO = 'LO';

    public static int $automation_LoanFileStatus_Create = 1;
    public static int $automation_LoanFileStatus_Update = 2;

    public static string $automation_Branch = 'Branch';
    public static string $automation_Broker = 'Broker';
    public static string $automation_LoanOfficer = 'Loan Officer';

    public static string $automation_BorrowerStatus = 'BorrowerStatus';
    public static string $automation_BorrowerType = 'BorrowerType';

    public static string $automation_LoanProgram = 'LoanProgram';


    public static string $automation_BorrowerStatus_Title = 'Borrower Status';
    public static string $automation_BorrowerType_Title = 'Borrower Type';

    public static string $automation_LoanProgram_Title = 'Loan Program';

    public static string $automation_Rule = 'Automation Rule';

    public static string $automation_Webhook = 'Webhook';
    public static string $automation_Task = 'Task';
    public static string $automation_Email = 'Email';
    public static string $automation_Employee = 'Employee';

    public static string $automation_change_file_status = 'Change File Status';

    public static string $automation_Instant = 'Instant';
    public static string $automation_Schedule = 'Schedule';


    public static string $automation_response_employee = 'Employee Assigned Successfully';
    public static string $automation_response_change_file_status = 'File Status Changed Successfully';

    public static string $automation_rule_trigger_tooltip = 'Please follow the order of the options in the dropdown list to create the rule. You can skip the option that is not needed for this rule.';


}