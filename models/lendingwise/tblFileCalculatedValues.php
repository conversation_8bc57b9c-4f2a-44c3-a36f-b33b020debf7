<?php

namespace models\lendingwise;

use Exception;
use models\composite\LoanAmortization;
use models\composite\LoanPaymentDue;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\loanPropertySummary;
use models\Controllers\LMRequest\PropertyAnalysis;
use models\HMLOLoanTermsCalculation;
use models\HMLOLoanTermsCalculation\LTC2Variables;
use models\lendingwise\db\tblFileCalculatedValues_db;
use models\myFileInfo\fileHMLONewLoanInfo;
use models\standard\Dates;
use models\standard\Strings;

/**
 *
 */
class tblFileCalculatedValues extends tblFileCalculatedValues_db
{
    protected static bool $_has_change_log = true; // maybe - verify it doesn't blow up the change log 11/20

    public static function GetCalculatedValuesForLoan(
        int $LMRId
    ): tblFileCalculatedValues
    {
        LMRequest::$LMRId = null;
        LMRequest::setLMRId($LMRId);
        PropertyAnalysis::updatePropertiesAnalysisValues($LMRId);
        loanPropertySummary::updateLoanInfoV2Values($LMRId);
        fileHMLONewLoanInfo::calculateYieldSpread($LMRId);
        HMLOLoanTermsCalculation::InitForLMRId($LMRId);

        $calc = tblFileCalculatedValues::Get(['LMRId' => $LMRId]);
        if (!$calc) {
            $calc = new tblFileCalculatedValues();
        }
        $calc->LMRId = $LMRId;

        $calc->TotalAssets = HMLOLoanTermsCalculation::$TotalAssets;

        $calc->TotalOwed = HMLOLoanTermsCalculation::$TotalOwed;
        $calc->TotalAutoMobiles = HMLOLoanTermsCalculation::$TotalAutoMobiles;
        $calc->TotalRehabCost = HMLOLoanTermsCalculation::$TotalRehabCost;

        $calc->ApplicationsSubmitted = HMLOLoanTermsCalculation::$ApplicationsSubmitted;
        $calc->ApplicationsApproved = HMLOLoanTermsCalculation::$ApplicationsApproved;
        $calc->TotalAmountApproved = HMLOLoanTermsCalculation::$TotalAmountApproved;

        $calc->AvailableBudget = HMLOLoanTermsCalculation::$availableBudget;

        $calc->TotalDrawsFunded = HMLOLoanTermsCalculation::$totalDrawsFunded;

        $calc->NetOperatingIncome = HMLOLoanTermsCalculation::$netOperatingIncome;

        $calc->TotalNetWorth = HMLOLoanTermsCalculation::$TotalNetWorth;

        $calc->TotalLoanAmount = HMLOLoanTermsCalculation::$totalLoanAmount;

        $calc->NetLenderFundsToTitle = Strings::replaceCommaValues(HMLOLoanTermsCalculation::$netLenderFundsToBorrower);
        $calc->CurrentEscrowBalance = HMLOLoanTermsCalculation::$availableBudget;
        $calc->NetMonthlyPaymentPITI = Strings::replaceCommaValues(HMLOLoanTermsCalculation::$netMonthlyPayment);
        $calc->TotalProjectCost = Strings::replaceCommaValues(HMLOLoanTermsCalculation::$totalProjectCost);
        $calc->SimpleARV = HMLOLoanTermsCalculation::$simpleARV;
        $calc->FullARV = HMLOLoanTermsCalculation::$ARV;
        $calc->AcquisitionLTV = HMLOLoanTermsCalculation::$acquisitionLTV;
        $calc->MarketLTV = HMLOLoanTermsCalculation::$marketLTV;
        $calc->LoanToCost = HMLOLoanTermsCalculation::$LTC;
        $calc->GrossProfit = HMLOLoanTermsCalculation::$grossProfit;
        $calc->GrossProfitMargin = HMLOLoanTermsCalculation::$grossProfitMargin;
        $calc->PerDiemDays = HMLOLoanTermsCalculation::$dailyEstPerDiemArray['diemDays'];
        $calc->PerDiemInterestAmount = HMLOLoanTermsCalculation::$dailyEstPerDiemArray['totalDailyInterestCharge'];
        $calc->PerDiemInterestPercent = HMLOLoanTermsCalculation::$lien1Rate;
        $calc->TotalPerDiemInterestAmount = HMLOLoanTermsCalculation::$totalEstPerDiem;
        $calc->TotalFeesAndCosts = Strings::replaceCommaValues(HMLOLoanTermsCalculation::$totalFeesAndCost);
        $calc->TotalCashToClose = Strings::replaceCommaValues(HMLOLoanTermsCalculation::$totalCashToClose);
        $calc->LTC_MarketValue = HMLOLoanTermsCalculation::$LTCMarketValue;
        $calc->LTC_InitialLoanAmount = HMLOLoanTermsCalculation::$LTCInitialLoanAmount;
        $calc->LTC_SoftHardCost = HMLOLoanTermsCalculation::$NewLoanToCost;
        $calc->InitialLoanAmount = HMLOLoanTermsCalculation::$initialLoanAmount;
        $calc->doRecalculate = 0;

        $calc->CurrentDTI = HMLOLoanTermsCalculation::$currentDTI;
        $calc->debtServiceRatio = HMLOLoanTermsCalculation::$debtServiceRatio;
        $calc->debtServiceRatioPITIA = HMLOLoanTermsCalculation::$debtServiceRatioPITIA;
        $calc->totalRequiredReserves = HMLOLoanTermsCalculation::$totalRequiredReserves;
        $calc->totalCashOutAmt = HMLOLoanTermsCalculation::$totalCashOutAmt;
        $calc->newTotalProjectCost = HMLOLoanTermsCalculation::$NewTotalProjectCost;
        $calc->CurrentLoanBalance = HMLOLoanTermsCalculation::$currentLoanBalance;
        $calc->paymentReservesAmt = HMLOLoanTermsCalculation::$paymentReservesAmt;

        $calc->PayOffAmount = HMLOLoanTermsCalculation::$payOffAmount;
        $calc->totalFeesAndCost = Strings::replaceCommaValues(HMLOLoanTermsCalculation::$totalFeesAndCost);

        $loanServicing = LMRequest::myFileInfo()->tblFile()->getLoanServicing();
        $loanServicing->checkValid();

        $calc->isValidForServicing = $loanServicing->isInvalid ? 0 : 1;
        $calc->PaymentFrequency = LMRequest::myFileInfo()->tblFile()->getTblFileHMLONewLoanInfo_by_fileID()->paymentFrequency;

        $calc->LTC2_additionalReserveInterest = LTC2Variables::$LTC2_additionalReserveInterest;
        $calc->LTC2_additionalOriginationInterest = LTC2Variables::$LTC2_additionalOriginationInterest;

        if (!$loanServicing->isInvalid) {
            try {
                LoanAmortization::ConvertToServicing($loanServicing);
            } catch (Exception $e) {
                Debug($e);
            }

            $PaymentDueDate = Dates::Datestamp(LoanPaymentDue::GetPaymentDueDate($loanServicing), '');
            $PaymentDate = $PaymentDueDate;

            $paymentDue = LoanPaymentDue::GetPaymentDue(
                $loanServicing,
                $PaymentDueDate,
                $PaymentDate
            );
            $calc->PaymentNextDueDate = $paymentDue->DueDate;
            $calc->PaymentLastPaymentDate = $paymentDue->LastPaymentDate;
            $calc->PaymentTotal = $paymentDue->Amount;
            $calc->PaymentPrincipal = $paymentDue->Principal;
            $calc->PaymentInterest = $paymentDue->Interest;
        } else {
            $calc->PaymentNextDueDate = null;
            $calc->PaymentLastPaymentDate = null;
            $calc->PaymentTotal = null;
            $calc->PaymentPrincipal = null;
            $calc->PaymentInterest = null;
        }

        $calc->Save();

        PropertyAnalysis::updatePropertiesAnalysisValues($LMRId);
        loanPropertySummary::updateLoanInfoV2Values($LMRId);


        return $calc;
    }


    /**
     * Calculates and retrieves various financial values and metrics for a loan based on the given LMRId.
     *
     * This method performs a series of calculations and updates on loan-related data, including property analysis,
     * loan terms, and payment information. The calculated values are saved in a tblFileCalculatedValues object
     * and returned to the caller. If the loan servicing is valid, additional servicing information such as payment
     * schedule and amounts is included.
     *
     * @param int $LMRId The ID of the loan request for which calculations are performed.
     * @return tblFileCalculatedValues|null An instance of tblFileCalculatedValues containing the calculated values,
     * or null if no values could be calculated.
     */
    public static function GetCalculatedValuesForOtherPcLoans(int $LMRId): ?tblFileCalculatedValues
    {
        LMRequest::$LMRId = null;
        LMRequest::setLMRId($LMRId);
        PropertyAnalysis::updatePropertiesAnalysisValues($LMRId);
        fileHMLONewLoanInfo::calculateYieldSpread($LMRId);
        HMLOLoanTermsCalculation::InitForLMRId($LMRId);

        $calc = tblFileCalculatedValues::Get(['LMRId' => $LMRId]);
        if (!$calc) {
            $calc = new tblFileCalculatedValues();
        }
        $calc->LMRId = $LMRId;

        $calc->TotalAssets = HMLOLoanTermsCalculation::$TotalAssets;

        $calc->TotalOwed = HMLOLoanTermsCalculation::$TotalOwed;
        $calc->TotalAutoMobiles = HMLOLoanTermsCalculation::$TotalAutoMobiles;
        $calc->TotalRehabCost = HMLOLoanTermsCalculation::$TotalRehabCost;

        $calc->ApplicationsSubmitted = HMLOLoanTermsCalculation::$ApplicationsSubmitted;
        $calc->ApplicationsApproved = HMLOLoanTermsCalculation::$ApplicationsApproved;
        $calc->TotalAmountApproved = HMLOLoanTermsCalculation::$TotalAmountApproved;

        $calc->AvailableBudget = HMLOLoanTermsCalculation::$availableBudget;

        $calc->TotalDrawsFunded = HMLOLoanTermsCalculation::$totalDrawsFunded;

        $calc->NetOperatingIncome = HMLOLoanTermsCalculation::$netOperatingIncome;

        $calc->TotalNetWorth = HMLOLoanTermsCalculation::$TotalNetWorth;

        $calc->TotalLoanAmount = HMLOLoanTermsCalculation::$totalLoanAmount;

        $calc->NetLenderFundsToTitle = Strings::replaceCommaValues(HMLOLoanTermsCalculation::$netLenderFundsToBorrower);
        $calc->CurrentEscrowBalance = HMLOLoanTermsCalculation::$availableBudget;
        $calc->NetMonthlyPaymentPITI = Strings::replaceCommaValues(HMLOLoanTermsCalculation::$netMonthlyPayment);
        $calc->TotalProjectCost = Strings::replaceCommaValues(HMLOLoanTermsCalculation::$totalProjectCost);
        $calc->SimpleARV = HMLOLoanTermsCalculation::$simpleARV;
        $calc->FullARV = HMLOLoanTermsCalculation::$ARV;
        $calc->AcquisitionLTV = HMLOLoanTermsCalculation::$acquisitionLTV;
        $calc->MarketLTV = HMLOLoanTermsCalculation::$marketLTV;
        $calc->LoanToCost = HMLOLoanTermsCalculation::$LTC;
        $calc->GrossProfit = HMLOLoanTermsCalculation::$grossProfit;
        $calc->GrossProfitMargin = HMLOLoanTermsCalculation::$grossProfitMargin;
        $calc->PerDiemDays = HMLOLoanTermsCalculation::$dailyEstPerDiemArray['diemDays'];
        $calc->PerDiemInterestAmount = HMLOLoanTermsCalculation::$dailyEstPerDiemArray['totalDailyInterestCharge'];
        $calc->PerDiemInterestPercent = HMLOLoanTermsCalculation::$lien1Rate;
        $calc->TotalPerDiemInterestAmount = HMLOLoanTermsCalculation::$totalEstPerDiem;
        $calc->TotalFeesAndCosts = Strings::replaceCommaValues(HMLOLoanTermsCalculation::$totalFeesAndCost);
        $calc->TotalCashToClose = Strings::replaceCommaValues(HMLOLoanTermsCalculation::$totalCashToClose);
        $calc->LTC_MarketValue = HMLOLoanTermsCalculation::$LTCMarketValue;
        $calc->LTC_InitialLoanAmount = HMLOLoanTermsCalculation::$LTCInitialLoanAmount;
        $calc->LTC_SoftHardCost = HMLOLoanTermsCalculation::$NewLoanToCost;
        $calc->InitialLoanAmount = HMLOLoanTermsCalculation::$initialLoanAmount;
        $calc->doRecalculate = 0;

        $calc->CurrentDTI = HMLOLoanTermsCalculation::$currentDTI;
        $calc->debtServiceRatio = HMLOLoanTermsCalculation::$debtServiceRatio;
        $calc->debtServiceRatioPITIA = HMLOLoanTermsCalculation::$debtServiceRatioPITIA;
        $calc->totalRequiredReserves = HMLOLoanTermsCalculation::$totalRequiredReserves;
        $calc->totalCashOutAmt = HMLOLoanTermsCalculation::$totalCashOutAmt;
        $calc->newTotalProjectCost = HMLOLoanTermsCalculation::$NewTotalProjectCost;
        $calc->CurrentLoanBalance = HMLOLoanTermsCalculation::$currentLoanBalance;
        $calc->paymentReservesAmt = HMLOLoanTermsCalculation::$paymentReservesAmt;

        $calc->PayOffAmount = HMLOLoanTermsCalculation::$payOffAmount;
        $calc->totalFeesAndCost = Strings::replaceCommaValues(HMLOLoanTermsCalculation::$totalFeesAndCost);
        $calc->PaymentFrequency = LMRequest::myFileInfo()->tblFile()->getTblFileHMLONewLoanInfo_by_fileID()->paymentFrequency;
        $calc->LTC2_additionalReserveInterest = LTC2Variables::$LTC2_additionalReserveInterest;
        $calc->LTC2_additionalOriginationInterest = LTC2Variables::$LTC2_additionalOriginationInterest;
        $calc->Save();
        return $calc;
    }
}
