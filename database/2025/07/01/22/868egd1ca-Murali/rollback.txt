# Rollback plan for updateTblFileCalculatedValues migration

# This rollback deletes tblFileCalculatedValues rows that were created by the migration
# for the supplied set of LMRIds. Because the table has no CreatedAt/UpdatedAt columns
# and we cannot infer the exact set inserted at runtime, you must provide the LMRIds to
# remove (from logs, job output, or a pre-recorded list).
#
# Script:
#   tasks/migration/rollback/updateTblFileCalculatedValues_rollback.php
#
# Usage examples:
# 1) Dry run with PCID filter (no rows deleted):
#    php tasks/migration/rollback/updateTblFileCalculatedValues_rollback.php -f /tmp/lmrs_to_rollback.txt -p 123 -n
#
# 2) Execute deletion for a small set of IDs inline:
#    php tasks/migration/rollback/updateTblFileCalculatedValues_rollback.php -i 101,102,103 --execute
#
# 3) Execute deletion for IDs from a file and PCID 789:
#    php tasks/migration/rollback/updateTblFileCalculatedValues_rollback.php -f /tmp/lmrs_to_rollback.txt -p 789 --execute
#
# File format for -f:
#   One LMRId per line (commas and whitespace are allowed; the script extracts numbers).
#
# Safety checklist:
# - Always run a dry run (-n) first to review counts and sample IDs.
# - Take a DB backup/snapshot of tblFileCalculatedValues before executing.
# - Prefer running in maintenance windows for large deletions.
# - After rollback, validate a few LMRIds to ensure the rows are removed.
