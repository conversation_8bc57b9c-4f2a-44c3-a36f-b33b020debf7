<?php
namespace models\constants\gl;

class glCreditScore	
{
    const MIN = 300;
    const MAX = 850;
    const RANGE = "300-850";
    const MAXLENGTH = 3;
    
    /**
     * Get minimum credit score
     * @return int
     */
    public static function getMin()
    {
        return self::MIN;
    }
    
    /**
     * Get maximum credit score
     * @return int
     */
    public static function getMax()
    {
        return self::MAX;
    }
    
    /**
     * Get credit score range as string
     * @return string
     */
    public static function getRange()
    {
        return self::RANGE;
    }
    
    /**
     * Get maximum length for credit score input
     * @return int
     */
    public static function getMaxLength()
    {
        return self::MAXLENGTH;
    }
    
    /**
     * Validate if credit score is within valid range
     * @param int $score
     * @return bool
     */
    public static function isValid($score)
    {
        return $score >= self::MIN && $score <= self::MAX;
    }
}
