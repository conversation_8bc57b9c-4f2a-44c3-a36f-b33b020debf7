<?php
//global variables
global $lockedSections, $allowToEdit, $publicUser, $fileTab, $LMRId, $isEF, $tabIndex;
global $acceptedPurchase, $acceptedPurchaseDispOpt, $PAExpirationDate, $isBorPersonallyGuaranteeLoan;
global $isBorBorrowedDownPayment, $borBorrowedDownPaymentDispOpt, $secondaryFinancingAmount, $borBorrowedDownPaymentExpln;
global $HMLOEstateHeldIn, $isBorIntendToOccupyPropAsPRI, $isCoBorIntendToOccupyPropAsPRI;
global $isTherePrePaymentPenalty, $prepayentSectionDisplay, $disabledInputForClient, $prePaymentPenaltyPercentage;
global $prePaymentPenalty, $extensionOptionPercentage, $prePaymentSelectValArr;
global $extensionOption, $extensionRatePercentage, $extensionOptionsAmt, $assumability, $involvedPurchase, $involvedPurchaseDispOpt;
global $wholesaleFee, $borComment, $rentalIncomePerMonthFieldDispOpt, $additionalPropertyRestrictionsDispOpt, $restrictionsExplain;
global $exitStrategy, $exitStrategyExplainDispOpt, $rentalIncomePerMonth, $additionalPropertyRestrictions;
global $landValueCls, $isOwnLand, $balloonPayment, $prePayExcessOf20percent, $limitedOrNot, $loanMadeWholly, $doesPropertyNeedRehabFootageDispTDDiv;
global $haveBorSquareFootage, $isBlanketLoan, $doesPropertyNeedRehabNoofFootageDispTDDiv, $securityInstrument;
global $borNoOfSquareFeet, $noOfPropertiesAcquiring, $desiredFundingAmount, $purposeOfLoan, $useOfFunds, $haveCurrentLoanBal;
global $doYouHaveInvoiceToFactor, $doYouHaveInvoiceToFactorDispOpt, $amount, $PCID, $loanSigning;
global $haveCurrentLoanBalDispOpt, $balance, $heldWith, $courtOrderNecessary, $loanPurpose, $prePaymentPenaltyResArr,
       $HMLOPCBasicLoanPurposeInfoArray;

global $exitFeeAmount, $exitFeePoints, $activeTab, $fileMC;

global $rehabToBeMade, $rehabTime, $isSubjectUnderConst, $areKnownHazards, $areProReports, $isSubjectSS, $changeInCircumstance, $changeDescription, $useOfProceeds;
global $hideThisField, $secondaryHolderName;

use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glEstateHeldIn;
use models\constants\gl\glFirstProbate;
use models\constants\gl\glHMLOCourtOrderNecessary;
use models\constants\gl\glHMLOExitStrategy;
use models\constants\gl\glHMLOLoanPurpose;
use models\constants\gl\glHMLOLoanSigning;
use models\constants\gl\glPCID;
use models\constants\gl\glprePaymentPenalty;
use models\constants\gl\glTitleType;
use models\constants\gl\glTypeOfProperty;
use models\constants\gSecurityInstrumentArray;
use models\constants\loanGuaranteeTypes;
use models\constants\purposeOfLoanArray;
use models\constants\show1003FieldsArray;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\CustomField;
use models\lendingwise\tblFile;
use models\lendingwise\tblFileHMLOPropInfo;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Strings;

$gSecurityInstrumentArray = gSecurityInstrumentArray::$gSecurityInstrumentArray;
$glEstateHeldIn = glEstateHeldIn::$glEstateHeldIn;
$glHMLOExitStrategy = glHMLOExitStrategy::$glHMLOExitStrategy;
$exitStrategyOptions = glHMLOExitStrategy::getExitStrategy($PCID);
$glHMLOLoanSigning = glHMLOLoanSigning::$glHMLOLoanSigning;
$glHMLOCourtOrderNecessary = glHMLOCourtOrderNecessary::$glHMLOCourtOrderNecessary;
$glHMLOLoanPurpose = glHMLOLoanPurpose::$glHMLOLoanPurpose;
$glTypeOfProperty = glTypeOfProperty::$glTypeOfProperty;
$glTitleType = glTitleType::$glTitleType;
$glFirstProbate = glFirstProbate::$glFirstProbate;
if ($PCID == glPCID::PCID_PROD_CV3) {
    glEstateHeldIn::$options = glEstateHeldIn::$optionsCV3;
}
$borrowingMoney = '';
$isBorIntendToOccupyDispOpt = $borrowingMoneyDispOpt = $haveOwnershipInterestDispOpt = 'display: none;';
$borrowingMoney = Strings::showField('borrowingMoney', 'fileHMLOPropertyInfo');
$haveOwnershipInterest = Strings::showField('haveOwnershipInterest', 'fileHMLOPropertyInfo');
$typePropOwned = Strings::showField('typePropOwned', 'fileHMLOPropertyInfo');
$titleType = Strings::showField('titleType', 'fileHMLOPropertyInfo');
if ($isBorIntendToOccupyPropAsPRI == 'Yes') {
    $isBorIntendToOccupyDispOpt = 'display: block;';
}
if ($borrowingMoney == 'Yes') {
    $borrowingMoneyDispOpt = 'display: block;';
}
if ($haveOwnershipInterest == 'Yes') {
    $haveOwnershipInterestDispOpt = 'display: block;';
}
$BackupAllowToEdit = '';
if (in_array('Additional Questions', $lockedSections ?? [])) {
    $BackupAllowToEdit = $allowToEdit;
    $allowToEdit = false;
}
$ischk = count($secArr = BaseHTML::sectionAccess2(['sId' => 'AQ', 'opt' => $fileTab, 'activeTab' => $activeTab])) > 0 ? 'checked' : '';
loanForm::pushSectionID('AQ');


if ($publicUser == 1) {
    $ischk = $LMRId > 0 && $ischk != '' ? 'checked' : '';
}
$show1003FieldsArr = [];
$show1003FieldsArr['AQ'] = array_diff(array_keys($secArr), show1003FieldsArray::$show1003FieldsArray['AQ']);
if ($activeTab == '1003') {  /* story-31529 hide some fields based on the requirement mentioned in story*/
    foreach ($show1003FieldsArr['AQ'] as $showFieldkey => $hideFieldValue) {
        if(is_array($secArr[$hideFieldValue])) {
            $secArr[$hideFieldValue]['fieldDisplay'] = 0;
            loanForm::hideField('AQ', $hideFieldValue);
        }
    }
}

if ($purposeOfLoan != '') {
    $purposeOfLoan = explode('~', $purposeOfLoan);
}
if (!is_array($purposeOfLoan)) {
    $purposeOfLoan = [];
}
if (in_array('Other', $purposeOfLoan)) {
    $purposeOfLoanDisplayCls = '';
} else {
    $purposeOfLoanDisplayCls = 'd-none';
}
$loanGuarantee = Strings::showField('loanGuaranteeType', 'fileHMLOPropertyInfo');
$successFeePayment = Strings::showField('successFeePayment', 'fileHMLOPropertyInfo');
?>

<!-- additionalQuestions.php -->
<div class="card card-custom HMLOLoanInfoSections  borrowerActiveSection AQ AQCard <?php if (trim($ischk) == 'checked') {
    echo 'secShow';
} else {
    echo 'secHide';
} ?>" style="<?php echo $HMLOLoanInfoSectionsDisp; ?>">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <?php echo loanForm::toggleSectionV2(
            'AQ',
            true,
            true
        ); ?>
    </div>

    <div class="card-body AQCard_body">
        <div class="row">
            <?php if ($isEF != 1) { ?>
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-6 form-group <?php echo loanForm::showField('acceptedPurchase'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('acceptedPurchase', 'col-md-8 '); ?>
                                <div class="col-md-4">
                                    <?php echo loanForm::radio(
                                        'acceptedPurchase',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $acceptedPurchase,
                                        [
                                            'Yes' => 'Yes',
                                            'No'  => 'No'
                                        ],
                                        'hideAndShowAcceptPurchaseAgreement(this.value, \'acceptedPurchaseDispOpt\');',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 form-group acceptedPurchaseDispOpt PAExpirationDate_child <?php echo BaseHTML::parentFieldAccess(['fNm' => 'acceptedPurchase', 'sArr' => $secArr, 'pv' => $acceptedPurchase, 'av' => 'Yes']); ?>"
                             style="<?php echo $acceptedPurchaseDispOpt ?>">
                            <div class="row">
                                <?php echo loanForm::label2('PAExpirationDate', 'col-md-8 '); ?>
                                <div class="col-md-4">
                                    <?php echo loanForm::date(
                                        'PAExpirationDate',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $PAExpirationDate,
                                        ' dateNewClass ',
                                        '',
                                        false
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php } ?>
            <div class="col-md-12">
                <div class="row">
                    <div class="col-md-6 form-group <?php echo loanForm::showField('loanGuarantee'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('loanGuarantee', 'col-md-8 '); ?>
                            <div class="col-md-4">
                                <?php echo loanForm::select(
                                    'loanGuarantee',
                                    $allowToEdit,
                                    $tabIndex++,
                                    $loanGuarantee,
                                    loanGuaranteeTypes::$options,
                                    '',
                                    '',
                                    '-- Select One --'
                                ); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 <?php if ($publicUser || $activeTab == 'FA' || $activeTab == 'QA' || $PCID != glPCID::PCID_4423) {
                echo 'd-none';
            } ?>">
                <div class="row">
                    <div class="col-md-6 form-group <?php echo loanForm::showField('successFeePayment'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('successFeePayment', 'col-md-8 ', 'See consulting Agreement/Addendum'); ?>
                            <div class="col-md-4">
                                <?php echo loanForm::radio(
                                    'successFeePayment',
                                    $allowToEdit,
                                    $tabIndex++,
                                    $successFeePayment,
                                    [
                                        '1' => '50%',
                                        '2' => '100%'
                                    ],
                                    '',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12">
                <div class="row">
                    <div class="col-md-6 form-group <?php echo loanForm::showField('involvedPurchase'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('involvedPurchase', 'col-md-8 '); ?>
                            <div class="col-md-4">
                                <?php echo loanForm::radio(
                                    'involvedPurchase',
                                    $allowToEdit,
                                    $tabIndex++,
                                    $involvedPurchase,
                                    [
                                        'Yes' => 'Yes',
                                        'No'  => 'No'
                                    ],
                                    'involvedPurchaseHideShow(this.value, \'wholesaleFee_disp\');',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 form-group  <?php echo loanForm::showField('wholesaleFee', $wholesaleFee); ?>"
                         style="<?php if ($involvedPurchase != 'Yes') {
                             echo 'display:none;';
                         } ?>">
                        <div class="row">
                            <?php echo loanForm::label2('wholesaleFee', 'col-md-8 '); ?>
                            <div class="col-md-4">
                                <?php echo loanForm::currency(
                                    'wholesaleFee',
                                    $allowToEdit,
                                    $tabIndex++,
                                    $wholesaleFee,
                                    '',
                                        '',
                                        '',
                                ); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php if ($isEF != 1) { ?>
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-6 form-group <?php echo loanForm::showField('exitStrategy'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('exitStrategy', 'col-md-4 '); ?>
                                <div class="col-md-8">

                                    <?php echo loanForm::select(
                                        'exitStrategy',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $exitStrategy,
                                        glHMLOExitStrategy::$filteredOptions,
                                        'exitStrategyHideShow(this.value, \'exitStrategyExplain\', \'\');fileCommon.mirrorField(this,\'exitStrategy_mirror\');',
                                        'chzn-select exitStrategy_mirror ',
                                        ' ',
                                        ' Please Select ' . loanForm::$permissions['exitStrategy']->fieldLabel
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 form-group exitStrategyExplain borComment_child <?php echo loanForm::showField('borComment'); ?>"
                             style="<?php echo $exitStrategyExplainDispOpt; ?>">
                            <div class="row">
                                <?php echo loanForm::label2('borComment', 'col-md-4 '); ?>
                                <div class="col-md-8">
                                    <?php echo loanForm::textarea(
                                        'borComment',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $borComment,
                                        '',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 form-group rentalIncomePerMonthField rentalIncomePerMonth_child <?php echo loanForm::showField('rentalIncomePerMonth'); ?>"
                             style="<?php
                             echo $rentalIncomePerMonthFieldDispOpt
                             ?>">
                            <div class="row">
                                <?php echo loanForm::label2('rentalIncomePerMonth', 'col-md-7 '); ?>
                                <div class="col-md-5">
                                    <?php echo loanForm::currency(
                                        'rentalIncomePerMonth',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $rentalIncomePerMonth,
                                        '',
                                            '',
                                            '',
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 form-group  expectedTimelineToExit_Disp <?php echo loanForm::showField('expectedTimelineToExit'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('expectedTimelineToExit', 'col-md-4 '); ?>
                                <div class="col-md-8">
                                    <?php if ($allowToEdit) { ?>
                                        <input type="text"
                                               name="expectedTimelineToExit"
                                               id="expectedTimelineToExit"
                                               class="form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'expectedTimelineToExit', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'expectedTimelineToExit', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                               tabindex="<?php echo $tabIndex++; ?>"
                                               maxlength="32"
                                               value="<?php echo htmlentities(Strings::showField('expectedTimelineToExit', 'fileHMLOPropertyInfo')); ?>">
                                    <?php } else { ?>
                                        <label><?php echo Strings::showField('expectedTimelineToExit', 'fileHMLOPropertyInfo'); ?></label>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-6 form-group <?php echo loanForm::showField('additionalPropertyRestrictions'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('additionalPropertyRestrictions', 'col-md-8 '); ?>
                                <div class="col-md-4">
                                    <?php echo loanForm::radio(
                                        'additionalPropertyRestrictions',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $additionalPropertyRestrictions,
                                        [
                                            'Yes' => 'Yes',
                                            'No'  => 'No'
                                        ],
                                        'additionalPropertyRestrictionsHideShow(this.value, \'restrictionsExplain\');',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 form-group restrictionsExplain restrictionsExplain_child <?php echo BaseHTML::parentFieldAccess([
                            'fNm' => 'additionalPropertyRestrictions', 'sArr' => $secArr, 'pv' => $additionalPropertyRestrictions, 'av' => 'Yes']); ?>"
                             style=" <?php if ($additionalPropertyRestrictions != 'Yes') {
                                 echo 'display:none;';
                             } ?>">
                            <div class="row">
                                <?php echo loanForm::label2('restrictionsExplain', 'col-md-4 '); ?>
                                <div class="col-md-8">
                                    <?php echo loanForm::textarea(
                                        'restrictionsExplain',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $restrictionsExplain,
                                        '',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php } ?>

            <?php if ($isEF != 1) { ?>
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-6 form-group <?php echo loanForm::showField('isSubjectUnderConst'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('isSubjectUnderConst', 'col-md-8 '); ?>
                                <div class="col-md-4">
                                    <?php echo loanForm::radio(
                                        'isSubjectUnderConst',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $isSubjectUnderConst,
                                        [
                                            'Yes' => 'Yes',
                                            'No'  => 'No'
                                        ],
                                        '',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 propertyNeedRehabFootageTddisp"
                     style="<?php echo $doesPropertyNeedRehabFootageDispTDDiv; ?>">
                    <div class="row">
                        <div class="col-md-6 form-group <?php echo loanForm::showField('haveBorSquareFootage'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('haveBorSquareFootage', 'col-md-8 '); ?>
                                <div class="col-md-4">
                                    <?php echo loanForm::radio(
                                        'haveBorSquareFootage',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $haveBorSquareFootage,
                                        [
                                            'Yes' => 'Yes',
                                            'No'  => 'No'
                                        ],
                                        'showAndHideBorSquareFootage(this.value, \'borSquareFootageDiv\');',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 form-group  borSquareFootageDiv borNoOfSquareFeet_child
                        <?php // TODO: loanForm.php ?>
                            <?php echo BaseHTML::parentFieldAccess(['fNm' => 'haveBorSquareFootage', 'sArr' => $secArr,
                                                                    'pv'  => $haveBorSquareFootage, 'av' => 'Yes']); ?>"
                             id="borSquareFootageDiv">
                            <div class="row">
                                <?php echo loanForm::label2('borNoOfSquareFeet', 'col-md-7 '); ?>
                                <div class="col-md-5">
                                    <?php if ($allowToEdit) { ?>
                                        <input type="number" name="borNoOfSquareFeet" id="borNoOfSquareFeet"
                                               tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess([
                                            'fNm' => 'borNoOfSquareFeet', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                               class="form-control <?php echo BaseHTML::fieldAccess(['fNm'  => 'borNoOfSquareFeet',
                                                                                                     'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               value="<?php echo $borNoOfSquareFeet; ?>" maxlength="10"
                                               autocomplete="off">
                                    <?php } else { ?>
                                        <label><?php echo $noOfPropertiesAcquiring ?></label>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php } ?>
            <div class="col-md-12">
                <div class="row">
                    <div class="col-md-6 form-group <?php echo loanForm::showField('rehabToBeMade'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('rehabToBeMade', 'col-md-5 '); ?>
                            <div class="col-md-7">
                                <?php echo loanForm::textarea(
                                    'rehabToBeMade',
                                    $allowToEdit,
                                    $tabIndex++,
                                    $rehabToBeMade,
                                    '',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 form-group <?php echo loanForm::showField('rehabTime'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('rehabTime', 'col-md-7 '); ?>
                            <div class="col-md-5">
                                <?php if ($allowToEdit) { ?>
                                    <input type="number" name="rehabTime" id="rehabTime"
                                           class="form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'rehabTime', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           value="<?php echo $rehabTime; ?>"
                                           tabindex="<?php echo $tabIndex++; ?>" autocomplete="off"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'rehabTime', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                <?php } else { ?>
                                    <label><?php echo $rehabTime; ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12">
                <div class="row">
                    <div class="col-md-6 form-group <?php echo loanForm::showField('isBorBorrowedDownPayment'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('isBorBorrowedDownPayment', 'col-md-8 '); ?>
                            <div class="col-md-4">
                                <?php echo loanForm::radio(
                                    'isBorBorrowedDownPayment',
                                    $allowToEdit,
                                    $tabIndex++,
                                    $isBorBorrowedDownPayment,
                                    [
                                        'Yes' => 'Yes',
                                        'No'  => 'No'
                                    ],
                                    'showAndHideBorBackgroundDiv(this.value, \'borBorrowedDownPayment\');',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 form-group borBorrowedDownPaymentTR secondaryHolderName_child <?php echo BaseHTML::parentFieldAccess(['fNm' => 'isBorBorrowedDownPayment', 'sArr' => $secArr, 'pv' => $isBorBorrowedDownPayment, 'av' => 'Yes']); ?>">
                        <div class="borBorrowedDownPaymentTR" style="<?php echo $borBorrowedDownPaymentDispOpt ?>">
                            <div class="row">
                                <?php echo loanForm::label2('secondaryHolderName', 'col-md-7 '); ?>
                                <div class="col-md-5">
                                    <?php echo loanForm::text(
                                        'secondaryHolderName',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $secondaryHolderName,
                                        '',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 form-group borBorrowedDownPaymentTR secondaryFinancingAmount_child <?php echo BaseHTML::parentFieldAccess(['fNm' => 'isBorBorrowedDownPayment', 'sArr' => $secArr, 'pv' => $isBorBorrowedDownPayment, 'av' => 'Yes']); ?>">
                        <div class="borBorrowedDownPaymentTR" style="<?php echo $borBorrowedDownPaymentDispOpt ?>">
                            <div class="row">
                                <?php echo loanForm::label2('secondaryFinancingAmount', 'col-md-7 '); ?>
                                <div class="col-md-5">
                                    <?php echo loanForm::currency(
                                        'secondaryFinancingAmount',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $secondaryFinancingAmount,
                                        '',
                                            '',
                                            '',
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php if ($hideThisField) { ?>
                        <div class="col-md-6 form-group borBorrowedDownPaymentTR borBorrowedDownPaymentExpln_child
                    <?php echo BaseHTML::parentFieldAccess(['fNm' => 'isBorBorrowedDownPayment', 'sArr' => $secArr,
                                                            'pv'  => $isBorBorrowedDownPayment, 'av' => 'Yes']); ?>"
                             style="<?php echo $borBorrowedDownPaymentDispOpt ?>">
                            <div class="row">
                                <?php echo loanForm::label2('borBorrowedDownPaymentExpln', 'col-md-5 '); ?>
                                <div class="col-md-7">
                                    <?php echo loanForm::textarea(
                                        'borBorrowedDownPaymentExpln',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $borBorrowedDownPaymentExpln,
                                        '',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
            <?php if ($isEF != 1) { ?>
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-6 form-group <?php echo loanForm::showField('doYouHaveInvoiceToFactor'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('doYouHaveInvoiceToFactor', 'col-md-8 '); ?>
                                <div class="col-md-4">
                                    <?php echo loanForm::radio(
                                        'doYouHaveInvoiceToFactor',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $doYouHaveInvoiceToFactor,
                                        [
                                            'Yes' => 'Yes',
                                            'No'  => 'No'
                                        ],
                                        'hideAndShowAcceptPurchaseAgreement(this.value, \'doYouHaveInvoiceToFactorDispOpt\');',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 form-group doYouHaveInvoiceToFactorDispOpt <?php echo loanForm::showField('doYouHaveInvoiceToFactor'); ?>"
                             style=" <?php if ($doYouHaveInvoiceToFactor != 'Yes') {
                                 echo 'display:none;';
                             } ?>">
                            <div class="row">
                                <?php echo loanForm::label2('amount', 'col-md-7 '); ?>
                                <div class="col-md-5">
                                    <?php echo loanForm::currency(
                                        'amount',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $amount,
                                        '',
                                            '',
                                            '',
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php } ?>
            <?php if ($isEF != 1) { ?>
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-6 form-group <?php echo loanForm::showField('haveCurrentLoanBal'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('haveCurrentLoanBal', 'col-md-8 '); ?>
                                <div class="col-md-4">
                                    <?php echo loanForm::radio(
                                        'haveCurrentLoanBal',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $haveCurrentLoanBal,
                                        [
                                            'Yes' => 'Yes',
                                            'No'  => 'No'
                                        ],
                                        'hideAndShowAcceptPurchaseAgreement(this.value, \'haveCurrentLoanBalDispOpt\');',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div style="<?php echo $haveCurrentLoanBalDispOpt; ?>"
                             class="col-md-6 form-group haveCurrentLoanBalDispOpt balance_child <?php echo BaseHTML::parentFieldAccess(['fNm' => 'haveCurrentLoanBal', 'sArr' => $secArr, 'pv' => $haveCurrentLoanBal, 'av' => 'Yes']); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('balance', 'col-md-7 '); ?>
                                <div class="col-md-5">
                                    <?php echo loanForm::currency(
                                        'balance',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $balance,
                                        '',
                                            '',
                                            '',
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <?php if ($hideThisField) { ?>
                            <div style="<?php echo $haveCurrentLoanBalDispOpt; ?>"
                                 class="col-md-6 form-group haveCurrentLoanBalDispOpt heldWith_child <?php echo BaseHTML::parentFieldAccess(['fNm' => 'haveCurrentLoanBal', 'sArr' => $secArr, 'pv' => $haveCurrentLoanBal, 'av' => 'Yes']); ?>">
                                <div class="row">
                                    <?php echo loanForm::label2('heldWith', 'col-md-2 '); ?>
                                    <div class="col-md-10">
                                        <?php echo loanForm::textarea(
                                            'heldWith',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $heldWith,
                                            '',
                                            ''
                                        ); ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-6 form-group <?php echo loanForm::showField('isBorIntendToOccupyPropAsPRI'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('isBorIntendToOccupyPropAsPRI', 'col-md-8 '); ?>
                                <div class="col-md-4">
                                    <?php echo loanForm::radio(
                                        'isBorIntendToOccupyPropAsPRI',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $isBorIntendToOccupyPropAsPRI,
                                        [
                                            'Yes' => 'Yes',
                                            'No'  => 'No'
                                        ],
                                        '',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div
                                class="col-md-6 form-group <?php
                                echo loanForm::showField('haveOwnershipInterest'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('haveOwnershipInterest', 'col-md-7 '); ?>
                                <div class="col-md-5">
                                    <?php echo loanForm::radio(
                                        'haveOwnershipInterest',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $haveOwnershipInterest,
                                        [
                                            'Yes' => 'Yes',
                                            'No'  => 'No'
                                        ],
                                        'hideAndShowAcceptPurchaseAgreement(this.value, \'haveOwnershipInterestDispOpt\');',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div style="<?php echo $haveOwnershipInterestDispOpt ?>"
                             class="col-md-6 form-group  haveOwnershipInterestDispOpt typePropOwned_child
                              <?php echo BaseHTML::parentFieldAccess(['fNm' => 'haveOwnershipInterest', 'sArr' => $secArr,
                                                                      'pv'  => $haveOwnershipInterest, 'av' => 'Yes']); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('typePropOwned', 'col-md-7 '); ?>
                                <div class="col-md-5">
                                    <?php echo loanForm::select(
                                        'typePropOwned',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $typePropOwned,
                                        glTypeOfProperty::$options,
                                        '',
                                        '',
                                        '-- Select One --'
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <?php if ($hideThisField) { ?>
                            <div style="<?php echo $haveOwnershipInterestDispOpt ?>"
                                 class="col-md-6 form-group  haveOwnershipInterestDispOpt titleType_child
                              <?php echo BaseHTML::parentFieldAccess(['fNm' => 'haveOwnershipInterest', 'sArr' => $secArr,
                                                                      'pv'  => $haveOwnershipInterest, 'av' => 'Yes']); ?>">
                                <div class="row">
                                    <?php echo loanForm::label2('titleType', 'col-md-7 '); ?>
                                    <div class="col-md-5">
                                        <?php echo loanForm::select(
                                            'titleType',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $titleType,
                                            glTitleType::$options,
                                            '',
                                            '',
                                            '-- Select One --'
                                        ); ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            <?php } ?>
            <div class="col-md-12">
                <div class="row">
                    <div class="col-md-6 form-group <?php echo loanForm::showField('borrowingMoney'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('borrowingMoney', 'col-md-8 '); ?>
                            <div class="col-md-4">
                                <?php echo loanForm::radio(
                                    'borrowingMoney',
                                    $allowToEdit,
                                    $tabIndex++,
                                    $borrowingMoney,
                                    [
                                        'Yes' => 'Yes',
                                        'No'  => 'No'
                                    ],
                                    'hideAndShowAcceptPurchaseAgreement(this.value, \'borrowingMoneyDispOpt\');',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div style="<?php echo $borrowingMoneyDispOpt; ?>"
                         class="col-md-6 form-group borrowingMoneyDispOpt borrowedAmt_child <?php echo BaseHTML::parentFieldAccess(['fNm' => 'borrowingMoney', 'sArr' => $secArr, 'pv' => $borrowingMoney, 'av' => 'Yes']); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('borrowedAmt', 'col-md-7 '); ?>
                            <div class="col-md-5">
                                <?php echo loanForm::currency(
                                    'borrowedAmt',
                                    $allowToEdit,
                                    $tabIndex++,
                                    Strings::showField('borrowedAmt', 'fileHMLOPropertyInfo'),
                                    '',
                                        '',
                                        '',
                                ); ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php if ($isEF != 1) { ?>
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col-md-6 form-group <?php echo loanForm::showField('purposeOfLoan'); ?>">
                                <div class="row">
                                    <?php echo loanForm::label2('purposeOfLoan', 'col-md-7 '); ?>
                                    <div class="col-md-5">
                                        <input type="hidden" name="purposeOfLoanHidden" id="purposeOfLoanHidden"
                                               value="<?php echo implode(',', $purposeOfLoan); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'purposeOfLoan', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                        <?php echo loanForm::selectMulti(
                                            'purposeOfLoan',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $purposeOfLoan,
                                            count($HMLOPCBasicLoanPurposeInfoArray ?? []) > 0 ? $HMLOPCBasicLoanPurposeInfoArray : purposeOfLoanArray::$purposeOfLoanArray,
                                            'onPuprposeofloanchange();',
                                            'chzn-select',
                                            '-- Select One --'
                                        ); ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 form-group <?php echo $purposeOfLoanDisplayCls; ?>"
                                 id="purposeOfLoanExplanationDiv">
                                <div class="row">
                                    <?php echo loanForm::label2('purposeOfLoanExplanation', 'col-md-7 font-weight-bold', '', 'Please Explain'); ?>
                                    <div class="col-md-5">
                                        <?php echo loanForm::textarea(
                                            'purposeOfLoanExplanation',
                                            $allowToEdit,
                                            $tabIndex++,
                                            Strings::showField('purposeOfLoanExplanation', 'fileHMLOPropertyInfo'),
                                            '',
                                            ''
                                        ); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 form-group <?php echo loanForm::showField('desiredFundingAmount'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('desiredFundingAmount', 'col-md-7 '); ?>
                                <div class="col-md-5">
                                    <?php echo loanForm::currency(
                                        'desiredFundingAmount',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $desiredFundingAmount,
                                        '',
                                            '',
                                            '',
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    </div>

                <?php } ?>

                <div class="col-md-6 form-group <?php echo loanForm::showField('assumability'); ?>">
                    <div class="row">
                        <?php echo loanForm::label2('assumability', 'col-md-6 '); ?>
                        <div class="col-md-6">
                            <?php echo loanForm::radio(
                                'assumability',
                                $allowToEdit,
                                $tabIndex++,
                                $assumability,
                                [
                                    'Yes' => 'Yes - See Terms &amp; Conditions',
                                    'No'  => 'No'
                                ],
                                '',
                                ''
                            ); ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('securityInstrument'); ?>">
                    <div class="row">
                        <?php echo loanForm::label2('securityInstrument', 'col-md-7 '); ?>
                        <div class="col-md-5">
                            <?php echo loanForm::select(
                                'securityInstrument',
                                $allowToEdit,
                                $tabIndex++,
                                $securityInstrument,
                                gSecurityInstrumentArray::$options,
                                '',
                                '',
                                '-- Select One --'
                            ); ?>
                        </div>
                    </div>
                </div>
                <?php if ($isEF != 1) { ?>
                    <div class="col-md-6 form-group isOwnLand_disp <?php echo loanForm::showField('isOwnLand'); ?> landValueCls"
                         style="<?php echo $landValueCls ?>">
                        <div class="row">
                            <?php echo loanForm::label2('isOwnLand', 'col-md-8 '); ?>
                            <div class="col-md-4">
                                <?php echo loanForm::radio(
                                    'isOwnLand',
                                    $allowToEdit,
                                    $tabIndex++,
                                    $isOwnLand,
                                    [
                                        'Yes' => 'Yes',
                                        'No'  => 'No'
                                    ],
                                    '',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 form-group <?php echo loanForm::showField('HMLOEstateHeldIn'); ?>">
                        <div class="row">
                            <label class="col-md-7 font-weight-bold"
                                   id="HMLOEstateHeldIn_for"><?php echo BaseHTML::fieldAccess(['fNm' => 'HMLOEstateHeldIn', 'sArr' => $secArr, 'opt' => 'L']); ?></label>
                            <div class="col-md-5">
                                <?php echo loanForm::select(
                                    'HMLOEstateHeldIn',
                                    $allowToEdit,
                                    $tabIndex++,
                                    $HMLOEstateHeldIn,
                                    glEstateHeldIn::$options,
                                    '',
                                    'HMLOEstateHeldInCls',
                                    '-- Select One --'
                                ); ?>
                            </div>
                        </div>
                    </div>
                <?php } ?>
                <div class="col-md-6 form-group <?php echo loanForm::showField('isBorPersonallyGuaranteeLoan'); ?>">
                    <div class="row">
                        <?php echo loanForm::label2('isBorPersonallyGuaranteeLoan', 'col-md-8 '); ?>
                        <div class="col-md-4">
                            <?php echo loanForm::radio(
                                'isBorPersonallyGuaranteeLoan',
                                $allowToEdit,
                                $tabIndex++,
                                $isBorPersonallyGuaranteeLoan,
                                [
                                    'Yes' => 'Yes',
                                    'No'  => 'No'
                                ],
                                '',
                                ''
                            ); ?>
                        </div>
                    </div>
                </div>

                <?php if ($isEF != 1) { ?>

                    <div class="col-md-6 form-group <?php echo loanForm::showField('balloonPayment'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('balloonPayment', 'col-md-8 '); ?>
                            <div class="col-md-4">
                                <?php echo loanForm::radio(
                                    'balloonPayment',
                                    $allowToEdit,
                                    $tabIndex++,
                                    $balloonPayment,
                                    [
                                        'Yes' => 'Yes',
                                        'No'  => 'No'
                                    ],
                                    '',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 form-group <?php echo loanForm::showField('useOfFunds'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('useOfFunds', 'col-md-4 '); ?>
                            <div class="col-md-8">
                                <?php
                                if (glCustomJobForProcessingCompany::isPC_CV3($PCID)) {
                                    $useFundsMaxLength = loanForm::getFieldLength('useOfFunds', 'tblFileHMLOPropInfo');
                                } else {
                                    $useFundsMaxLength = 5000;
                                }
                                echo loanForm::textarea(
                                    'useOfFunds',
                                    $allowToEdit,
                                    $tabIndex++,
                                    $useOfFunds,
                                    'validateMaxLength',
                                    'assignValueToUseOfFundsMirrorfield(this.value,this.id);',
                                    '',
                                    $useFundsMaxLength
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 form-group <?php echo loanForm::showField('isCoBorIntendToOccupyPropAsPRI'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('isCoBorIntendToOccupyPropAsPRI', 'col-md-8 '); ?>
                            <div class="col-md-4">
                                <?php echo loanForm::radio(
                                    'isCoBorIntendToOccupyPropAsPRI',
                                    $allowToEdit,
                                    $tabIndex++,
                                    $isCoBorIntendToOccupyPropAsPRI,
                                    [
                                        'Yes' => 'Yes',
                                        'No'  => 'No'
                                    ],
                                    '',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 form-group <?php echo loanForm::showField('prePayExcessOf20percent'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('prePayExcessOf20percent', 'col-md-8 '); ?>
                            <div class="col-md-4">
                                <?php echo loanForm::radio(
                                    'prePayExcessOf20percent',
                                    $allowToEdit,
                                    $tabIndex++,
                                    $prePayExcessOf20percent,
                                    [
                                        'Yes' => 'Yes',
                                        'No'  => 'No'
                                    ],
                                    '',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 form-group <?php echo loanForm::showField('limitedOrNot'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('limitedOrNot', 'col-md-8 '); ?>
                            <div class="col-md-4">
                                <?php echo loanForm::radio(
                                    'limitedOrNot',
                                    $allowToEdit,
                                    $tabIndex++,
                                    $limitedOrNot,
                                    [
                                        'Yes' => 'Yes',
                                        'No'  => 'No'
                                    ],
                                    '',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 form-group <?php echo loanForm::showField('loanMadeWholly'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('loanMadeWholly', 'col-md-7 '); ?>
                            <div class="col-md-5">
                                <?php echo loanForm::radio(
                                    'loanMadeWholly',
                                    $allowToEdit,
                                    $tabIndex++,
                                    $loanMadeWholly,
                                    [
                                        'May'      => 'May',
                                        'Will'     => 'Will',
                                        'Will Not' => 'Will Not',
                                    ],
                                    '',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                <?php } ?>
                <div class="col-md-6 form-group <?php echo loanForm::showField('famBizAffil'); ?>">
                    <div class="row">
                        <?php echo loanForm::label2('famBizAffil', 'col-md-8 '); ?>
                        <div class="col-md-4">
                            <?php echo loanForm::radio(
                                'famBizAffil',
                                $allowToEdit,
                                $tabIndex++,
                                Strings::showField('famBizAffil', 'fileHMLOPropertyInfo'),
                                [
                                    'Yes' => 'Yes',
                                    'No'  => 'No',
                                ],
                                '',
                                ''
                            ); ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('applyOtherLoan'); ?>">
                    <div class="row">
                        <?php echo loanForm::label2('applyOtherLoan', 'col-md-8 '); ?>
                        <div class="col-md-4">
                            <?php echo loanForm::radio(
                                'applyOtherLoan',
                                $allowToEdit,
                                $tabIndex++,
                                Strings::showField('applyOtherLoan', 'fileHMLOPropertyInfo'),
                                [
                                    'Yes' => 'Yes',
                                    'No'  => 'No',
                                ],
                                '',
                                ''
                            ); ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 form-group <?php echo loanForm::showField('applyNewCredit'); ?>">
                    <div class="row">
                        <?php echo loanForm::label2('applyNewCredit', 'col-md-8 '); ?>
                        <div class="col-md-4">
                            <?php echo loanForm::radio(
                                'applyNewCredit',
                                $allowToEdit,
                                $tabIndex++,
                                Strings::showField('applyNewCredit', 'fileHMLOPropertyInfo'),
                                [
                                    'Yes' => 'Yes',
                                    'No'  => 'No',
                                ],
                                '',
                                ''
                            ); ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-6 form-group <?php echo loanForm::showField('areKnownHazards'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('areKnownHazards', 'col-md-8 '); ?>
                                <div class="col-md-4">
                                    <?php echo loanForm::radio(
                                        'areKnownHazards',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $areKnownHazards,
                                        [
                                            'Yes' => 'Yes',
                                            'No'  => 'No',
                                        ],
                                        'showAndHideBorSquareFootage(this.value, \'areProReportsDiv\');',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 form-group  areProReportsDiv areKnownHazards_child
                            <?php echo BaseHTML::parentFieldAccess(['fNm' => 'areKnownHazards', 'sArr' => $secArr,
                                                                    'pv'  => $areKnownHazards, 'av' => 'Yes']); ?>"
                             id="areProReportsDiv">
                            <div class="row">
                                <?php echo loanForm::label2('areProReports', 'col-md-7 '); ?>
                                <div class="col-md-5">
                                    <?php echo loanForm::radio(
                                        'areProReports',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $areProReports,
                                        [
                                            'Yes' => 'Yes',
                                            'No'  => 'No',
                                        ],
                                        '',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12 separator separator-dashed my-2 separator12">
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-6 form-group <?php echo loanForm::showField('isSubjectSS'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('isSubjectSS', 'col-md-8 '); ?>
                                <div class="col-md-4">
                                    <?php echo loanForm::radio(
                                        'isSubjectSS',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $isSubjectSS,
                                        [
                                            'Yes' => 'Yes',
                                            'No'  => 'No',
                                        ],
                                        '',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php if ($hideThisField) { ?>
                    <div class="col-md-6 form-group <?php echo loanForm::showField('displayNotes'); ?>">
                        <div class="row ">
                            <?php echo loanForm::label2('displayNotes', 'col-md-7 '); ?>
                            <div class="col-md-5">
                                <?php echo loanForm::textarea(
                                    'displayNotes',
                                    $allowToEdit,
                                    $tabIndex++,
                                    Strings::showField('displayNotes', 'fileHMLOPropertyInfo'),
                                    '',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                <?php } ?>
                <?php if (in_array($PCID, $glFirstProbate)) { ?>
                    <div class="col-md-6 form-group">
                        <div class="row">
                            <?php echo loanForm::label2('loanSigning', 'col-md-7 font-weight-bold', '', 'How will you be signing for this loan?'); ?>
                            <div class="col-md-5">
                                <?php echo loanForm::select(
                                    'loanSigning',
                                    $allowToEdit && $disabledInputForClient,
                                    $tabIndex++,
                                    $loanSigning,
                                    glHMLOLoanSigning::$options,
                                    '',
                                    '',
                                    '-- Select One --'
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 form-group">
                        <div class="row">
                            <?php echo loanForm::label2('courtOrderNecessary', 'col-md-7 font-weight-bold', '', 'Will a court order be necessary for this loan?'); ?>
                            <div class="col-md-5">
                                <?php echo loanForm::select(
                                    'courtOrderNecessary',
                                    $allowToEdit && $disabledInputForClient,
                                    $tabIndex++,
                                    $courtOrderNecessary,
                                    glHMLOCourtOrderNecessary::$options,
                                    '',
                                    '',
                                    '-- Select One --'
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 form-group">
                        <div class="row">
                            <?php echo loanForm::label2('loanPurpose', 'col-md-7 font-weight-bold', '', 'Loan Purpose'); ?>
                            <div class="col-md-5">
                                <?php echo loanForm::select(
                                    'courtOrderNecessary',
                                    $allowToEdit && $disabledInputForClient,
                                    $tabIndex++,
                                    $loanPurpose,
                                    glHMLOLoanPurpose::$options,
                                    '',
                                    '',
                                    '-- Select One --'
                                ); ?>
                            </div>
                        </div>
                    </div>
                <?php } ?>


                <div class="col-md-6 form-group <?php echo loanForm::showField('useOfProceeds'); ?>">
                    <div class="row">
                        <?php echo loanForm::label2(
                            'useOfProceeds',
                            'col-md-7 ',
                            '',
                            loanForm::changeLog(
                                LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->HMLOPID,
                                'useOfProceeds',
                                tblFileHMLOPropInfo::class
                            )
                        ); ?>
                        <div class="col-md-5">
                            <?php echo loanForm::text(
                                'useOfProceeds',
                                $allowToEdit,
                                $tabIndex++,
                                $useOfProceeds,
                                '',
                                ''
                            ); ?>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="row">
            <div class="col-md-6 form-group <?php echo loanForm::showField('changeInCircumstance'); ?>">
                <div class="row">
                    <?php echo loanForm::label2('changeInCircumstance', 'col-md-8 '); ?>
                    <div class="col-md-4">
                        <?php echo loanForm::radio(
                            'changeInCircumstance',
                            $allowToEdit,
                            $tabIndex++,
                            $changeInCircumstance,
                            [
                                'Yes' => 'Yes',
                                'No'  => 'No',
                            ],
                            'hideAndShowAcceptPurchaseAgreement(this.value, \'changeDescriptionDispOpt\');',
                            ''
                        ); ?>
                    </div>
                </div>
            </div>
            <div class="col-md-6 form-group changeDescriptionDispOpt changeDescription_child <?php echo BaseHTML::parentFieldAccess(['fNm' => 'changeInCircumstance', 'sArr' => $secArr, 'pv' => $changeInCircumstance, 'av' => 'Yes']); ?>">
                <div class="form-group row <?php echo loanForm::showField('changeDescription'); ?>">
                    <?php echo loanForm::label2('changeDescription', 'col-md-7 '); ?>
                    <div class="col-md-5">
                        <?php echo loanForm::textarea(
                            'changeDescription',
                            $allowToEdit,
                            $tabIndex++,
                            Strings::showField('changeDescription', 'fileHMLOPropertyInfo'),
                            '',
                            ''
                        ); ?>
                    </div>
                </div>
            </div>
        </div>

        <?php echo CustomField::RenderForTabSection(
            PageVariables::$PCID,
            tblFile::class,
            LMRequest::$LMRId,
            'AQ',
            $fileTab,
            $activeTab,
            LMRequest::myFileInfo()->getFileTypes(),
            LMRequest::myFileInfo()->getLoanPrograms()
        ); ?>
    </div>
</div>

<script>
    $(document).ready(function () {
        exitStrategyHideShow($("#exitStrategy").val(), 'exitStrategyExplain', '');
    });
    $('select.js-example-basic-multiple').select2();

    function onPuprposeofloanchange() {
        let purposeOfLoan = $('#purposeOfLoan').val().toString();
        let purposeOfLoanArray = purposeOfLoan.split(',');
        if ($.inArray('Other', purposeOfLoanArray) != -1) {
            $('#purposeOfLoanExplanationDiv').removeClass('d-none');
            $('#purposeOfLoanExplanation').val('');
        } else {
            $('#purposeOfLoanExplanationDiv').addClass('d-none');
            $('#purposeOfLoanExplanation').val('');
        }
    }
</script>
<!-- additionalQuestions.php -->

<?php
if (in_array('Additional Questions', $lockedSections ?? [])) {
    //$BackupAllowToEdit = $allowToEdit;
    $allowToEdit = $BackupAllowToEdit;
}
?>
