<?php

namespace models\constants;

class glMandatoryDisableFormFields
{
    public static array $glMandatoryDisableFormFields = [
        'isCoBorrower',
        'isPresentAdd',
        'coBorMailingAddrAsPresent',
        'isborrowerAdd',
        'entityBillAddrChk',
        'paidAtOrBeforeClose',
        'paidAtOrBeforeCloseAnother',
        'contactAtWork1',
        'contactAtWork2',
        'liabilityAtorBeforeClose',
        'waterFront',
        'basementHome',
        'garageHome',
        'basementFinish',
        'propertyIsNFIPNonParticipating',
        'condominiumOrHOAFee',
        'HOAAllowRentals',
        'IsCommunity',
        'isHOAOrCOAFee',
        'mAddressSwitchCard',
        'mAddressSwitchCheck',
        'primTotalNetHouseHoldIncome',
        'dtiTable',
        'creditReportUpload',
        'compensatingFactors',
        'doNotSell',
        'totalLoanAmount',
        'interestRate',
        'loanTerm',
        'obtainedFromAgency',
        'NOILoanNumber',
        'LMRId',
        'fileCreatedDate',
        'loanNumber',
        'assignedEmployee',
        'priorityLevel'
    ];
}