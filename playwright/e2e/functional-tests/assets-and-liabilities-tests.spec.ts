import { test, expect } from "@playwright/test";
import { AppManager } from "../../support/AppManager/PageObjectManager";

test.describe("Assets & Liabilities Section", () => {
  let loanFile: string = "/backoffice/LMRequest.php?eId=c20e03e74919f6c3&lId=791dd0d1871d0329&rId=0d269c4e3d97619c&tabOpt=AL&op=a72f9e967052513d&supp=VIPhelp";
  let app: AppManager;

  test.beforeEach(async ({ page }) => {
    app = new AppManager(page);
    await app.borrowerInfoTab.openLoanFile(loanFile); // adjust if you have a custom navigation/start
  });
  test("Create, Update, Delete on Assets & Liabilities section", async () => {
    // ===== CREATE =====
    await test.step("Fill Assets & Liabilities (create)", async () => {
      app.utilities.logStep("Fill Assets & Liabilities (create)");
      await app.assetsSection.fillAssetsSection(app.assetsSectionCreate);
    });

    await test.step("Validate Assets & Liabilities (create)", async () => {
      app.utilities.logStep("Validate Assets & Liabilities (create)");
      await app.assetsSection.validateCreateAssetsSection(
        app.assetsSectionCreate
      );
    });

    // ===== UPDATE =====
    await test.step("Update Assets & Liabilities", async () => {
      app.utilities.logStep("Update Assets & Liabilities");
      await app.assetsSection.updateAssetsSection(app.assetsSectionUpdate);
    });

    await test.step("Validate Assets & Liabilities (update)", async () => {
      app.utilities.logStep("Validate Assets & Liabilities (update)");
      await app.assetsSection.validateUpdateAssetsSection(
        app.assetsSectionUpdate
      );
    });

    // ===== DELETE =====
    await test.step("Delete Assets & Liabilities", async () => {
      app.utilities.logStep("Delete Assets & Liabilities");
      await app.assetsSection.clearAssetsSection(app.assetsSectionDelete);
    });

    await test.step("Validate Assets & Liabilities (delete)", async () => {
      app.utilities.logStep("Validate Assets & Liabilities (delete)");
      await app.assetsSection.validateDeleteAssetsSection(
        app.assetsSectionDelete
      );
    });
  });
  test("giftsGrants, financialAccountsSecurities and contingentLiabilities create-update-delete flows", async () => {
    // ---------- Gifts Or Grants ----------
    await test.step("Create Gifts or Grants", async () => {
      app.utilities.logStep("Create Gifts or Grants");
      await app.AssetsSubsectionsGiftsFASandCL.createGiftsOrGrants(
        app.giftsGrantsCreate
      );
    });

    await test.step("Validate Gifts or Grants (create)", async () => {
      app.utilities.logStep("Validate Gifts or Grants (create)");
      await app.AssetsSubsectionsGiftsFASandCL.validateCreateGiftsOrGrants(
        app.giftsGrantsCreate
      );
    });

    await test.step("Update Gifts or Grants", async () => {
      app.utilities.logStep("Update Gifts or Grants");
      await app.AssetsSubsectionsGiftsFASandCL.updateGiftsOrGrants(
        app.giftsGrantsUpdate
      );
    });

    await test.step("Validate Gifts or Grants (update)", async () => {
      app.utilities.logStep("Validate Gifts or Grants (update)");
      await app.AssetsSubsectionsGiftsFASandCL.validateUpdateGiftsOrGrants(
        app.giftsGrantsUpdate
      );
    });

    await test.step("Delete Gifts or Grants", async () => {
      app.utilities.logStep("Delete Gifts or Grants");
      await app.AssetsSubsectionsGiftsFASandCL.clearGiftsOrGrants(
        app.giftsGrantsDelete
      );
    });

    await test.step("Validate Gifts or Grants (delete)", async () => {
      app.utilities.logStep("Validate Gifts or Grants (delete)");
      await app.AssetsSubsectionsGiftsFASandCL.validateDeleteGiftsOrGrants(
        app.giftsGrantsDelete
      );
    });

    await test.step("Create Gifts or Grants plus section", async () => {
      app.utilities.logStep("Create Gifts or Grants plus section");
      await app.AssetsSubsectionsGiftsFASandCL.createGiftsOrGrantsPlusSection(
        app.giftsGrantsCreate
      );
    });

    await test.step("Validate Gifts or Grants plus section", async () => {
      app.utilities.logStep("Validate Gifts or Grants plus section)");
      await app.AssetsSubsectionsGiftsFASandCL.validateCreateGiftsOrGrantsPlusSection(
        app.giftsGrantsCreate
      );
    });

    await test.step("Delete Gifts or Grants", async () => {
      app.utilities.logStep("Delete Gifts or Grants");
      await app.AssetsSubsectionsGiftsFASandCL.clearGiftsOrGrantsPlusSection();
    });

    // ---------- Financial Accounts & Securities ----------
    await test.step("Create Financial Accounts & Securities", async () => {
      app.utilities.logStep("Create Financial Accounts & Securities");
      await app.AssetsSubsectionsGiftsFASandCL.createFinancialAccountsAndSecurities(
        app.financialAccountsSecuritiesCreate
      );
    });

    await test.step("Validate FAS (create)", async () => {
      app.utilities.logStep("Validate FAS (create)");
      await app.AssetsSubsectionsGiftsFASandCL.validateCreateFinancialAccountsAndSecurities(
        app.financialAccountsSecuritiesCreate
      );
    });

    await test.step("Update Financial Accounts & Securities", async () => {
      app.utilities.logStep("Update Financial Accounts & Securities");
      await app.AssetsSubsectionsGiftsFASandCL.updateFinancialAccountsAndSecurities(
        app.financialAccountsSecuritiesUpdate
      );
    });

    await test.step("Validate FAS (update)", async () => {
      app.utilities.logStep("Validate FAS (update)");
      await app.AssetsSubsectionsGiftsFASandCL.validateUpdateFinancialAccountsAndSecurities(
        app.financialAccountsSecuritiesUpdate
      );
    });

    await test.step("Delete Financial Accounts & Securities", async () => {
      app.utilities.logStep("Delete Financial Accounts & Securities");
      await app.AssetsSubsectionsGiftsFASandCL.clearFinancialAccountsAndSecurities(
        app.financialAccountsSecuritiesDelete
      );
    });

    await test.step("Validate FAS (delete)", async () => {
      app.utilities.logStep("Validate FAS (delete)");
      await app.AssetsSubsectionsGiftsFASandCL.validateDeleteFinancialAccountsAndSecurities(
        app.financialAccountsSecuritiesDelete
      );
    });

    await test.step("Create Financial Accounts & Securities plus section", async () => {
      app.utilities.logStep(
        "Create Financial Accounts & Securities plus section"
      );
      await app.AssetsSubsectionsGiftsFASandCL.createFinancialAccountsAndSecuritiesPlus(
        app.financialAccountsSecuritiesCreate
      );
    });

    await test.step("Validate FAS (create) plus section", async () => {
      app.utilities.logStep("Validate FAS (create) plus section");
      await app.AssetsSubsectionsGiftsFASandCL.validateCreateFinancialAccountsAndSecuritiesPlus(
        app.financialAccountsSecuritiesCreate
      );
    });

    await test.step("Delete Financial Accounts & Securities plus section", async () => {
      app.utilities.logStep(
        "Delete Financial Accounts & Securities plus section"
      );
      await app.AssetsSubsectionsGiftsFASandCL.clearFinancialAccountsAndSecuritiesPlus();
    });

    // ---------- Contingent Liabilities ----------
    await test.step("Create Contingent Liabilities", async () => {
      app.utilities.logStep("Create Contingent Liabilities");
      await app.AssetsSubsectionsGiftsFASandCL.createContingentLiabilities(
        app.contingentLiabilitiesCreate
      );
    });

    await test.step("Validate CL (create)", async () => {
      app.utilities.logStep("Validate CL (create)");
      await app.AssetsSubsectionsGiftsFASandCL.validateCreateContingentLiabilities(
        app.contingentLiabilitiesCreate
      );
    });

    await test.step("Update Contingent Liabilities", async () => {
      app.utilities.logStep("Update Contingent Liabilities");
      await app.AssetsSubsectionsGiftsFASandCL.updateContingentLiabilities(
        app.contingentLiabilitiesUpdate
      );
    });

    await test.step("Validate CL (update)", async () => {
      app.utilities.logStep("Validate CL (update)");
      await app.AssetsSubsectionsGiftsFASandCL.validateUpdateContingentLiabilities(
        app.contingentLiabilitiesUpdate
      );
    });

    await test.step("Delete Contingent Liabilities", async () => {
      app.utilities.logStep("Delete Contingent Liabilities");
      await app.AssetsSubsectionsGiftsFASandCL.clearContingentLiabilities(
        app.contingentLiabilitiesDelete
      );
    });

    await test.step("Validate CL (delete)", async () => {
      app.utilities.logStep("Validate Contingent Liabilities (delete)");
      await app.AssetsSubsectionsGiftsFASandCL.validateDeleteContingentLiabilities(
        app.contingentLiabilitiesDelete
      );
    });

    await test.step("Create Contingent Liabilities plus section", async () => {
      app.utilities.logStep("Create Contingent Liabilities plus section");
      await app.AssetsSubsectionsGiftsFASandCL.createContingentLiabilitiesPlus(
        app.contingentLiabilitiesCreate
      );
    });

    await test.step("Validate CL (create)plus section", async () => {
      app.utilities.logStep("Validate CL (create) plus section");
      await app.AssetsSubsectionsGiftsFASandCL.validateCreateContingentLiabilitiesPlus(
        app.contingentLiabilitiesCreate
      );
    });

    await test.step("Delete Contingent Liabilities plus section", async () => {
      app.utilities.logStep("Delete Contingent Liabilities plus section");
      await app.AssetsSubsectionsGiftsFASandCL.clearContingentLiabilitiesPlus();
    });
  });
  test("Schedule Real Estate Owned and Sold)", async () => {
    // ===== CREATE =====
    await test.step("Create Schedule REO", async () => {
      app.utilities.logStep("Create Schedule Real Estate Owned and Sold");
      await app.scheduleRealEstateSection.createScheduleRealEstate(
        app.scheduleREOCreate
      );
    });

    await test.step("Validate Schedule REO (create)", async () => {
      app.utilities.logStep("Validate Schedule Real Estate Owned and Sold");
      await app.scheduleRealEstateSection.validateCreateScheduleRealEstate(
        app.scheduleREOCreate
      );
    });

    // ===== UPDATE =====
    await test.step("Update Schedule REO ", async () => {
      app.utilities.logStep("Update Schedule Real Estate Owned and Sold");
      await app.scheduleRealEstateSection.updateScheduleRealEstate(
        app.scheduleREOUpdate
      );
    });

    await test.step("Validate Schedule REO (update)", async () => {
      app.utilities.logStep(
        "Validate update Schedule Real Estate Owned and Sold"
      );
      await app.scheduleRealEstateSection.validateUpdateScheduleRealEstate(
        app.scheduleREOUpdate
      );
    });

    // ===== DELETE =====
    await test.step("Delete Schedule REO ", async () => {
      app.utilities.logStep("Clear Schedule Real Estate Owned and Sold");
      await app.scheduleRealEstateSection.clearScheduleRealEstate();
    });

    await test.step("Validate Schedule REO (delete)", async () => {
      app.utilities.logStep(
        "Validate clear Schedule Real Estate Owned and Sold"
      );
      await app.scheduleRealEstateSection.validateClearScheduleRealEstate();
    });
  });
});
