<?php
global $URLPOSTING, $activeTab, $borrowerEmail, $docArray, $userTimeZone, $oldFPCID, $LMRId,
       $PCID, $propertyValuationDocInfo,
       $userGroup, $SID, $isHMLOSelOpt, $assetId, $paymentReserves, $lien1Payment, $totalInsurance, $cliType,
       $isHOALien, $QAID, $allowToSeeBillingSectionForFile, $borrowerActiveSectionDisp, $publicUser,
       $emailOpt, $allowToEdit, $webFormFOpt, $loanOfficerId, $executiveId, $HMLORealEstateTaxes, $oBroker,
       $fieldsInfo, $tabIndex, $REBroker, $REBrokerYesBtn, $agentNumber, $brokerFName, $brokerLName, $brokerEmail, $LmbInfo,
       $brokerCompany, $brokerPhone, $RELoanofficer, $RELoanofficerYesBtn, $loanofficerFName, $loanofficerLName, $loanofficerEmail,
       $loanofficerCompany, $LoanofficerPhone, $LMRClientTypeDisplay, $PCBasicLoanTabLMRIDsExists, $ft, $servicesRequested,
       $LMRClientTypeInfo, $fileLP, $loanInfoLPSectionDisp, $myFileInfo, $propDetailsProcess, $branchHAInfoArray, $leadSource,
       $hereAbout, $referringParty, $hideThisField, $userRole, $isBlanketLoanDiv, $isBlanketLoan, $isBlanketLoanDispOpt, $noOfPropertiesAcquiring,
       $HMLOLoanInfoSectionsDisp, $HMLOTAC, $HMLOTACQA, $wfOpt, $aud, $allowCaptcha, $isPLO, $loanPgmDetails,
       $isCoBorrower, $reqCnt, $UType, $email, $UName, $propertyInfo, $fOpt, $oBranch, $bRc, $aRc,
       $fileRecordDate, $empChecklistNotesInfoArray,
       $branchChecklistNotesInfoArray, $agentChecklistNotesInfoArray, $chkNotReqEmpInfo, $chkNotReqBranchInfo, $chkNotReqAgentInfo;

use models\composite\oBranch\getBranchAndAgentRequiredDocs;
use models\composite\oBroker\getBranchBrokerList;
use models\composite\oBroker\listAllAgentsLoanOfficer;
use models\composite\oChecklist\getChecklistNameID;
use models\composite\oChecklist\getDocStatusFileName;
use models\composite\oChecklist\getRequiredDocsAdditionalLogic;
use models\composite\oChecklist\requiredDocsForAdditionalLoanProgram;
use models\composite\oChecklist\requiredDocsForFile;
use models\composite\oChecklist\requiredDocsForInternalLoanProgram;
use models\composite\oFile\getFileInfo\PCInfo;
use models\composite\oFile\getFileServiceTypes;
use models\composite\oFile\getInfoForDocsUpload;
use models\composite\oLockFile\isFileLockedLastVal;
use models\composite\oPC\getPCInternalServiceType;
use models\composite\oServiceType\getServiceTypes;
use models\constants\docStatusArray;
use models\constants\gl\glAgentLabelChanges;
use models\constants\gl\glAllowHMLOPCToEditBrokerINLV;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glFirstRehabLending;
use models\constants\gl\globalSBALoanProductsCat;
use models\constants\gl\glPCID;
use models\constants\gl\glPropDetailsProcess;
use models\constants\GpropertyTypeNumbArray;
use models\Controllers\Base\generateWebformLinks;
use models\Controllers\LMRequest\mortgageNotes;
use models\Controllers\LMRequest\WebForm;
use models\Controllers\loanForm;
use models\Controllers\backoffice\LoanStagesController;
use models\cypher;
use models\PageVariables;
use models\Request;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Dates;
use models\standard\Strings;

$userNumber = PageVariables::$userNumber;
$globalSBALoanProductsCat = globalSBALoanProductsCat::$globalSBALoanProductsCat;
$glPropDetailsProcess =  glPropDetailsProcess::getPropDetailsProcess($PCID);
$glFirstRehabLending = glFirstRehabLending::$glFirstRehabLending;
$glAgentLabelChanges = glAgentLabelChanges::$glAgentLabelChanges;

if ($URLPOSTING == 1) {
    require 'webformInitVars.php';
} else {
    require 'HMLOLoanInfoVars.php';
}
global $fileTab;
if ($activeTab == 'QAPP') $fileTab = 'QA';
if ($activeTab == 'LI') $fileTab = 'FA';

$borrowerEmailLink = '';

$recordDate = Strings::showField('recordDate', 'LMRInfo');

if (isset($_REQUEST['borrowerEmail'])) $borrowerEmailLink = trim($_REQUEST['borrowerEmail']);

if ($borrowerEmailLink != '') {
    $borrowerEmail = $borrowerEmailLink;
} else {
    if (!$borrowerEmail) {
        $borrowerEmail = Strings::showField('borrowerEmail', 'LMRInfo');
    }

}
$coBorrowerLink = '';
$BorrowerLink = '';
if ($publicUser) {
    generateWebformLinks::init($LMRId);
    if ($wfOpt == 'QA') {
        $coBorrowerLink = generateWebformLinks::$quickAppLinkCoBorrowerMulti;
        $BorrowerLink = generateWebformLinks::$quickAppLinkBorrowerMulti;
    } else if ($wfOpt == 'FA') {
        $coBorrowerLink = generateWebformLinks::$fullAppLinkCoBorrowerMulti;
        $BorrowerLink = generateWebformLinks::$fullAppLinkBorrowerMulti;
    }
}
/**
 * Desc : Proof of sale (HUD)
 * Date : 09 Mar, 2017
 */

$docCnt = 1;
$proofOfSale1 = '';
$proofOfSale2 = '';
$proofOfSale3 = '';
$proofOfSale4 = '';
$proofOfSale5 = '';
$proofOfSale6 = '';
$proofOfSale7 = '';
$proofOfSale8 = '';
$proofOfSale9 = '';
$proofOfSale10 = '';
$proofOfSale11 = '';
$proofOfSale12 = '';
$proofOfSale13 = '';
$proofOfSale14 = '';
$proofOfSale15 = '';
$hudDocN7 = '';
$hudDocN8 = '';
$hudDocN9 = '';
$hudDocN10 = '';
$hudDocN11 = '';
$hudDocN12 = '';
$hudDocN13 = '';
$hudDocN14 = '';
$hudDocN15 = '';
for ($doc = 0; $doc < count($docArray); $doc++) {
    $tempDocArray = [];
    $docCategoryArray = [];
    $flatNotes = '';
    $uploadDocUrl = '';
    $docName = '';
    $displayDocName = '';
    $docId = 0;
    $myUploadedBy = '';
    $myUploadedRole = '';
    $docCategory = '';

    $docName = trim($docArray[$doc]['docName']);
    $displayDocName = trim($docArray[$doc]['displayDocName']);
    $uploadedDate = trim($docArray[$doc]['uploadedDate']);
    $userId = trim($docArray[$doc]['uploadedBy']);
    $userType = trim($docArray[$doc]['uploadingUserType']);
    $docCategory = trim($docArray[$doc]['docCategory']);
    $docId = trim($docArray[$doc]['docID']);
    $fileType = trim($docArray[$doc]['fileType']);

    $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
    $ipArray['outputZone'] = $userTimeZone;
    $ipArray['inputTime'] = $uploadedDate;
    $uploadedDate = Dates::timeZoneConversion($ipArray);

    $docCategoryArray = explode('-', $docCategory);

    if (count($docCategoryArray) > 0) {
        if ($docCategoryArray[0] == 'Proof of sale (HUD)') {

            if (Dates::IsEmpty($uploadedDate)) {
                $uploadedDate = '';
            } else {
                $uploadedDate = Dates::formatDateWithRE($uploadedDate, 'YMD_HMS', 'm/d/Y h:i A');
            }

            if ($displayDocName == '' || $displayDocName == NULL) {
                $displayDocName = $docName;
            }

            $tempRecDate = str_replace('-', '', $recordDate);
            $folderName = $oldFPCID . '/' . date('Y', strtotime($tempRecDate)) . '/' . date('m', strtotime($tempRecDate)) . '/' . date('d', strtotime($tempRecDate));
            $fileValue = $LMRId;

            $fP = $folderName . '/' . $fileValue . '/upload';
            $uploadDocUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($docName) . '&fd=' . cypher::myEncryption(CONST_PATH_LMR_FILE_DOCS . $fP) . '&opt=enc';
            if (isset($docCategoryArray[1])) ${'proofOfSale' . $docCategoryArray[1]} = $uploadDocUrl;
            if (isset($docCategoryArray[1])) ${'hudDocN' . $docCategoryArray[1]} = $displayDocName;
        }
    }
}
if (in_array($PCID, $glFirstRehabLending)) {
    unset($glPropDetailsProcess[2]);
    $glPropDetailsProcess = array_values($glPropDetailsProcess);
}

$titlereportDoc = '';
$pc = 1;

if (count($propertyValuationDocInfo) > 0) {
    $keysArray = [];
    $keysArray = array_keys($propertyValuationDocInfo);
    for ($i = 0; $i < count($keysArray); $i++) {
        $docCategory = '';
        $uploadDocUrl = '';
        $docName = '';
        $displayDocName = '';
        $fileType = '';
        $docID = '';
        $uploadedBy = '';
        $docCategory = trim($keysArray[$i]);
        $docName = trim($propertyValuationDocInfo[$docCategory]['docName']);
        $displayDocName = trim($propertyValuationDocInfo[$docCategory]['displayDocName']);
        $fileType = trim($propertyValuationDocInfo[$docCategory]['fileType']);
        $docID = trim($propertyValuationDocInfo[$docCategory]['docID']);
        $uploadedBy = trim($propertyValuationDocInfo[$docCategory]['uploadedBy']);

        $tempRecDate = str_replace('-', '', $recordDate);
        $folderName = $oldFPCID . '/' . date('Y', strtotime($tempRecDate)) . '/' . date('m', strtotime($tempRecDate)) . '/' . date('d', strtotime($tempRecDate));
        $fileValue = $LMRId;

        $fP = $folderName . '/' . $fileValue . '/property';
        ${strtolower(str_replace(' ', '', $docCategory)) . 'Doc'} = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($docName) . '&fd=' . cypher::myEncryption(CONST_PATH_LMR_FILE_DOCS . $fP) . '&opt=enc';
        ${strtolower(str_replace(' ', '', $docCategory)) . 'DocName'} = substr($displayDocName, 0, 18) . '.' . $fileType;
    }
    $proInsCnt = $pc;
}
if ($userGroup == 'Client') {
    $disableFld = '';
}
?>
<input type="hidden" name="SID" value="<?php echo $SID; ?>">
<input type="hidden" name="UGroup" value="<?php echo $userGroup; ?>">
<input type="hidden" name="isHMLOOpt" id="isHMLOOpt" value="<?php echo $isHMLOSelOpt; ?>">
<input type="hidden" name="assetId" value="<?php echo $assetId; ?>">
<input type="hidden" name="paymentReserves" value="<?php echo $paymentReserves; ?>">
<input type="hidden" name="HMLORealEstateTaxes" id="HMLORealEstateTaxes"
       value="<?php echo $HMLORealEstateTaxes; ?>">
<input type="hidden" name="totalInsurance" value="<?php echo $totalInsurance; ?>">
<input type="hidden" name="mortgage1MonthlyPayment" value="<?php echo $lien1Payment; ?>">
<input type="hidden" name="cliType" id="cliType" value="<?php echo $cliType; ?>">
<input type="hidden" name="recordDate" id="recordDate"
       value="<?php echo Strings::showField('recordDate', 'LMRInfo') ?>"/>
<input type="hidden" name="QAID" id="QAID" value="<?php echo $QAID; ?>"/>
<input type="hidden" name="RUID" value="<?php echo Strings::showField('RUID', 'billingUrlInfo') ?>">
<input type="hidden" name="allowToSeeBillingSectionForFile" id="allowToSeeBillingSectionForFile"
       value="<?php echo $allowToSeeBillingSectionForFile ?>">
<input type="hidden" name="inPro" id="inPro" value="<?php if ($borrowerActiveSectionDisp == '') {
    echo 'hide';
} ?>">
<?php
//for automation
$fileRow = '';
if ($LMRId == 0) { //automation for new file
    $isFssUpdated = 'Yes';
    $fileRow = 'Insert';
} else {
    $isFssUpdated = 'No';
    $fileRow = 'Update';
}
$fileRow = Request::isset('fileRow') ? cypher::myDecryption(Request::GetClean('fileRow')) : $fileRow;
?>
<!-- automation -->
<input type="hidden" name="isFssUpdated" id="isFssUpdated" value="<?php echo $isFssUpdated; ?>">
<input type="hidden" name="lastUpdatedParam" id="lastUpdatedParam" value="PFS">
<input type="hidden" name="triggerRule" id="triggerRule" value="Yes">
<input type="hidden" name="userAutomationControlAccess" id="userAutomationControlAccess" value="0">
<input type="hidden" name="fileRow" id="fileRow" value="<?php echo $fileRow; ?>">
<?php if ($publicUser) { ?>
    <input type="hidden" name="previousModStatusId"
           value="<?php echo Strings::showField('primeStatusId', 'ResponseInfo') ?>">
<?php } ?>
<!-- // automation // -->

<?php
if ($publicUser != 1) { // Not allow this section for public users
    require 'fileAdminInfo.php';
} else {
    ?>
    <input type="hidden" name="selectedPC" id="selectedPC" value="<?php echo $PCID; ?>">
    <?php
    if ($PCID == 4326) { // **exception** PCID = 4326 (BD Capital)
        $internalLoanProgramList = [];
        $ip = [
            'PCID'       => $PCID,
            'keyNeeded'  => 'n',
            'moduleCode' => 'HMLO',
        ];
        $internalLoanProgramList = getPCInternalServiceType::getReport($ip);
        $serviceCnt = 0;
        if (count($internalLoanProgramList) > 0) $serviceCnt = count($internalLoanProgramList);
        for ($a = 0; $a < $serviceCnt; $a++) {
            $LMRClientTypeCode = '';
            $LMRClientType = '';
            $LMRClientTypeCode = trim($internalLoanProgramList[$a]['LMRClientType']);
            $LMRClientType = trim($internalLoanProgramList[$a]['serviceType']);
            if ($internalLoanProgramList[$a]['internalLoanProgram'] == '1' && $LMRClientType == 'To Be Determined') { ?>
                <input type="hidden" name="LMRInternalLoanProgram[]" class="" value="<?php echo $LMRClientTypeCode; ?>">
                <?php
            }
        }
    }
}

/**
 *
 * Customization for PC = Lendterra -->  Allow to edit the broker info in the webform on Mar 16, 2018.
 *
 * Added the Testing Purpose PC Dave = 820, Awata = 2, Lendterra = 3126 PC's
 * Ticket ID : 156022288
 **/
$allowUserToEditBroker = 0;
if (glAllowHMLOPCToEditBrokerINLV::is($PCID) && $emailOpt == 'Email' && $LMRId > 0 && $allowToEdit) {
    $allowUserToEditBroker = 1;
}
?>

<?php
$wizardNum = 1;
$categoryArray = [
    '1' => 'GeneralInfoStep',
    '2' => 'ApplicantInfoStep',
    '3' => 'BackgroundExpStep',
    '4' => 'BorrowerFinancialStep',
    '5' => 'LoanPropertyStep',
    '6' => 'AdditionalInfoStep',
    '7' => 'RequiredDocsStep',
];
if (isset($_REQUEST['p'])) {
    $wizardNum = array_search(cypher::myDecryption($_REQUEST['p']) . 'Step', $categoryArray);
    if (isset($_COOKIE['_cWPC' . $LMRId])) {
        $wizardNum = $_COOKIE['_cWPC' . $LMRId];
    }
} else {

    if (isset($_REQUEST['wn'])) {
        $wizardNum = cypher::myDecryption($_REQUEST['wn']); //wizardNum
        if (isset($_COOKIE['_cWPC' . $LMRId])) {
            $wizardNum = $_COOKIE['_cWPC' . $LMRId];
        }
    }
    if (isset($_COOKIE['_cWPC' . $LMRId])) {
        $wizardNum = $_COOKIE['_cWPC' . $LMRId];
    }
}
?>
<style>
    .navbar-toggler.collapsed > .arrow-down, .navbar-toggler:not(.collapsed) > .arrow-up {
        display: none;
    }
</style>
<input type="hidden" id="wizardForm" name="wizardForm" value="1">
<input type="hidden" name="currentWizardPage" id="currentWizardPage"
       value="<?php echo htmlspecialchars($wizardNum); ?>"/>
<input type="hidden" name="nextWizardPage" id="nextWizardPage" value=""/>
<input type="hidden" name="isAutoSave" id="isAutoSave" value="0"/>
<div class="row">
    <div class="col-12">
        <?php echo LoanStagesController::forLoanFile($LMRId)->renderStagesHtml(); ?>
    </div>
</div>

<!--begin: Wizard-->
<div class="wizard wizard-2" id="kt_wizard" data-wizard-state="step-first" data-wizard-clickable="false">
    <!--begin: Wizard Nav-->
    <div class="wizard-nav  py-md-4 px-md-4 py-lg-2 px-lg-2 mt-2 text-right">
        <nav class=" navbar-expand-lg navbar-light p-0 ">
            <button class="navbar-toggler mb-2 collapsed ml-auto border-0" type="button" data-toggle="collapse"
                    data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false"
                    aria-label="Toggle navigation">
                  <span class=" btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon   arrow-down">
                      <i class="ki icon-nm ki-arrow-down"></i>
                  </span>
                <span class="btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon arrow-up">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </span>
            </button>
            <div class="collapse navbar-collapse text-left" id="navbarNav">
                <div class="wizard-steps">

                    <!--begin::Wizard Step 1 Nav-->
                    <div id="GeneralInfoStep" class="wizard-step" data-wizard-type="step"
                         data-wizard-state="current"
                         data-wizard-clickable="true">
                        <div class="wizard-wrapper">
                            <div class="wizard-icon">
                                <i class="flaticon2-writing tooltipClass icon-2x"
                                   title="General Info"></i>
                            </div>
                            <div class="wizard-label">
                                <h3 class="wizard-title">General Info</h3>
                                <div class="wizard-desc <?php echo glCustomJobForProcessingCompany::hideWizardDescription($PCID) ?>">
                                    Initial Loan App Setup
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--begin::Wizard Step 1 Nav-->

                    <?php
                    //Steps 2 //Applicant Info
                    $step1ApplicantInfo = 'hide categoryHide';
                    if ((count(BaseHTML::sectionAccess(['sId' => 'BCI', 'opt' => $fileTab])) > 0)
                        || (count(Arrays::getValueFromArray('CBI', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('BEN', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('AG', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('GOVT', $fieldsInfo)) > 0)
                    ) {
                        $step1ApplicantInfo = '';
                    }
                    ?>
                    <!--begin::Wizard Step 2 Nav-->
                    <div id="ApplicantInfoStep" class="wizard-step <?php echo $step1ApplicantInfo; ?>"
                         data-wizard-type="step"
                         data-wizard-clickable="true">
                        <div class="wizard-wrapper">
                            <div class="wizard-icon">
                                <i class="flaticon2-avatar  tooltipClass icon-2x"
                                   title="Applicant Info"></i>
                            </div>
                            <div class="wizard-label">
                                <h3 class="wizard-title">Applicant Info</h3>
                                <div class="wizard-desc <?php echo glCustomJobForProcessingCompany::hideWizardDescription($PCID) ?>">
                                    Borrower & Guarantor Info
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--end::Wizard Step 2 Nav-->


                    <?php
                    //Background &experinc3 Info
                    $step2BackgroundExp = 'hide categoryHide';
                    if ((count(BaseHTML::sectionAccess(['sId' => 'BB', 'opt' => $fileTab])) > 0)
                        || (count(Arrays::getValueFromArray('CBB', $fieldsInfo)) > 0 && $isCoBorrower == 1)
                        || (count(Arrays::getValueFromArray('BE', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('TT', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('CBE', $fieldsInfo)) > 0 && $isCoBorrower == 1)
                        || (count(Arrays::getValueFromArray('PM', $fieldsInfo)) > 0)
                    ) {
                        $step2BackgroundExp = '';
                    }
                    ?>
                    <!--begin::Wizard Step 3 Nav-->
                    <div id="BackgroundExpStep" class="wizard-step <?php echo $step2BackgroundExp; ?>"
                         data-wizard-type="step"
                         data-wizard-clickable="true">
                        <div class="wizard-wrapper">
                            <div class="wizard-icon">
                                <i class="flaticon2-search  tooltipClass icon-2x"
                                   title="Background & Experience"></i>
                            </div>
                            <div class="wizard-label">
                                <h3 class="wizard-title">
                                    <?php if ($PCID == glPCID::PCID_PROD_CV3) { ?>
                                        Experience
                                    <?php } else { ?>
                                        Background & Experience
                                    <?php } ?>
                                </h3>
                                <div class="wizard-desc <?php echo glCustomJobForProcessingCompany::hideWizardDescription($PCID) ?>">
                                    Disclosures, track record & misc questions.
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--end::Wizard Step 3 Nav-->


                    <?php
                    //Borrower Financials
                    $step3BorrowerFina = 'hide categoryHide';
                    /*      echo count(sectionAccess(array('sId' => 'Assets', 'opt' => $fileTab)));
                          echo count(Arrays::getValueFromArray('SORE', $fieldsInfo));
                          echo count(Arrays::getValueFromArray('BEI', $fieldsInfo));
                          echo count(Arrays::getValueFromArray('CBEI', $fieldsInfo));
                          echo count(Arrays::getValueFromArray('CBMI', $fieldsInfo));
                          echo count(Arrays::getValueFromArray('BMI', $fieldsInfo));
                          echo count(Arrays::getValueFromArray('BME', $fieldsInfo));
                          echo count(Arrays::getValueFromArray('CBME', $fieldsInfo));
                          echo count(Arrays::getValueFromArray('CLNRE', $fieldsInfo));
                          echo count(Arrays::getValueFromArray('CL', $fieldsInfo));*/

                    if ((count(BaseHTML::sectionAccess(['sId' => 'Assets', 'opt' => $fileTab])) > 0)
                        || (count(Arrays::getValueFromArray('SORE', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('BEI', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('CBEI', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('CBMI', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('BMI', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('BME', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('CBME', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('CLNRE', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('CL', $fieldsInfo)) > 0)
                    ) {
                        $step3BorrowerFina = '';
                    }
                    ?>
                    <!--begin::Wizard Step 4 Nav-->
                    <div id="BorrowerFinancialStep" class="wizard-step <?php echo $step3BorrowerFina; ?>"
                         data-wizard-type="step"
                         data-wizard-clickable="true">
                        <div class="wizard-wrapper">
                            <div class="wizard-icon">
                                <i class="fas fa-money-check-alt tooltipClass  icon-2x"
                                   title="Borrower Financials"></i>
                            </div>
                            <div class="wizard-label">
                                <h3 class="wizard-title">
                                    <?php if ($PCID == glPCID::PCID_PROD_CV3) { ?>
                                        Employment Information
                                    <?php } else { ?>
                                        Borrower Financials
                                    <?php } ?>
                                </h3>
                                <div class="wizard-desc <?php echo glCustomJobForProcessingCompany::hideWizardDescription($PCID) ?>">
                                    Detailed financials (if Applicable)
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--end::Wizard Step 4 Nav-->


                    <!--begin::Wizard Step 5 Nav-->
                    <?php
                    // Loan & Property/Collateral
                    $step4LoanPropertColl = 'hide categoryHide';
                    if ((count(BaseHTML::sectionAccess(['sId' => 'LT', 'opt' => $fileTab])) > 0)
                        || (count(Arrays::getValueFromArray('RCM', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('PD', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('SPCF', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('EPC', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('COL', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('1LMS', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('2LMS', $fieldsInfo)) > 0)
                    ) {
                        $step4LoanPropertColl = '';
                    }
                    ?>
                    <div id="LoanPropertyStep" class="wizard-step <?php echo $step4LoanPropertColl; ?>"
                         data-wizard-type="step"
                         data-wizard-clickable="true">
                        <div class="wizard-wrapper">
                            <div class="wizard-icon">
                                <i class="flaticon2-percentage tooltipClass  icon-2x"
                                   title="Loan & Property/Collateral"></i>
                            </div>
                            <div class="wizard-label">
                                <h3 class="wizard-title"><?php if ($PCID == glPCID::PCID_PROD_CV3) { ?>
                                        Transaction Details
                                    <?php } else { ?> Loan & Property/Collateral <?php } ?>
                                </h3>
                                <div class="wizard-desc <?php echo glCustomJobForProcessingCompany::hideWizardDescription($PCID) ?>">
                                    Collateral details supporting the loan
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--end::Wizard Step 5 Nav-->

                    <!--begin::Wizard Step 6 Nav-->
                    <?php
                    // Loan & Property/Collateral
                    $step5Additional = 'hide categoryHide';
                    if ((count(BaseHTML::sectionAccess(['sId' => 'FRC', 'opt' => $fileTab])) > 0)
                        || (count(Arrays::getValueFromArray('QA', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('AQ', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('TI', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('AI', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('EI', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('ICI', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('MEMO', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('BPI', $fieldsInfo)) > 0)
                        || (count(Arrays::getValueFromArray('TAC', $fieldsInfo)) > 0)
                    ) {
                        $step5Additional = '';
                    }
                    ?>
                    <div id="AdditionalInfoStep" class="wizard-step <?php echo $step5Additional; ?>"
                         data-wizard-type="step"
                         data-wizard-clickable="true">
                        <div class="wizard-wrapper">
                            <div class="wizard-icon">
                                <i class="far fa-question-circle tooltipClass  icon-2x"
                                   title="Additional Information"></i>
                            </div>
                            <div class="wizard-label">
                                <h3 class="wizard-title">Additional Information</h3>
                                <div class="wizard-desc <?php echo glCustomJobForProcessingCompany::hideWizardDescription($PCID) ?>">
                                    Detailed questions to finalize the loan
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--end::Wizard Step 6 Nav-->
                    <!--begin::Wizard Step 7 Nav-->
                    <?php if ($reqCnt > 0) { ?>
                        <div id="RequiredDocsStep" class="wizard-step <?php if ($LMRId == 0) {
                            echo 'hide categoryHide';
                        } ?>" data-wizard-type="step" data-wizard-clickable="true">
                            <div class="wizard-wrapper">
                                <div class="wizard-icon">
                                    <i class="flaticon-upload tooltipClass  icon-2x"
                                       title="Additional Information"></i>
                                </div>
                                <div class="wizard-label">
                                    <h3 class="wizard-title">Required Docs</h3>
                                    <div class="wizard-desc <?php echo glCustomJobForProcessingCompany::hideWizardDescription($PCID) ?>">
                                        Upload supporting docs
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                    <!--end::Wizard Step 7 Nav-->


                </div>
            </div>
        </nav>
    </div>
    <!--end: Wizard Nav-->


    <!--begin: Wizard Body-->
    <div class="wizard-body py-4 px-currentWizardPage8  px-lg-10  ">
        <!--begin: Wizard Form-->
        <div class="row">
            <?php
            if (count($secArr = BaseHTML::sectionAccess2(['sId' => 'ACF', 'opt' => $fileTab])) > 0) { // Get Active Fields only...
                loanForm::pushSectionID('ACF');

                if (count(Arrays::getValueFromArray('ACF', $fieldsInfo)) > 0) {
                    ?>
                    <div class="col-md-12 <?php echo loanForm::showField('guidelinewarning'); ?>"
                         id="divGuidelinesErrorMsg"></div>
                    <?php
                }
            }
            ?>
            <div class=" col-xxl-12   px-0" id="wizardMainPage">
                <!--  style="height: 600px !important;overflow: auto;"-->


                <!--begin: Wizard Step 1-->
                <div id="GeneralInfoStepBody" class="pb-5 step-content-class" data-wizard-type="step-content"
                     data-wizard-state="current">
                    <?php

                    if ($publicUser == 1 && ($webFormFOpt != 'agent' || $loanOfficerId > 0)) { // Agent Section Only shows Branch Web Form Only.
                        $preferredLoanOfficerBrokerInfoArray = $preferredBrokerInfoArray = $preferredLoanofficerInfoArray = [];
                        $agentSectionDiv = 'display: block;';
                        $inArray = ['executiveId' => $executiveId, 'fOpt' => $webFormFOpt];
                        $preferredLoanOfficerBrokerInfoArray = getBranchBrokerList::getReport($inArray);

                        if ($loanOfficerId > 0) {
                            //echo $loanOfficerId;
                            $brokerList = [];
                            $ip['externalBroker'] = 0;
                            $ip['loanOfficerId'] = $loanOfficerId;
                            $ip['userGroup'] = 'Agent';
                            $ip['PCID'] = $PCID;
                            if ($loanOfficerId > 0) {
                                $brokerList = listAllAgentsLoanOfficer::getReport($ip);
                            }
                            foreach ($brokerList as $brokeachKey => $eachBrokerList) {
                                $brokerListNewArray[$brokeachKey] = $eachBrokerList;
                                $brokerListNewArray[$brokeachKey]['brokerFName'] = $eachBrokerList['bName'];
                                $brokerListNewArray[$brokeachKey]['brokerLName'] = $eachBrokerList['bLName'];
                            }
                            $preferredLoanOfficerBrokerInfoArray = $brokerListNewArray;
                            // pr($preferredLoanOfficerBrokerInfoArray);
                        }


                        foreach ($preferredLoanOfficerBrokerInfoArray as $eachLBuser) {
                            if ($eachLBuser['externalBroker'] == '0') {
                                $preferredBrokerInfoArray[] = $eachLBuser;
                            }
                            if ($eachLBuser['externalBroker'] == '1') {
                                $preferredLoanofficerInfoArray[] = $eachLBuser;
                            }
                        }

                        /*
                         * “Agent/Broker Info” as a label “Loan Officer/Mortgage Broker Info” - Dayton Capital Partners, LLC
                         * Ticket ID : 156333030
                         * Mar 28, 2018
                         * Make it as global on Mar 30, 2018
                        */
                        $agentLabel = 'Agent/Broker';
                        if (in_array($PCID, $glAgentLabelChanges)) {
                            $agentLabel = 'Loan Officer/Broker';
                        }
//$PCquickAppFieldsInfo = array();
                        if (($emailOpt != 'Email' || $allowUserToEditBroker == 1)) {

                            $secArr = BaseHTML::sectionAccess2(['sId' => 'ABI', 'opt' => $fileTab]);
                            loanForm::pushSectionID('ABI');

                            ?>


                            <div class="card card-custom ABI ABICard <?php if (count(Arrays::getValueFromArray('ABI', $fieldsInfo)) <= 0) {
                                echo 'secHide';
                            } ?>">
                                <div class="card-header card-header-tabs-line bg-gray-100  ">
                                    <div class="card-title">
                                        <h3 class="card-label">
                                            <?php echo BaseHTML::getSectionHeading('ABI'); ?>
                                        </h3>
                                        <?php if (trim(BaseHTML::getSectionTooltip('ABI')) != '') { ?>&nbsp;
                                            <i class="popoverClass fas fa-info-circle text-primary "
                                               data-html="true"
                                               data-content="<?php echo BaseHTML::getSectionTooltip('ABI'); ?>"></i>
                                        <?php } ?>
                                    </div>
                                    <div class="card-toolbar ">
                                        <a href="javascript:void(0);"
                                           class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                                           data-card-tool="toggle"
                                           data-section="ABICard"
                                           data-toggle="tooltip" data-placement="top" title=""
                                           data-original-title="Toggle Card">
                                            <i class="ki ki-arrow-down icon-nm"></i>
                                        </a>
                                        <a href="#"
                                           class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none"
                                           data-card-tool="reload"
                                           data-toggle="tooltip" data-placement="top" title=""
                                           data-original-title="Reload Card">
                                            <i class="ki ki-reload icon-nm"></i>
                                        </a>
                                        <a href="#"
                                           class="btn btn-icon btn-sm btn-hover-light-primary d-none"
                                           data-card-tool="remove"
                                           data-toggle="tooltip" data-placement="top" title=""
                                           data-original-title="Remove Card">
                                            <i class="ki ki-close icon-nm"></i>
                                        </a>
                                    </div>
                                </div>
                                <div class="card-body ABICard_body">

                                    <!--  Broker -->
                                    <?php if (!$agentNumber) { ?>
                                        <div id="REBrokerQuestion"
                                             class="row form-group bg-gray-100 py-2  REBrokerLODisp REBroker_disp <?php echo loanForm::showField('REBroker'); ?><?php if ($PCID == 2853) {
                                                 //echo 'secHide';
                                             } //2853 is express, 3363 stage lw demo 2, 3580 is lendingwise-dave in live ?>">
                                            <?php echo loanForm::label('REBroker', 'col-md-5 '); ?>
                                            <div class="col-md-1"></div>

                                            <div class="col-md-6">
                                                <?php
                                                if ($LMRId == 0 || $allowUserToEditBroker == 1) { ?>
                                                    <input type="radio" class="radiobtn mandatory"
                                                           name="REBroker"
                                                           id="REBroker" value="Yes"
                                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $REBroker) . ' ' . $REBrokerYesBtn; ?>
                                                           onclick="showAndHideBrokerInfo(this.value, 'BrokerInfoDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'REBroker', 'sArr' => $secArr, 'opt' => 'I']); ?>>Yes
                                                    <input type="radio" name="REBroker" id="REBroker" value="No"
                                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $REBroker); ?>
                                                           onclick="showAndHideBrokerInfo(this.value, 'BrokerInfoDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'REBroker', 'sArr' => $secArr, 'opt' => 'I']); ?>>No

                                                    <?php
                                                } else {
                                                    echo '<b>' . $REBroker . '</b>';
                                                }
                                                ?>
                                            </div>
                                        </div>
                                        <div id="BrokerInfoDiv"
                                             class="  brokerSection  REBrokerLODisp <?php echo BaseHTML::parentFieldAccess(['fNm' => 'REBroker', 'sArr' => $secArr, 'pv' => $REBroker, 'av' => 'Yes']); ?>">
                                            <!-- Broker Info Section Start -->
                                            <div class="row">
                                                <?php if (count($preferredBrokerInfoArray) > 0) { ?>
                                                    <div class=" col-md-6 LMRBroker_disp <?php echo loanForm::showField('LMRBroker'); ?>">
                                                        <div class="row form-group">
                                                            <?php echo loanForm::label('LMRBroker', 'col-md-5 '); ?>
                                                            <div class="col-md-7">
                                                                <?php
                                                                if (($LMRId == 0 || $allowUserToEditBroker == 1)) {
                                                                    ?>
                                                                    <select class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'LMRBroker', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                            name="LMRBroker" id="LMRBroker"
                                                                            tabindex="<?php echo $tabIndex++; ?>"
                                                                            onchange="updateBrokerNo(this.value, 'loanModForm');populateBrokerInfo('loanModForm', this.value, 'DD');" <?php echo BaseHTML::fieldAccess(['fNm' => 'LMRBroker', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                                        <option value=""> - Select / New -
                                                                        </option>
                                                                        <?php
                                                                        for ($pd = 0; $pd < count($preferredBrokerInfoArray); $pd++) {
                                                                            $tempPrefArray = [];
                                                                            $selLMRBrokerNo = 0;
                                                                            $selBrokerFName = '';
                                                                            $selBrokerLName = '';
                                                                            $selLMRBrokerName = '';
                                                                            $sOpt = '';
                                                                            $tempPrefArray = $preferredBrokerInfoArray[$pd];
                                                                            $selLMRBrokerNo = cypher::myEncryption($tempPrefArray['brokerNumber']);
                                                                            $selBrokerFName = $tempPrefArray['brokerFName'];
                                                                            $selBrokerLName = $tempPrefArray['brokerLName'];
                                                                            $selLMRBrokerName = $selBrokerFName . ' ' . $selBrokerLName;
                                                                            if ($selLMRBrokerNo == cypher::myEncryption($agentNumber)) $sOpt = 'selected';
                                                                            ?>
                                                                            <option value="<?php echo $selLMRBrokerNo; ?>" <?php echo $sOpt ?> ><?php echo $selLMRBrokerName ?></option>
                                                                        <?php } ?>
                                                                    </select>
                                                                <?php } else {
                                                                    echo '<b>' . $brokerFName . ' ' . $brokerLName . '</b>';
                                                                } ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php }
                                                ?>
                                                <div class=" col-md-6 <?php echo loanForm::showField('REBrokerEmail'); ?>">
                                                    <div class="row form-group">
                                                        <?php echo loanForm::label('REBrokerEmail', 'col-md-5 '); ?>
                                                        <div class="col-md-7">
                                                            <?php
                                                            if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                                                ?>
                                                                <input type="email"
                                                                       class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerEmail', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                       name="REBrokerEmail" id="REBrokerEmail"
                                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                                       value="<?php echo $brokerEmail; ?>"
                                                                       autocomplete="off"
                                                                       onblur="checkREBrokerEmailExist('loanModForm');" <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerEmail', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                                            <?php } else {
                                                                echo '<b>' . $brokerEmail . '</b>';
                                                            } ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="<?php echo $LmbInfo; ?>" class="LmbInfo row ">

                                                <div class=" col-md-6 REBrokerFirstName_disp <?php echo loanForm::showField('REBrokerFirstName'); ?>">
                                                    <div class="row form-group">
                                                        <?php echo loanForm::label('REBrokerFirstName', 'col-md-5 '); ?>
                                                        <div class="col-md-7">
                                                            <?php
                                                            if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                                                ?>
                                                                <input type="text"
                                                                       class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerFirstName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                       name="REBrokerFirstName"
                                                                       id="REBrokerFirstName"
                                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                                       value="<?php echo $brokerFName; ?>"
                                                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerFirstName', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                                <?php
                                                            } else {
                                                                echo '<b>' . $brokerFName . '</b>';
                                                            }
                                                            ?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 REBrokerLastName_disp <?php echo loanForm::showField('REBrokerLastName'); ?>">
                                                    <div class="row form-group">
                                                        <?php echo loanForm::label('REBrokerLastName', 'col-md-5 '); ?>
                                                        <div class="col-md-7">
                                                            <?php
                                                            if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                                                ?>
                                                                <input type="text"
                                                                       class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerLastName', 'sArr' => $secArr, 'opt' => 'M']); ?>>"
                                                                       name="REBrokerLastName"
                                                                       id="REBrokerLastName"
                                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                                       value="<?php echo $brokerLName; ?>"
                                                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerLastName', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                                <?php
                                                            } else {
                                                                echo '<b>' . $brokerLName . '</b>';
                                                            }
                                                            ?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class=" col-md-6 REBrokerCompany_disp <?php echo loanForm::showField('REBrokerCompany'); ?>">
                                                    <div class="row form-group">
                                                        <?php echo loanForm::label('REBrokerCompany', 'col-md-5 '); ?>
                                                        <div class="col-md-7">
                                                            <?php
                                                            if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                                                ?>
                                                                <input type="text"
                                                                       class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerCompany', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                       name="REBrokerCompany"
                                                                       id="REBrokerCompany"
                                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                                       class="form-control input-sm"
                                                                       value="<?php echo $brokerCompany; ?>"
                                                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerCompany', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                            <?php } else {
                                                                echo '<b>' . $brokerCompany . '</b>';
                                                            }
                                                            ?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 brokerPhone_disp <?php echo loanForm::showField('brokerPhone'); ?>">
                                                    <div class="row form-group">
                                                        <?php echo loanForm::label('brokerPhone', 'col-md-5 '); ?>
                                                        <div class="col-md-7 brokerSection">
                                                            <?php
                                                            if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                                                ?>
                                                                <input type="text"
                                                                       class="form-control input-sm mask_phone <?php echo BaseHTML::fieldAccess(['fNm' => 'brokerPhone', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                       name="brokerPhone" id="brokerPhone"
                                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                                       value="<?php echo $brokerPhone; ?>"
                                                                       autocomplete="off"
                                                                       placeholder="(___) ___ - ____ Ext ____" <?php echo BaseHTML::fieldAccess(['fNm' => 'brokerPhone', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                                <?php
                                                            } else {
                                                                echo '<b>' . Strings::formatPhoneNumber($brokerPhone) . '</b>';
                                                            }
                                                            ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    <?php } ?>
                                    <!--  End of Broker-->

                                    <?php if ($loanOfficerId == '0') { ?>
                                        <!--  Loan Officer -->
                                        <div id="RELoanofficerQuestion"
                                             class="row form-group  bg-gray-100 py-2  RELoanofficer_disp <?php echo loanForm::showField('RELoanofficer'); ?> <?php if ($PCID == 2853) {
                                                 echo 'secHide';
                                             } //2853 is express, 3363 stage lw demo 2, 3580 is lendingwise-dave in live ?>">
                                            <?php echo loanForm::label('RELoanofficer', 'col-md-5 '); ?>
                                            <div class="col-md-1"></div>
                                            <div class="col-md-6">
                                                <?php
                                                if ($LMRId == 0 || $allowUserToEditBroker == 1) { ?>

                                                    <input type="radio" class="radiobtn mandatory"
                                                           name="RELoanofficer"
                                                           id="RELoanofficer"
                                                           value="Yes"
                                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $RELoanofficer) . ' ' . $RELoanofficerYesBtn; ?>
                                                           onclick="showAndHideLoanofficerInfo(this.value, 'LoanofficerInfoDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficer', 'sArr' => $secArr, 'opt' => 'I']); ?>>Yes
                                                    <input type="radio" name="RELoanofficer"
                                                           id="RELoanofficer"
                                                           value="No"
                                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $RELoanofficer); ?>
                                                           onclick="showAndHideLoanofficerInfo(this.value, 'LoanofficerInfoDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficer', 'sArr' => $secArr, 'opt' => 'I']); ?>>No

                                                    <?php
                                                } else {
                                                    if ($RELoanofficer == '') {
                                                        $RELoanofficer = 'No';
                                                    }
                                                    echo '<b>' . $RELoanofficer . '</b>';
                                                }
                                                ?>
                                            </div>
                                        </div>
                                        <div id="LoanofficerInfoDiv"
                                             class="loanofficerSection  <?php echo BaseHTML::parentFieldAccess(['fNm' => 'RELoanofficer', 'sArr' => $secArr, 'pv' => $RELoanofficer, 'av' => 'Yes']); ?>">
                                            <!-- Loan Officer Section Start -->

                                            <div class="row">
                                                <?php
                                                if (count($preferredLoanofficerInfoArray) > 0) { ?>
                                                    <div class=" col-md-6 LMRLoanofficer_disp <?php echo loanForm::showField('LMRLoanofficer'); ?>">
                                                        <div class="row form-group">
                                                            <?php echo loanForm::label('LMRLoanofficer', 'col-md-5 '); ?>
                                                            <div class="col-md-7">
                                                                <?php
                                                                if (($LMRId == 0 || $allowUserToEditBroker == 1)) {
                                                                    ?>
                                                                    <select class="form-control input-sm disableLoanofficerfields <?php echo BaseHTML::fieldAccess(['fNm' => 'LMRLoanofficer', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                            name="LMRLoanofficer"
                                                                            id="LMRLoanofficer"
                                                                            tabindex="<?php echo $tabIndex++; ?>"
                                                                            onchange="updateLoanofficerNo(this.value, 'loanModForm');populateLoanofficerInfo('loanModForm', this.value, 'DD');" <?php echo BaseHTML::fieldAccess(['fNm' => 'LMRLoanofficer', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                                        <option value=""> - Select / New -
                                                                        </option>
                                                                        <?php
                                                                        for ($pd = 0; $pd < count($preferredLoanofficerInfoArray); $pd++) {
                                                                            $tempPrefArray = [];
                                                                            $selLMRLoanofficerNo = 0;
                                                                            $selBrokerFName = '';
                                                                            $selBrokerLName = '';
                                                                            $selLMRLoanofficerName = '';
                                                                            $sOpt = '';
                                                                            $tempPrefArray = $preferredLoanofficerInfoArray[$pd];
                                                                            $selLMRLoanofficerNo = cypher::myEncryption($tempPrefArray['brokerNumber']);
                                                                            $selBrokerFName = $tempPrefArray['brokerFName'];
                                                                            $selBrokerLName = $tempPrefArray['brokerLName'];
                                                                            $selLMRLoanofficerName = $selBrokerFName . ' ' . $selBrokerLName;
                                                                            if ($selLMRLoanofficerNo == cypher::myEncryption($agentNumber)) $sOpt = 'selected';
                                                                            ?>
                                                                            <option value="<?php echo $selLMRLoanofficerNo; ?>" <?php echo $sOpt ?> ><?php echo $selLMRLoanofficerName ?></option>
                                                                        <?php } ?>
                                                                    </select>
                                                                <?php } else {
                                                                    echo '<b>' . $loanofficerFName . ' ' . $loanofficerLName . '</b>';
                                                                } ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php }
                                                ?>
                                                <div class=" col-md-6 <?php echo loanForm::showField('RELoanofficerEmail'); ?>">
                                                    <div class="form-group row">
                                                        <?php echo loanForm::label('RELoanofficerEmail', 'col-md-5 '); ?>
                                                        <div class="col-md-7">
                                                            <?php
                                                            if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                                                ?>
                                                                <input type="email"
                                                                       class="form-control input-sm disableLoanofficerfields <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerEmail', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                       name="RELoanofficerEmail"
                                                                       id="RELoanofficerEmail"
                                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                                       value="<?php echo $loanofficerEmail; ?>"
                                                                       autocomplete="off"
                                                                       onblur="checkLoanofficerEmailExist('loanModForm');" <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerEmail', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                                            <?php } else {
                                                                echo '<b>' . $loanofficerEmail . '</b>';
                                                            } ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="<?php echo $LmbInfo; ?>" class="LmbInfo row ">

                                                <div class="col-md-6 RELoanofficerFirstName_disp <?php echo loanForm::showField('RELoanofficerFirstName'); ?>">
                                                    <div class="form-group row">
                                                        <?php echo loanForm::label('RELoanofficerFirstName', 'col-md-5 '); ?>
                                                        <div class="col-md-7">
                                                            <?php
                                                            if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                                                ?>
                                                                <input type="text"
                                                                       class="form-control disableLoanofficerfields input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerFirstName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                       name="RELoanofficerFirstName"
                                                                       id="RELoanofficerFirstName"
                                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                                       value="<?php echo $loanofficerFName; ?>"
                                                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerFirstName', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                                <?php
                                                            } else {
                                                                echo '<b>' . $loanofficerFName . '</b>';
                                                            }
                                                            ?>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class=" col-md-6 RELoanofficerLastName_disp <?php echo loanForm::showField('RELoanofficerLastName'); ?>">
                                                    <div class="form-group row">
                                                        <?php echo loanForm::label('RELoanofficerLastName', 'col-md-5 '); ?>
                                                        <div class="col-md-7">
                                                            <?php
                                                            if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                                                ?>
                                                                <input type="text"
                                                                       class="form-control disableLoanofficerfields input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerLastName', 'sArr' => $secArr, 'opt' => 'M']); ?>>"
                                                                       name="RELoanofficerLastName"
                                                                       id="RELoanofficerLastName"
                                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                                       value="<?php echo $loanofficerLName; ?>"
                                                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerLastName', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                                <?php
                                                            } else {
                                                                echo '<b>' . $brokerLName . '</b>';
                                                            }
                                                            ?>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class=" col-md-6 RELoanofficerCompany_disp <?php echo loanForm::showField('RELoanofficerCompany'); ?>">
                                                    <div class="form-group row">
                                                        <?php echo loanForm::label('RELoanofficerCompany', 'col-md-5 '); ?>
                                                        <div class="col-md-7">
                                                            <?php
                                                            if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                                                ?>
                                                                <input type="text"
                                                                       class="form-control disableLoanofficerfields input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerCompany', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                       name="RELoanofficerCompany"
                                                                       id="RELoanofficerCompany"
                                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                                       class="form-control input-sm"
                                                                       value="<?php echo $loanofficerCompany; ?>"
                                                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerCompany', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                            <?php } else {
                                                                echo '<b>' . $loanofficerCompany . '</b>';
                                                            }
                                                            ?>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-md-6 LoanofficerPhone_disp <?php echo loanForm::showField('LoanofficerPhone'); ?>">
                                                    <div class="form-group row">
                                                        <?php echo loanForm::label('LoanofficerPhone', 'col-md-5 '); ?>
                                                        <div class="col-md-7 loanofficerSection">
                                                            <?php
                                                            if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                                                ?>
                                                                <input type="text"
                                                                       class="form-control disableLoanofficerfields input-sm mask_phone <?php echo BaseHTML::fieldAccess(['fNm' => 'LoanofficerPhone', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                       name="LoanofficerPhone"
                                                                       id="LoanofficerPhone"
                                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                                       value="<?php echo $LoanofficerPhone; ?>"
                                                                       autocomplete="off"
                                                                       placeholder="(___) ___ - ____ Ext ____"
                                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'LoanofficerPhone', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                                <?php
                                                            } else {
                                                                echo '<b>' . Strings::formatPhoneNumber($LoanofficerPhone) . '</b>';
                                                            }
                                                            ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- end of Loan Officer -->
                                    <?php } ?>
                                </div>
                            </div>
                            <?php
                        }
                    }  // Agent Section Only shows Branch Web Form Only.


                    if ($publicUser == 1) {
                        if (count($secArr = BaseHTML::sectionAccess2(['sId' => 'Admin', 'opt' => $fileTab])) > 0) {
                            loanForm::pushSectionID('Admin');

                            ?>
                            <div class="card border-0 whatkindofloanCard ">
                                <div class="card-body whatkindofloanCard_body">
                                    <?php
                                    if ($LMRId > 0 && $webFormFOpt == 'branch' && strpos($LMRClientTypeDisplay, 'none') == true) { ?>
                                        <div class="row form-group">
                                            <?php echo loanForm::label('LMRClientType', 'col-md-5 '); ?>
                                            <div class="col-md-1"></div>
                                            <div class="col-md-6 font-weight-bold">
                                                <?php
                                                $servicesRequestedWIthkey = Arrays::buildKeyByValue($servicesRequested, 'LMRClientType');
                                                echo($servicesRequestedWIthkey[$LMRClientTypeInfo[0]['ClientType']][0]['serviceType']);
                                                ?>
                                            </div>
                                        </div>
                                    <?php } ?>
                                    <div class="lmrClientTypeDisp row form-group"
                                         style="<?php echo $LMRClientTypeDisplay; ?>">
                                        <div class="col-md-6">
                                            <div class="form-group row ">
                                                <?php echo loanForm::label('LMRClientType', 'col-md-4 '); ?>
                                                <div class="col-md-1 align-self-center">
                                                    <i id="loanprogramtooltip"
                                                       data-html="true"
                                                       class="fa fa-info-circle text-primary tooltipClass <?php if ($LMRId == 0) {
                                                           echo 'hide';
                                                       } ?>"
                                                       title=""></i>
                                                </div>
                                                <div class="col-md-7">
                                                    <select class="form-control mandatory input-sm"
                                                            data-placeholder=""
                                                            name="LMRClientType[]"
                                                            id="LMRClientType"
                                                            tabindex="<?php echo $tabIndex++; ?>"
                                                            onchange="formControl.controlFormFields('fileModule', '',this.id,'loanProgram');fixAdditionalLoanProgChosen(this.value);
                                                                    showAndHideLandFieldsNew(this.value);
                                                                    allowToEditDisabledFields(this.value, '');
                                                                    getPCMinMaxLoanGuidelines('loanModForm', '<?php echo $PCID ?>');
                                                            <?php
                                                            if (in_array($LMRId, $PCBasicLoanTabLMRIDsExists)) {
                                                            } else { ?>
                                                                    populatePCBasicLoanInfo('loanModForm', this.value, '<?php echo $PCID ?>', '<?php echo $ft ?>');
                                                            <?php } ?>"
                                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'LMRClientType', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                                        <option value="">- Select -</option>
                                                        <?php
                                                        $serviceCnt = 0;
                                                        if (count($servicesRequested) > 0) $serviceCnt = count($servicesRequested);
                                                        for ($j = 0; $j < $serviceCnt; $j++) {
                                                            $LMRClientTypeCode = '';
                                                            $sOpt = '';
                                                            $LMRClientType = '';
                                                            $chk = '';
                                                            $LMRClientTypeCode = trim($servicesRequested[$j]['LMRClientType']);
                                                            $LMRClientType = trim($servicesRequested[$j]['serviceType']);
                                                            $chk = Strings::isKeyChecked($LMRClientTypeInfo, 'ClientType', $LMRClientTypeCode);
                                                            if (trim($chk) == 'checked') $chk = 'selected ';
                                                            $displayOption = '';
                                                            if ($LMRClientTypeCode == 'TBD' && $LMRClientTypeInfo[0]['ClientType'] != 'TBD') {
                                                                $displayOption = "style = 'display:none;' ";
                                                            }
                                                            if ($servicesRequested[$j]['internalLoanProgram'] == 0) { ?>
                                                                <option <?php echo $chk; ?> <?php echo $displayOption; ?>
                                                                        value="<?php echo $LMRClientTypeCode; ?>"><?php echo $LMRClientType; ?></option>
                                                            <?php }
                                                        }
                                                        if (in_array('TBD', $fileLP) && $LMRId > 0) { ?>
                                                            <option selected
                                                                    value="<?php echo 'TBD'; ?>"><?php echo 'TBD'; ?></option>
                                                        <?php } ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="loanInfoLPSection row form-group <?php echo loanForm::showField('LMRClientType'); ?> "
                                         style="<?php echo $loanInfoLPSectionDisp ?>">

                                        <div class=" col-md-6 sbaLoanProduct_disp <?php echo loanForm::showField('sbaLoanProduct'); ?>">
                                            <div class="form-group row ">
                                                <?php echo loanForm::label('sbaLoanProduct', 'col-md-5 '); ?>
                                                <div class="col-md-7">
                                                    <select data-placeholder="Select SBA Loan Product"
                                                            name="sbaLoanProduct"
                                                            id="sbaLoanProduct"
                                                            tabindex="<?php echo $tabIndex++; ?>"
                                                            class="chzn-select form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'sbaLoanProduct', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                            data-placeholder="Select SBA Loan Product">
                                                        <option></option>
                                                        <?php
                                                        if (count($HMLOPCBasicSBALoanProductInfoArray ?? []) > 0) {
                                                            foreach ($HMLOPCBasicSBALoanProductInfoArray as $eachSBALoanProductID) { ?>
                                                                <option value="<?php echo $eachSBALoanProductID; ?>" <?php if (Strings::showField('sbaLoanProduct', 'ResponseInfo') == $eachSBALoanProductID) {
                                                                    echo 'selected';
                                                                } ?>><?php echo $globalSBALoanProductsCat[$eachSBALoanProductID]; ?></option>
                                                            <?php }
                                                        } else {
                                                            foreach ($globalSBALoanProductsCat as $eachSBALoanProductID => $eachSBALoanProductVal) { ?>
                                                                <option value="<?php echo $eachSBALoanProductID; ?>" <?php if (Strings::showField('sbaLoanProduct', 'ResponseInfo') == $eachSBALoanProductID) {
                                                                    echo 'selected';
                                                                } ?>><?php echo $eachSBALoanProductVal; ?></option>
                                                            <?php }
                                                        } ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class=" col-md-6 additionalLoanProgram_disp <?php echo loanForm::showField('additionalLoanProgram'); ?>">
                                            <div class="row form-group">
                                                <label class="col-md-5 font-weight-bold"
                                                       for="LMRadditionalLoanProgram"><?php echo BaseHTML::fieldAccess(['fNm' => 'additionalLoanProgram', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                                    :</label>
                                                <div class="col-md-7">
                                                    <select class=" chzn-select <?php echo BaseHTML::fieldAccess(['fNm' => 'additionalLoanProgram', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                            data-placeholder="Select Additional Loan Programs"
                                                            id="LMRadditionalLoanProgram"
                                                            onchange="formControl.controlFormFields('fileModule', '','LMRClientType','')"
                                                            name="LMRadditionalLoanProgram[]" multiple="">
                                                        <?php
                                                        $serviceCnt = 0;
                                                        if (count($servicesRequested) > 0) $serviceCnt = count($servicesRequested);
                                                        for ($j = 0; $j < $serviceCnt; $j++) {
                                                            $LMRClientTypeCode = '';
                                                            $sOpt = '';
                                                            $LMRClientType = '';
                                                            $LMRClientTypeCode = trim($servicesRequested[$j]['LMRClientType']);
                                                            $LMRClientType = trim($servicesRequested[$j]['serviceType']);
                                                            $chk = '';
                                                            if (in_array($LMRClientTypeCode, $myFileInfo['LMRadditionalLoanprograms'] ?? [])) {
                                                                $chk = 'selected ';
                                                            }
                                                            $displayOption = '';
                                                            if ($LMRClientTypeCode == 'TBD') {
                                                                $displayOption = "style = 'display:none;' ";
                                                            }
                                                            if ($servicesRequested[$j]['internalLoanProgram'] == 0) { ?>
                                                                <option <?php echo $chk; ?> <?php echo $displayOption; ?>
                                                                        value="<?php echo $LMRClientTypeCode; ?>"><?php echo $LMRClientType; ?></option>
                                                                <?php
                                                            }
                                                        } ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class=" col-md-6 propDetailsProcess_disp <?php echo loanForm::showField('propDetailsProcess'); ?> ">
                                            <div class="row form-group">
                                                <?php echo loanForm::label('propDetailsProcess', 'col-md-5 '); ?>
                                                <div class="col-md-7">
                                                    <?php if ($allowToEdit) { ?>
                                                        <select class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propDetailsProcess', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                name="propDetailsProcess"
                                                                id="propDetailsProcess"
                                                                tabindex="<?php echo $tabIndex++; ?>"
                                                                onchange="populatePropertyDetails(this.value);" <?php echo BaseHTML::fieldAccess(['fNm' => 'propDetailsProcess', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                            <option value=''> - Select -</option>
                                                            <?php
                                                            for ($i = 0; $i < count($glPropDetailsProcess); $i++) {
                                                                $sOpt = '';
                                                                $propDetals = '';
                                                                $propDetals = trim($glPropDetailsProcess[$i]);
                                                                $sOpt = Arrays::isSelected($propDetals, $propDetailsProcess);
                                                                echo "<option value=\"" . $propDetals . "\" " . $sOpt . '>' . $propDetals . '</option>';
                                                            }
                                                            ?>
                                                        </select>
                                                    <?php } else {
                                                        echo '<b>' . $propDetailsProcess . '</b>';
                                                    } ?>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6 leadSource_disp <?php echo loanForm::showField('leadSource'); ?>">
                                            <div class="row form-group">
                                                <?php echo loanForm::label('leadSource', 'col-md-5 '); ?>
                                                <div class="col-md-7">
                                                    <select class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'leadSource', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                            name="branchLeadSource" id="leadSource"
                                                            TABINDEX="<?php echo $tabIndex++ ?>"
                                                            onchange="checkRef(this.value);" <?php echo BaseHTML::fieldAccess(['fNm' => 'leadSource', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                        <option value="">- Select -</option>
                                                        <?php
                                                        for ($bh = 0; $bh < count($branchHAInfoArray); $bh++) {
                                                            $branchHearAbout = '';
                                                            $sOpt = '';
                                                            $branchHearAbout = $branchHAInfoArray[$bh]['branchHearAbout'];
                                                            $sOpt = Arrays::isSelected($branchHearAbout, $leadSource);
                                                            ?>
                                                            <option <?php echo $sOpt; ?>
                                                                    value="<?php echo $branchHearAbout; ?>"><?php echo $branchHearAbout; ?></option>
                                                            <?php
                                                        }
                                                        ?>
                                                        <option <?php echo Arrays::isSelected('Other', $leadSource) ?>
                                                                value="Other">Other
                                                        </option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class=" col-md-6 <?php echo BaseHTML::parentFieldAccess(['fNm' => 'leadSource', 'sArr' => $secArr, 'pv' => $leadSource, 'av' => 'Other']); ?>"
                                             id="refDiv">
                <textarea class="form-control input-sm " name="hereAbout" id="hereAbout"
                          tabindex="<?php echo $tabIndex++; ?>"
                          placeholder="Let us know how you heard about us."><?php echo $hereAbout; ?></textarea>
                                        </div>


                                        <div class="col-md-6 referringParty_disp <?php echo loanForm::showField('referringParty'); ?>">
                                            <div class="row form-group">
                                                <label class="col-md-5 font-weight-bold"
                                                       for="referringParty"><?php echo BaseHTML::fieldAccess(['fNm' => 'referringParty', 'sArr' => $secArr, 'opt' => 'L']); ?></label>
                                                <div class="col-md-7">
                                                    <?php if ($allowToEdit) { ?>
                                                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'referringParty', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                               type="text" name="referringParty"
                                                               id="referringParty"
                                                               value="<?php echo $referringParty; ?>"
                                                               autocomplete="off"
                                                               TABINDEX="<?php echo $tabIndex++ ?>"
                                                               maxlength="45" <?php echo BaseHTML::fieldAccess(['fNm' => 'referringParty', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                                                    <?php } else { ?>
                                                        <h5><?php echo $referringParty; ?></h5>
                                                    <?php } ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php
                        }
                    } // Public User..
                    ?>
                    <?php if ($allowCaptcha == 1 && !RECAPTCHA_DISABLE) { ?>
                        <!-- CAPTCHA-reCAPTCHA -->
                        <div class="row justify-content-center recaptchaCode">
                            <div class="col-md-4 ">
                                <script src="https://www.google.com/recaptcha/api.js" async defer></script>
                                <div class="g-recaptcha"
                                     data-sitekey="<?php echo PCInfo::getCaptchaKey($PCID, 'siteKey'); ?>"></div>
                                <p id="human_valid" class="text-danger hidden">Please verify you are human!</p>
                            </div>
                        </div>
                        <!--//CAPTCHA-reCAPTCHA//-->
                    <?php } ?>
                </div>
                <!--begin: Wizard Step 1-->


                <!--begin: Wizard Step 2-->
                <div id="ApplicantInfoStepBody"
                     class="pb-5 step-content-class <?php echo $step1ApplicantInfo; ?>"
                     data-wizard-type="step-content">
                    <?php require 'borrowerInfo.php'; ?>
                    <?php require 'coborInformation.php'; ?>
                    <?php require 'businessEntitySection.php'; ?>
                    <?php require 'additionalGuarantorsSection.php'; ?>
                    <?php
                    if ($hideThisField) {
                        require 'governmentInfo.php';
                    }
                    ?>
                </div>
                <!--end: Wizard Step 2-->

                <!--begin: Wizard Step 3-->
                <div id="BackgroundExpStepBody"
                     class="pb-5 step-content-class <?php echo $step2BackgroundExp; ?>"
                     data-wizard-type="step-content">
                    <!--  Borrower Background Section Start -->
                    <?php require 'borrowerBackground.php'; ?>

                    <!--  Co-Borrower Background Section Start -->
                    <?php require 'coborrowerBackground.php'; ?>

                    <!-- Borrower Experience Section -->
                    <?php require 'borrowerExperience.php'; ?>

                    <!--  Co-Borrower Experience Section End -->
                    <?php require 'coBorrowerExperience.php'; ?>

                    <!-- Property Management Section Start -->
                    <?php require 'propertyManagementSection.php'; ?>

                    <?php require 'sbaBackgroundAdditionalQuestions.php'; ?>

                </div>
                <!--end: Wizard Step 3-->

                <!--begin: Wizard Step 4-->
                <div id="BorrowerFinancialStepBody"
                     class="pb-5 step-content-class <?php echo $step3BorrowerFina; ?>"
                     data-wizard-type="step-content">
                    <?php
                    require 'assetsForm.php';
                    require 'giftsOrGrantsSection.php';
                    require 'scheduleRealEstate.php';

                    ?>
                    <?php require 'incExpWFForm.php'; ?>
                    <?php require 'FinancialAccountsAndSecurities.php'; ?>
                    <?php require 'creditorsLiabilitiesSection.php'; ?>
                    <?php require_once 'LMRequest/sections/partnerShipsForm.php'; ?>
                    <?php require 'otherNewMortgageLoansSection.php'; ?>
                </div>
                <!--end: Wizard Step 4-->

                <!--begin: Wizard Step 5-->
                <div id="LoanPropertyStepBody"
                     class="pb-5 step-content-class <?php echo $step4LoanPropertColl; ?>"
                     data-wizard-type="step-content">

                    <?php
                    if (!isset($lockedSections)) {
                        $lockedSections = [];

                        if ($allowToEdit) {
                            $lockInfo = (PageVariables::$userRole == 'Super' ? [] : (isFileLockedLastVal::getReport(['LMRId' => $LMRId]) ?? [])); //            // Superuser or file owner can access unlocked sections

                            if (!empty($lockInfo)) {

                                $lockedFile = $lockInfo['locked'] ? 1 : 0;
                                $lockedSections = $lockedFile ? (str_contains($lockInfo['lockedSection'] ?? '', ',')
                                    ? explode(',', $lockInfo['lockedSection'])
                                    : [$lockInfo['lockedSection'] ?? '']) : [];


                                $lockStatus = $lockInfo['locked'] ? 'Locked' : 'Unlocked';
                                $iconClass = $lockInfo['locked'] ? 'fa-lock text-danger' : 'fa-unlock';

                                $lockedSectionTxt =
                                    '<span class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2"
  title="' . $lockStatus . ' By: ' . $lockInfo['lockedBy'] . ' (' . $lockInfo['lockedUserRole'] . ')">
    <i class="fa ' . $iconClass . '  icon-md"></i>
</span>';

                                if (($lockInfo['lockedUID'] ?? null) == PageVariables::$userNumber) {
                                    $lockedSections = [];
                                }
                            }
                        }
                    }
                    require 'LMRequest/loanInfo/loanSettingSection.php';
                    require 'LMRequest/loanInfo/importantDatesSection.php';
                    require 'LMRequest/loanInfo/rehabSection.php';
                    require 'LMRequest/loanInfo/loanTermsSection.php';
                    require 'LMRequest/loanInfo/additionalLoanSettingsSection.php';
                    require_once 'LMRequest/sections/refinanceMortgageForm.php';
                    //require 'HMLOLoanTermsForm.php';
                    ?>
                    <!-- HMLOLoanInfoFormWizard.php -->
                    <input type="hidden" id="subjectPropertyJson"
                           value='<?php echo json_encode(GpropertyTypeNumbArray::$GpropertyTypeNumbArray); ?>'>
                    <input type="hidden" id="subjectStateJson" value='<?php echo json_encode($stateArray); ?>'>
                    <?php require_once 'propDetailsSection.php'; ?>
                    <?php require 'subjectPropertyCashFlow.php'; ?>
                    <?php require 'estimatedProjectCost.php'; ?>
                    <?php require 'collateralSection.php'; ?>
                    <?php require 'equipmentFinancingForm.php'; ?>
                    <?php require 'picturesOfProperty.php'; ?>
                    <div id="propertyPictures"></div>

                    <!-- lien1 mortgage Section Start -->
                    <?php require 'lien1MortgageScenario.php'; ?>

                    <!-- lien2 mortgage Section Start -->
                    <?php require 'lien2MortgageScenario.php'; ?>


                </div>
                <!--end: Wizard Step 5-->

                <!--begin: Wizard Step 6-->
                <div id="AdditionalInfoStepBody" class="pb-5 step-content-class <?php echo $step5Additional; ?>"
                     data-wizard-type="step-content">

                    <!-- Foreclosure start div -->
                    <?php require 'foreclosure.php'; ?>

                    <?php require 'loanWorkoutQA.php'; ?>

                    <?php require 'LMRequest/loanInfo/additionalQuestions.php'; ?>
                    <?php
                    if ($hideThisField) {
                        require 'titleInfoSection.php';
                    }
                    ?>

                    <?php
                    if ($hideThisField) {
                        require 'attorneyInfo.php';
                    }
                    ?>
                    <?php
                    if ($hideThisField) {
                        require 'escrowInfo.php';
                        require 'generalContractor.php';
                        require 'financialAdvisor.php';
                        require 'accountant.php';
                    }
                    ?>

                    <?php
                    if ($hideThisField) {
                        require 'insuranceInfo.php';
                    }
                    ?>
                    <?php
                    if ($activeTab == 'PI') {
                        if ($hideThisField) {
                            require 'insuranceDetails.php';
                        }
                    }
                    ?>

                    <?php require 'creditMemoSection.php'; ?>

                    <!-- "Billing/Payment Information Start-->
                    <?php
                    if ($hideThisField) {
                        require_once 'LMRequest/sections/paymentInfoForm.php';
                    }
                    ?>
                    <!-- "Billing/Payment Information End-->


                    <?php
                    mortgageNotes::$fileTab = $fileTab;
                    mortgageNotes::$HMLOLoanInfoSectionsDisp = $HMLOLoanInfoSectionsDisp;
                    mortgageNotes::$allowToEdit = $allowToEdit;

                    require_once 'LMRequest/sections/mortgageNotes.php';

                    WebForm::$isCoborrower = (bool)$isCoBorrower;
                    WebForm::getSignatures($LMRId, $fileTab);

                    if ($publicUser == 1) {

                        WebForm::$publicUser = $publicUser;
                        WebForm::$allowToEdit = $allowToEdit;
                        WebForm::$activeTab = $activeTab;
                        WebForm::$PCID = $PCID;
                        WebForm::$URLLink = $URLLink;
                        WebForm::$secArr = $secArr;
                        WebForm::$opt = $wfOpt;
                        WebForm::$LMRId = $LMRId;
                        WebForm::$hideThisField = $hideThisField;
                        WebForm::$UType = $UType;
                        WebForm::$BorrowerLink = $BorrowerLink;
                        WebForm::$coBorrowerLink = $coBorrowerLink;
                        WebForm::$isMultiStepWebForm = true;
                        WebForm::$termsConditionsText = (WebForm::$activeTab == 'LI' ? $HMLOTAC : $HMLOTACQA);

                        require_once __DIR__ . '/../webForm/sections/termsAndConditions.php';


                        require_once __DIR__ . '/../webForm/sections/showSignature.php';
                        require_once __DIR__ . '/../webForm/sections/signaturePad.php';

                        if ($aud == 1) { //value assigned in HMLOWebForm.php
                            $ip['fileID'] = $LMRId;
                            $docCheckListName = getChecklistNameID::getReport($ip);
                            if (isset($myFileInfo['docArray'])) $tempDocInfoArray = $myFileInfo['docArray'];
                            /* Upload docs section */
                            for ($i = 0; $i < count($tempDocInfoArray); $i++) {
                                $userId = 0;
                                $userType = '';
                                $userId = $tempDocInfoArray[$i]['uploadedBy'];
                                $userType = $tempDocInfoArray[$i]['uploadingUserType'];
                                if ($userType == 'Processor' || $userType == 'Employee') {
                                    $empIdArray[] = $userId;
                                }
                                if ($userType == 'LMR Executive' || $userType == 'Branch') {
                                    $branchIdArray[] = $userId;
                                }
                                if ($userType == 'Broker' || $userType == 'Agent') {
                                    $agentIdArray[] = $userId;
                                }
                                if ($userType == 'Client' && $userId > 0) {
                                    $clientIdArray[] = $userId;
                                }
                                if ($tempDocInfoArray[$i]['docCategory'] == 'Checklist Items') {
                                    $tempDocInfoArray[$i]['docChecklistName'] = $docCheckListName[$tempDocInfoArray[$i]['docID']];
                                } elseif (!in_array($tempDocInfoArray[$i]['docCategory'], ['Appraisal1', 'Appraisal2', 'BPO1', 'BPO2', 'BPO3', 'Title Report', 'Property Insurance Coverage1', 'Property Insurance Coverage2', 'Property Insurance Coverage3', 'Property Insurance Coverage4', 'Property Insurance Coverage5', 'Property Insurance Coverage', 'AVM1', 'AVM2', 'AVM3'])) {
                                    $tempDocInfoArray[$i]['docChecklistName'] = $tempDocInfoArray[$i]['docCategory']; //"Other new";
                                    $tempDocInfoArray[$i]['docCategory'] = 'Other';
                                }
                            }

                            require 'getAllFileDocsInfoFromRemote.php';
                            /* Upload docs end */
                            ?>
                            <!-- Upload Documents List section start-->
                            <div class="block-content row">
                                <div class="text-on-pannel text-primary">Uploaded Files, Documents &amp;
                                    Binder
                                </div>
                                <?php require 'fileUploadDocList.php'; ?>
                            </div>
                            <!-- Upload Documents List section end -->
                            <?php
                        }

//                        if ($allowToEdit && false) {
//                            if ($allowCaptcha == 1) { ?>
                        <!--                              CAPTCHA-reCAPTCHA -->
                        <!--                                <div class="card card-custom">-->
                        <!--                                    <div class="card-body">-->
                        <!--                                        <div class="row">-->
                        <!--                                            <div class="col-md-4 "></div>-->
                        <!--                                            <div class="col-md-4 ">-->
                        <!--                                                <script src="https://www.google.com/recaptcha/api.js" async-->
                        <!--                                                        defer></script>-->
                        <!--                                                <div class="g-recaptcha"-->
                        <!--                                                     data-sitekey="6Le3D98UAAAAAEjI_CswyiOxvTlmc0q0i0y8V0YM"></div>-->
                        <!--                                                <p id="human_valid" class="text-danger hidden">Please verify you-->
                        <!--                                                    are-->
                        <!--                                                    human!</p>-->
                        <!--                                                <br/>-->
                        <!--                                            </div>-->
                        <!--                                            <div class="col-md-4 "></div>-->
                        <!--                                        </div>-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                              //CAPTCHA-reCAPTCHA//-->
                        <!--                            --><?php //}
//
//                        }
                    }
                    ?>


                </div>
                <!--end: Wizard Step 6-->

                <!--begin: Wizard Step 7-->

                <?php
                $maxWizard = 7;
                if ($reqCnt > 0) {
                    $maxWizard--; ?>
                    <div id="RequiredDocsStepBody" class="pb-5 step-content-class" data-wizard-type="step-content">
                        <!--begin::Section-->
                        <?php
                        if ($LMRId > 0) {
                            $ip = ['lid' => $LMRId, 'UType' => $UType, 'email' => $email, 'UName' => $UName];
                            $fileUploadInfo = getInfoForDocsUpload::getReport($ip);

                            if (count($myFileInfo) > 0) {

                                if (array_key_exists('LMRInfo', $myFileInfo)) $LMRInfoArray = $myFileInfo['LMRInfo'];
                                if (array_key_exists('FileProInfo', $myFileInfo)) $typeOfHMLOLoanRequesting = $myFileInfo['fileHMLOPropertyInfo']['typeOfHMLOLoanRequesting'];

                                if (array_key_exists('FileProInfo', $myFileInfo)) $isHouseProperty = $myFileInfo['FileProInfo'][0]['isHouseProperty'];
                                if (array_key_exists('propertyType', $LMRInfoArray)) $propertyType = $LMRInfoArray['propertyType'];
                                if (array_key_exists('propertyState', $LMRInfoArray)) $propertyState = $LMRInfoArray['propertyState'];
                                if (array_key_exists('fileHMLOEntityInfo', $myFileInfo)) $entityType = $myFileInfo['fileHMLOEntityInfo']['entityType'];
                                if (array_key_exists('fileHMLOEntityInfo', $myFileInfo)) $entityState = $myFileInfo['fileHMLOEntityInfo']['entityState'];
                                if (array_key_exists('fileHMLOInfo', $myFileInfo)) $borrowerCreditScore = $myFileInfo['fileHMLOInfo']['borCreditScoreRange'];

                                if (array_key_exists('FileProInfo', $myFileInfo)) $propertyNeedRehab = $myFileInfo['fileHMLOPropertyInfo']['propertyNeedRehab'];
                                if (array_key_exists('FileProInfo', $myFileInfo)) $isBlanketLoan = $myFileInfo['fileHMLOPropertyInfo']['isBlanketLoan'];
                                if (array_key_exists('fileHMLOBackGroundInfo', $myFileInfo)) $isBorUSCitizen = $myFileInfo['fileHMLOBackGroundInfo']['isBorUSCitizen'];
                                $isCoBorrower = trim($LMRInfoArray['isCoBorrower']);
                                $PCIDNew = $LMRInfoArray['FPCID'];
                                $checklistServiceTypeArray = getFileServiceTypes::getReport(['fileID' => $LMRId, 'PCID' => $PCIDNew]);
                                $ServiceType = ($checklistServiceTypeArray[$LMRId][0]['ClientType']);

                                if (array_key_exists('fileHMLOEntityInfo', $myFileInfo)) $borrowerType = $myFileInfo['fileHMLOEntityInfo']['borrowerType'];

                            }


                            $glLMRClientTypeArray = getServiceTypes::getReport(['activeStatus' => '1']);

                            if (count($fileUploadInfo) > 0) {
                                if (array_key_exists('uploadDocsDetails', $fileUploadInfo)) $uploadDocsInfoArray = $fileUploadInfo['uploadDocsDetails'];
                                if (array_key_exists('entityDetails', $fileUploadInfo)) $entityDetailsArray = $fileUploadInfo['entityDetails'];
                                if (array_key_exists('fileHMLOChecklistUploadDocsNew', $fileUploadInfo)) $fileHMLOChecklistUploadDocs = $fileUploadInfo['fileHMLOChecklistUploadDocsNew'];
                                if (array_key_exists('clientTypeInfo', $fileUploadInfo)) $clientTypeInfoArray = $fileUploadInfo['clientTypeInfo'];
                                if (array_key_exists('fileModuleInfo', $fileUploadInfo)) $fileModuleInfo = $fileUploadInfo['fileModuleInfo'];
                                if (array_key_exists('PCInfo', $fileUploadInfo)) $PCInfoArray = $fileUploadInfo['PCInfo'][0];
                                if (array_key_exists('fileRequiredDocItems', $fileUploadInfo)) $bothPCFileChecklistArray = $fileUploadInfo['fileRequiredDocItems'];
                                if (array_key_exists('checklistNotesInfo', $fileUploadInfo)) $checklistNotesInfo = $fileUploadInfo['checklistNotesInfo'];
                                if (array_key_exists('checklistActivatedInfo', $fileUploadInfo)) $checklistActivatedInfo = $fileUploadInfo['checklistActivatedInfo'];

                                if (array_key_exists('empCheckInfo', $fileUploadInfo)) $empCheckInfo = $fileUploadInfo['empCheckInfo'];
                                if (array_key_exists('branchCheckInfo', $fileUploadInfo)) $branchCheckInfo = $fileUploadInfo['branchCheckInfo'];
                                if (array_key_exists('QAInfo', $fileUploadInfo)) $QAInfo = $fileUploadInfo['QAInfo'];
                                if (array_key_exists('LMRClientTypeInfo', $fileUploadInfo)) $LMRClientTypeInfo = $fileUploadInfo['LMRClientTypeInfo'];
                                if (array_key_exists('branchInfo', $fileUploadInfo)) $LMRBranchInfo = $fileUploadInfo['branchInfo'];
                                if (array_key_exists('LMRInternalLoanprograms', $fileUploadInfo)) $LMRInternalLoanprogramsArray = $fileUploadInfo['LMRInternalLoanprograms'];
                                if (array_key_exists('LMRadditionalLoanprograms', $fileUploadInfo)) $LMRadditionalLoanprogramsArray = $fileUploadInfo['LMRadditionalLoanprograms'];
                            }


                            $apComm = '';
                            for ($mci = 0; $mci < count($fileModuleInfo); $mci++) {
                                $tempMC = '';
                                $tempMC = $fileModuleInfo[$mci]['moduleCode'];
                                $fileModulesWithName[$tempMC] = $fileModuleInfo[$mci];
                                $_mTy .= $apComm . $tempMC;
                                $apComm = ',';
                                $fileModules[] = $fileModuleInfo[$mci]['moduleCode'];
                            }


                            /* Get Files modules with name. */
                            for ($chNo = 0; $chNo < count($checklistNotesInfo ?? []); $chNo++) {
                                $clTy = '';
                                $temArr = [];
                                $clCID = '';
                                $clTy = $checklistNotesInfo[$chNo]['CLType'];
                                $clCID = $checklistNotesInfo[$chNo]['CID'];
                                $fileChecklistNotesInfo[$clTy][$clCID] = $checklistNotesInfo[$chNo];
                            }

                            /* Get Activated Checklist Info. */
                            for ($chAc = 0; $chAc < count($checklistActivatedInfo ?? []); $chAc++) {
                                $clTy = '';
                                $temArr = [];
                                $clCID = '';
                                $clTy = $checklistActivatedInfo[$chAc]['CLType'];
                                $clCID = $checklistActivatedInfo[$chAc]['docId'];
                                $LMRChecklistInfo[$clTy][$clCID] = $checklistActivatedInfo[$chAc];
                            }

                            for ($emp = 0; $emp < count($empCheckInfo ?? []); $emp++) {
                                $empId = '';
                                $empId = $empCheckInfo[$emp]['AID'];
                                $empChkInfoArray[$empId] = $empCheckInfo[$emp];
                            }
                            for ($bch = 0; $bch < count($branchCheckInfo ?? []); $bch++) {
                                $exId = '';
                                $exId = $branchCheckInfo[$bch]['executiveId'];
                                $LMRBranchChkInfoArray[$exId] = $branchCheckInfo[$bch];
                            }

                            if (count($uploadDocsInfoArray ?? []) > 0) {
                                $uploadDocsInfoArray = $uploadDocsInfoArray[0];

                                $PCID = $uploadDocsInfoArray['FPCID'];
                                $borrowerFname = $uploadDocsInfoArray['borrowerName'];
                                $borrowerLname = $uploadDocsInfoArray['borrowerLName'];
                                $borrowerName = $borrowerFname . ' ' . $borrowerLname;
                                $coBorrowerFirName = $uploadDocsInfoArray['coBorrowerFName'];
                                $coBorrowerLName = $uploadDocsInfoArray['coBorrowerLName'];
                                $coBorrowerFName = $coBorrowerFirName . ' ' . $coBorrowerLName;
                                $borrowerEmail = $uploadDocsInfoArray['borrowerEmail'];
                                $agentFname = $uploadDocsInfoArray['firstName'];
                                $agentLname = $uploadDocsInfoArray['lastName'];
                                $agentName = $agentFname . ' ' . $agentLname;
                                $LMRExecutive = $uploadDocsInfoArray['LMRExecutive'];
                                $propertyAddress = $uploadDocsInfoArray['propertyAddress'];
                                $propertyCity = $uploadDocsInfoArray['propertyCity'];
                                $propertyState = $uploadDocsInfoArray['propertyState'];
                                $propertyZip = $uploadDocsInfoArray['propertyZip'];
                                $clientId = $uploadDocsInfoArray['clientId'];
                                $brokerNumber = $uploadDocsInfoArray['brokerNumber'];
                                $executiveId = $uploadDocsInfoArray['executiveId'];
                                $mortgageNotes = $uploadDocsInfoArray['mortgageNotes'];
                                if ($propertyAddress != '') {
                                    $propertyInfo .= $propertyAddress;
                                }
                                if ($propertyCity != '') {
                                    $propertyInfo .= ', ' . $propertyCity;
                                }
                                if ($propertyState != '') {
                                    $propertyInfo .= ', ' . $propertyState;
                                }
                                if ($propertyZip != '') {
                                    $propertyInfo .= ' ' . $propertyZip;
                                }

                            }

                            if (count($entityDetailsArray) > 0) {
                                $LMRResponseId = $entityDetailsArray[0]['LMRResponseId'];
                                $entityName = $entityDetailsArray[0]['entityName'];
                                $entityType = $entityDetailsArray[0]['entityType'];
                                $entityAddress = $entityDetailsArray[0]['entityAddress'];
                                $entityCity = $entityDetailsArray[0]['entityCity'];
                                $entityState = $entityDetailsArray[0]['entityState'];
                                $entityZip = $entityDetailsArray[0]['entityZip'];
                                $primaryStatus = $entityDetailsArray[0]['primaryStatus'];
                                $entityStateOfFormation = $entityDetailsArray[0]['entityStateOfFormation'];

                                if ($entityName != '') {
                                    $entityDetails = $entityName;
                                }
                            }

                            /* Get Loan Programs */
                            if (count($clientTypeInfoArray) > 0) {
                                $apCo = '';
                                for ($i = 0; $i < count($clientTypeInfoArray); $i++) {
                                    $clientType = '';
                                    $clientType = $clientTypeInfoArray[$i]['ClientType'];
                                    $_sty .= $apCo . $clientType;
                                    $apCo = ',';
                                }
                            }

                            $bothPCFileChecklistArray = requiredDocsForFile::getReportObjects($LMRId,
                                $PCIDNew,
                                implode(',', $fileModules),
                                $clientType,
                                $UType
                            );

                            if ($wfOpt == 'FA') {
                                $reqDoc = 'LV';
                            } else {
                                $reqDoc = 'SV';
                            }

                            $mcArray = [
                                '_pcID' => $PCID,
                                '_mTy'  => $_mTy,
                                '_sty'  => $_sty,
                                '_as'   => 1,
                                'wfTy'  => $reqDoc,
                            ];

                            /**
                             * Broker will follow the branch required doc setting.
                             * Card #219
                             */
                            if ($executiveId > 0 && ($fOpt == 'agent' || $fOpt == 'branch')) {
                                $mcArray['_exID'] = $executiveId;
                                $displayedAndMandatoryItems = getBranchAndAgentRequiredDocs::getReport($mcArray);
                                if (count($displayedAndMandatoryItems) > 0 && $UType != 'CoBorrower') {
                                    $formUrl = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($bRc) . '&aRc=' . cypher::myEncryption($aRc) . '&fOpt=' . cypher::myEncryption($fOpt) . '&op=' . cypher::myEncryption($wfOpt) . '&lid=' . cypher::myEncryption($LMRId) . '&ft=' . $ft;
                                } else {
                                    header('Location: thankHMLOWebForm.php?bRc=' . cypher::myEncryption($bRc) . '&aRc=' . cypher::myEncryption($aRc) . '&fOpt=' . cypher::myEncryption($fOpt) . '&lid=' . cypher::myEncryption($LMRId) . '&op=' . cypher::myEncryption($wfOpt) . '&ft=' . $ft);
                                    exit();
                                }
                            }
                            /* Branch Logo */
                            $branchLogo = '';
                            if (array_key_exists('logo', $LMRBranchInfo[0])) {
                                $branchLogo = $LMRBranchInfo[0]['logo'];
                            }
                            /* End Of Branch Logo */


                            if (trim($branchLogo) != '') {
                                $logoExit = false;
                                $logoExit = file_exists(CONST_BRANCH_LOGO_PATH . $branchLogo);
                                if ($logoExit) {
                                    [$userLogoWidth, $userLogoHeight] = getimagesize(CONST_BRANCH_LOGO_URL . $branchLogo);
                                    $userLogo = CONST_BRANCH_LOGO_URL . $branchLogo;
                                    $branchLogo = CONST_BRANCH_LOGO_URL . $branchLogo;
                                } else {
                                    $branchLogo = '';
                                }
                            } else {
                                $branchLogo = '';
                            }

                            if (count($PCInfoArray) > 0 && $branchLogo == '') {
                                $userLogo = $PCInfoArray['procCompLogo'];
                            }

                            if (trim($userLogo) != '' && trim($branchLogo) == '') {
                                $logoExit = false;
                                $logoExit = file_exists(CONST_PATH_PC_LOGO . $userLogo);
                                if ($logoExit) {
                                    [$userLogoWidth, $userLogoHeight] = getimagesize(CONST_PATH_PC_LOGO . $userLogo);
                                    $userLogo = CONST_PC_LOGO_URL . $userLogo;
                                } else {
                                    $userLogo = '';
                                }
                            }

                            if ($UType == 'Branch') {
                                $userGroup = 'Branch';
                                $userRole = 'Branch';
                                $userNumber = $executiveId;
                                $userName = $LMRExecutive;
                                $uploadedBy = $LMRExecutive;
                                $uploadingUserType = 'Branch';
                            } else if ($UType == 'Agent/Broker' || $UType == 'Loan Officer/Mortgage Broker') {
                                $userGroup = 'Agent';
                                $userRole = 'Agent';
                                $userNumber = $brokerNumber;
                                $userName = $agentName;
                                $uploadedBy = $agentName;
                                $uploadingUserType = 'Agent';
                            } else {
                                $userGroup = 'Client';
                                $userRole = 'Client';
                                $userNumber = $clientId;
                                $userName = $borrowerName;
                                $uploadedBy = $borrowerName;
                                $uploadingUserType = 'Client';
                            }
                            $documentListNameWise = [];

                            /* Additional logic */
                            $requiredAdditionalCond['typeOfHMLOLoanRequesting'] = $typeOfHMLOLoanRequesting;
                            $requiredAdditionalCond['isHouseProperty'] = $isHouseProperty;
                            $requiredAdditionalCond['propertyType'] = $propertyType;
                            $requiredAdditionalCond['propertyState'] = $propertyState; //exist
                            $requiredAdditionalCond['entityType'] = $entityType; //exist
                            $requiredAdditionalCond['entityState'] = $entityState; //exist
                            $requiredAdditionalCond['borrowerCreditScore'] = $borrowerCreditScore;


                            $requiredAdditionalCond['isCoBorrower'] = $isCoBorrower;
                            $requiredAdditionalCond['propertyNeedRehab'] = $propertyNeedRehab;
                            $requiredAdditionalCond['isBlanketLoan'] = $isBlanketLoan;
                            $requiredAdditionalCond['isBorUSCitizen'] = $isBorUSCitizen;
                            $requiredAdditionalCond['borrowerType'] = $borrowerType;


                            $requiredDocsArrayNewPCMID = Arrays::buildKeyByValue($bothPCFileChecklistArray, 'PCMID');

//$PMID_Keys = implode(",", array_filter(array_keys($requiredDocsArrayNewPCMID)));
                            $PMID_Keys1 = array_filter(array_keys($requiredDocsArrayNewPCMID));
//$PMID_Keys1Array = implode(",", $PMID_Keys1);
                            /* End Additional logic */


                            // pr($bothPCFileChecklistArray);
                            if (count($bothPCFileChecklistArray ?? []) > 0) {
                                $requiredDocsArray = Arrays::buildKeyByValue($bothPCFileChecklistArray, 'moduleType');
                                $reqArray = [];
                                $notReqArray = [];
                                $moduleAndServiceRequiredDocs = $tempArray = [];

                                foreach ($requiredDocsArray as $temMC => $temArr) {
                                    //  for ($rd = 0; $rd < count($temArr); $rd++) {
                                    foreach ($temArr as $eachDoc) {
                                        $st = '';
                                        $ub = '';
                                        $mt = '';
                                        $st = $eachDoc->serviceType;
                                        $ub = $eachDoc->updatedBy;
                                        $mt = $eachDoc->moduleType;
                                        if ($ub > 0 && $ub != '') {
                                            $notReqArray[$st][] = $temArr[$rd];
                                        } else {
                                            if (count($displayedAndMandatoryItems) > 0) {
                                                if (isset($displayedAndMandatoryItems[$eachDoc->PCMID])) {
                                                    if ($wfOpt == 'FA') $eachDoc->mandatory = $displayedAndMandatoryItems[$eachDoc->PCMID][0]['mandatoryInLV'];
                                                    if ($wfOpt == 'QA') $eachDoc->mandatory = $displayedAndMandatoryItems[$eachDoc->PCMID][0]['mandatoryInSV'];
                                                    $reqArray[$st][] = $eachDoc;
                                                }
                                            } else {
                                                $reqArray[$st][] = $temArr[$rd];
                                            }
                                        }
                                        $documentListNameWise[] = $eachDoc->docName;
                                    }
                                }

                                for ($msr = 0; $msr < count($clientTypeInfoArray); $msr++) {
                                    $tempArr1 = [];
                                    $tempArr2 = [];
                                    $st = '';
                                    $st = $clientTypeInfoArray[$msr]['ClientType'];
                                    if (array_key_exists($st, $reqArray)) {
                                        $tempArr1 = Arrays::sortDbResult($reqArray[$st], 'requiredBy', SORT_ASC);                                     // | Order "Required BY" Asc
                                    }
                                    if (count($tempArr1) > 0) $moduleAndServiceRequiredDocs[$st] = $tempArr1;
                                }

                                $moduleAndServiceRequiredDocs = [];
                                foreach ($reqArray as $reServiceKey => $reVal) {
                                    foreach ($reVal as $reEach) {
                                        $moduleAndServiceRequiredDocs[$clientType][] = $reEach;
                                    }
                                }
                                if (count($moduleAndServiceRequiredDocs) > 0) {
                                    $moduleAndServiceRequiredDocs = Arrays::sortDbResult($moduleAndServiceRequiredDocs, 'requiredBy', SORT_ASC);                                     // | Order "Required BY" Asc
                                }
                            }
                            //prMulti(array("moduleAndServiceRequiredDocs" => $moduleAndServiceRequiredDocs, "LMRinternalLoanProgramRequiredDocsNameArray" => $LMRinternalLoanProgramRequiredDocsNameArray));

                            /* Internal Loan Program Support Code */
                            $LMRinternalLoanProgramRequiredDocs = [];
                            if (count($LMRInternalLoanprogramsArray ?? []) > 0) {

                                $LMRInternalLoanprograms = [];
                                foreach ($LMRInternalLoanprogramsArray as $lmrInterloanProgram) {
                                    $LMRInternalLoanprograms[] = $lmrInterloanProgram['internalLoanProgram'];
                                }

                                if (count($LMRInternalLoanprograms) > 0) {
                                    $LMRinternalLoanProgramTempArray['serviceTypes'] = implode(',', $LMRInternalLoanprograms);
                                    $LMRinternalLoanProgramTempArray['PCID'] = $PCID;
                                    $LMRinternalLoanProgramTempArray['LMRId'] = $LMRId;
                                    $LMRinternalLoanProgramTempArray['requiredBy'] = 'Borrower';
                                    $LMRinternalLoanProgramTempArray['moduleType'] = implode(',', $fileModules);
                                    $LMRinternalLoanProgramRequiredDocs = (requiredDocsForInternalLoanProgram::getReport($LMRinternalLoanProgramTempArray));
                                }

                                $requiredDocsArrayNewPCMID = Arrays::buildKeyByValue($LMRinternalLoanProgramRequiredDocs, 'PCMID');
                                $PMID_Keys2 = array_filter(array_keys($requiredDocsArrayNewPCMID));

                                $LMRinternalLoanProgramRequiredDocsNameArray = [];
                                if (count($LMRinternalLoanProgramRequiredDocs) > 0) {
                                    $LMRinternalLoanProgramRequiredDocsNameArray = Arrays::buildKeyByValue($LMRinternalLoanProgramRequiredDocs, 'moduleType');
                                }

                                $loadedModuleServiceKey = '';
                                foreach ($moduleAndServiceRequiredDocs as $moduleKeyFromDocs => $modValues) {
                                    $loadedModuleServiceKey = $moduleKeyFromDocs;
                                    $docArrbyName = Arrays::buildKeyByValue($modValues, 'docName');
                                }
                                foreach ($LMRinternalLoanProgramRequiredDocsNameArray as $internalModuleKey => $internalModuleValue) {
                                    foreach ($internalModuleValue as $inModVa) {
                                        if (trim($inModVa['updatedBy']) > 0 && trim($inModVa['updatedBy']) != '') {

                                        } else {

                                            if (!in_array(trim($inModVa['docName']), $documentListNameWise)) {
                                                $moduleAndServiceRequiredDocs[$loadedModuleServiceKey][] = $inModVa;
                                            } else {
                                                $found_key = array_search($inModVa['docName'], array_keys($docArrbyName));
                                                if ($found_key != '') {
                                                    if ($inModVa['updateOn'] != '') {
                                                        $moduleAndServiceRequiredDocs[$loadedModuleServiceKey][$found_key] = $inModVa;
                                                        $documentListNameWise[] = $inModVa['docName'];
                                                    }
                                                }
                                            }
                                        }

                                    }
                                }
                            }


                            /* additional Loan Program Support Code */
                            $LMRadditionalLoanProgramRequiredDocs = [];
                            if (count($LMRadditionalLoanprogramsArray ?? []) > 0) {

                                $LMRadditionalLoanprograms = [];
                                foreach ($LMRadditionalLoanprogramsArray as $lmrAdditionalloanProgram) {
                                    $LMRadditionalLoanprograms[] = $lmrAdditionalloanProgram['additionalLoanProgram'];
                                }

                                if (count($LMRadditionalLoanprograms) > 0) {
                                    $LMRadditionalLoanProgramTempArray['serviceTypes'] = implode(',', $LMRadditionalLoanprograms);
                                    $LMRadditionalLoanProgramTempArray['PCID'] = $PCID;
                                    $LMRadditionalLoanProgramTempArray['LMRId'] = $LMRId;
                                    $LMRadditionalLoanProgramTempArray['requiredBy'] = 'Borrower';
                                    $LMRadditionalLoanProgramTempArray['moduleType'] = implode(',', $fileModules);
                                    $LMRadditionalLoanProgramRequiredDocs = (requiredDocsForAdditionalLoanProgram::getReportObjects(
                                        $LMRId,
                                        $PCID,
                                        implode(',', $fileModules),
                                        implode(',', $LMRadditionalLoanprograms),
                                        'Borrower'
                                    ));
                                }

                                $requiredDocsArrayNewPCMID = Arrays::buildKeyByValue($LMRadditionalLoanProgramRequiredDocs, 'PCMID');
                                $PMID_Keys3 = array_filter(array_keys($requiredDocsArrayNewPCMID));

                                $LMRadditionalLoanProgramRequiredDocsNameArray = [];
                                if (count($LMRadditionalLoanProgramRequiredDocs) > 0) {
                                    $LMRadditionalLoanProgramRequiredDocsNameArray = Arrays::buildKeyByValue($LMRadditionalLoanProgramRequiredDocs, 'moduleType');
                                }
//pr($LMRadditionalLoanProgramRequiredDocsNameArray);
                                $loadedModuleServiceKey = '';
                                $docArrbyName = [];
                                foreach ($moduleAndServiceRequiredDocs as $moduleKeyFromDocs => $modValues) {
                                    $loadedModuleServiceKey = $moduleKeyFromDocs;
                                    $docArrbyName = Arrays::buildKeyByValue($modValues, 'docName');
                                }
                                foreach ($LMRadditionalLoanProgramRequiredDocsNameArray as $additionalModuleKey => $additionalModuleValue) {
                                    foreach ($additionalModuleValue as $inModVa) {
                                        if (trim($inModVa->updatedBy) > 0 && trim($inModVa->updatedBy) != '') {

                                        } else {

                                            if (!in_array(trim($inModVa->docName), $documentListNameWise)) {
                                                $moduleAndServiceRequiredDocs[$loadedModuleServiceKey][] = $inModVa;
                                            } else {
                                                $found_key = array_search($inModVa->docName, array_keys($docArrbyName));
                                                if ($found_key != '') {
                                                    if ($inModVa->updateOn != '') {
                                                        $moduleAndServiceRequiredDocs[$loadedModuleServiceKey][$found_key] = $inModVa;
                                                    }
                                                }
                                            }
                                        }

                                    }
                                }
                            }

                            $PMID_Keys1Array = implode(',', array_merge($PMID_Keys1 ?? [], $PMID_Keys2 ?? [], $PMID_Keys3 ?? []));

                            $additionalLogicResult = [];
                            if ($PMID_Keys1Array != '') {
                                $additionalLogicResult = getRequiredDocsAdditionalLogic::getReport(['PCMIDs' => $PMID_Keys1Array, 'PCID' => $PCID]);
                            }

                            /*End of Internal Loan Program Support Code */
                            $FileStatusByName = [];

                            $FileStatus = getDocStatusFileName::getReport(['LMRID' => $LMRId, 'PCID' => $PCID]);
                            $FileStatusByName = Arrays::buildKeyByValue($FileStatus, 'docName');


                            // pr($moduleAndServiceRequiredDocs);
                            $disp = 0;
                            foreach ($moduleAndServiceRequiredDocs as $tST => $mSTArray) {
                                usort($mSTArray, function ($a, $b) {
                                    return $a->displayOrder - $b->displayOrder;
                                });
                                if (count($mSTArray) > 0) {
                                    $chk = 0;
                                    // if (count($additionalLogicResult) > 0) {
                                    $mSTArray = BaseHTML::docAdditionalLogicObjects($additionalLogicResult, $requiredAdditionalCond, $mSTArray);
                                    // }

                                    $doc = 0;
                                    //  for ($doc = 0; $doc < count($mSTArray); $doc++) {                                                         // | Start Inner For
                                    foreach ($mSTArray as $eachDoc) {                                                         // | Start Inner For
                                        $hideForAdditionalLogic = '';
                                        $moduleName = '';
                                        $chST = '';
                                        $chMT = '';
                                        $chTy = '';
                                        $chId = '';
                                        $uBy = '';
                                        $missChkOpt = '';
                                        $LMRChkDisableOpt = false;
                                        $dnUrl = '';
                                        $docN = '';
                                        $docStatus = CONST_DEFAULT_CHECKLIST_STATUS;
                                        $rqBy = '';
                                        $chkUpdatedUser = '';
                                        $notes = '';
                                        $flatNotes = '';
                                        $CLUpdatedBy = '';
                                        $SID = 0;
                                        $UID = 0;
                                        $docUType = '';
                                        $CheckListUpdatedOn = '';
                                        $CLUpdatedOn = '';
                                        $chkReqUpdatedUser = '';
                                        $manReqDocs = $checklistDesc = 0;
                                        $docNameTemp = '';
                                        $hideForAdditionalLogic = $eachDoc->hideForAdditionalLogic; // | Get Checklist Name.
                                        $refDocName = '';
                                        $refDocUrl = '';

                                        if ($ServiceType != '') {
                                            $chST = $ServiceType;
                                        } else {
                                            if (!isset($eachDoc->internalLoanPrograms)) {
                                                $chST = $eachDoc->serviceType;                                                    // | Get Checklist Service Type.
                                            }
                                        }

                                        $chMT = $eachDoc->moduleType;                                                            // | Get Checklist Service Type.
                                        if (array_key_exists($chMT, $fileModulesWithName)) $moduleName = $fileModulesWithName[$chMT]['moduleName'];          // | Get Module Name.

                                        $chTy = $eachDoc->CLType;                                                                // | Get Checklist Type.
                                        $uBy = $eachDoc->updatedBy;                                                             // | Get Checklist Type.
                                        if ($chTy == 'PCL') {
                                            $chId = $eachDoc->PCMID;                                                                 // | Get PC Checklist Id.
                                        } else {
                                            $chId = $eachDoc->FMID;                                                                  // | Get File Checklist Id.
                                        }
                                        $docN = $eachDoc->docName;                                                               // | Get Checklist Name.
                                        $docNameTemp = preg_replace("/\r|\n/", '', $eachDoc->docName);   // | Get Checklist Name.
                                        $docNameTemp = addslashes($docNameTemp);
                                        $rqBy = $eachDoc->required;                                                              // | Get Required By.
                                        if (isset($eachDoc->mandatory)) $manReqDocs = $eachDoc->mandatory;
                                        $checklistDesc = htmlentities($eachDoc->description);                                                             // | Get Required By.

                                        $refDocName = $eachDoc->refDocName;

                                        if ($refDocName != '') {
                                            $refDocName = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($refDocName) . '&fd=' . cypher::myEncryption(CONST_PATH_PC_UP_DOC . $PCID) . '&opt=enc';
                                        }
                                        $refDocUrl = $eachDoc->refDocUrl;


                                        /* Get Activated checklist items and info - Start. */
                                        if (array_key_exists($chTy, $LMRChecklistInfo ?? [])) {                                                      // | Checklist Type Check
                                            if (array_key_exists($chId, $LMRChecklistInfo[$chTy])) {                                             // | Checklist is Active.
                                                $missChkOpt = 'checked';
                                                $chkUpdatedUserType = '';
                                                $chkUpdatedUserId = '';
                                                $chkUpdatedUser = '';
                                                $docStatus = 0;

                                                $chkUpdatedUserType = trim($LMRChecklistInfo[$chTy][$chId]['updatedUserType']);
                                                $chkUpdatedUserId = trim($LMRChecklistInfo[$chTy][$chId]['updatedBy']);
                                                $chkUpdatedOn = trim($LMRChecklistInfo[$chTy][$chId]['updatedOn']);
                                                $docStatus = trim($LMRChecklistInfo[$chTy][$chId]['docStatus']);
                                                $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
                                                $ipArray['outputZone'] = $userTimeZone;
                                                $ipArray['inputTime'] = $chkUpdatedOn;
                                                $chkUpdatedOn = Dates::timeZoneConversion($ipArray);

                                                if ($chkUpdatedUserType == 'Employee') {
                                                    if (count($empChkInfoArray) > 0) {
                                                        if (array_key_exists($chkUpdatedUserId, $empChkInfoArray)) {
                                                            $chkUpdatedUser = trim($empChkInfoArray[$chkUpdatedUserId]['processorName']);
                                                        }
                                                    }
                                                } else if ($chkUpdatedUserType == 'Branch') {
                                                    if (array_key_exists($chkUpdatedUserId, $LMRBranchChkInfoArray)) {
                                                        $chkUpdatedUser = trim($LMRBranchChkInfoArray[$chkUpdatedUserId]['LMRExecutive']);
                                                    }
                                                } else if ($chkUpdatedUserType == 'Agent') {
                                                    if (trim($chkUpdatedUserId) == trim($userNumber)) {
                                                        $chkUpdatedUser = trim($userName);
                                                    }
                                                }

                                                if (trim($chkUpdatedUser) != '') {
                                                    $chkUpdatedUser = 'Document checked by <b>' . ucwords($chkUpdatedUser) . '</b> on ' . Dates::formatDateWithRE($chkUpdatedOn, 'YMD_HMS', 'M j, Y h:i A');
                                                    if ($chkUpdatedOn != '') {
                                                        $chkUpdatedUser .= ' - ' . $userTimeZone;
                                                    }
                                                }

                                                if (($userRole == 'Manager') || ($userRole == 'Super') || (($chkUpdatedUserType == $userGroup) && ($chkUpdatedUserId == $userNumber))) {
                                                    $LMRChkDisableOpt = false;
                                                } else {
                                                    $LMRChkDisableOpt = true;
                                                }

                                                if ($allowToEdit) {
                                                } else {
                                                    $LMRChkDisableOpt = true;
                                                }
                                            }
                                        }
                                        /* Get Activated checklist items and info - End. */

                                        if (array_key_exists($docN, $FileStatusByName)) {                                                      // | Checklist Type Check

                                            $missChkOpt = 'checked';
                                            $chkUpdatedUserType = '';
                                            $chkUpdatedUserId = '';
                                            $chkUpdatedUser = '';
                                            $docStatus = 0;

                                            $chkUpdatedUserType = trim($FileStatusByName[$docN][0]['updatedUserType']);
                                            $chkUpdatedUserId = trim($FileStatusByName[$docN][0]['updatedBy']);
                                            $chkUpdatedOn = trim($FileStatusByName[$docN][0]['updatedOn']);
                                            $docStatus = trim($FileStatusByName[$docN][0]['docStatus']);
                                            if (trim($FileStatusByName[$docN][0]['updateComment']) == 'Unchecked') {
                                                $missChkOpt = '';
                                            }


                                            $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
                                            $ipArray['outputZone'] = $userTimeZone;
                                            $ipArray['inputTime'] = $chkUpdatedOn;
                                            $chkUpdatedOn = Dates::timeZoneConversion($ipArray);

                                            if ($chkUpdatedUserType == 'Employee') {
                                                if (count($empChkInfoArray) > 0) {
                                                    if (array_key_exists($chkUpdatedUserId, $empChkInfoArray)) {
                                                        $chkUpdatedUser = trim($empChkInfoArray[$chkUpdatedUserId]['processorName']);
                                                    }
                                                }
                                            } else if ($chkUpdatedUserType == 'Branch') {
                                                if (array_key_exists($chkUpdatedUserId, $LMRBranchChkInfoArray)) {
                                                    $chkUpdatedUser = trim($LMRBranchChkInfoArray[$chkUpdatedUserId]['LMRExecutive']);
                                                }
                                            } else if ($chkUpdatedUserType == 'Agent') {
                                                if (trim($chkUpdatedUserId) == trim($userNumber)) {
                                                    $chkUpdatedUser = trim($userName);
                                                }
                                            }

                                            if (trim($chkUpdatedUser) != '') {
                                                $chkUpdatedUser = 'Document checked by <b>' . ucwords($chkUpdatedUser) . '</b> on ' . Dates::formatDateWithRE($chkUpdatedOn, 'YMD_HMS', 'M j, Y h:i A');
                                                if ($chkUpdatedOn != '') {
                                                    $chkUpdatedUser .= ' - ' . $userTimeZone;
                                                }
                                            }

                                            if (($userRole == 'Manager') || ($userRole == 'Super') || (($chkUpdatedUserType == $userGroup) && ($chkUpdatedUserId == $userNumber))) {
                                                $LMRChkDisableOpt = false;
                                            } else {
                                                $LMRChkDisableOpt = true;
                                            }

                                            if ($allowToEdit) {
                                            } else {
                                                $LMRChkDisableOpt = true;
                                            }

                                        }

                                        $checklistDescription = '';
                                        if ($checklistDesc != '') {
                                            $checklistDescription = <<<EOT

      <a class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass"  title="Description: {$checklistDesc}"> <i class="fa fa-info-circle text-primary tooltipClass" ></i></a>
EOT;
                                        }

                                        /* Get Uploaded Doc - Start.*/
                                        if (count($fileHMLOChecklistUploadDocs) > 0) {
                                            if (array_key_exists($chTy, $fileHMLOChecklistUploadDocs)) {
                                                if (array_key_exists($chId, $fileHMLOChecklistUploadDocs[$chTy]) && $uBy == '') {
                                                    $dnUrl = "<a title =\"Click to view documents\" href=\"" . CONST_URL_POPS . "viewHMLOChecklistDocs.php\" id=\"fileID=" . cypher::myEncryption($LMRId) . '&PCChecklistID=' . cypher::myEncryption($chId) . '&oldFPCID=' . cypher::myEncryption($PCID) . '&fileRecordDate=' . cypher::myEncryption($fileRecordDate) . '&CLType=' . cypher::myEncryption($chTy) . "\" name=\"File: " . $borrowerName . " > Show Upload Documents\" style=\"text-decoration:none\">" . wordwrap($docN, 45, "\n", TRUE) . '</a>';
                                                }
                                            }
                                        }

                                        if ($dnUrl == '') $dnUrl = wordwrap($docN, 45, "\n", TRUE);
                                        /* Get Uploaded Doc - End.*/

                                        /* Get Note - Start.*/
                                        if (array_key_exists($chTy, $fileChecklistNotesInfo ?? [])) {
                                            if (array_key_exists($chId, $fileChecklistNotesInfo[$chTy])) {
                                                $flatNotes = trim(rawurldecode($fileChecklistNotesInfo[$chTy][$chId]['notes']));
                                                $SID = trim($fileChecklistNotesInfo[$chTy][$chId]['SID']);
                                                $UID = trim($fileChecklistNotesInfo[$chTy][$chId]['UID']);
                                                $docUType = trim($fileChecklistNotesInfo[$chTy][$chId]['UType']);
                                                $CLUpdatedOn = trim($fileChecklistNotesInfo[$chTy][$chId]['recordDate']);
                                                $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
                                                $ipArray['outputZone'] = $userTimeZone;
                                                $ipArray['inputTime'] = $CLUpdatedOn;
                                                $CLUpdatedOn = Dates::timeZoneConversion($ipArray);
                                            }
                                        }

                                        if ($flatNotes == '') {
                                            $notes = "<a class=\"fa fa-comments-o text-primary\" style=\"text-decoration:none;\"></a>";
                                        } else {
                                            /** Who Updated the check List Notes on Aug 8, 2016 **/
                                            if ($UID > 0) {
                                                if ($docUType == 'Employee') {
                                                    if (count($empChecklistNotesInfoArray ?? []) > 0) {
                                                        if (array_key_exists($UID, $empChecklistNotesInfoArray)) {
                                                            $CLUpdatedBy = trim($empChecklistNotesInfoArray[$UID]['processorName']);
                                                        }
                                                    }
                                                } else if ($docUType == 'Branch') {
                                                    if (count($branchChecklistNotesInfoArray) > 0) {
                                                        if (array_key_exists($UID, $branchChecklistNotesInfoArray)) {
                                                            $CLUpdatedBy = trim($branchChecklistNotesInfoArray[$UID]['LMRExecutive']);
                                                        }
                                                    }
                                                } else if ($docUType == 'Agent') {
                                                    if (count($agentChecklistNotesInfoArray) > 0) {
                                                        if (array_key_exists($UID, $agentChecklistNotesInfoArray)) {
                                                            $CLUpdatedBy = trim($agentChecklistNotesInfoArray[$UID]['agentName']);
                                                        }
                                                    }
                                                }
                                            } else {
                                                $CLUpdatedBy = 'Admin';
                                            }

                                            $CheckListUpdatedOn = '<b>' . ucwords($CLUpdatedBy) . '</b> on ' . Dates::formatDateWithRE($CLUpdatedOn, 'YMD_HMS', 'M j, Y h:i A');
                                            if ($CLUpdatedOn != '') {
                                                $CheckListUpdatedOn .= ' - ' . $userTimeZone;
                                            }

                                            $notes = "<i class=\"fa fa-comments popoverClass text-primary\" style=\"text-decoration:none;\"  title='Notes' data-html='true' data-content=\"" . $flatNotes . '<br>- ' . $CheckListUpdatedOn . "\"></i></div>";
                                        }
                                        /* Get Note - End .*/

                                        /* Get Required Info - End .*/
                                        $unUT = '';
                                        $unUO = '';
                                        $unUT = $eachDoc->updatedUserType;                                                             // | Get Not Req changed user type.
                                        $unUO = $eachDoc->updatedOn;                                                                   // | Get Not Req changed user type.
                                        $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
                                        $ipArray['outputZone'] = $userTimeZone;
                                        $ipArray['inputTime'] = $unUO;
                                        $unUO = Dates::timeZoneConversion($ipArray);

                                        if ($unUT == 'Employee') {
                                            if (count($chkNotReqEmpInfo ?? []) > 0) {
                                                if (array_key_exists($uBy, $chkNotReqEmpInfo)) {
                                                    $chkReqUpdatedUser = trim($chkNotReqEmpInfo[$uBy]['processorName']);
                                                }
                                            }
                                        } else if ($unUT == 'Branch') {
                                            if (array_key_exists($uBy, $chkNotReqBranchInfo ?? [])) {
                                                $chkReqUpdatedUser = trim($chkNotReqBranchInfo[$uBy]['LMRExecutive']);
                                            }
                                        } else if ($unUT == 'Agent') {
                                            if (array_key_exists($uBy, $chkNotReqAgentInfo ?? [])) {
                                                $chkReqUpdatedUser = trim($chkNotReqAgentInfo[$uBy]['agentName']);
                                            }
                                        }
                                        if (trim($chkReqUpdatedUser) != '') {
                                            $chkReqUpdatedUser = "\nNot required marked by " . ucwords($chkReqUpdatedUser) . ' on ' . Dates::formatDateWithRE($unUO, 'YMD_HMS', 'M j, Y h:i A');
                                        }
                                        if ($unUO != '') {
                                            $chkReqUpdatedUser .= ' - ' . $userTimeZone;
                                        }
                                        if ($userGroup == 'Client') $chkReqUpdatedUser = 'Document not required.';
                                        if ($docStatus == '' || $docStatus == 0) $docStatus = 9;

                                        $clsName = docStatusArray::getStyle($docStatus);

                                        $cls = ''; //if ($doc % 2 == 0) $cls = 'even'; $uBy = '';
                                        if ($doc == 0) {         // | Head Start.?>
                                            <div class="card card-custom fileChecklistForm_<?php echo $doc; ?>" >
                                            <div class="card-header card-header-tabs-line bg-gray-100  d-none">
                                                <div class="card-title">
                                                    <h3 class="card-label">
                                                        <?php echo $moduleName;
                                                        if (array_key_exists($chST, $glLMRClientTypeArray)) echo ' - ' . $glLMRClientTypeArray[$chST]; ?>
                                                    </h3>
                                                </div>
                                                <div class="card-toolbar ">
                                                    <a href="javascript:void(0);"
                                                       class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                                                       data-card-tool="toggle"
                                                       data-section="fileChecklistForm_<?php echo $doc; ?>"
                                                       data-toggle="tooltip" data-placement="top" title=""
                                                       data-original-title="Toggle Card">
                                                        <i class="ki ki-arrow-down icon-nm"></i>
                                                    </a>
                                                    <a href="#"
                                                       class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none"
                                                       data-card-tool="reload"
                                                       data-toggle="tooltip" data-placement="top" title=""
                                                       data-original-title="Reload Card">
                                                        <i class="ki ki-reload icon-nm"></i>
                                                    </a>
                                                    <a href="#"
                                                       class="btn btn-icon btn-sm btn-hover-light-primary d-none"
                                                       data-card-tool="remove"
                                                       data-toggle="tooltip" data-placement="top" title=""
                                                       data-original-title="Remove Card">
                                                        <i class="ki ki-close icon-nm"></i>
                                                    </a>
                                                </div>
                                            </div>

                                            <div class="card-body fileChecklistForm_<?php echo $doc; ?>_body px-2">
                                            <?php if ($disp == 0) {
                                                /** legend Section Start. **/ ?>
                                                <div class="d-xl-flex justify-content-end ">
                                                    <div class="table-responsive d-none ">
                                                        <table class="">
                                                            <tbody>
                                                            <tr>
                                                                <?php foreach (docStatusArray::getStatuses() as $k => $item) { ?>
                                                                    <td class="p-2 docStatus"
                                                                        style="<?php echo $item->style(); ?>"><?php echo $item->name; ?></td>
                                                                <?php } ?>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            <?php } /** legend Section End. **/ ?>

                                            <div class="table-responsive">
                                            <table class="table table-hover LWcustomTable table-bordered table-condensed table-sm table-vertical-center datatableRequiredDocs ">
                                            <thead class="thead-light">
                                            <tr>
                                                <th class="hide" width="5%"></th>
                                                <th width="20%">Upload Document</th>
                                                <th width="20%">Required Doc Name</th>
                                                <th width="10%">Required By</th>
                                                <th width="15%">Required Doc Status</th>
                                                <th width="5%">Time Stamp</th>
                                                <th width="5%">BORROWER/BROKER<br/>NOTES</th>
                                                <th width="5%">UNDERWRITING<br/>CONDITIONS</th>
                                                <?php if (count($displayedAndMandatoryItems) > 0) { ?>
                                                    <th width="5%">Mandatory</th>
                                                <?php } ?>
                                            </tr>
                                            </thead>
                                        <?php }        // | Head Close.?>

                                        <tr title="<?php echo $dnUrl; ?>"
                                            class=" <?php echo $cls; ?><?php echo ' ' . $hideForAdditionalLogic; ?>">
                                            <td class="hide <?php echo $clsName; ?>"
                                                id="TR_<?php echo cypher::myEncryption($chId); ?>">&nbsp;&nbsp;&nbsp;
                                                <input type="checkbox" name="checklistDocNo[]"
                                                       id="chk_cb_<?php echo $chk ?>" disabled
                                                       value="<?php echo $chId; ?>" <?php echo $missChkOpt; ?>>
                                            </td>

                                            <td class=" <?PHP echo $clsName; ?>">
                                                <?php if (!in_array($docStatus, [6, 12, 4])) { // Stop a document upload if document is ok/approved in document upload portal ?>
                                                    <div class="dropzone <?PHP echo $clsName; ?>"
                                                         id="dropzone_<?php echo $chId; ?>"></div>
                                                <?php } else {
                                                    echo '<h6>Document already uploaded and approved.</h6>';
                                                } ?>
                                            </td>


                                            <td style="text-align: center;">
                                                <div class="d-flex justify-content-between">
                                    <span class="align-middle"
                                          id="chName_<?php echo $chId; ?>"><?php echo $dnUrl; ?></span>
                                                    <div class="d-flex justify-content-end">
                                                        <?php echo $checklistDescription ?>
                                                        <?php if ($refDocName != '' || $refDocUrl != '') {
                                                            if ($refDocName != '') { ?>
                                                                <a title="Click To Download Ref Document"
                                                                   target="_blank"
                                                                   href="<?php echo $refDocName ?>"
                                                                   class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass"><i
                                                                            class="fa fa-download"></i></a>
                                                            <?php }
                                                            if ($refDocUrl != '') { ?>

                                                                <a title="Click To Open Ref Link"
                                                                   href="<?php echo $refDocUrl ?>"
                                                                   target="_blank"
                                                                   class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass"><i
                                                                            class="fa fa-link"></i> </a>
                                                            <?php }
                                                        } ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?php echo $rqBy; ?></td>
                                            <td>
							<span class="docStatus_<?php echo $chId ?>">
								<span class="<?php echo $clsName; ?> pad5 docStatus"><?php if ($docStatus != 1) echo docStatusArray::getStatus($docStatus)->name; ?></span>
							</span>
                                                <input type="hidden" name="docStatus_<?php echo $chId ?>"
                                                       id="docStatus_<?php echo $chId ?>"
                                                       value="<?php echo $docStatus; ?>">
                                            </td>
                                            <td style="text-align: center;">
                                                <?php if ($chkUpdatedUser != '') { ?>
                                                    <div id="chk_info_<?php echo $chk; ?>"
                                                         class="pad5 left with-children-tip">
                                                        <i class="fa fa-info-circle text-primary tooltipClass"
                                                           data-html="true"
                                                           title="<?php echo $chkUpdatedUser; ?>"></i>
                                                    </div>
                                                <?php } ?>
                                            </td>

                                            <td style="text-align: center;">
                                                <div id="flatNotesDiv_<?php echo cypher::myEncryption($chId) ?>_<?php echo cypher::myEncryption($LMRId) ?>_<?php echo cypher::myEncryption($chTy) ?>"><?php echo $notes; ?>
                                                </div>
                                            </td>
                                            <td></td>

                                            <?php if (count($displayedAndMandatoryItems) > 0 && $hideForAdditionalLogic == '') { ?>
                                                <td>
                                                    <?php if ($manReqDocs == 1) { ?>
                                                        <input type="hidden" name="uploadCnt"
                                                               id="<?php echo $chId; ?>"
                                                               class="uploadCnt"
                                                               value="<?php echo $chId; ?>">
                                                        <a class="fa fa-check icon-green fa-2x tip-bottom"
                                                           title=""
                                                           style="text-decoration:none; cursor: pointer;"></a>
                                                    <?php } ?>
                                                </td>
                                            <?php } ?>
                                        </tr>

                                        <?php if (!in_array($docStatus, [6, 12, 4])) { //Stop a document upload if document is ok/approved in document upload portal ?>
                                            <script>
                                                Dropzone.autoDiscover = false;

                                                $(function () {
                                                    var configmaxFileuploadsize = "<?php echo CONST_GLUPLOAD_MAX_BYTESFILESIZE_ALLOWED; ?>";
                                                    var maxFileuploadsize = parseInt(Math.floor(Math.log(configmaxFileuploadsize) / Math.log(1024)));

                                                    var myDropzone = new Dropzone("#dropzone_<?php echo $chId; ?>", {
                                                        url: "/backoffice/api_v2/upload_docs",
                                                        params: {
                                                            'uploadedBy': '<?php echo str_replace("'", "\'", $userNumber); ?>',
                                                            'uploadingUserType': '<?php echo str_replace("'", "\'", $userGroup); ?>',
                                                            'LMRResponseId': '<?php echo $LMRResponseId; ?>',
                                                            'LMRId': '<?php echo cypher::myEncryption($LMRId); ?>',
                                                            'PCID': '<?php echo cypher::myEncryption($PCID); ?>',
                                                            'userGroup': '<?php echo $userGroup; ?>',
                                                            'userNumber': '<?php echo $userNumber; ?>',
                                                            'userName': '<?php echo str_replace("'", "\'", $userName); ?>',
                                                            'docType': '<?php echo $chTy; ?>',
                                                            'PCMID': '<?php echo $chId; ?>',
                                                            'reqDocCategoryName': '<?php echo $docNameTemp;?>'
                                                        },
                                                        maxFilesize: (Math.round(configmaxFileuploadsize / Math.pow(1024, maxFileuploadsize), 2)),
                                                        maxFiles: 10,
                                                        acceptedFiles: ".pdf, .doc, .docx, .xls, .xlsx, .gif, .jpg, .jpeg, .png, .html, .htm, .shtml, .bmp, .csv, .txt",
                                                        init: function () {
                                                            this.on("error", function (file, message) {
                                                                toastrNotification(message, 'error');
                                                                this.removeFile(file);
                                                            });
                                                        },
                                                        success: function (file, responseObj, bytesSent) {
                                                            //var responseObj = $.parseJSON(response);
                                                            if (parseInt(responseObj.code) === 100) {
                                                                $("#upForm").append("<input type='hidden' name='docArray[]' value='" + file.name + '<?php echo ' - Document Type: ' . $docNameTemp; ?>' + "' >");
                                                                $('.docStatus_' +<?php echo $chId; ?>).html('<span class="pad5 docStatus" style="<?php echo docStatusArray::getStyle(docStatusArray::DOC_STATUS_PENDING_REVIEW); ?>">Pending Review</span>');
                                                                $('#docStatus_' +<?php echo $chId; ?>).val(4);
                                                                $("#btnSave").attr("disabled", false); //enable once file uploaded
                                                                toastrNotification(responseObj.msg, "success");
                                                            } else {
                                                                toastrNotification(responseObj.msg, 'error');
                                                                this.removeFile(this.files[0]);
                                                                return false;
                                                            }

                                                        }
                                                    });
                                                });
                                            </script>
                                            <?php
                                        }
                                        if ($doc == (count($mSTArray) - 1)) { ?>
                                            </table>
                                            </div>
                                            </div>
                                            </div>
                                            <?php
                                        }
                                        $chk++;
                                        $disp++;
                                        $doc++;
                                    }
                                }
                            }
                            ?>
                            <style>
                                tr[class*="hideForAdditionalLogic"] {
                                    display: none !important;
                                }

                                .dropzone {
                                    background-color: transparent !important;
                                    border: 1px dashed #0000002b !important;
                                    padding: 0px 0px !important;
                                }

                                .dz-max-files-reached {
                                    pointer-events: none !important;
                                    cursor: default;
                                }
                            </style>
                        <?php } else { ?>

                            <h4 class="mb-10 font-weight-bold text-dark">Required Docs Will Displayed after Form
                                Submitted</h4>
                        <?php } ?>
                        <!--end::Section-->
                    </div>
                <?php } ?>

                <!--end: Wizard Step 7-->
            </div>

            <!--begin: Wizard Actions-->
            <div class=" col-xxl-12 mt-8 ">
                <div class="row form-group">
                    <div class="col-6 text-left mb-2">
                        <button type="button" onclick="$('#submitType').val(this.value);" value="previous"
                                id="wizardPreviousButton"
                                class="btn btn-primary font-weight-bolder text-uppercase px-9 py-4"
                                data-wizard-type="action-prev">Previous
                        </button>
                    </div>
                    <div class="col-6  text-right mb-2">
                        <button type="button" onclick="$('#submitType').val(this.value);" value="next"
                                id="wizardNextButton"
                                class="btn btn-success font-weight-bolder text-uppercase px-9 py-4 font-size-h4"
                                data-wizard-type="action-next">Next
                        </button>
                        <input type="button" name="requiredDocSave" id="requiredDocSave"
                               onclick="return KTWizard2.validateRequiredDocUpload('uploadCnt');$('#submitType').val(this.value);"
                               class="btn btn-success font-weight-bolder text-uppercase px-9 py-4 font-size-h4 "
                               value="Save & Submit"
                            <?php if ($wizardNum <= $maxWizard) { ?> style="display: none;" <?php } ?> >
                    </div>
                </div>
                <div class="row form-group">
                    <div class="col-md-12  text-center mb-2">
                        <?php
                        if ($publicUser == 1) {
                            if ($allowToEdit) { ?>
                                <!-- data-wizard-type="action-submit"-->
                                <input type="submit" value="Save & Finish Later" name="btnSave" id="btnSave"
                                       class="btn btn-primary font-weight-bolder text-uppercase px-9 py-4
                    <?php if ($LMRId > 0) {
                                       } else {
                                           echo 'hide';
                                       } ?> mx-auto"
                                       onclick="$('#submitType').val(this.value);"
                                       tabindex="<?php echo $tabIndex++ ?>">
                                <input type="hidden" name="submitType" id="submitType"
                                       value="">
                                <?php
                                /*         if ($wfOpt == 'FA') {
                                             echo '<div class="col-md-12 align-center text-center" >
<input type="submit" value="Submit" name="btnSave" id="btnSave"   class="btn btn-primary right" tabindex="' . $tabIndex++ . '" data-wizard-type="action-submit">
                 <input type="hidden" name="submitType" id="submitType" value="Save & Finish Later">
             </div>';
                                         } else {
                                             echo '<div class="col-md-12 align-center text-center" data-wizard-type="action-submit" >
<input type="submit" value="Submit" name="btnSave" id="btnSave" class="btn btn-primary" tabindex="' . $tabIndex++ . '">
</div>';
                                         }*/
                            }
                        } ?>
                    </div>
                </div>
            </div>
            <!--end: Wizard Actions-->

            <!--end: Wizard-->
        </div>
        <!--end: Wizard Form-->
    </div>
    <!--end: Wizard Body-->
</div>
<!--end: Wizard-->


<?php if ($isPLO == 0 && $publicUser == 1) { ?>
    <div class="col-md-12 no-padding no-margin">
        <div class="no-padding no-margin text-right">
        <span class="col-md-12 no-padding no-margin"
              style="letter-spacing: 1px; font-size: 14px; font-weight: 600; color: #666; font-family: 'Lato', sans-serif;text-align: right;padding-bottom:2px;">
             Webform Fueled By:<br>
        </span>
            <a href="https://www.lendingwise.com/" target="_blank">
                <img src="<?php echo CONST_SITE_URL; ?>assets/images/logonew.svg"
                     alt="Webform Fueled By LendingWise"
                     style="width: 180px;">
            </a>
        </div>
    </div>
<?php } ?>

<?php if ($allowUserToEditBroker == 1) { ?>
    <script type="text/javascript">
        jQuery(".agentInfoCls").each(function () {
            $(this).addClass('disabledKeyFields');
        });
    </script>
    <?php
}
if ($borrowerEmailLink != '') { ?>
    <script type="text/javascript">
        populateClientBackgroundEntityInfo('loanModForm', '<?php echo htmlspecialchars($borrowerEmail) ?>', '<?php echo $PCID ?>');
    </script>
<?php } ?>

<script>
    $(function () {
        $('#loanprogramtooltip').prop('title', atob("<?php echo base64_encode($loanPgmDetails); ?>"));
        validateMinMaxLoanGuidelines('No');
        fixAdditionalLoanProgChosen(document.getElementById("LMRClientType").value);

        setTimeout(function () {
            formControl.formFields = <?php echo json_encode($fieldsInfo); ?>;
            formControl.fileTab = "<?php echo htmlspecialchars($fileTab); ?>";
            formControl.publicUser = "<?php echo intval($publicUser); ?>";
            formControl.userGroup = "<?php echo $userGroup; ?>";
            formControl.activeTab = '<?php echo htmlspecialchars($activeTab); ?>';
        }, 100);
    });

    function fixAdditionalLoanProgChosen(removeMe = '') {
        if (removeMe) {
            $("#LMRadditionalLoanProgram option[value='" + removeMe + "']").attr('disabled', 'disabled').siblings().removeAttr('disabled');
            $("#LMRadditionalLoanProgram").trigger('chosen:updated');
        }
    }
    <?php
    if ($PCID == 4026) {  //open broker div custom code for tenet (4026), express cap (2853)-removed... (and stage-lendingwise-demo2 - 3363, 3580 is lendingwise-dave in live)
    ?>
    $(document).ready(function () {
        $("#REBroker").prop("checked", true);
        showAndHideBrokerInfo('Yes', 'BrokerInfoDiv');
    });
    <?php
    }
    ?>
    <?php
    if ($PCID == 2853) {  //open lo div custom code for express cap (2853) (and stage-lendingwise-demo2 - 3363, but not for tenet)
    ?>
    $(document).ready(function () {
        $("#RELoanofficer").prop("checked", true);
        showAndHideLoanofficerInfo('Yes', 'LoanofficerInfoDiv');
    });
    <?php
    }
    ?>
    if ($('.REBrokerLODisp.secShow').length > 0) { //show title
        $(".ABICard").show();
    } else { //hide title
        $(".ABICard").hide();
    }
</script>
<?php
if (isset($_SESSION['wizardMsg'])) {
    if ($_SESSION['wizardMsg'] != '') { ?>
        <script>
            msgType = 'success';
            toastr.options = {
                "positionClass": "toast-center-center",
                "closeButton": true,
                "showDuration": "100000",
                "hideDuration": "100000",
                "timeOut": "2800",
                "extendedTimeOut": "100000",
                "showEasing": "swing",
                "hideEasing": "linear",
                "showMethod": "fadeIn",
                "hideMethod": "fadeOut",
                "allowHtml": true,
            };
            var $toast = toastr['success']('<?php echo $_SESSION['wizardMsg'];?>');
        </script>
        <?php
        unset($_SESSION['wizardMsg']);
    }
}
?>
<!-- HMLOLoanInfoFormWizard.php -->
