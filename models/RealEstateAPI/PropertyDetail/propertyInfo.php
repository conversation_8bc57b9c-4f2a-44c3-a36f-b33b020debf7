<?php

namespace models\RealEstateAPI\PropertyDetail;

use models\RealEstateAPI\PropertyDetail\propertyInfo\address;
use models\types\strongType;

class propertyInfo extends strongType
{
    public ?string $airConditioningAvailable = null;
    public ?string $airConditioningType = null;
    public ?string $basementFinishedPercent = null;
    public ?string $basementSquareFeet = null;
    public ?string $basementSquareFeetFinished = null;
    public ?string $basementSquareFeetUnFinished = null;
    public ?string $bathrooms = null;
    public ?string $bedrooms = null;
    public ?string $buildingSquareFeet = null;
    public ?string $carport = null;
    public ?string $construction = null;
    public ?string $deck = null;
    public ?string $deckArea = null;
    public ?string $fireplace = null;
    public ?string $fireplaces = null;
    public ?string $garageSquareFeet = null;
    public ?string $garageSquareFeetFinished = null;
    public ?string $garageSquareFeetUnfinished = null;
    public ?string $heatingType = null;
    public ?string $interiorStructure = null;
    public ?string $latitude = null;
    public ?string $livingSquareFeet = null;
    public ?string $longitude = null;
    public ?string $lotSquareFeet = null;
    public ?string $parcelAccountNumber = null;
    public ?string $parkingSpaces = null;
    public ?string $partialBathrooms = null;
    public ?string $patio = null;
    public ?string $patioArea = null;
    public ?string $plumbingFixturesCount = null;
    public ?string $pool = null;
    public ?string $poolArea = null;
    public ?string $porchArea = null;
    public ?string $porchType = null;
    public ?string $pricePerSquareFoot = null;
    public ?string $propertyUse = null;
    public ?string $propertyUseCode = null;
    public ?string $roofConstruction = null;
    public ?string $roofMaterial = null;
    public ?string $roomsCount = null;
    public ?string $rvParking = null;
    public ?string $safetyFireSprinklers = null;
    public ?string $stories = null;
    public ?string $unitsCount = null;
    public ?string $yearBuilt = null;
    public ?string $basementType = null;
    public ?string $garageType = null;
    public ?string $taxExemptionHomeownerFlag = null;
    public ?string $heatingFuelType = null;
    public ?string $buildingsCount = null;
    public ?string $basementSquareFeetUnfinished = null;
    public ?string $utilitiesSewageUsage = null;
    public ?string $utilitiesWaterSource = null;
    public ?string $breezeway = null;
    public ?string $attic = null;
    public ?string $featureBalcony = null;
    public ?string $hoa = null;
    public ?string $buildingCondition = null;

    public ?address $address = null;


    public function __construct(?array $data = null)
    {
        $this->address = new address($data['address']);
        unset($data['address']);

        parent::__construct($data);
    }
}