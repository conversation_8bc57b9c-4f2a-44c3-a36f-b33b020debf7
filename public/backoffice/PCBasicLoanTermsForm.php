<?php
global $assignedPCID, $ssURL, $modulesArray, $userRole, $allowPCUsersToMarketPlace;

use models\composite\oPC\getPCBasicLoanInfo;
use models\composite\oPC\getPCServiceType;
use models\constants\gl\glHMLOExtensionOption;
use models\constants\GpropertyTypeNumbArray;
use models\Controllers\LMRequest\Property;
use models\cypher;
use models\standard\Arrays;
use models\standard\Currency;
use pages\backoffice\processingCompany\processingCompany;

$glHMLOExtensionOption = glHMLOExtensionOption::$glHMLOExtensionOption;

$resultArray = $PCLMRClientTypeInfoArray = $basicInfoArray = $stateArray = [];
$PCNichesArray = [];
$loanPgmInfoArray = $transactionTypeInfoArray = $loanPropertyTypeInfoArray = $loanFuturePropertyTypeInfoArray = [];
$extnOptionInfoArray = $loanTermInfoArray = $loanOccupancyInfoArray = $loanStateInfoArray = $loanNichesInfoArray = [];
$marketPlaceLoanProgramInfoArray = [];
$loanAmortizationInfoArray = [];
$entityTypeInfoArray = [];
$rateLockPeriodInfoArray = [];
$exitStrategyInfoArray = [];
$exitStrategyInfo = [];
if ($assignedPCID > 0) {
    $ip['PCID'] = $assignedPCID;
    $ip['keyNeeded'] = 'n';
    $ip['getInternalLoanPrograms'] = '1';
    $PCLMRClientTypeInfoArray = getPCServiceType::getReport($ip);
    $resultArray = getPCBasicLoanInfo::getReport(['PCID' => $assignedPCID]);
    //   echo "<pre>";
    //  print_r($resultArray);
}

$stateArray = Arrays::fetchStates();
/** Fetch all States **/
if (count($resultArray) > 0) {
    if (array_key_exists('basicInfo', $resultArray)) {
        $basicInfoArray = $resultArray['basicInfo'];
    }
    $loanPgmInfoArray = $resultArray['loanPgmInfo'];
    $transactionTypeInfoArray = $resultArray['transactionTypeInfo'];
    $loanPropertyTypeInfoArray = $resultArray['loanPropertyTypeInfo'];
    $loanFuturePropertyTypeInfoArray = $resultArray['loanFuturePropertyTypeInfo'];
    $extnOptionInfoArray = $resultArray['extnOptionInfo'];
    $loanTermInfoArray = $resultArray['loanTermInfo'];
    $loanOccupancyInfoArray = $resultArray['loanOccupancyInfo'];
    $loanStateInfoArray = $resultArray['loanStateInfo'];
    $loanNichesInfoArray = $resultArray['loanNichesInfo'];
    $PCNichesArray = $resultArray['glNiches'];
    $marketPlaceLoanProgramInfoArray = $resultArray['marketPlaceLoanProgramInfo'];
    $marketPlaceLoanProgranNotificationInfoArray = $resultArray['marketPlaceLoanProgranNotification'];
    $loanAmortizationInfoArray = $resultArray['loanAmortizationInfo'];
    $entityTypeInfoArray = $resultArray['loanEntityTypeInfo'];
    $rateLockPeriodInfoArray = $resultArray['rateLockPeriodInfo'];
    $exitStrategyInfoArray = $resultArray['exitStrategyInfo'];
}
$mpTemplateurl = $ssURL . 'sampleTemplates/mpSampleTemplate.csv';
?>
<div class="card card-custom PCBasicLoanTermsFormCard">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                Custom Loan Guidelines
            </h3>
        </div>
        <div class="card-toolbar ">
            <a style="text-decoration:none"
               id="addNewCLG"
               data-href="<?php echo CONST_URL_POPS; ?>addBasicLoanTerms.php"
               data-id="PCID=<?php echo cypher::myEncryption($assignedPCID); ?>&showSaveBtn=1"
               title="Click to add"
               data-name="Loan Guidelines, Marketplace & Drop Down Form Field Controls"
               data-icon-txt='<ul class="list-group list-group-flush p-0"> <li class="list-group-item"> Drop Down Form Field Controls- Allows you to conditionally control the drop down values within various drop down fields throughout the entire platform including quick & full app webforms based on the selected loan program(s). </li>
                    <li class="list-group-item">Loan Guidelines Warnings- The system will trigger loan guidelines warnings if loan parameters fall outside the thresholds set for min/max loan amount, min/max rate, min fico, min experience for flips, ground up construction, min/max points, LTV, LTC, or ARV</li>
                    <li class="list-group-item">Marketplace Engine- If you have the Marketplace feature enabled, you can run eligibility searches against the detailed loan parameters & filters tied to your loan program(s) & send "financing requests" to the point of contact established on each program/product.</li>
                    </ul>'
               data-wsize="modal-xl" data-toggle="modal" data-target="#exampleModal1"
               class="btn btn-sm btn-primary tooltipClass">+ Add New</a>

            <a href="javascript:void(0);"
               class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
               data-card-tool="toggle"
               data-section="PCBasicLoanTermsFormCard"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none" data-card-tool="reload"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                <i class="ki ki-close icon-nm"></i>
            </a>
        </div>
    </div>
    <div class="card-body PCBasicLoanTermsFormCard_body">
        <form action="/backoffice/processingCompany/customLoanSettings"
              method="post"
              name="customLoanSettingsForm"
              id="customLoanSettingsForm">
            <input type="hidden" name="customLoanPCID" id="customLoanPCID" value="<?php echo cypher::myEncryption(processingCompany::$tblProcessingCompany->PCID); ?>">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-8 font-weight-bold">Allow Fees & Costs to be updated via a loan program
                            change?
                            <i class="fa fa-info-circle text-primary tooltipClass"
                               style="text-decoration:none;"
                               title="When false, fees and costs will only populate from your custom loan guidelines when a file is created. When true, fees and costs can be populated from your custom loan guidelines when a file is edited."></i>
                        </label>
                        <div class="col-md-4">
                            <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control"
                                               type="checkbox"
                                                <?php if (processingCompany::$tblProcessingCompany->allowToImportFeesCostOnInternalLoanChange) { ?>
                                                    checked="checked"
                                                <?php } ?>
                                               value="<?php echo processingCompany::$tblProcessingCompany->allowToImportFeesCostOnInternalLoanChange ?>"
                                               id="allowToImportFeesCostOnInternalLoanChangeCheckbox"
                                               onchange="toggleSwitch('allowToImportFeesCostOnInternalLoanChangeCheckbox', 'allowToImportFeesCostOnInternalLoanChange','1','0' );"/>
                                        <input type="hidden"
                                               name="allowToImportFeesCostOnInternalLoanChange"
                                               id="allowToImportFeesCostOnInternalLoanChange"
                                               value="<?php echo processingCompany::$tblProcessingCompany->allowToImportFeesCostOnInternalLoanChange ?>">
                                        <span></span>
                                    </label>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row form-group">
                <div class="col-md-12 text-center">
                    <button type="submit" class="btn btn-primary mr-2" id="save-next">Save</button>
                </div>
            </div>
        </form>

        <div class="row mb-4">
            <div class="bg-gray-100 col-md-12 py-4">
                Custom Loan Guidelines are used to accomplish the following items:
                <ul class="list-unstyled">
                    <li> - Set up warnings for when a loan does not meet select criteria.</li>
                    <li> - Configure available values in select dropdown fields.</li>
                    <li> - Optionally create a marketplace product.</li>
                </ul>
            </div>
        </div>
        <?php if ((array_search('HMLO', array_column($modulesArray, 'moduleCode')) !== FALSE) && ($userRole == 'Manager') && $allowPCUsersToMarketPlace == 1) { ?>
            <div class="row mb-4">
                <form id="mpImportData" class="col-md-12">
                    <div class="row">
                        <div class="col-md-3"><label class="font-weight-bold">View Sample Template</label>
                            <a class="excel btn btn-secondary btn-sm exportXLS far fa-file-excel text-success tooltipClass"
                               target="_blank"
                               href="https://docs.google.com/spreadsheets/d/17lP7iXjkbh5wILzxg9BBMq_D9QodHJkks46DAyV_5n4/edit?userstoinvite=chris%40lendingwise.com&ts=6040fdb8&actionButton=1#gid=0"
                               title="Click to Download Sample Template"></a></div>
                        <div class="col-md-3"><input type="FILE" class="form-control" name="mpfileSrc" id="mpfileSrc">
                        </div>
                        <div class="col-md-3"><input class="btn btn-primary" type="button" name="importmpdata"
                                                     id="importmpdata"
                                                     value="Import Data" onclick="importMPData()"></div>
                    </div>
                </form>
            </div>
        <?php } ?>
        <?php
        if (count($basicInfoArray) > 0) {
            for ($i = 0;
                 $i < count($basicInfoArray);
                 $i++) {

                // print_r($basicInfoArray[$i]);
                //exit;
                $loanProgram = [];
                $cls = '';
                $propertyTypeName = [];
                $futurePropertyTypeName = [];
                $BLID = 0;
                $extensionOptionName = [];
                $extnOption = [];
                $niches = [];
                $propertyType = [];
                $futurePropertyType = [];
                $serviceType = [];
                $state = [];
                $stateName = [];
                $nichesName = [];
                $transactionType = [];
                $marketPlaceLoanProgram = [];
                $marketPlaceLoanProgranNotification = [];
                $Amortization = [];
                $rateLockPeriodData = [];

                $BLID = trim($basicInfoArray[$i]['BLID']);
                $enableRehabConstruction = $basicInfoArray[$i]['enableRehabConstruction'];

                $hideRehabConVal = '';
                if ($enableRehabConstruction == 0) {
                    $hideRehabConVal = ' hideRehabList ';
                }

                /** Get the Loan Program Name start **/
                if (count($loanPgmInfoArray) > 0) {
                    if (array_key_exists($BLID, $loanPgmInfoArray)) {
                        $loanProgram = $loanPgmInfoArray[$BLID];
                    }
                }

                for ($l = 0; $l < count($loanProgram); $l++) {
                    $temploanProgram = '';
                    $temploanProgram = trim($loanProgram[$l]['loanPgm']);
                    for ($j = 0; $j < count($PCLMRClientTypeInfoArray); $j++) {
                        if (trim($PCLMRClientTypeInfoArray[$j]['LMRClientType']) == $temploanProgram) {
                            $serviceType[] = trim($PCLMRClientTypeInfoArray[$j]['serviceType']);
                        }
                    }
                }

                /** Get the Loan Program Name End **/

                if (count($rateLockPeriodInfoArray) > 0) {
                    if (array_key_exists($BLID, $rateLockPeriodInfoArray)) {
                        $rateLockPeriodData = $rateLockPeriodInfoArray[$BLID];
                    }
                }

                /** Get the Property Type Name start **/
                if (count($loanPropertyTypeInfoArray) > 0) {
                    $propertyTypeKeyArray = [];
                    if (array_key_exists($BLID, $loanPropertyTypeInfoArray)) {
                        $propertyType = $loanPropertyTypeInfoArray[$BLID];
                    }
                    if (count(GpropertyTypeNumbArray::$GpropertyTypeNumbArray) > 0) {
                        $propertyTypeKeyArray = array_keys(GpropertyTypeNumbArray::$GpropertyTypeNumbArray);
                    }
                    foreach ($propertyType as $propertyTypeValue) {
                        $tempPropType = '';
                        $tempPropType = trim($propertyTypeValue['propertyType']);
                        foreach ($propertyTypeKeyArray as $propertyTypeKey) {
                            if (trim($propertyTypeKey) == $tempPropType) {
                                $propertyTypeName[] = trim(GpropertyTypeNumbArray::$GpropertyTypeNumbArray[$propertyTypeKey]);
                            }
                        }
                    }
                }
                /** Get the Property Type Name end **/

                /** Get the Future Property Type Name start **/
                if (count($loanFuturePropertyTypeInfoArray) > 0) {
                    $futurePropertyTypeKeyArray = [];
                    if (array_key_exists($BLID, $loanFuturePropertyTypeInfoArray)) {
                        $futurePropertyType = $loanFuturePropertyTypeInfoArray[$BLID];
                    }
                    if (count(Property::$propertyFuturePropertyType) > 0) {
                        $futurePropertyTypeKeyArray = array_keys(Property::$propertyFuturePropertyType);
                    }
                    foreach ($futurePropertyType as $futurePropertyTypeValue) {
                        $tempPropType = '';
                        $tempPropType = trim($futurePropertyTypeValue['futurePropertyType']);
                        foreach ($futurePropertyTypeKeyArray as $futurePropertyTypeKey) {
                            if (trim($futurePropertyTypeKey) == $tempPropType) {
                                $futurePropertyTypeName[] = trim(Property::$propertyFuturePropertyType[$futurePropertyTypeKey]);
                            }
                        }
                    }
                }
                /** Get the Future Property Type Name end **/

                $entityTypeData = [];
                if (count($entityTypeInfoArray ?? []) > 0) {
                    foreach ($entityTypeInfoArray as $etKey => $etVal) {
                        if ($BLID == $etVal['BLID']) {
                            $entityTypeData[] = $etVal['entityType'];
                        }
                    }
                }
                /** Get the Extension Options Name start **/


                if (count($extnOptionInfoArray) > 0) {
                    if (array_key_exists($BLID, $extnOptionInfoArray)) $extnOption = $extnOptionInfoArray[$BLID];
                }

                if (count($glHMLOExtensionOption) > 0) {
                    $glHMLOExtensionOptionKey = array_keys($glHMLOExtensionOption);
                }
                for ($l = 0; $l < count($extnOption); $l++) {
                    $tempExtnOption = '';
                    $tempExtnOption = trim($extnOption[$l]['extnOption']);
                    for ($j = 0; $j < count($glHMLOExtensionOptionKey); $j++) {
                        if (trim($glHMLOExtensionOptionKey[$j]) == $tempExtnOption) {
                            $extensionOptionName[] = trim($glHMLOExtensionOption[$glHMLOExtensionOptionKey[$j]]);
                        }
                    }
                }


                /** Get the Extension Options Name end **/

                /** Get the State Name start **/

                if (count($loanStateInfoArray) > 0) {
                    if (array_key_exists($BLID, $loanStateInfoArray)) {
                        $state = $loanStateInfoArray[$BLID];
                    }
                }
                for ($l = 0; $l < count($state); $l++) {
                    $tempState = '';
                    $tempState = trim($state[$l]['stateCode']);
                    for ($j = 0; $j < count($stateArray); $j++) {
                        if (trim($stateArray[$j]['stateCode']) == $tempState) {
                            $stateName[] = trim($stateArray[$j]['stateName']);
                        }
                    }
                }

                /** Get the State Name end **/

                /** Get the Niches Name start **/

                if (count($loanNichesInfoArray) > 0) {
                    if (array_key_exists($BLID, $loanNichesInfoArray)) {
                        $niches = $loanNichesInfoArray[$BLID];
                    }
                }
                for ($l = 0; $l < count($niches); $l++) {
                    $tempNiches = '';
                    $tempNiches = trim($niches[$l]['nichesID']);
                    for ($j = 0; $j < count($PCNichesArray); $j++) {
                        if (trim($PCNichesArray[$j]['NID']) == $tempNiches) {
                            $nichesName[] = trim($PCNichesArray[$j]['niches']);
                        }
                    }
                }


                if (count($transactionTypeInfoArray) > 0) {
                    if (array_key_exists($BLID, $transactionTypeInfoArray)) {
                        $transactionType = $transactionTypeInfoArray[$BLID];
                    }
                }


                if (count($marketPlaceLoanProgramInfoArray) > 0) {
                    if (array_key_exists($BLID, $marketPlaceLoanProgramInfoArray)) {
                        $marketPlaceLoanProgram = $marketPlaceLoanProgramInfoArray[$BLID];
                    }
                }

                if (count($marketPlaceLoanProgranNotificationInfoArray) > 0) {
                    if (array_key_exists($BLID, $marketPlaceLoanProgranNotificationInfoArray)) {
                        $marketPlaceLoanProgranNotification = $marketPlaceLoanProgranNotificationInfoArray[$BLID];
                    }
                }

                $AmortizationVal = [];
                if (count($loanAmortizationInfoArray) > 0) {
                    if (array_key_exists($BLID, $loanAmortizationInfoArray)) {
                        $Amortization = $loanAmortizationInfoArray[$BLID];
                        foreach ($Amortization as $eachAmortization) {
                            $AmortizationVal[] = $eachAmortization['AmortizationVal'];
                        }
                    }

                }
                $rateLockPeriodVal = [];
                if (count($rateLockPeriodData) > 0) {
                    foreach ($rateLockPeriodData as $eachRateLockPeriod) {
                        $rateLockPeriodVal[] = $eachRateLockPeriod['rateLockPeriod'];
                    }
                }

                $loanTermInfo = $loanOccupancyInfo = [];
                if (count($loanTermInfoArray) > 0) {
                    if (array_key_exists($BLID, $loanTermInfoArray)) {
                        $loanTermInfo = $loanTermInfoArray[$BLID];
                    }
                }
                if (count($loanOccupancyInfoArray) > 0) {
                    if (array_key_exists($BLID, $loanOccupancyInfoArray)) {
                        $loanOccupancyInfo = $loanOccupancyInfoArray[$BLID];
                    }
                }
                $exitStrategyInfo = !empty($exitStrategyInfoArray[$BLID]) ? array_column($exitStrategyInfoArray[$BLID] , 'exitStrategy') : [];


                /** Get the Niches Name end **/
                if ($i % 2 == 0) {
                    $cls = 'even';
                }
                ?>
                <div class="card card-custom customLoanGuidelineCard_<?php echo $i; ?>">
                    <div class="card-header card-header-tabs-line bg-gray-100  ">
                        <div class="card-title text-truncate tooltipClass"
                             title="<?php echo implode(', ', array_unique($serviceType)); ?>">
                            <h6 class="card-label  text-truncate"> Custom
                                Guidelines <?php if (count($marketPlaceLoanProgram) > 0) {
                                    echo '& Marketplace';
                                    $hideIfMarketPlaceYesClass = 'hideIfMarketPlaceYesClass';
                                    $hideIfMarketPlaceNoClass = '';
                                } else {
                                    $hideIfMarketPlaceYesClass = '';
                                    $hideIfMarketPlaceNoClass = 'hideIfMarketPlaceNoClass';
                                } ?> <?php echo $i + 1; ?>
                                - <?php echo implode(', ', array_unique($serviceType)); ?>
                            </h6>
                        </div>
                        <div class="card-toolbar ">
                            <a href="javascript:void(0);"
                               class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                               data-card-tool="toggle"
                               data-section="customLoanGuidelineCard_<?php echo $i; ?>"
                               data-toggle="tooltip" data-placement="top" title=""
                               data-original-title="Toggle Card">
                                <i class="ki ki-arrow-down icon-nm"></i>
                            </a>
                        </div>
                    </div>
                    <div class="card-body customLoanGuidelineCard_<?php echo $i; ?>_body" style="display: none;">
                        <div class="row form-group ">
                            <div class="col-md-12 bg-gray-100   text-center justify-content-around">
                                <div class="row">
                                    <div class="col-md-4"></div>
                                    <div class="col-md-4 align-self-center"><span
                                                class="font-weight-bold align-items-center align-self-center"><u>Custom Guidelines <?php echo $i + 1; ?></u></span>
                                    </div>
                                    <div class="col-md-4 text-right">
                                        <a class="btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon  tooltipClass"
                                           style="text-decoration:none;"
                                           data-href="<?php echo CONST_URL_POPS; ?>addBasicLoanTerms.php"
                                           data-id="PCID=<?php echo cypher::myEncryption($assignedPCID) ?>&BLID=<?php echo cypher::myEncryption($BLID) ?>&showSaveBtn=1"
                                           title="Click to edit"
                                           data-name="Edit Custom Loan Guidelines"
                                           data-wsize="modal-xl" data-toggle="modal" data-target="#exampleModal1">
                                            <i class="fa fa-edit text-success"></i>
                                        </a>
                                        <a class="btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 tooltipClass"
                                           style="text-decoration:none;"
                                           href="javascript:deleteBasicLoanTerm('<?php echo cypher::myEncryption($BLID) ?>','<?php echo cypher::myEncryption($assignedPCID) ?>');"
                                           alt="Click to delete" title="Click to delete">
                                            <i class="flaticon2-trash text-danger"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row  form-group <?php echo $cls; ?> odd">
                            <div class="col-md-2 col-sm-4 col-xs-12">
                                <h5>Loan Program : </h5>
                            </div>
                            <div class="col-md-4 col-sm-8 col-xs-12">
                                <?php echo implode(', ', array_unique($serviceType)); ?>
                            </div>
                            <div class="col-md-2 col-sm-8 col-xs-12">
                                <h5>Transaction Type :</h5>
                            </div>
                            <div class="col-md-4 col-sm-8 col-xs-12">
                                <?php echo str_replace(',', ', ', Arrays::implode2dArray(',', $transactionType, 'transactionType')); ?>
                            </div>
                        </div>


                        <?php if (count($marketPlaceLoanProgram) > 0 && $allowPCUsersToMarketPlace == 1) { ?>
                            <div class="row   form-group <?php echo $cls; ?>">
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>MarketPlace Loan Category :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    <?php echo str_replace(',', ', ', Arrays::implode2dArray(',', $marketPlaceLoanProgram, 'LoanProgramName')); ?>
                                </div>
                            </div>
                        <?php } ?>

                        <div class="row  form-group ">
                            <div class="col-md-2 col-sm-4 col-xs-12">
                                <h5>Min Loan Amount :</h5>
                            </div>
                            <div class="col-md-4 col-sm-8 col-xs-12">
                                $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['minLoanAmount']) ?>
                            </div>
                            <div class="col-md-2 col-sm-8 col-xs-12">
                                <h5>Max Loan Amount :</h5>
                            </div>
                            <div class="col-md-4 col-sm-8 col-xs-12">
                                $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['maxLoanAmount']) ?>
                            </div>
                        </div>

                        <div class="row  form-group ">
                            <div class="col-md-3 col-sm-4 col-xs-12">
                                <div class="row ">
                                    <div class="col-md-6"><h5>Min Rate :</h5></div>
                                    <div class="col-md-6">
                                        <?php echo $basicInfoArray[$i]['minRate'] ?>&nbsp;%
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-8 col-xs-12">
                                <div class="row ">
                                    <div class="col-md-6"><h5>Max Rate :</h5></div>
                                    <div class="col-md-6">
                                        <?php echo $basicInfoArray[$i]['maxRate'] ?>&nbsp;%
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-8 col-xs-12">
                                <div class="row ">
                                    <div class="col-md-9"><h5>Min Origination Points :</h5></div>
                                    <div class="col-md-3"><?php echo $basicInfoArray[$i]['minPoints'] ?></div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-8 col-xs-12">
                                <div class="row ">
                                    <div class="col-md-9"><h5>Max Origination Points :</h5></div>
                                    <div class="col-md-3"><?php echo $basicInfoArray[$i]['maxPoints'] ?></div>
                                </div>
                            </div>
                        </div>

                        <div class="row  form-group ">
                            <div class="col-md-3 col-sm-8 col-xs-12">
                                <div class="row ">
                                    <div class="col-md-6"><h5>Min Mid Fico :</h5></div>
                                    <div class="col-md-6"><?php echo $basicInfoArray[$i]['minMidFico'] ?></div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-8 col-xs-12">
                                <div class="row ">
                                    <div class="col-md-6"><h5>Min DSCR::</h5></div>
                                    <div class="col-md-6"><?php echo $basicInfoArray[$i]['minDSCR'] ?> %</div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-8 col-xs-12">
                                <div class="row d-none">
                                    <div class="col-md-6"><h5>Max Mid Fico :</h5></div>
                                    <div class="col-md-6"><?php echo $basicInfoArray[$i]['maxMidFico'] ?></div>
                                </div>
                            </div>
                        </div>


                        <div class="row  form-group <?php echo $hideRehabConVal ?>">
                            <div class="col-md-4 col-sm-4 col-xs-12">
                                <h5>Min # of properties completed for fix and flip :</h5>
                            </div>
                            <div class="col-md-2 col-sm-4 col-xs-12">
                                &nbsp;<?php echo $basicInfoArray[$i]['minPropertyForFixFlop'] ?>
                            </div>
                            <div class="col-md-4 col-sm-4 col-xs-12 d-none">
                                <h5>Max # of properties completed for fix and flip :</h5>
                            </div>
                            <div class="col-md-2 col-sm-4 col-xs-12 d-none">
                                &nbsp;<?php echo $basicInfoArray[$i]['maxPropertyForFixFlop'] ?>
                            </div>
                        </div>


                        <div class="row  form-group odd <?php echo $hideRehabConVal ?>">
                            <div class="col-md-4 col-sm-4 col-xs-12">
                                <h5>Min # of properties completed for ground up construction :</h5>
                            </div>
                            <div class="col-md-2 col-sm-4 col-xs-12">
                                &nbsp;<?php echo $basicInfoArray[$i]['minPropertyForGrndConst'] ?>
                            </div>
                            <div class="col-md-4 col-sm-4 col-xs-12 d-none">
                                <h5>Max # of properties completed for ground up construction :</h5>
                            </div>
                            <div class="col-md-2 col-sm-4 col-xs-12 d-none">
                                &nbsp;<?php echo $basicInfoArray[$i]['maxPropertyForGrndConst'] ?>
                            </div>
                        </div>


                        <div class="row  form-group <?php echo $hideIfMarketPlaceYesClass; ?>">
                            <div class="col-md-2 col-sm-4 col-xs-12">
                                <h5>Credit Score Range :</h5>
                            </div>
                            <div class="col-md-10 col-sm-8 col-xs-12">
									<span style="font-weight:bold;">&nbsp;&nbsp;<?php echo $basicInfoArray[$i]['PCBorrCreditScoreRange']; ?>
                            </div>
                        </div>


                        <div class="row  form-group odd">
                            <div class="col-md-2 col-sm-8 col-xs-12">
                                <h5>Property Type :</h5>
                            </div>
                            <div class="col-md-4 col-sm-8 col-xs-12">
                                <?php echo implode(', ', $propertyTypeName); ?>
                            </div>
                            <div class="col-md-2 col-sm-8 col-xs-12">
                                <h5>Future Property Type :</h5>
                            </div>
                            <div class="col-md-4 col-sm-8 col-xs-12">
                                <?php echo implode(', ', $futurePropertyTypeName); ?>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-md-2 col-sm-8 col-xs-12">
                                <h5>Entity Type :</h5>
                            </div>
                            <div class="col-md-4 col-sm-8 col-xs-12">
                                <?php echo implode(', ', $entityTypeData); ?>
                            </div>
                            <div class="col-md-2 col-sm-4 col-xs-12 <?php echo $hideIfMarketPlaceYesClass; ?>">
                                <h5>Extension Options :</h5>
                            </div>
                            <div class="col-md-4 col-sm-8 col-xs-12 <?php echo $hideIfMarketPlaceYesClass; ?>">
                                <?php echo implode(', ', $extensionOptionName); ?>
                            </div>
                        </div>


                        <div class="row  form-group">
                            <div class="col-md-2 col-sm-8 col-xs-12">
                                <h5>Loan Term :</h5>
                            </div>
                            <div class="col-md-4 col-sm-8 col-xs-12">
                                <?php echo str_replace(',', ', ', Arrays::implode2dArray(',', $loanTermInfo, 'loanTerm')); ?>
                            </div>
                            <div class="col-md-2 col-sm-4 col-xs-12">
                                <h5>Borrower Occupancy :</h5>
                            </div>
                            <div class="col-md-4 col-sm-8 col-xs-12">
                                <?php echo str_replace(',', ', ', Arrays::implode2dArray(',', $loanOccupancyInfo, 'occupancy')); ?>
                            </div>
                        </div>


                        <div class="row  form-group odd">
                            <div class="col-md-2 col-sm-8 col-xs-12 <?php echo $hideIfMarketPlaceYesClass; ?>">
                                <h5>Down Payment % :</h5>
                            </div>
                            <div class="col-md-4 col-sm-8 col-xs-12 <?php echo $hideIfMarketPlaceYesClass; ?>">
                                <?php echo $basicInfoArray[$i]['downPaymentPercentage']; ?> %
                            </div>
                            <div class="col-md-2 col-sm-4 col-xs-12 <?php echo $hideRehabConVal ?>">
                                <h5>Rehab/Construction % Financed :</h5>
                            </div>
                            <div class="col-md-4 col-sm-8 col-xs-12 <?php echo $hideRehabConVal ?>">
                                <?php echo $basicInfoArray[$i]['rehabCostPercentageFinanced']; ?> %
                            </div>
                        </div>


                        <div class="row  form-group">
                            <div class="col-md-2 col-sm-8 col-xs-12">
                                <h5>State :</h5>
                            </div>
                            <div class="col-md-4 col-sm-8 col-xs-12">
                                <?php echo implode(', ', $stateName) ?>
                            </div>
                            <div class="col-md-2 col-sm-8 col-xs-12 <?php echo $hideIfMarketPlaceNoClass; ?>">
                                <h5>Niches :</h5>
                            </div>
                            <div class="col-md-4 col-sm-8 col-xs-12 <?php echo $hideIfMarketPlaceNoClass; ?>">
                                <?php echo implode(', ', $nichesName) ?>
                            </div>
                        </div>


                        <div class="row  form-group odd">
                            <div class="col-md-2 col-sm-8 col-xs-12">
                                <h5>Amortization :</h5>
                            </div>
                            <div class="col-md-4 col-sm-8 col-xs-12">
                                <?php echo implode(', ', $AmortizationVal) ?>
                            </div>
                            <div class="col-md-2 col-sm-8 col-xs-12">
                                <h5>Rate Lock Period :</h5>
                            </div>
                            <div class="col-md-4 col-sm-8 col-xs-12 ">
                                <?php echo implode(', ', $rateLockPeriodVal); ?>
                            </div>
                        </div>
                        <div class="row  form-group odd">

                            <div class="col-md-2 col-sm-8 col-xs-12 ">
                                <h5>Loan/Exit Plan :</h5>
                            </div>
                            <div class="col-md-4 col-sm-8 col-xs-12 ">
                                <?php echo implode(', ', $exitStrategyInfo) ?>
                            </div>
                        </div>

                        <?php if (count($transactionType) > 0) { ?>
                            <div class="row  form-group odd">
                                <div class="col-md-12 bg-gray-100 align-self-center ">
                                    <h4>Max Acquisition LTV :</h4>
                                </div>
                            </div>

                            <div class="row  form-group odd ">
                                <div class=" col-md-2  font-weight-bold <?php echo $hideRehabConVal ?>">Total Loan to
                                    Cost
                                </div>
                                <div class=" col-md-1 <?php echo $hideRehabConVal ?>">
                                    <h5><?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['totalLTC']) ?>
                                        %</h5>
                                </div>
                                <?php
                                for ($j = 0; $j < count($transactionType); $j++) {
                                    $tempTransType = '';
                                    $tempTransType = trim($transactionType[$j]['transactionType']);
                                    ?>
                                    <div class=" col-md-2  font-weight-bold"><?php echo $tempTransType; ?></div>
                                    <div class=" col-md-1">
                                        <h5><?php echo Currency::formatDollarAmountWithDecimal($transactionType[$j]['maxLTV']) ?>
                                            &nbsp;%</h5></div>
                                    <?php
                                    if ($tempTransType == 'Purchase' || $tempTransType == 'Commercial Purchase') { ?>
                                        <div class=" col-md-2 font-weight-bold <?php echo $hideRehabConVal ?>  ">After
                                            Rehab
                                            Value
                                            for <?php echo $tempTransType; ?>
                                        </div>
                                        <div class=" col-md-1  <?php echo $hideRehabConVal ?>">
                                            <h5><?php echo Currency::formatDollarAmountWithDecimal($transactionType[$j]['maxLTVAfterRehab']) ?>
                                                &nbsp;%</h5></div>
                                        <?php
                                    }
                                }
                                ?>
                            </div>
                        <?php } ?>

                        <div class="<?php echo $hideIfMarketPlaceYesClass; ?>">
                            <div class="row  form-group ">
                                <div class="col-md-12 bg-gray-100 align-self-center mb-2 ">
                                    <h4>Default Fees:</h4>
                                </div>
                            </div>
                            <div class="row  form-group  <?php echo $hideIfMarketPlaceYesClass; ?>">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Origination Points @ <span
                                                style="color:#044170;font-weight:bold;"><?php echo $basicInfoArray[$i]['originationPointsRate'] ?></span>
                                        Point</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['originationPointsValue']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Broker Points @ <span
                                                style="color:#044170;font-weight:bold;"><?php echo $basicInfoArray[$i]['brokerPointsRate'] ?></span>
                                        Points</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['brokerPointsValue']); ?>
                                </div>
                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Closing Cost Financing fee:</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['closingCostFinancingFee']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Attorney fee:</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['attorneyFee']); ?>
                                </div>
                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Application Fee:</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['applicationFee']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Estimated Title Insurance Fees</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['estdTitleClosingFee']); ?>
                                </div>
                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Processing Fees:</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['processingFee']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Appraisal Fee:</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['appraisalFee']); ?>
                                </div>
                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Draws Set Up Fee:</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['drawsSetUpFee']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Draws Fee:</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['drawsFee']); ?>
                                </div>
                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Valuation - BPO:</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['valuationBPOFee']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Valuation - AVM:</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['valuationAVMFee']); ?>
                                </div>
                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Credit Report :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['creditReportFee']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Background Check:</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['backgroundCheckFee']); ?>
                                </div>
                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Flood Certificate :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['floodCertificateFee']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Document Preparation :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['documentPreparationFee']); ?>
                                </div>
                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Wire Fee :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['wireFee']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Servicing Set Up Fee :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['servicingSetUpFee']); ?>
                                </div>
                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Tax Service :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['taxServiceFee']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Flood Service :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['floodServiceFee']); ?>
                                </div>
                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Inspection Fees :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['inspectionFees']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Project Feasibility :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['projectFeasibility']); ?>
                                </div>
                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Due Diligence :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['dueDiligence']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Ucc/Lien Search :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['UccLienSearch']); ?>
                                </div>
                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Lender Credit to Offset 3rd Party Fees :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['thirdPartyFees']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Other :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['otherFee']); ?>
                                </div>
                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Escrow Fees :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['escrowFees']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Recording Fee :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['recordingFee']); ?>
                                </div>
                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Underwriting Fees :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['underwritingFees']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Property tax :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['propertyTax']); ?>
                                </div>
                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Discount Fee :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['bufferAndMessengerFee']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Travel Notary Fee :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['travelNotaryFee']); ?>
                                </div>
                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Pre paid Interest :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['prePaidInterest']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Real Estate Taxes :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['realEstateTaxes']); ?>
                                </div>
                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Insurance Premium :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['insurancePremium']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Pay Off Liens/Creditors :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['payOffLiensCreditors']); ?>
                                </div>

                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Wire Transfer Fee to Title :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['wireTransferFeeToTitle']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Wire Transfer Fee to Escrow :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['wireTransferFeeToEscrow']); ?>
                                </div>
                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Past Due Property Taxes :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['pastDuePropertyTaxes']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Survey :</h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['survey']); ?>
                                </div>

                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Wholesale Admin Fee :</h5>
                                </div>
                                <div class="col-md-10 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['wholeSaleAdminFee']); ?>
                                </div>
                            </div>
                            <div class="row  form-group">
                                <div class="col-md-2 col-sm-4 col-xs-12">
                                    <h5>Tax impounds @ <span
                                                class="h5"><?php echo $basicInfoArray[$i]['taxImpoundsMonth']; ?> @ $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['taxImpoundsMonthAmt']); ?></span>
                                    </h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['taxImpoundsFee']); ?>
                                </div>
                                <div class="col-md-2 col-sm-8 col-xs-12">
                                    <h5>Ins impounds @ <span
                                                class="h5"><?php echo $basicInfoArray[$i]['insImpoundsMonth']; ?>@ $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['insImpoundsMonthAmt']); ?></span>
                                    </h5>
                                </div>
                                <div class="col-md-4 col-sm-8 col-xs-12">
                                    $ <?php echo Currency::formatDollarAmountWithDecimal($basicInfoArray[$i]['insImpoundsFee']); ?>
                                </div>
                            </div>
                        </div>

                        <div class="row  form-group odd">
                            <div class="col-md-3 "><h5> Loan Program Details :</h5>
                            </div>
                            <div class="col-md-9 border p-4">
                                <?php echo urldecode($basicInfoArray[$i]['loanPgmDetails']); ?>
                            </div>
                        </div>

                        <?php if ($basicInfoArray[$i]['reqForLoanProUnderwriting'] != '') { ?>
                            <div class="row form-group ">
                                <div class="col-md-3">
                                    <h5>Additional Terms &amp; Requirements for Loan Processing &amp;
                                        Underwriting :</h5>
                                </div>
                                <div class="col-md-9 border p-4"><?php echo urldecode($basicInfoArray[$i]['reqForLoanProUnderwriting']); ?></div>
                            </div>
                        <?php } ?>
                    </div>
                </div>
                <?php
            }
        }
        ?>
    </div>
</div>
<style>
    .hideRehabList {
        display: none;
    }

    .hideIfMarketPlaceYesClass {
        display: none;
    }

    .hideIfMarketPlaceNoClass {
        display: none;
    }
</style>
<script>
    function importMPData() {
        let _mpfileSrc = $("#mpfileSrc");
        let ext = _mpfileSrc.val().split('.').pop().toLowerCase();

        if (_mpfileSrc.get(0).files.length == 0) {
            toastrNotification("Please select the file", 'error');
            return false;
        } else if (ext !== 'csv' && ext !== 'xlsx' && ext !== 'xls') {
            toastrNotification("Please upload CSV, XLSX, or XLS file", 'error');
            return false;
        } else {

            let fd = new FormData(document.getElementById("mpImportData"));
            fd.append('file', _mpfileSrc.get(0)); // since this is your file input

            $.ajax({
                url: "importMPData.php",
                type: "post",
                dataType: 'json',
                processData: false, // important
                contentType: false, // important
                data: fd,
                success: function (text) {

                    if (text == "format") {
                        toastrNotificationWithReloadDelay("upload correct format file", 'error');
                    } else if (text == "success") {
                        toastrNotificationWithReloadDelay("Data imported successfully", 'success');
                    } else {
                        toastrNotificationWithReloadDelay(text, 'error');
                    }
                }
            });
        }
    }
</script>
