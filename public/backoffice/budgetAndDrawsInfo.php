<?php

use models\constants\gl\glUserGroup;
use models\Controllers\loanForm;
use models\cypher;
use models\lendingwise\tblBudgetAndDraws;
use models\lendingwise\tblFileHMLO;
use models\standard\Currency;
use models\standard\Strings;

require 'HMLOLoanInfoVars.php';
$GCFirstName = $GCLastName = $GCLicense = $GCEmail = $GCPhone = '';
$GCContactID = 0;
$GCCompanyName = '';
global $fileContacts, $budgetAndDrawsInfo, $LMRId, $oldFPCID, $userGroup, $allowToEdit, $tabIndex,
       $rehabCostFinanced, $userRole, $stateArray, $initialLoanAmount, $rehabCost, $tLAToolTip, $totalLoanAmount, $currentLoanBalance,
       $availableBudget, $drawsFee, $drawFundCalOnDrawsFee, $defaultDrawFee;

$POPSURL = CONST_URL_POPS;
$siteUrl = CONST_SITE_URL;
/**
 * Description : Bring GC into contacts list
 * Developer   : Viji
 * Author      : Awatasoftsys
 * Date        : Feb 01, 2018
 **/

$GCContacts = [];

if (array_key_exists('General Contractor', $fileContacts)) {
    $GCContacts = $fileContacts['General Contractor'];
}

if (count($GCContacts) > 0) {
    $GCContactID = trim($GCContacts['CID']);
    $GCFirstName = trim($GCContacts['contactName']);
    $GCLastName = trim($GCContacts['contactLName']);
    $GCEmail = trim($GCContacts['email']);
    $GCPhone = trim($GCContacts['phone']);
    $GCCompanyName = trim($GCContacts['companyName']);
    $GCLicense = trim($GCContacts['licenseNo']);
}
$gurCnt = count($budgetAndDrawsInfo ?? []);
if ($gurCnt == 0) $gurCnt = 1;

$totalDrawsFunded = $projectCompletion = 0;

$enResId = cypher::myEncryption(Strings::showField('LMRResponseId', 'ResponseInfo'));
$enExID = cypher::myEncryption(Strings::showField('FBRID', 'LMRInfo'));
$enLMRID = cypher::myEncryption($LMRId);
$borrowerFName = Strings::showField('borrowerFName', 'LMRInfo');
$borrowerLName = Strings::showField('borrowerLName', 'LMRInfo');

$budgetAndDrawsObjects = tblBudgetAndDraws::Search(['LMRId' => $LMRId]);
if (!sizeof($budgetAndDrawsObjects)) {
    $budgetAndDrawsObjects = [new tblBudgetAndDraws()];
}

foreach ($budgetAndDrawsObjects as $aGur => $budgetAndDrawsObject) {
    $totalDrawsFunded += $budgetAndDrawsObject->amountAddedToTotalDrawsFunded;
}

if ($rehabCostFinanced > 0) {
    $projectCompletion = Strings::replaceCommaValues($totalDrawsFunded) / Strings::replaceCommaValues($rehabCostFinanced) * 100;
}

$currentLoanBalanceDraws = $currentLoanBalance + $totalDrawsFunded;
?>
<!-- Project Summary Section Start -->
<div class="card card-custom projectSummaryBudgetAndDraws ">
    <input type="hidden" name="curDate" id="curDate" value="<?php echo date('m-d-Y'); ?>">
    <input type="hidden" name="curDate" id="curDateMMDDYYYY" value="<?php echo date('m/d/Y'); ?>">
    <input type="hidden" name="currentLoanBalanceJS" id="currentLoanBalanceJS" value="<?php echo $currentLoanBalance; ?>">

    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                Project Summary
            </h3>
        </div>
        <div class="card-toolbar ">
            <a href="javascript:void(0);"
               class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
               data-card-tool="toggle"
               data-section="projectSummaryBudgetAndDraws"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none" data-card-tool="reload"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                <i class="ki ki-close icon-nm"></i>
            </a>
        </div>
    </div>
    <div class="card-body projectSummaryBudgetAndDraws_body">
        <div class="row">
            <?php if ($userGroup == glUserGroup::USER_GROUP_EMPLOYEE || $userGroup == glUserGroup::USER_GROUP_SUPER) { ?>
                <div class="form-group col-md-3">
                    <label class="col-md-12 font-weight-bold" for="projectName">Project Name : <span class="h6"
                                                                                                     id="projectName"><?php echo Strings::showField('projectName', 'ResponseInfo'); ?></span></label>
                </div>
            <?php } ?>

            <div class="form-group col-md-3">
                <label class="col-md-12  font-weight-bold" for="propertyAddress">Property Address : <span class="h6"
                                                                                                          id="propertyAddress"><?php echo Strings::showField('propertyAddress', 'LMRInfo'); ?></span></label>
            </div>

            <div class="form-group col-md-2">
                <label class="col-md-12  font-weight-bold" for="propertyCity">City : <span class="h6"
                                                                                           id="propertyCity"><?php echo Strings::showField('propertyCity', 'LMRInfo'); ?></span></label>
            </div>

            <div class="form-group col-md-2">
                <label class="col-md-12  font-weight-bold" for="propertyState">State : <span class="h6"
                                                                                             id="propertyState"><?php echo Strings::getStateFullName($stateArray, (Strings::showField('propertyState', 'LMRInfo'))); ?></span></label>
            </div>

            <div class="form-group col-md-2">
                <label class="col-md-12  font-weight-bold" for="propertyZip">Zip : <span class="h6"
                                                                                         id="propertyZip"><?php echo Strings::showField('propertyZip', 'LMRInfo'); ?></span></label>
            </div>

            <div class="form-group col-md-3">
                <label class="col-md-12  font-weight-bold" for="homeValue">Original As-Is Value : <br/><span class="h6"
                                                                                                             id="homeValue"> $ <?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('homeValue', 'LMRInfo')); ?></span></label>
            </div>

            <div class="form-group col-md-3">
                <label class="col-md-12  font-weight-bold" for="assessedValue">ARV : <br/><span class="h6"
                                                                                                id="assessedValue"> $ <?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('assessedValue', 'listingRealtorInfo')); ?></span></label>
            </div>

            <div class="form-group col-md-3">
                <label class="col-md-12  font-weight-bold" for="initialLoanAmount">Initial Loan Amount :<br/> <span
                            class="h6"
                            id="initialLoanAmount"> $ <?php echo Currency::formatDollarAmountWithDecimal($initialLoanAmount) ?></span></label>
            </div>

            <div class="form-group col-md-3">
                <label class="col-md-12  font-weight-bold" for="rehabCost">Total Rehab/Const. Cost : <br/><span
                            class="h6"
                            id="rehabCost"> $ <?php echo Currency::formatDollarAmountWithDecimal($rehabCost) ?></span></label>
            </div>

            <div class="form-group col-md-3">
                <label class="col-md-12  font-weight-bold" for="rehabCostFinanced">Rehab/Construction Financed :
                    <br/><span class="h6"
                               id="rehabCostFinanced"> $ <?php echo Currency::formatDollarAmountWithDecimal($rehabCostFinanced) ?></span></label>
            </div>

            <div class="form-group col-md-3">
                <label class="col-md-12  font-weight-bold" for="coTotalAmt">Total Loan Amount : <a
                            class="fa fa-info-circle text-primary tooltipClass"
                            href="javascript:void(0);" title="<?php echo $tLAToolTip; ?>"></a><br/><span
                            class="totalLoanAmount h6"
                            id="coTotalAmt"> $ <?php echo Currency::formatDollarAmountWithDecimal($totalLoanAmount) ?></span></label>
            </div>

            <div class="form-group col-md-3">
                <label class="col-md-12  font-weight-bold" for="currentLoanBalance">Current Loan Balance : &nbsp;<a
                            class="fa fa-info-circle tooltipClass text-primary"
                            href="javascript:void(0);"
                            title="Current Loan Balance = Initial Loan Amount + Pre-paid Interest Reserves + Closing Costs + Funded Draws"></a><br><span
                            class="currentLoanBalance h6"
                            id="currentLoanBalance"
                            name="currentLoanBalance"> $ <?php echo Currency::formatDollarAmountWithDecimal($currentLoanBalanceDraws) ?></span></label>
            </div>

            <div class="form-group col-md-3">
                <label class="col-md-12  font-weight-bold" for="totalDrawsFunded">Total Draws Funded : <br/><span
                            class="totalDrawsFunded h6"
                            id="totalDrawsFunded"> $ <?php echo Currency::formatDollarAmountWithDecimal($totalDrawsFunded) ?></span></label>
            </div>
            <div class="form-group col-md-3">
                <label class="col-md-12  font-weight-bold" for="availableBudget">Current Escrow Balance : <a
                            class="fa fa-info-circle  tooltipClass text-primary" style="text-decoration:none;"
                            href="javascript:void(0);" title="Available Budget"></a><br/><span
                            class="availableBudget h6"
                            id="availableBudget"> $ <?php echo Currency::formatDollarAmountWithDecimal($availableBudget) ?></span></label>
            </div>

            <div class="form-group col-md-3">
                <label class="col-md-12  font-weight-bold" for="projectCompletion">Project Completion : <br/><span
                            class="projectCompletion h6"
                            id="projectCompletion"><?php echo Currency::formatDollarAmountWithDecimal($projectCompletion) ?></span></label>
            </div>
        </div>
    </div>
</div>
<!-- Project Summary Section End -->


<!-- Draw Requests Section Start -->
<div class="card card-custom  drawSecSectionBudgetAndDraws ">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                Draw Requests
                <span class="text-danger ml-2">Important Changes
                    <i class="fa fa-info-circle tooltipClass text-danger"
                       title="We have made some changes & bug fixes on the draw request module.
                   The calculations on initial entry will match values after you save.
                   And we updated the total draws funded amount for when you use the auto subtract draw fee check box, which is now defaulted as on. "></i></span>
            </h3>
        </div>
        <div class="card-toolbar ">
            <span class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                  data-card-tool="toggle"
                  data-section="drawSecSectionBudgetAndDraws"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none" data-card-tool="reload"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </span>
            <span class="btn btn-icon btn-sm btn-hover-light-primary d-none"
                  data-card-tool="remove"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="Remove Card">
                <i class="ki ki-close icon-nm"></i>
            </span>
        </div>
    </div>

    <div class="card-body drawSecSectionBudgetAndDraws_body drawSecSection">
        <div class="row form-group ">
            <div class="col-lg-6 col-md-4">
                <div class="row">
                    <label class="col-md-3 font-weight-bold" for="defaultDrawFee">
                        Default Draw Fee
                        <i class="fa fa-info-circle tooltipClass text-primary"
                           title="Save a value here to pre-populate draw fees"></i>
                        <?php if (in_array('defaultDrawFee', loanForm::$changeLogColumns_tblFileHMLO)) { ?>
                            <span
                                    data-primary_key="<?php echo cypher::myEncryption(loanForm::$tblFile->getTblFileHMLO_by_fileID()[0]->HMLOID); ?>"
                                    data-column="<?php echo cypher::myEncryption('defaultDrawFee'); ?>"
                                    data-object="<?php echo cypher::myEncryption(tblFileHMLO::class); ?>"
                                    data-title="Default Draw Fee"
                                    onclick="changeLog.showFor(this);"
                            ><i class="fa fa-clock cursor-pointer"></i></span>
                        <?php } ?>
                    </label>
                    <div class="col-md-4">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   name="defaultDrawFee"
                                   id="defaultDrawFee"
                                   class="form-control "
                                   onblur="currencyConverter(this, this.value);budgetAndDraws.setDefaultDrawFee(this.value);"
                                   placeholder="0.00"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($defaultDrawFee); ?>">
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-4">
                <div class="checkbox-inline ">
                    <label class="checkbox font-weight-bold">
                        <input class="drawFundCalOnDrawsFeeClass"
                               type="checkbox"
                               name="drawFundCalOnDrawsFee"
                               value="1"
                            <?php if ($drawFundCalOnDrawsFee == 1) {
                                echo 'checked';
                            } ?> id="drawFundCalOnDrawsFee">
                        <span></span>
                        Automatically subtract draw fee from borrower funded amount?
                        <i class="fa fa-info-circle tooltipClass text-primary ml-2"
                           title="When disabled, the draw fee is added to the approved draw amount & the total draws funded amount. When enabled, the draw fee is NOT added to the approved draw amount or the total draws funded amount. Its assumed servicing will deduct the draw fee and issue payment to the lender. "></i>
                    </label>
                </div>
            </div>
        </div>
        <?php require_once __DIR__ . '/budgetAndDrawsInfo/DrawRequests.php'; ?>
    </div>
</div>

<?php require_once __DIR__ . '/budgetAndDrawsInfo/propRehabSection.php'; // Rehab/Construction Costs ?>

<div style="text-align: center">
    <input type="submit" class="btn btn-primary btnSave" name="btnSave" id="saveBtn" value="Save"
           tabindex="<?php echo $tabIndex++; ?>">
    <input type="submit" class="btn btn-primary btnSave" name="btnSave" id="saveNextBtn" value="Save & Next"
           tabindex="<?php echo $tabIndex++; ?>" onclick="if(this.disabled==false) {return true;} else {return false;}">
</div>
<style>
    .drawSecAlternate:nth-of-type(odd) {
        background-color: #f3f6f9;
    }

    .drawSecAlternate:nth-of-type(even) {
        background-color: #fff;
    }
</style>
