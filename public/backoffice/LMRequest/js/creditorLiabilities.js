$(document).ready(function () {
    if ($('.cd.secShow').length > 0) { //show title
        $(".CDTitle").show();
    } else { //hide title
        $(".CDTitle").hide();
    }
    //Collection Agency Details
    if ($('.cad.secShow').length > 0) { //show title
        $(".CadTitle").css("display", "block");
    } else { //hide title
        $(".CadTitle").css("display", "none");
    }
});

$(".creditorPhone_mask:enabled").inputmask("mask", {mask: "(999) 999 - 9999 Ext 9999"});
$(".creditorRepPhone_mask:enabled").inputmask("mask", {mask: "(999) 999 - 9999 Ext 9999"});
$(".creditorAgentPhone_mask:enabled").inputmask("mask", {mask: "(999) 999 - 9999 Ext 9999"});
$(".creditorAgentFax_mask:enabled").inputmask("mask", {mask: "(999) 999 - 9999"});
$(".creditorRepFax_mask:enabled").inputmask("mask", {mask: "(999) 999 - 9999"});

//remove the rows

class creditorLiabilities {

    static clearLastFormFields(cnt) {

        $("#clnreFieldsDiv_" + cnt).find('input:text, textarea').val('');
        $('#creditorStatus_' + cnt + " option:selected").prop("selected", false);
        $("#creditorStatus_" + cnt).trigger("chosen:updated");
        $('#creditorType_' + cnt + " option:selected").prop("selected", false);
        $("#creditorType_" + cnt).trigger("chosen:updated");
        $('#accDesc_' + cnt + " option:selected").prop("selected", false);
        $("#accDesc_" + cnt).trigger("chosen:updated");
        $("#creditorAcctTypeOpen_" + cnt).prop('checked', false);
        $("#creditorAcctTypeClose_" + cnt).prop('checked', false);
        $("#creditorAcctStatusCur_" + cnt).prop('checked', false);
        $("#creditorAcctStatusDel_" + cnt).prop('checked', false);
        $("#payAtBeforeClosingYes_" + cnt).prop('checked', false);
        $("#payAtBeforeClosingNo_" + cnt).prop('checked', false);
    }

    static removeLastFormFields(cnt) {
        if ($('.clnreFields').length === 1) {
            creditorLiabilities.clearLastFormFields(cnt);
        } else {
            $("#remClnreFields_" + cnt).remove();
            $("#clnreFieldsDiv_" + cnt).remove();
        }
    }
}

$(document).on('click', '.remClnreFields', function () {

    let removedId = $(this).attr('id').split('_');
    let cnt = parseInt(removedId[1]);

    let id = $(this).attr('data-id');
    let _page = $(this).attr('data-page');

    if (typeof id !== 'undefined' && id !== false && id !== '') {
        $.confirm({
            icon: 'fa fa-warning',
            closeIcon: true,
            title: 'Confirm',
            content: "Are you sure you want to delete?",
            type: 'red',
            backgroundDismiss: true,
            buttons: {
                yes: function () {
                    HTTP.Post('/api/' + _page, {
                        id: id,
                    }, function (resObj) {
                        if (resObj.success) {
                            creditorLiabilities.removeLastFormFields(cnt);
                        } else {
                            toastrNotification(resObj.error, 'error');
                        }
                    });
                },
                cancel: function () {

                },
            }
        });
    } else {
        creditorLiabilities.removeLastFormFields(cnt);
    }

    /*
        if ($('.clnreFields').length === 1) {
            $("#clnreFieldsDiv_" + cnt).find('input:text, textarea').val('');
            $('#creditorStatus_' + cnt + " option:selected").prop("selected", false);
            $("#creditorStatus_" + cnt).trigger("chosen:updated");
            $('#creditorType_' + cnt + " option:selected").prop("selected", false);
            $("#creditorType_" + cnt).trigger("chosen:updated");
            $('#accDesc_' + cnt + " option:selected").prop("selected", false);
            $("#accDesc_" + cnt).trigger("chosen:updated");
            $("#creditorAcctTypeOpen_" + cnt).prop('checked', false);
            $("#creditorAcctTypeClose_" + cnt).prop('checked', false);
            $("#creditorAcctStatusCur_" + cnt).prop('checked', false);
            $("#creditorAcctStatusDel_" + cnt).prop('checked', false);
            $("#payAtBeforeClosingYes_" + cnt).prop('checked', false);
            $("#payAtBeforeClosingNo_" + cnt).prop('checked', false);
        } else {

            let id = $(this).attr('data-id');
            let _page = $(this).attr('data-page');

            if (typeof id !== 'undefined' && id !== false && id !== '') {
                $.confirm({
                    icon: 'fa fa-warning',
                    closeIcon: true,
                    title: 'Confirm',
                    content: "Are you sure you want to delete?",
                    type: 'red',
                    backgroundDismiss: true,
                    buttons: {
                        yes: function () {
                            HTTP.Post('/api/' + _page, {
                                id: id,
                            }, function (resObj) {
                                if (resObj.success) {
                                    $("#remClnreFields_" + cnt).remove(); //remove the red icon
                                    $("#clnreFieldsDiv_" + cnt).remove(); //remove the fields DIV
                                } else {
                                    toastrNotification(resObj.error, 'error');
                                }
                            });
                        },
                        cancel: function () {

                        },
                    }
                });
            } else {
                $("#remClnreFields_" + cnt).remove(); //remove the red icon
                $("#clnreFieldsDiv_" + cnt).remove(); //remove the fields DIV
            }
        }*/
    enableSaveButton();
});
//calculations
//Total Liabilities - Account Balance
$(document).on('blur', '.accBal', function () {
    let cleanData = 0;
    let clTotalLiabilities = 0;
    $('.accBal').each(function () {
        cleanData = $(this).val() !== '' ? replaceCommaValues($(this).val()) : 0;
        clTotalLiabilities += parseFloat(cleanData);
        $("#totalLiabilities").val(clTotalLiabilities.toFixed(2));
    });
});
//Total Liabilities - Total Monthly Payments
$(document).on('blur', '.minPay', function () {
    let cleanData = 0;
    let clTotalMonthlyPayment = 0;
    $('.minPay').each(function () {
        cleanData = $(this).val() !== '' ? replaceCommaValues($(this).val()) : 0;
        clTotalMonthlyPayment += parseFloat(cleanData);
        $("#TotalMonthlyPaymentCL").val(clTotalMonthlyPayment.toFixed(2));
    });
});

//Creditor Details

$(document).on('click', '#clAddMore,.clAddMore', function () {

    let innerSec = 'clnreFields';
    let rowObj = $('.' + innerSec + ':last').clone();
    let divId = rowObj.attr('id');
    let divIdArray = divId.split('_');
    let inputIdCnt = parseInt(divIdArray[parseInt(divIdArray.length) - 1]);
    let newInputId = inputIdCnt + 1;
    let mainSecLength = $('.' + innerSec).length;

    $('.' + innerSec).last().clone()
        .attr('id', function (idx, attrVal) {
            try {
                let innerSecDivArray = attrVal.split('_');
                if (innerSecDivArray.length === 2) {
                    return innerSecDivArray[0] + '_' + (parseInt(innerSecDivArray[1]) + 1);
                }
            } catch (e) {
            }
        })
        .find(":input")
        .attr('id', function (idx, attrVal) {
            try {
                let innerSecDivArray = attrVal.split('_');
                if (innerSecDivArray.length === 2) {
                    return innerSecDivArray[0] + '_' + (parseInt(innerSecDivArray[1]) + 1);
                }
            } catch (e) {
            }
        })
        .attr('name', function (idx, attrVal) {
            try {
                return attrVal.replace('[' + inputIdCnt + ']', '[' + newInputId + ']'); // fix is here
            } catch (e) {
            }
        })
        .attr('type', function (idx, attrVal) {
            if (attrVal === 'radio') {
                let radioname = this.name;
                let nameArr = radioname.split('_');
                $(this).attr('name', nameArr[0] + "_" + newInputId);
                // $(this).attr('id', nameArr[0] + "_" + cnt);
                $(this).prop("checked", false);
            } else {
                $(this).val('');
            }
        }).removeAttr('checked').prop("checked", false).end()
        .find('.remClnreFields')
        .attr('id', function (idx, attrVal) {
            try {
                return 'remClnreFields_' + newInputId;
            } catch (e) {
            }
        })
        .removeAttr('data-id')
        .removeAttr('data-page')
        .end()
        .find('h3.clnreHeaderCls').html('Creditors / Liabilities (Non-Real Estate) : ' + (newInputId + 1)).end()
        .find('.chosen-container').remove().end()
        .find('.chzn-select').show().end()
        .find('div.remoevDIve').remove().end()
        .find('.is-invalid').removeClass('is-invalid').end()
        .find('label').attr('for', function (idx, attrVal) {
        try {
            let innerSecDivArray = attrVal.split('_');
            if (innerSecDivArray.length === 2) {
                return innerSecDivArray[0] + '_' + (parseInt(innerSecDivArray[1]) + 1);
            }
        } catch (e) {
        }
    }).end()
        //.find('.' + icrementSec).html(newInputId + 1).end()
        //.find('.removeCloneButton').removeClass('d-none').end()
        .insertAfter('.' + innerSec + ':last');

    $('.chzn-select').chosen({allow_single_deselect: true, search_contains: true})
    $(".creditorPhone_mask:enabled").inputmask("mask", {mask: "(999) 999 - 9999 Ext 9999"});
    $(".creditorRepPhone_mask:enabled").inputmask("mask", {mask: "(999) 999 - 9999 Ext 9999"});
    $(".creditorAgentPhone_mask:enabled").inputmask("mask", {mask: "(999) 999 - 9999 Ext 9999"});
    $(".creditorAgentFax_mask:enabled").inputmask("mask", {mask: "(999) 999 - 9999"});
    $(".creditorRepFax_mask:enabled").inputmask("mask", {mask: "(999) 999 - 9999"});

    $("#clnreFieldsDiv_" + newInputId).prepend(rem);
});


