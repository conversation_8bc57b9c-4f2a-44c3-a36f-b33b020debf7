<?php

use models\composite\oCustomDocs\getAllDynamicDatas;
use models\composite\oFile\getFileInfo;
use models\composite\oPackage\getLibPackage;
use models\composite\oPackage\recordPackageAccess;
use models\composite\oServiceType\getServiceTypes;
use models\composite\oTrust\saveTrustDocPositions;
use models\composite\proposalFormula;
use models\constants\gl\glHAMPDocIdArray;
use models\constants\typeOfHMLOLoanRequesting;
use models\Controllers\LMRequest\Property;
use models\cypher;
use models\HMLOLoanTermsCalculation;
use models\pdf\pkgUtilPDF;
use models\Security;
use models\standard\Arrays;
use models\standard\Dates;
use models\standard\Integers;
use models\standard\Strings;
use models\UploadServer;

/**
 * @param $inArray
 * @return mixed
 */
function generateAppropriatePkg($inArray)
{
    global $processingCompanyArray;
    global $LMRLogoUploadPath, $LMRLogoUrl, $selfPath;
    global $pdf, $leftAndRightMarginVal, $topMarginVal;
    global $glPositionArray;
    global $oPDF, $glLMRClientTypeArray;
    global $newPDFFormat;

    $glHAMPDocIdArray = glHAMPDocIdArray::$glHAMPDocIdArray;

    $source = null;
    $fileDetails = [];
    $LMRId = 0;
    $pkgID = 0;
    $responseId = 0;
    $selPkg = [];
    $printOutput = '';
    $oPDF = '';
    $txnID = '';
    $glPositionArray = [];
    $fillableArr = [];
    $needESign = '';
    $docPath = '';
    $footerDetailArray = [];
    $processorPerson = false;
    $LMRPerson = false;
    $agPer = false;
    $attach = '';
    $PCID = 0;
    $loggedInUserId = 0;
    $loggedInUserType = '';
    $processingCompanyId = 0;
    $clientPer = false;
    $opt = '';
    $PkgInfoArray = [];
    $LMRInfoArray = [];
    $fileCreatedDate = '';
    $recreate = 0;
    $QAInfoArray = [];
    $QAInfoArray = [];
    $shortSaleInfo = [];
    $borrowerLName = '';
    $HROpt = '';
    $saveHRPath = '';
    $HRDate = Dates::Timestamp();
    $subject = '';
    $message = '';
    $userTimeZone = '';
    $CIID = 0;
    $creditorInfo = [];
    $faxReceiverName = '';
    $faxReceiverNo = '';
    $oldFPCID = 0;
    $noOfPages = 0;
    $glLMRClientTypeArray = [];
    $openFileName = '';
    $displayLender = '';
    $displayPropertyAdd = '';


    $LMRId = trim($inArray['LMRId']);
    $pkgID = trim($inArray['pkgID']);
    $responseId = trim($inArray['responseId']);
    if (array_key_exists('printOutput', $inArray)) $printOutput = trim($inArray['printOutput']);
    if (array_key_exists('txnID', $inArray)) $txnID = trim($inArray['txnID']);
    if (array_key_exists('attach', $inArray)) $attach = trim($inArray['attach']);
    if (array_key_exists('opt', $inArray)) $opt = trim($inArray['opt']);
    if (array_key_exists('PCID', $inArray)) $PCID = trim($inArray['PCID']);
    if (array_key_exists('userNumber', $inArray)) $userNumber = trim($inArray['userNumber']);
    if (array_key_exists('userGroup', $inArray)) $userGroup = trim($inArray['userGroup']);
    if (array_key_exists('recreate', $inArray)) $recreate = trim($inArray['recreate']);
    if (array_key_exists('HROpt', $inArray)) $HROpt = trim($inArray['HROpt']);
    if (array_key_exists('HRDate', $inArray)) $HRDate = trim($inArray['HRDate']);
    if (array_key_exists('userTimeZone', $inArray)) $userTimeZone = trim($inArray['userTimeZone']);
    if (array_key_exists('faxReceiverNo', $inArray)) $faxReceiverNo = trim($inArray['faxReceiverNo']);
    if (array_key_exists('faxReceiverName', $inArray)) $faxReceiverName = trim($inArray['faxReceiverName']);

    if (array_key_exists('subjectFax', $inArray)) $subject = trim($inArray['subjectFax']);
    if (array_key_exists('messageFax', $inArray)) $message = trim($inArray['messageFax']);

    if (array_key_exists('displayLender', $inArray)) $displayLender = trim($inArray['displayLender']);
    if (array_key_exists('displayPropertyAdd', $inArray)) $displayPropertyAdd = trim($inArray['displayPropertyAdd']);
    if (array_key_exists('noOfPages', $inArray)) $noOfPages = trim($inArray['noOfPages']);
    if (array_key_exists('openFileName', $inArray)) $openFileName = trim($inArray['openFileName']);
    if (array_key_exists('fillableArr', $inArray)) $fillableArr = $inArray['fillableArr'];

    $glPositions = isset($inArray['glPositions']) ? $inArray['glPositions'] : null;

    if ($pkgID > 0) $selPkg = preg_split('/[, ]+/', $pkgID, -1, PREG_SPLIT_NO_EMPTY);
    if ($LMRId > 0) {
        $ipArray['LMRId'] = $LMRId;
        $ipArray['pkgID'] = $pkgID;
        $ipArray['txnID'] = $txnID;
        if (array_key_exists('CRID', $inArray)) $ipArray['CIID'] = trim($inArray['CRID']);

        /* if ($pkgID == '430' || $pkgID == '538')	$ipArray["fetchTab"] = 'HUD'; */

        if(isset($inArray['LMRArray'])){
            $fileDetails = $inArray['LMRArray'];
        } else {
            $fileDetails = getFileInfo::getReport($ipArray);
        }
        $mergeData = getAllDynamicDatas::getReport(['LMRID' => $LMRId, 'multiOpt' => '']);

        if ($pkgID == '414' || $pkgID == '607' || $pkgID == '606' || $pkgID == '613' || $pkgID == '614') {
            $glLMRClientTypeArray = getServiceTypes::getReport([
                'activeStatus' => '1',
            ]);
        }
    } else {
        Property::init();
    }
    if (count($fileDetails) > 0) {
        $PkgInfoArray = $fileDetails[$LMRId];
        $myFileInfo = $PkgInfoArray;
    }
    if (count($PkgInfoArray) > 0) {
        $LMRInfoArray = $PkgInfoArray['LMRInfo'];
        $QAInfoArray = $PkgInfoArray['QAInfo'];
        $shortSaleInfo = $PkgInfoArray['listingRealtorInfo'];
        $creditorInfo = $PkgInfoArray['creditorInfo'];

    }
    if (count($LMRInfoArray) > 0) {
        $fileCreatedDate = $LMRInfoArray['recordDate'];
        if (Integers::checkEmailIsDummy($LMRInfoArray['borrowerEmail'])) {
            $fileDetails[$LMRId]['LMRInfo']['borrowerEmail'] = '';
            /** Hide Dummy email for all PDF **/
        }
        /** Process single quote for the borrower & co-borrower name to reflect in all PDFs **/

        $fileDetails[$LMRId]['LMRInfo']['borrowerFName'] = Strings::undoHTMLEntitiesForPDF($fileDetails[$LMRId]['LMRInfo']['borrowerFName']);
        $fileDetails[$LMRId]['LMRInfo']['borrowerName'] = Strings::undoHTMLEntitiesForPDF($fileDetails[$LMRId]['LMRInfo']['borrowerName']);
        $fileDetails[$LMRId]['LMRInfo']['borrowerLName'] = Strings::undoHTMLEntitiesForPDF($fileDetails[$LMRId]['LMRInfo']['borrowerLName']);
        $fileDetails[$LMRId]['LMRInfo']['coBorrowerFName'] = Strings::undoHTMLEntitiesForPDF($fileDetails[$LMRId]['LMRInfo']['coBorrowerFName']);
        $fileDetails[$LMRId]['LMRInfo']['coBorrowerLName'] = Strings::undoHTMLEntitiesForPDF($fileDetails[$LMRId]['LMRInfo']['coBorrowerLName']);
        $borrowerLName = ($fileDetails[$LMRId]['LMRInfo']['borrowerLName']);
        $oldFPCID = trim($fileDetails[$LMRId]['LMRInfo']['oldFPCID']);
    }

    if (count($QAInfoArray) > 0) {
        $QAInfoArray['offerPropertyAgentName'] = '';
        $QAInfoArray['offerPropertyAgencyName'] = '';
        $QAInfoArray['OPAPhone'] = '';
        $QAInfoArray['propertyListedDate'] = '';

        $fileDetails[$LMRId]['QAInfo'] = $QAInfoArray;
    }

    if (count($shortSaleInfo) > 0) {
        if (array_key_exists('listingDate', $shortSaleInfo)) {
            $QAInfoArray['propertyListedDate'] = $shortSaleInfo['listingDate'];
        }
        if (array_key_exists('agency', $shortSaleInfo)) {
            $QAInfoArray['offerPropertyAgencyName'] = $shortSaleInfo['agency'];
        }
        if (array_key_exists('realtor', $shortSaleInfo)) {
            $QAInfoArray['offerPropertyAgentName'] = $shortSaleInfo['realtor'];
        }
        if (array_key_exists('realtorPhoneNumber', $shortSaleInfo)) {
            $QAInfoArray['OPAPhone'] = $shortSaleInfo['realtorPhoneNumber'];
        }
        $fileDetails[$LMRId]['QAInfo'] = $QAInfoArray;
    }

    if (isset($_REQUEST['clId'])) {
        $loggedInUserId = cypher::myDecryption($_REQUEST['clId']);
        $loggedInUserType = 'Client';
    }
    if ($pkgID > 0 && $LMRId > 0) {
        $inArray['PKGID'] = $pkgID;
        $inArray['PCID'] = $PCID;
        $inArray['loggedInUserId'] = $userNumber;
        $inArray['loggedInUserType'] = $userGroup;
        $inArray['LMRId'] = $LMRId;

        $insCnt = recordPackageAccess::getReport($inArray);
    }
    $ipArray = [];
    $ipArray['PCID'] = $PCID;
    $ipArray['activeStatus'] = '1';
    $pkgs = getLibPackage::getReport($ipArray);

    $newPDFFormat = 0;
    if (array_key_exists($pkgID, $pkgs)) {
        $newPDFFormat = $pkgs[$pkgID]['newPDFFormat'];
    }

    /* Get package information from pkgDocsArray */
    $myDoc = [];

    if ($newPDFFormat == 1) {

        $oPDF = new pkgUtilPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, NEW_PDF_PAGE_FORMAT, true, 'UTF-8', false);
    } else {
        $oPDF = new pkgUtilPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
    }

    $oPDF->setMargins(leftAndRightMarginVal, topMarginVal, leftAndRightMarginVal);
    $oPDF->setAutoPageBreak(TRUE, leftAndRightMarginVal);

    $oPDF->setImageScale(PDF_IMAGE_SCALE_RATIO);
    $oPDF->setFontSubsetting(false);
    $oPDF->AliasNbPages();
    $oPDF->setTitle($openFileName);

    //Final Loan Amount//
    $finalLoanAmount = $fileDetails[$LMRId]['fileHMLONewLoanInfo']['totalLoanAmount'] ?? 0;
    $fileDetails[$LMRId]['fileHMLONewLoanInfo']['finalLoanAmtPkg'] = $finalLoanAmount;
    $fileDetails[$LMRId]['fileHMLONewLoanInfo']['finalInitialAmtPkg'] = Strings::replaceCommaValues(trim($mergeData[$LMRId]['acquisitionPriceFinanced']));
    $fileDetails[$LMRId]['fileHMLONewLoanInfo']['totalFeesAndCostPkg'] = Strings::replaceCommaValues(trim($mergeData[$LMRId]['totalFeesAndCost']));
    $fileDetails[$LMRId]['fileHMLONewLoanInfo']['finalTotalCashToCloseAmtPkg'] = Strings::replaceCommaValues(trim($mergeData[$LMRId]['totalCashToClose']));

    //Total Cash Out
    $fileDetails[$LMRId]['fileHMLONewLoanInfo']['totalCashOut'] = Strings::replaceCommaValues(trim($mergeData[$LMRId]['totalCashOut'])) ?? 0;

    //rehabCostFinanced//
    $rehabCost = Integers::showFieldFloat('rehabCost', 'fileHMLOInfo');
    $rehabCostPercentageFinanced = $fileDetails[$LMRId]['fileHMLONewLoanInfo']['rehabCostPercentageFinanced'];
    $rehabCostFinanced = proposalFormula::calculateRehabCostFinancedByPercentage($rehabCost, $rehabCostPercentageFinanced);
    $fileDetails[$LMRId]['fileHMLONewLoanInfo']['rehabCostFinanced'] = $rehabCostFinanced;

    //currentEscrowBalance//
    $totalDrawsFunded = 0;
    $budgetAndDrawsInfo = $fileDetails[$LMRId]['budgetAndDrawsInfo'] ?? [];
    foreach ($budgetAndDrawsInfo as $draw) {
        $totalDrawsFunded += Strings::replaceCommaValues($draw['amountAddedToTotalDrawsFunded']);
    }
    $availableBudget = Strings::replaceCommaValues($rehabCostFinanced) - Strings::replaceCommaValues($totalDrawsFunded);
    $fileDetails[$LMRId]['fileHMLOPropertyInfo']['currentEscrowBalance'] = $availableBudget;

    //currentLoanBalance//
    $currentLoanBalance = $fileDetails[$LMRId]['fileHMLONewLoanInfo']['currentLoanBalance'];

    $CORTotalLoanAmt = $fileDetails[$LMRId]['fileHMLONewLoanInfo']['CORTotalLoanAmt'] ?? 0;
    $typeOfHMLOLoanRequesting = $fileDetails[$LMRId]['fileHMLOPropertyInfo']['typeOfHMLOLoanRequesting'];

    $costBasis = Integers::showFieldFloat('costBasis', 'listingRealtorInfo');
    $maxAmtToPutDown = Strings::showField('maxAmtToPutDown', 'fileHMLOPropertyInfo');
    $totalLoanAmount = $fileDetails[$LMRId]['fileHMLONewLoanInfo']['totalLoanAmount'];
    $closingCostFinanced = Strings::showField('closingCostFinanced', 'fileHMLONewLoanInfo');
    $prepaidInterestReserve = Strings::showField('prepaidInterestReserve', 'fileHMLONewLoanInfo');

    $acquisitionPriceFinancedArr = proposalFormula::calculateInitialLoanAmount([
        'costBasis' => $costBasis,
        'maxAmtToPutDown' => $maxAmtToPutDown,
        'typeOfHMLOLoanRequesting' => $typeOfHMLOLoanRequesting,
        'totalLoanAmount' => $totalLoanAmount,
        'rehabCostFinanced' => $rehabCostFinanced,
        'closingCostFinanced' => $closingCostFinanced,
        'prepaidInterestReserve' => $prepaidInterestReserve,
    ]);

    $acquisitionPriceFinanced = Arrays::getArrayValue('acquisitionPriceFinanced', $acquisitionPriceFinancedArr);

    if (HMLOLoanTermsCalculation::isTransactionTypeRefinance($typeOfHMLOLoanRequesting)
        || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::NEW_CONSTRUCTION_EXISTING_LAND
        || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::DELAYED_PURCHASE) {
        $initialLoanAmount = $CORTotalLoanAmt;
    } else {
        $initialLoanAmount = $acquisitionPriceFinanced;
    }

    $prepaidInterestReserveForCal = $fileDetails[$LMRId]['fileHMLONewLoanInfo']['prepaidInterestReserve'];
    $haveInterestreserve = $fileDetails[$LMRId]['fileHMLONewLoanInfo']['haveInterestreserve'];
    if ($haveInterestreserve == 'No' || $haveInterestreserve == '') {
        $prepaidInterestReserveForCal = 0;
    }

    $filepaydownInfo = $fileDetails[$LMRId]['paydownInfo'];
    $paydownamount = 0;
    if (!is_array($filepaydownInfo)) {
        $filepaydownInfo = [];
    }
    for ($paycount = 0; $paycount < count($filepaydownInfo); $paycount++) {
        $paydownamount = $paydownamount + $filepaydownInfo[$paycount]['principalPayDownAmount'];
    }

    $CLBInArray = [
        'initLAmt' => $initialLoanAmount,
        'prePIR' => $prepaidInterestReserveForCal,
        'closingCost' => $closingCostFinanced,
        'funDraw' => $totalDrawsFunded,
        'principalPayDown' => $paydownamount,
    ];

    $currentLoanBalance = proposalFormula::calculateCurrentLoanBalance($CLBInArray, $typeOfHMLOLoanRequesting, $fileDetails[$LMRId]['fileHMLONewLoanInfo']['initialAdvance']);
    $fileDetails[$LMRId]['fileHMLONewLoanInfo']['currentLoanBalance'] = $currentLoanBalance;
    //hide null brokers
    if ($fileDetails[$LMRId]['BrokerInfo']['firstName'] == 'null') {
        $fileDetails[$LMRId]['BrokerInfo']['firstName'] = '';
        $fileDetails[$LMRId]['BrokerInfo']['email'] = '';
    }

    $ip['LMRId'] = $LMRId;
    $ip['fileDetails'] = $fileDetails;
    $ip['pkgID'] = $pkgID;
    $ip['txnID'] = $txnID;
    $ip['opt'] = $opt;
    $ip['loggedInUserId'] = $userNumber;
    $ip['loggedInUserType'] = $userGroup;
    $ip['userTimeZone'] = $userTimeZone;
    $ip['faxReceiverNo'] = $faxReceiverNo;
    $ip['faxReceiverName'] = $faxReceiverName;
    $ip['subjectFax'] = $subject;
    $ip['messageFax'] = $message;
    $ip['displayLender'] = $displayLender;
    $ip['displayPropertyAdd'] = $displayPropertyAdd;
    $ip['noOfPages'] = $noOfPages;
    $ip['glLMRClientTypeArray'] = $glLMRClientTypeArray;
    $footerDetailArray['txnID'] = $txnID;
    $ip['recreate'] = $recreate;
    $ip['fillableArr'] = $fillableArr;


//    echo count($selPkg) . ' selPkg' . PHP_EOL;

    for ($d = 0; $d < count($selPkg); $d++) {

        $myDoc = [];
        $needESign = '';
        $company = '';
        $pkgName = '';
        $documentNameSave = '';
        $creditorName = '';
        if (array_key_exists(trim($selPkg[$d]), $pkgs)) $myDoc = $pkgs[trim($selPkg[$d])];

        $fileName = $myDoc['fileName'];
        if (!$myDoc['fileName']) {
            Debug('fileName empty', $myDoc);
        }
        switch ($fileName) {
            case 'ach-form-sba.php':
                $fileName = 'ACHFormSBA.php';
                break;
            case 'agencyDisclosurePkg.php':
                $fileName = 'agencyDisclosure.php';
                break;
            case 'ThemisBabbs.php':
                $fileName = 'babbsRetainer.php';
                break;
            case 'BLMShortSalePackage_2013.php':
                $fileName = 'BLMShortSalePackage2013.php';
                break;
            case 'CreditCheck_GreatOak.php':
                $fileName = 'creditCheckAuthorizationGreatOak.php';
                break;
            case '_dynamicQuestionsExample.php':
                $fileName = 'dynamicQuestionsExample.php';
                break;
            case 'ecoa.php':
                $fileName = 'EqualCreditOpportunityAct.php';
                break;
            case 'fciACH.php':
                $fileName = 'fciACHform.php';
                break;
            case 'firstAmericanMitigatorsPdf.php':
                $fileName = 'firstAmericanMitigators.php';
                break;
            case 'masterWorkUp_HomeWorx.php':
                $fileName = 'masterWorkUp.php';
                break;
            case 'appMCA.php':
                $fileName = 'MCA_Application.php';
                break;
            case 'MiddaghRetainerPdf.php':
                $fileName = 'MiddaghRetainer.php';
                break;
            case 'moneyAvenueFeeAgreement_0003.php':
                $fileName = 'MoneyAvenueFeeAgreement.php';
                break;
            case 'mssCooperatingBroker1.php':
                $fileName = 'cooperatingBroker1.php';
                break;
            case 'mssCollectionChecklistPdf.php':
                $fileName = 'mssCollectionChecklist.php';
                break;
            case 'mssReferralAgreementPdf.php':
                $fileName = 'mssReferralAgreement.php';
                break;
            case 'mssThirdPartyAuthPdf.php':
                $fileName = 'mssThirdPartyAuth.php';
                break;
            case 'DIYPurchaseAgreement_maderPdf.php':
                $fileName = 'DIYPurchaseAgreement_mader.php';
                break;
            case 'DIYPurchaseAgreement_mader_cobPdf.php':
                $fileName = 'DIYPurchaseAgreement_mader_cob.php';
                break;
            case 'velocityCommercialLoanApp.php':
                $fileName = 'CommercialLoanApplication.php';
                break;
            case '4506C_2021.php':
                $fileName = 'Form4506C_2021.php';
                break;
            case 'Form_w9.php':
                $fileName = 'loanApp_w9.php';
                break;
            case 'Balloon_Payment_Disclosure.php':
                $fileName = 'BalloonPaymentDisclosure.php';
                break;
            case 'Seterus-2015.php':
                $fileName = 'Seterus2015.php';
                break;
            case 'createReferences.php':
                $fileName = 'references.php';
                break;
            case 'createMissingDocDemandLetterPkg.php':
                $fileName = 'CreateMissingDocDemandLetter.php';
                break;
            case 'Mader_EMA_agreement.php':
                $fileName = 'MaderEMAAgreement.php';
                break;
            case 'createFaxCoverSheet.php':
                $fileName = 'FaxCoverSheet.php';
                break;
            case '_print_r_package.php':
                $fileName = 'print_r_package.php';
                break;
            case 'doddFrankPdf.php':
                $fileName = 'doddFrank.php';
                break;
        }
        if (!file_exists(__DIR__ . '/../models/packages/' . $fileName)) {
            $fileName = 'Package' . ucfirst($fileName);
            if (!file_exists(__DIR__ . '/../models/packages/' . $fileName)) {
                $fileName = str_replace('_', '', $fileName);
                if (!file_exists(__DIR__ . '/../models/packages/' . $fileName)) {
                    switch ($fileName) {
                        case 'PackageVanguardPdf.php':
                            $fileName = 'PackageVanguard.php';
                            break;
                        case 'PackageFirstAmericanMitigatorsSettlementPdf.php':
                            $fileName = 'PackageFirstAmericanMitigatorsSettlement.php';
                            break;
                        case 'PackagePromissoryNote-FEMBI.php':
                            $fileName = 'PackagePromissoryNoteFEMBI.php';
                            break;
                        case 'PackageSSRegnPdf.php':
                            $fileName = 'PackageSSRegn.php';
                            break;
                        case 'PackageUnityLawGroupOnePaymentPdf.php':
                            $fileName = 'PackageUnityLawGroupOnePayment.php';
                            break;
                        case 'PackageUnityLawGroupRecurringPdf.php':
                            $fileName = 'PackageUnityLawGroupRecurring.php';
                            break;
                        case 'PackageHUDShortSalePdfNew.php':
                            $fileName = 'PackageHUDShortSaleNew.php';
                            break;
                        case 'PackageDrawrequest.php':
                            $fileName = 'PackageDrawRequest.php';
                            break;
                        case 'PackageHiscapitalfunding.php':
                            $fileName = 'PackageHisCapitalFunding.php';
                            break;
                        case 'PackageVerticalfund.php':
                            $fileName = 'PackageVerticalFund.php';
                            break;
                        case 'PackageMortgageFraudReportv2.php':
                            $fileName = 'PackageMortgageFraudReportV2.php';
                            break;
                        case 'PackageMasterproofoffunds.php':
                            $fileName = 'PackageMasterProofOfFunds.php';
                            break;
                        case 'PackageDIYPurchaseAgreementmader.php':
                            $fileName = 'PackageDIYPurchaseAgreementMader.php';
                            break;
                        case 'PackageDIYPurchaseAgreementmadercob.php':
                            $fileName = 'PackageDIYPurchaseAgreementMaderCOB.php';
                            break;
                        case 'PackagePAFform.php':
                            $fileName = 'PackagePAFForm.php';
                            break;
                        case 'PackagePaymentauthorization.php':
                            $fileName = 'PackagePaymentAuthorization.php';
                            break;
                        case 'PackageProofofFundsLetter.php':
                            $fileName = 'PackageProofOfFundsLetter.php';
                            break;
                        case 'PackageQuicklending.php':
                            $fileName = 'PackageQuickLending.php';
                            break;
                        case 'PackageSecurityDeedofTrust.php':
                            $fileName = 'PackageSecurityDeedOfTrust.php';
                            break;
                        case 'PackageLoanAppw9.php':
                            $fileName = 'PackageLoanAppW9.php';
                            break;
                        case 'PackageSlsRMA.php':
                            $fileName = 'PackageSLSRMA.php';
                            break;
                        case 'PackageSlsThirdPartyAuth.php':
                            $fileName = 'PackageSLSThirdPartyAuth.php';
                            break;
                        case 'PackageSlsRMA2017.php':
                            $fileName = 'PackageSLSRMA2017.php';
                            break;
                        case 'PackageFaxCoverSheet.php':
                            $fileName = 'PackageCreateFaxCoverSheet.php';
                            break;
                        case 'PackageCreateFAndBLawPkg.php':
                            $fileName = 'PackageCreateFAndBLaw.php';
                            break;
                        case 'PackageCreateMarsDisclosurePdf.php':
                            $fileName = 'PackageCreateMarsDisclosure.php';
                            break;
                        case 'PackageCreateDemandLetterPONPkg.php':
                            $fileName = 'PackageCreateDemandLetterPON.php';
                            break;
                        case 'Package4506T2012.php':
                            $fileName = 'PackageGeneral4506T2012.php';
                            break;
                        default:
                            Debug('file does not exist: ' . $fileName);
                    }
                }
            }
        }

        $documentNameSave = $myDoc['pkgName'];
        $myDoc['pkgName'] = 'models\\packages\\' . str_replace('.php', '', $fileName);

        if (!class_exists($myDoc['pkgName'])) {
            Debug('class not found: ' . $myDoc['pkgName'], debug_backtrace());
        }

        if (array_key_exists('esign', $myDoc)) {
            $needESign = trim($myDoc['esign']);
        }
        if (array_key_exists('Company', $myDoc)) {
            $company = trim($myDoc['Company']);
        }
        if (array_key_exists('pkgName', $myDoc)) {
            $pkgName = trim($myDoc['pkgName']);
        }

        if ($needESign == '1') {
            $oPDF->SetFooterDetails($footerDetailArray);
        }

        $ip['myDoc'] = $myDoc;
        $ip['oPDF'] = $oPDF;
        $ip['company'] = $company;
        $ip['pkgName'] = $pkgName;
        $ip['source'] = $source;

//        echo 'GeneratePDF' . PHP_EOL;
        $oPDF = $pkgName::GeneratePDF($ip);
//        echo 'Done GeneratePDF' . PHP_EOL;

    }

    if($glPositions){
        return $glPositionArray;
    }

    if ($HROpt == 'Yes') {
        $saveHRPath = Strings::removeDisAllowedChars($borrowerLName) . '_' . Strings::removeDisAllowedChars($HRDate) . '.pdf';
    } else {
        $creditorName = '';
        $saveDocPath = Strings::removeDisAllowedChars($documentNameSave) . '_' . Strings::removeDisAllowedChars($borrowerLName) . '_' . cypher::myEncryption($responseId) . '_' . cypher::myEncryption($pkgID) . '.pdf';

        if (array_key_exists('CRID', $inArray)) {
            for ($i = 0; $i < count($creditorInfo); $i++) {
                $creditorName = ucwords(trim($creditorInfo[$i]['creditorName']));
            }
            if ($creditorName != '') {
                $saveDocPath = Strings::removeDisAllowedChars($documentNameSave . ' - ' . $creditorName) . '_' . Strings::removeDisAllowedChars($borrowerLName) . '_' . cypher::myEncryption($responseId) . '_' . cypher::myEncryption($pkgID) . '_' . cypher::myEncryption(trim($inArray['CRID'])) . '.pdf';
            } else {
                $saveDocPath = Strings::removeDisAllowedChars($documentNameSave) . '_' . Strings::removeDisAllowedChars($borrowerLName) . '_' . cypher::myEncryption($responseId) . '_' . cypher::myEncryption($pkgID) . '.pdf';

            }
        }
    }

    // dd($attach, $needESign, $recreate );

    if (($attach == 'y' || $needESign != '1') && $recreate != '1') {
        $glPositionArray = [];

        if ($opt == 'sample') {/* If sample no need to save in the third server */
            if (trim($openFileName) == '') $openFileName = $saveDocPath;
            return $oPDF->Output($openFileName, $inArray['OutputMode'] ?? 'I');
            //return $oPDF->Output($openFileName);
        }

        if (array_key_exists('CRID', $inArray)) {
            $infoArray['CRID'] = $inArray['CRID'];
            $infoArray['creditorName'] = $creditorName;
        }
        $tmpPkgName = CONST_ROOT_PATH . 'temp/' . Security::HashLegacy('s_' . $LMRId . '_' . $pkgID) . '.pdf';

        $oPDF->Output($tmpPkgName, 'F');

        if (file_exists($tmpPkgName)) {
            $data = file_get_contents($tmpPkgName);

            $infoArray['oPDF'] = base64_encode($data);
            $infoArray['saveHRPath'] = $saveHRPath;
            $infoArray['saveDocPath'] = $saveDocPath;
            $infoArray['LMRID'] = $LMRId;
            $infoArray['recordDate'] = $fileCreatedDate;
            $infoArray['oldFPCID'] = $oldFPCID;
            $infoArray['HROpt'] = $HROpt;

            $docPath = UploadServer::pkgController($infoArray);
            unlink($tmpPkgName);
        }

    } elseif ($needESign == '1') {

        if ($recreate == 1) {
            $glPositionArray = [];
        } else {
            /*** Save x and y position Start ***/
            if (count($glPositionArray) > 0) {
                $inPosArray['docPosition'] = $glPositionArray;
                $inPosArray['txnID'] = $txnID;
                $posID = saveTrustDocPositions::getReport($inPosArray);
            }
            /*** Save x and y position End ***/
        }

        $tmpPkgName = CONST_ROOT_PATH . 'temp/' . Security::HashLegacy('esign_' . $LMRId . '_' . $pkgID) . '.pdf';
        if ($printOutput == 'n') {
            $oPDF->Output($tmpPkgName, 'F');
        } else {
            $oPDF->Output($tmpPkgName, 'FI');
        }

        if (file_exists($tmpPkgName) && $printOutput == 'n') {
            $handle = fopen($tmpPkgName, 'r');
            $data = fread($handle, filesize($tmpPkgName));

            $saveDocPath = $txnID . '.pdf';

            $infoArray['oPDF'] = base64_encode($data);
            $infoArray['eSignDocs'] = 1;
            $infoArray['saveDocPath'] = $saveDocPath;
            $infoArray['LMRID'] = $LMRId;
            $infoArray['encLMRID'] = cypher::myEncryption($LMRId);
            $infoArray['recordDate'] = $fileCreatedDate;
            $infoArray['oldFPCID'] = $oldFPCID;

            $docPath = UploadServer::pkgController($infoArray);

            unlink($tmpPkgName);
        }
    }


//        ob_end_clean();
//          $oPDF->Output();

    if (($attach == 'y' || $needESign != '1') && $recreate != '1') {
        if ($HROpt == 'Yes') {
            return $saveHRPath;
        } else {
            return CONST_PATH_LMR_FILE_DOCS . $docPath;
        }
    } else {
        return $oPDF;
    }
}

/* If preliminarily approved ask for the following docs
Ref: 161459027
PKGId = 972 - Pacific Full Loan App (Local)
PKGId = 966 - Pacific Full Loan App (Local)*/

/**
 * @return string
 */
function preliminaryApproval(): string
{
    $str = "* Receipt and review of background and credit for borrower/guarantor, and contractor, if applicable.\n";
    $str .= "* Lender to verify that Borrower(s) has acceptable cash to close.\n";
    $str .= "* Borrower to provide (and lender to review) most recent acceptable purchase contract or sale agreement for property.\n";
    $str .= "* Borrower to provide (and lender to review) rehab plans and budget.\n";
    $str .= "* Acceptable  (to  Lender)  third  party  broker's  price  opinion  (BPO)  and/or  appraisal,  ordered  by  Lender,  evidencing  as-is  value and after-repair-value (\"ARV\").\n";
    $str .= "* Adequate project, property and title insurance, as determined by Lender.\n";
    $str .= "* Lender  to  utilize  third  party  services  to  determine  that  all  parties  involved  in  transaction  have  no  history  of  criminal  and/or fraudulent behavior.\n";
    $str .= "* Lender to determine if transaction conforms to federal and state regulations.\n";
    $str .= "* Lender to determine if transaction conforms to investor requirements.\n";
    $str .= '* Lender to review estimated and final closing statements prior to closing transaction.';

    /*$str = "<ol type=\"1\">";
    $str .= "<li>Complete Full Online Loan Application.</li>";
    if ($PKGID != 966) {
        $str .= "<li>Complete Rehab/Construction Budget.</li><li>Full Property Appraisal.</li>";
    }
    $str .= "<li>Property hazard insurance on the subject property naming lender as an additional insured party.</li><li>Flood insurance if property is located within a flood zone.</li>";
    if ($PKGID != 966) {
        $str .= "<li>LLC/Corp Entity Documents like operating agreement, articles of incorporation, & W- Form.</li>";
    }
    $str .= "</ol>";
    $str .= "<p>Other conditions & items will may be required once loans is approved & in underwriting, including but not limited to:</p><ul><li>Application Fee</li><li> Tri-merge Credit Report for borrower/co-borrower</li>";

    if ($PKGID == 966) {
        $str .= "<li>Bank Statements</li>";
    } else {
        $str .= "<li>2 Months Bank Statements</li>";
    }
    $str .= "<li>2- Years tax returns</li>";
    if ($PKGID != 966) {
        $str .= "<li> Property Survey</li><li>Copies of all engineering, environmental or significant contractor work proposal for the property.</li>";
    }
    $str .= "</ul>";*/
    return $str;
}

