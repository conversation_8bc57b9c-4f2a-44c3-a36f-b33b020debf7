<?php

namespace models\Controllers;

use models\constants\gl\glCountryArray;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\MaxLengthFields;
use models\cypher;
use models\Database2;
use models\dbExaminer;
use models\FormHelper\FormField;
use models\lendingwise\tblCustomField;
use models\lendingwise\tblFieldsQuickApp;
use models\lendingwise\tblFile;
use models\lendingwise\tblFileHMLO;
use models\lendingwise\tblFormFieldsMaster;
use models\lendingwise\tblPCSectionHeading;
use models\lendingwise_log\ChangeLog;
use models\standard\Arrays;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;

class loanForm extends strongType
{
    public static ?array $fields = null;

    /* @var tblFormFieldsMaster[][] $masterFields */
    public static ?array $completeMasterFields = null;

    /* @var tblFormFieldsMaster[] $masterFields */
    public static ?array $masterFields = null;

    /* @var tblFieldsQuickApp[] $sections */
    public static ?array $permissions = null;

    /* @var tblFieldsQuickApp[] $sections */
    public static ?array $completePermissions = null;

    /* @var tblPCSectionHeading[] $sections */
    public static ?array $sections = null;

    public static ?string $encLMRId = null;
    public static ?int $LMRId = null;
    public static ?array $changeLogColumns = null;
    public static ?string $fileTab = null;
    public static ?array $fileMC = null;
    public static ?array $fileLoanPrograms = null;
    public static ?array $fileInternalLoanPrograms = null;
    public static ?array $fileTypes = null;
    public static ?string $sectionId = null;
    public static ?array $fieldColumnTypes = null;
    public static ?array $fileAdditionalLoanPrograms = null;
    public static ?string $currentSectionID = null;

    public static ?array $hiddenValues = [];
    public static ?array $showValues = [];
    public static ?array $allowedFields = null;

    public static ?array $changeLogColumns_tblFileHMLO = null;
    public static ?tblFile $tblFile = null;
    public static ?int $PCID = null;
    public static ?array $formFields = null;
    public static ?int $isPublicUser = null;


    /**
     * @var tblFieldsQuickApp[]|tblFormFieldsMaster[]
     */
    private static ?array $pcSettings = null;
    public static ?array $customFieldsBySectionId = null;

    public static ?int $customFieldId = null;

    public static ?int $customField_Display_QA = null;
    public static ?int $customField_Display_FA = null;
    public static ?int $customField_Display_BO = null;


    public static function init(
        int     $PCID,
        ?string $fileTab,
        ?string $encLMRId = null,
        ?int    $LMRId = null,
        ?array  $fileMC = null,
        ?array  $fileLoanPrograms = null,
        ?array  $LMRInternalLoanPrograms = null,
        ?array  $fileAdditionalLoanPrograms = null,
        ?array  $allowedFields = null,
        ?int $isPublicUser = 0
    )
    {

        //pr(func_get_args());
        self::$PCID = $PCID;
        self::$encLMRId = $encLMRId;
        self::$LMRId = $LMRId;
        self::$fileMC = $fileMC;
        self::$fileLoanPrograms = $fileLoanPrograms;
        self::$fileInternalLoanPrograms = $LMRInternalLoanPrograms;
        self::$fileAdditionalLoanPrograms = $fileAdditionalLoanPrograms;
        self::$isPublicUser = $isPublicUser;

        self::$allowedFields = [];
        if ($allowedFields) {
            foreach ($allowedFields as $sectionID => $fields) {
                foreach ($fields as $fieldName => $settings) {
                    self::$allowedFields[$sectionID][] = $fieldName;
                }
            }
        }

        if (self::$LMRId) {
            self::$tblFile = tblFile::Get(['LMRId' => $LMRId]);

            self::$changeLogColumns = ChangeLog::GetColumnsFor(tblFile::class, $LMRId);
            self::$changeLogColumns_tblFileHMLO = ChangeLog::GetColumnsFor(tblFileHMLO::class, self::$tblFile->getTblFileHMLO_by_fileID()[0]->HMLOID);
        }

        $masterList = tblFormFieldsMaster::GetAll(null, ['sectionID' => 'asc', 'displayOrder' => 'asc']);

        self::$fileTab = $fileTab;
        self::$fields = [];
        self::$completeMasterFields = [];

        foreach ($masterList as $item) {
            $item->init();
            self::$fields[$item->sectionID][$item->fieldName] = $item;
            self::$completeMasterFields[$item->sectionID][$item->fieldName] = $item;
        }

        $sql = '
            SELECT
                fieldName, columnType
            FROM lendingwise_log.table_fields
        ';
        $res = Database2::getInstance()->queryData($sql);
        self::$fieldColumnTypes = [];
        foreach ($res as $row) {
            self::$fieldColumnTypes[$row['fieldName']] = $row['columnType'];
        }

        self::getPCFormFields($PCID);

        $sections = tblPCSectionHeading::GetAll(['PCID' => $PCID]);
        self::$sections = [];
        foreach ($sections as $item) {
            self::$sections[$item->sectionID] = $item;
        }

        ksort(self::$sections);


        $customFields = tblCustomField::GetAll([
            'PCID'       => $PCID,
            'isActive'   => 1,
        ], [
            'DisplayOrder' => 'ASC',
            'Label'        => 'ASC',
        ]);

        self::$customFieldsBySectionId = Arrays::buildKeyByValue($customFields,'sectionID');
    }

    public static function getPCFormFields(?int $PCID): void
    {
        if(self::$pcSettings) {
            return;
        }

        self::$pcSettings = $PCID ? tblFieldsQuickApp::GetCachedForPCID($PCID) : tblFormFieldsMaster::GetAll();

        foreach (self::$pcSettings as $item) {
            $item->init();
            self::$completePermissions[$item->sectionID][$item->fieldName] = $item;
            self::$formFields[$item->sectionID][$item->fieldName] = $item;
        }
    }

    public static function hideField(string $sectionID, string $fieldName): void
    {
        self::$hiddenValues[$sectionID][$fieldName] = true;
    }

    public static function showFieldCustom(string $sectionID, string $fieldName): void
    {
        self::$showValues[$sectionID][$fieldName] = true;
    }

    /**
     * @param string $sectionID
     * @return void
     */
    private static function setSection(string $sectionID): void
    {
        self::$currentSectionID = $sectionID;
        self::$masterFields = self::$completeMasterFields[$sectionID] ?? null;
        self::$permissions = self::$completePermissions[$sectionID] ?? null;
    }

    private static ?array $sectionIDStack = [];

    public static function pushSectionID(string $sectionID): void
    {
        self::$sectionIDStack[] = $sectionID;
        self::setSection($sectionID);
    }

    public static function popSectionID()
    {
        array_pop(self::$sectionIDStack);
        if (!sizeof(self::$sectionIDStack)) {
            return;
        }
        self::pushSectionID(array_pop(self::$sectionIDStack));
    }

    public static function getSectionID(): ?string
    {
        return self::$currentSectionID;
    }

    public static function getSectionHeading(string $sectionID, string $default = ''): ?string
    {
        return self::$sections[$sectionID]->sectionHeading ?: $default;
    }

    public static function getTooltip(string $sectionID, ?string $default = ''): ?string
    {
        return self::$sections[$sectionID]->sectionTooltip ?: $default;
    }

    public static function isMandatory(string $fieldName): bool
    {
        if (!isset(self::$permissions[$fieldName])) {
            return false;
        }
        if (self::$fileTab == 'FA') {
            return self::$permissions[$fieldName]->FAMandatory;
        } else if (self::$fileTab == 'BO') {
            return self::$permissions[$fieldName]->BOMandatory;
        } else {
            return self::$permissions[$fieldName]->mandatory;
        }
    }

    private static ?bool $displayValue = null;

    public static array $parentFieldForVisibility = [
        'otherMortgageBalance1'                => 'otherMortgage1',
        'unsecuredLoanBalance1'                => 'unsecuredLoans1',
        'creditCardsBalance1'                  => 'creditCards1',
        'studentLoansBalance1'                 => 'studentLoans1',
        'childSupportOrAlimonyMonthlyBalance1' => 'childSupportOrAlimonyMonthly1',

        'expFedTaxOwed'         => 'expFedTax',
        'expStateTaxOwed'       => 'expStateTax',
        'expRentalPayOwed'      => 'expRentalPay',
        'expMortgPayResiOwed'   => 'expMortgPayResi',
        'expMortgPayInvestOwed' => 'expMortgPayInvest',
        'expPropTaxResiOwed'    => 'expPropTaxResi',
        'expPropTaxInvestOwed'  => 'expPropTaxInvest',
        'expLoanPaymentsOwed'   => 'expLoanPayments',
        'expInsOwed'            => 'expIns',
        'expInvestmentsOwed'    => 'expInvestments',
        'expTuitionOwed'        => 'expTuition',
        'expOtherLivingOwed'    => 'expOtherLiving',
        'expMedicalOwed'        => 'expMedical',
        'otherBalance1'         => 'other1',
    ];

    public static function isVisible(string $fieldName): bool
    {
        if ($_REQUEST['hide_all_fields'] ?? null) {
            return false;
        }

        if (isset(self::$parentFieldForVisibility[$fieldName])) {
            $fieldName = self::$parentFieldForVisibility[$fieldName];
        }

        $hiddenValue = self::$hiddenValues[self::getSectionID()][$fieldName] ?? null;
        if ($hiddenValue) {
            DebugNotice(' <!-- hidden value --> ');
            return false;
        }
        $showValue = self::$showValues[self::getSectionID()][$fieldName] ?? null;
        if ($showValue) {
            DebugNotice(' <!-- show value --> ');
            return true;
        }

        if (sizeof(self::$allowedFields)) {
            if (!isset(self::$allowedFields[self::getSectionID()])) {
                DebugNotice(' <!-- not an allowed field a --> ');
                return false;
            }
            if (!in_array($fieldName, self::$allowedFields[self::getSectionID()])) {
                DebugNotice(' <!-- not an allowed field b --> ');
                return false;
            }
        }

        // if permissions are not set at the user level, check that the master setting is active
        if (!isset(self::$permissions[$fieldName])) {
            if (isset(self::$masterFields[$fieldName])) {
                DebugNotice(' <!-- master active status ' . $fieldName . ': ' . (self::$masterFields[$fieldName]->activeStatus ? 'yes' : 'no') . ' --> ');
                return (bool)(self::$masterFields[$fieldName]->activeStatus);
            }
            DebugNotice(' <!-- permissions not set for ' . $fieldName . ' --> ');
            return true;
        }

        if (!self::$permissions[$fieldName]->fileType) {
            DebugNotice(' <!-- no file type, no display for ' . $fieldName . ' --> ');
            return false; // no file type, no display
        }

        // FADisplay - QADisplay - BODisplay
        // FADisplay - if full app tab, must be set
        // QADisplay - if qa tab, must be set
        // BODisplay - if not QA or FA tab, must be set

        // AND

        // file type - loan file type must be in selected option - default True

        // AND
        // (
        // loan program - loan program of file must be in selected option - default True
        // OR
        // loan program - loan program of file must be in selected custom loan program
        // )

        self::$displayValue = !(
            self::$permissions[$fieldName]->BODisplay
            || self::$permissions[$fieldName]->QADisplay
            || self::$permissions[$fieldName]->FADisplay
        );

        $matchTab = false;
        switch (self::$fileTab) {
            case 'FA':
                if (self::$permissions[$fieldName]->FADisplay) {
                    $matchTab = true;
                }
                break;
            case 'QA':
                if (self::$permissions[$fieldName]->QADisplay) {
                    $matchTab = true;
                }
                break;
            default:
                if (self::$permissions[$fieldName]->BODisplay) {
                    $matchTab = true;
                }
        }

        $matchedFileType = false;
        $noFileTypeGiven = false;
        if (empty(self::$fileMC)) {
            $noFileTypeGiven = true;
        } else {

            foreach (self::$fileMC as $fileType) {
                if ($matchedFileType) {
                    break;
                }
                if (in_array($fileType, self::$permissions[$fieldName]->_fileTypes)) {
                    $matchedFileType = true;
                }
            }
        }

        $matchLP = false;
        $matchILP = false;
        $matchALP = false;

        if (self::$permissions[$fieldName]->enableQAUniqueLoanPrograms && self::$fileTab == 'QA') {
            DebugNotice(' <!-- ' . $fieldName . ' ' . self::$permissions[$fieldName]->enableQAUniqueLoanPrograms . ' --> ');
            foreach (self::$fileLoanPrograms as $lp) {
                if (in_array($lp, self::$permissions[$fieldName]->_QALoanPrograms)) {
                    DebugNotice(' <!-- LP ' . $lp . ' ' . self::$permissions[$fieldName]->QALoanPrograms . ' --> ');
                    $matchLP = true;
                    break;
                }
            }
            foreach (self::$fileInternalLoanPrograms as $lp) {
                if (in_array($lp, self::$permissions[$fieldName]->_QALoanPrograms)) {
                    DebugNotice(' <!-- ILP ' . $lp . ' ' . self::$permissions[$fieldName]->QALoanPrograms . ' --> ');
                    $matchILP = true;
                    break;
                }
            }
            foreach (self::$fileAdditionalLoanPrograms as $lp) {
                if (in_array($lp, self::$permissions[$fieldName]->_QALoanPrograms)) {
                    DebugNotice(' <!--ALP  ' . $lp . ' ' . self::$permissions[$fieldName]->QALoanPrograms . ' --> ');
                    $matchALP = true;
                    break;
                }
            }
        }

        if (self::$permissions[$fieldName]->enableFAUniqueLoanPrograms && self::$fileTab == 'FA') {
            DebugNotice(' <!-- ' . $fieldName . ' ' . self::$permissions[$fieldName]->enableFAUniqueLoanPrograms . ' --> ');
            foreach (self::$fileLoanPrograms as $lp) {
                if (in_array($lp, self::$permissions[$fieldName]->_FALoanPrograms)) {
                    DebugNotice(' <!-- LP ' . $lp . ' ' . self::$permissions[$fieldName]->FALoanPrograms . ' --> ');
                    $matchLP = true;
                    break;
                }
            }
            foreach (self::$fileInternalLoanPrograms as $lp) {
                if (in_array($lp, self::$permissions[$fieldName]->_FALoanPrograms)) {
                    DebugNotice(' <!-- ILP ' . $lp . ' ' . self::$permissions[$fieldName]->QALoanPrograms . ' --> ');
                    $matchILP = true;
                    break;
                }
            }
            foreach (self::$fileAdditionalLoanPrograms as $lp) {
                if (in_array($lp, self::$permissions[$fieldName]->_FALoanPrograms)) {
                    DebugNotice(' <!--ALP  ' . $lp . ' ' . self::$permissions[$fieldName]->QALoanPrograms . ' --> ');
                    $matchALP = true;
                    break;
                }
            }
        }

        if (((!self::$permissions[$fieldName]->enableQAUniqueLoanPrograms && self::$fileTab == 'QA')
                || (!self::$permissions[$fieldName]->enableFAUniqueLoanPrograms && self::$fileTab == 'FA'))
            || self::$fileTab == 'BO') {
            if (!self::$permissions[$fieldName]->loanProgram
                || (
                    (!self::$fileLoanPrograms || !sizeof(self::$fileLoanPrograms))
                    && (!self::$fileInternalLoanPrograms || !sizeof(self::$fileInternalLoanPrograms))
                    && (!self::$fileAdditionalLoanPrograms || !sizeof(self::$fileAdditionalLoanPrograms))
                )
            ) {
                DebugNotice(' <!-- none set ' . self::$permissions[$fieldName]->loanProgram . ' --> ');

                $matchILP = true;
                $matchLP = true;
                $matchALP = true;
            } else {

                foreach (self::$fileLoanPrograms as $lp) {
                    if (in_array($lp, self::$permissions[$fieldName]->_loanPrograms)) {
                        DebugNotice(' <!-- LP ' . $lp . ' ' . self::$permissions[$fieldName]->loanProgram . ' --> ');
                        $matchLP = true;
                        break;
                    }
                }
                foreach (self::$fileInternalLoanPrograms as $lp) {
                    if (in_array($lp, self::$permissions[$fieldName]->_loanPrograms)) {
                        DebugNotice(' <!-- ILP ' . $lp . ' ' . self::$permissions[$fieldName]->loanProgram . ' --> ');
                        $matchILP = true;
                        break;
                    }
                }
                foreach (self::$fileAdditionalLoanPrograms as $lp) {
                    if (in_array($lp, self::$permissions[$fieldName]->_loanPrograms)) {
                        DebugNotice(' <!--ALP  ' . $lp . ' ' . self::$permissions[$fieldName]->loanProgram . ' --> ');
                        $matchALP = true;
                        break;
                    }
                }
            }
        }
        //   DebugNotice(" <!-- $fieldName : ($matchTab && ($matchedFileType || $noFileTypeGiven)) && ($matchLP || $matchILP || $matchALP) --> ");

        return ($matchTab && ($matchedFileType || $noFileTypeGiven)) && ($matchLP || $matchILP || $matchALP);
    }

    public static function showField(string $fieldName, ?string $value = null, ?bool $showField = false): string
    {
        // self::isVisible($fieldName);
        //  || ($value && self::$displayValue) // showing hidden fields when they have a value needs to be a whole thing - 2023-05-03
        return $fieldName . '_disp ' .
            (self::isVisible($fieldName) ||
            ($value
                && $showField) ? 'secShow' . (!self::isVisible($fieldName) ? ' showDisabledNotification ' : '') : 'secHide');
        //   return $fieldName . '_disp ' . (self::isVisible($fieldName) ? 'secShow' : 'secHide');
    }

    public static function isParentFieldVisible(array $fields): ?string
    {

        foreach ($fields as $fieldName) {
            if (self::isVisible($fieldName)) {
                return '';
            }
        }
        return ' hide ';
    }

    /**
     * @param string $fieldName
     * @return string
     */
    public static function isEnabled(string $fieldName): string
    {
        return self::isVisible($fieldName) ? '' : ' disabled ';
    }

    public static function checkbox(
        string  $fieldName,
        bool    $canEdit,
        int     $tabIndex,
        ?string $value,
        ?bool   $checked,
        ?string $class = null,
        ?string $onBlur = null,
        ?bool   $readOnly = false,
        ?string $label = ''
    ): string
    {
        if (self::isMandatory($fieldName)) {
            $class .= ' mandatory ';
        }

        $allowed = self::isVisible($fieldName);

        $formField = self::$permissions[$fieldName];


        return '
            <div class="checkbox-inline">
                <label class="checkbox" for="' . $fieldName . '">
                <input 
                    tabindex="' . $tabIndex . '"
                    class="form-control ' . $class . '" 
                    type="checkbox" 
                    ' . ($checked ? 'checked="checked"' : '') . ' 
                    name="' . $fieldName . '" 
                    id="' . $fieldName . '"
                       ' . ($allowed ? '' : 'disabled') . '
                       ' . ($readOnly ? 'readonly' : '') . '
                    ' . ($canEdit ? 'onchange="' . $onBlur . '"' : '') . '
                    value="' . htmlentities($value) . '" /><span></span>
                ' . ($label ?: $formField->fieldLabel) . '</label>
            </div>
        ';
    }

    public static function switch(
        string  $fieldName,
        bool    $canEdit,
        int     $tabIndex,
        ?string $value,
        ?string $class = null,
        ?string $onChange = null,
        ?bool   $readOnly = false,
        ?string $actualFieldname = null
    ): string
    {
        if ($actualFieldname) {
            if (self::isMandatory($actualFieldname)) {
                $class .= ' mandatory ';
            }

            $allowed = self::isVisible($actualFieldname);
        } else {
            if (self::isMandatory($fieldName)) {
                $class .= ' mandatory ';
            }

            $allowed = self::isVisible($fieldName);
        }

        return '
            <span class="switch switch-icon">
                <label class="font-weight-bold">
                    <input 
                        tabindex="' . $tabIndex . '"
                        class="form-control ' . $class . '" 
                        ' . ($value ? 'checked="checked"' : '') . ' 
                       ' . ($allowed ? '' : 'disabled') . '
                       ' . ($readOnly ? 'readonly' : '') . '
                        id="' . $fieldName . '_switch" 
                        type="checkbox"
                        
                        ' . ($canEdit ? 'onchange="' . $onChange . '"' : '') . '
                ><input 
                    type="hidden" 
                    name="' . $fieldName . '" 
                    id="' . $fieldName . '"
                    value="' . htmlentities($value) . '" />
                    <span></span>
                </label>
            </span>        
        ';
    }

    public static function currency(
        string  $fieldName,
        bool    $canEdit,
        int     $tabIndex,
        ?string $value,
        ?string $class = null,
        ?string $onBlur = null,
        ?string $onChange = null,
        ?bool   $readOnly = false,
        ?string $onClick = null,
        bool    $disabled = false,
        ?string $tooltip = ''
    ): string
    {

        if (!$canEdit) {
            return '<span style="font-weight: bold;" id="' . $fieldName . '">$' . Currency::formatDollarAmountWithDecimal($value) . '</span>';
        }

        if (self::isMandatory($fieldName)) {
            $class .= ' mandatory ';
        }

        $allowed = self::isVisible($fieldName);

        if ($tooltip) {
            $tooltip = ' <div class="input-group-append"
                                     data-toggle="tooltip"
                                     data-trigger="hover"
                                     data-html="true"
                                     title ="' . $tooltip . '">
                                     <div class="input-group-text">
                                        <i class="fa fa-info-circle text-primary"></i>
                                    </div></div>';
        }

        return '
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text">$</span>
                </div>
                <input type="text"
                       class="form-control ' . $class . '"
                       name="' . $fieldName . '"
                       placeholder="0.00"
                       id="' . $fieldName . '"
                       value="' . Currency::formatDollarAmountWithDecimalZeros($value) . '"
                       tabindex="' . $tabIndex . '"
                       autocomplete="off"
                       onblur="currencyConverter(this, this.value); ' . $onBlur . '"
                       onchange="' . ($onChange ? $onChange . '; ' : '') . ' currencyConverter(this, this.value);"
                       onclick="' . $onClick . '"
                       ' . ($allowed && !$disabled ? '' : 'disabled') . '
                       ' . ($readOnly ? 'readonly' : '') . '
                />' . $tooltip . '
            </div>
        ';
    }

    public static function simpleCurrency(
        string  $fieldId,
        string  $fieldName,
        ?string $value,
        ?string $class = null,
        ?string $onBlur = null,
        ?string $onClick = null,
        ?string $Prepend = '$',
        ?string $Append = null
    ): string
    {
        $data = self::getFieldData();

        return '
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text">' . $Prepend . '</span>
                </div>
                <input type="text"
                       class="customFormField form-control ' . $class . '"
                       name="' . $fieldName . '"
                       placeholder="0.00"
                       id="' . ($fieldId ?: self::fieldIdFromName($fieldName)) . '"
                       value="' . (is_null($value) ? '' : (Strings::Currency($value, false, 2, '0.00'))) . '"
                       autocomplete="off"
                       onblur="currencyConverter(this, this.value); ' . $onBlur . '"
                       onclick="' . $onClick . '"
                       ' . $data . '
                />
                ' . ($Append ? '
                <div class="input-group-append">
                    <span class="input-group-text">' . $Append . '</span>
                </div>
                ' : '') . '
            </div>
        ';
    }

    public static function date(
        string  $fieldName,
        bool    $canEdit,
        int     $tabIndex,
        ?string $value,
        string  $class = '',
        string  $onBlur = '',
        bool    $readOnly = false
    ): string
    {
        if (!$canEdit) {
            return '<span style="font-weight: bold;" id="' . $fieldName . '">' . Dates::StandardDate($value) . '</span>';
        }

        if (self::isMandatory($fieldName)) {
            $class .= ' mandatory ';
        }

        $allowed = self::isVisible($fieldName);

        return '
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text">
                        <i class="fa fa-calendar text-primary"></i>
                    </span>
                </div>
                <input type="text"
                       class="form-control ' . $class . ' dateNewClass "
                       name="' . $fieldName . '"
                       id="' . $fieldName . '"
                       value="' .  Dates::StandardDate($value,null)  . '"
                       tabindex="' . $tabIndex . '"
                       autocomplete="off"
                       placeholder="MM/DD/YYYY"
                       onblur="' . $onBlur . '"
                       ' . ($allowed ? '' : 'disabled') . '
                       ' . ($readOnly ? 'readonly' : '') . '
                />
            </div>
        ';
    }

    public static function simpleDate(
        string  $fieldId,
        string  $fieldName,
        ?string $value,
        string  $class = '',
        string  $onBlur = ''
    ): string
    {
        $data = self::getFieldData();

        return '
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text">
                        <i class="fa fa-calendar text-primary"></i>
                    </span>
                </div>
                <input type="date"
                       class="customFormField form-control ' . $class . '"
                       name="' . $fieldName . '"
                       id="' . ($fieldId ?: self::fieldIdFromName($fieldName)) . '"
                       data-value="' . htmlentities($value) . '"
                       value="' . Dates::Datestamp($value, '') . '"
                       autocomplete="off"
                       placeholder="MM/DD/YYYY"
                       onblur="' . $onBlur . '"
                       ' . $data . '
                />
            </div>
        ';
    }

    public static function simpleTime(
        string  $fieldId,
        string  $fieldName,
        ?string $value,
        string  $class = '',
        string  $onBlur = ''
    ): string
    {
        $id = $fieldId ?: self::fieldIdFromName($fieldName);
        return '
        <script>
    $(function() {
        $("#' . $id . '").timepicker({
            defaultTime: "11:45:20 AM",
            minuteStep: 1,
            showSeconds: true,
            showMeridian: true
        });
    });
    </script>
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text">
                        <i class="fa fa-clock text-primary"></i>
                    </span>
                </div>
                <input type="text"
                       class="form-control ' . $class . '"
                       name="' . $fieldName . '"
                       id="' . $id . '"
                       data-value="' . htmlentities($value) . '"
                       value="' . $value . '"
                       autocomplete="off"
                       placeholder=""
                       onblur="' . $onBlur . '"
                />
            </div>
        ';
    }


    public static function simpleEmail(
        string  $fieldId,
        string  $fieldName,
        ?string $value,
        string  $class = '',
        string  $onBlur = ''
    ): string
    {
        $data = self::getFieldData();

        return '
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text">
                        <i class="fa fa-envelope text-primary"></i>
                    </span>
                </div>
                <input type="email"
                       class="customFormField form-control ' . $class . '"
                       name="' . $fieldName . '"
                       id="' . ($fieldId ?: self::fieldIdFromName($fieldName)) . '"
                       value="' . htmlentities($value) . '"
                       autocomplete="off"
                       onblur="' . $onBlur . '"
                       ' . $data . '
                />
            </div>
        ';
    }

    public static function simpleTextArea(
        string  $fieldId,
        string  $fieldName,
        ?string $value,
        string  $class = '',
        string  $onBlur = ''
    ): string
    {
        $data = self::getFieldData();

        return '
                <textarea
                       class="customFormField form-control ' . $class . '"
                       name="' . $fieldName . '"
                       id="' . ($fieldId ?: self::fieldIdFromName($fieldName)) . '"
                       autocomplete="off"
                       onblur="' . $onBlur . '"
                       ' . $data . '
                >' . $value . '</textarea>
        ';
    }

    public static function text(
        string  $fieldName,
        ?bool   $canEdit = null,
        ?int    $tabIndex = null,
        ?string $value = null,
        ?string $class = '',
        ?string $onBlur = '',
        ?bool   $readOnly = false,
        ?string $maxlength = '',
        ?array  $data = null,
        bool    $disabled = false,
        ?array   $attributes = [] // New parameter for extra HTML attributes
    ): string
    {
        if (!$canEdit) {
            return '<span style="font-weight: bold;" id="' . $fieldName . '">' . $value . '</span>';
        }

        if (self::isMandatory($fieldName)) {
            $class .= ' mandatory ';
        }

        $allowed = self::isVisible($fieldName);

//        if (!$allowed) {
//            return self::hidden($fieldName, $value);
//        }

        $type = self::$fieldColumnTypes[$fieldName] ?? null;
        if ($type && !$maxlength) {
            $maxlength = dbExaminer::getDataLength($type);
        }

        $data_text = [];
        if ($data && sizeof($data)) {
            foreach ($data as $key => $val) {
                $data_text[] = 'data-' . $key . '= "' . htmlentities($val) . '"';
            }
        }

        // Build extra HTML attributes
        $extraAttrs = '';
        if (!empty($attributes)) {
            foreach ($attributes as $attr => $val) {
                $extraAttrs .= ' ' . $attr . '="' . htmlentities($val) . '"';
            }
        }

        return '
                <input type="text"
                       class="form-control ' . $class . '"
                       name="' . $fieldName . '"
                       id="' . $fieldName . '"
                       value="' . htmlentities($value) . '"
                       tabindex="' . $tabIndex . '"
                       maxlength ="' . $maxlength . '"
                       onblur="' . $onBlur . '"
                       ' . implode(PHP_EOL, $data_text) . '
                       ' . ($allowed && !$disabled ? '' : 'disabled') . '
                       ' . ($readOnly ? 'readonly' : '') . '
                       ' . $extraAttrs . '
                />
        ';
    }

    public static function textarea(
        string  $fieldName,
        bool    $canEdit,
        int     $tabIndex,
        ?string $value,
        ?string $class = '',
        ?string $onBlur = '',
        ?bool   $readOnly = false,
        ?string $maxlength = '',
        ?string $inc = ''
    ): string
    {
        if (!$canEdit) {
            return '<span style="font-weight: bold;" id="' . $fieldName . '">' . $value . '</span>';
        }

        if (self::isMandatory($fieldName)) {
            $class .= ' mandatory ';
        }

        $allowed = self::isVisible($fieldName);

        $type = self::$fieldColumnTypes[$fieldName] ?? null;

        if ($type) {
            $maxlength = dbExaminer::getDataLength($type);
        }
        $fieldData = '
                <textarea
                       class="form-control ' . $class . '"
                       name="' . $fieldName . '"';
        if ($inc) {
            $fieldData .= 'id="' . $fieldName . '_' . $inc . '"';
        } else {
            $fieldData .= 'id="' . $fieldName . '"';
        }

        $fieldData .= 'tabindex="' . $tabIndex . '"
                       maxlength ="' . $maxlength . '"
                       onblur="' . $onBlur . '"
                       ' . ($allowed ? '' : 'disabled') . '
                       ' . ($readOnly ? 'readonly' : '') . '
                >' . $value . '</textarea>
        ';
        return $fieldData;
    }

    public static function zipCode(
        string  $fieldName,
        bool    $canEdit,
        int     $tabIndex,
        ?string $value,
        string  $class,
        string  $onBlur = '',
        bool    $readOnly = false
    ): string
    {
        if (!$canEdit) {
            return '<span style="font-weight: bold;" id="' . $fieldName . '">' . $value . '</span>';
        }

        if (self::isMandatory($fieldName)) {
            $class .= ' mandatory ';
        }

        $allowed = self::isVisible($fieldName);

        return '
                <input type="text"
                       class="form-control zipCode ' . $class . '"
                       name="' . $fieldName . '"
                       id="' . $fieldName . '"
                       value="' . htmlentities($value) . '"
                       tabindex="' . $tabIndex . '"
                       onblur="' . $onBlur . '"
                       ' . ($allowed ? '' : 'disabled') . '
                       ' . ($readOnly ? 'readonly' : '') . '
                />
        ';
    }


    public static function simpleZipCode(
        string  $fieldId,
        string  $fieldName,
        ?string $value,
        string  $class = '',
        string  $onBlur = ''
    ): string
    {
        $data = self::getFieldData();

        return '
                <input type="text"
                       class="customFormField form-control zipCode ' . $class . '"
                       name="' . $fieldName . '"
                       id="' . ($fieldId ?: self::fieldIdFromName($fieldName)) . '"
                       value="' . htmlentities($value) . '"
                       onblur="' . $onBlur . '"
                       ' . $data . '
                />
        ';
    }

    public static function email(
        string  $fieldName,
        bool    $canEdit,
        int     $tabIndex,
        ?string $value,
        string  $class,
        string  $onBlur = '',
        bool    $readOnly = false
    ): string
    {
        if (!$canEdit) {
            return '<span style="font-weight: bold;" id="' . $fieldName . '">' . $value . '</span>';
        }

        if (self::isMandatory($fieldName)) {
            $class .= ' mandatory ';
        }

        $allowed = self::isVisible($fieldName);

        return '
                <input type="email"
                       class="form-control ' . $class . '"
                       name="' . $fieldName . '"
                       id="' . $fieldName . '"
                       value="' . htmlentities($value) . '"
                       tabindex="' . $tabIndex . '"
                       onblur="' . $onBlur . '"
                       ' . ($allowed ? '' : 'disabled') . '
                       ' . ($readOnly ? 'readonly' : '') . '
                />
        ';
    }

    public static function simpleHidden(
        string  $fieldName,
        ?string $value
    ): string
    {
        return '<input 
        type="hidden" 
        value="' . htmlentities($value) . '" 
        name="' . $fieldName . '" 
        id="' . (self::fieldIdFromName($fieldName)) . '"
         />';
    }

    public static function phone(
        string  $fieldName,
        bool    $canEdit,
        int     $tabIndex,
        ?string $value,
        ?string $class = '',
        ?string $onBlur = '',
        ?bool   $readOnly = false
    ): string
    {
        if (!$canEdit) {
            return '<span style="font-weight: bold;" id="' . $fieldName . '">' . Strings::formatPhoneNumber($value) . '</span>';
        }

        if (self::isMandatory($fieldName)) {
            $class .= ' mandatory ';
        }

        $allowed = self::isVisible($fieldName);

        return '
                <input type="text"
                       class="form-control mask_phone ' . $class . '"
                       name="' . $fieldName . '"
                       id="' . $fieldName . '"
                       value="' . Strings::formatPhoneNumber($value) . '"
                       placeholder="(___) ___ - ____ Ext ____"
                       tabindex="' . $tabIndex . '"
                       onblur="' . $onBlur . '"
                       ' . ($allowed ? '' : 'disabled') . '
                       ' . ($readOnly ? 'readonly' : '') . '
                />
        ';
    }

    public static function simplePhone(
        string  $fieldId,
        string  $fieldName,
        ?string $value,
        ?string $class = '',
        ?string $onBlur = ''
    ): string
    {
        $data = self::getFieldData();

        return '
                <input type="text"
                       class="customFormField form-control mask_phone ' . $class . '"
                       name="' . $fieldName . '"
                       id="' . ($fieldId ?: self::fieldIdFromName($fieldName)) . '"
                       value="' . Strings::formatPhoneNumber($value) . '"
                       placeholder="(___) ___ - ____ Ext ____"
                       onblur="' . $onBlur . '"
                       ' . $data . '
                />
        ';
    }

    public static function simpleText(
        string  $fieldId,
        string  $fieldName,
        ?string $value,
        ?string $class = '',
        ?string $onBlur = '',
        ?array  $data = null,
        ?string $placeholder = ''
    ): string
    {
        $_data = '';
        if ($data) {
            foreach ($data as $k => $v) {
                $_data .= 'data-' . $k . '="' . htmlentities($v) . '"' . PHP_EOL;
            }
        }

        $_data .= ' ' . self::getFieldData();

        return '
                <input type="text"
                       class="customFormField form-control ' . $class . '"
                       name="' . $fieldName . '"
                       id="' . ($fieldId ?: self::fieldIdFromName($fieldName)) . '"
                       value="' . htmlentities($value) . '"
                       onblur="' . ($onBlur ? $onBlur : 'return true') . '"
                       placeholder="' . htmlentities($placeholder) . '"
                       ' . $_data . '
                />
        ';
    }

    public static function cell(
        string  $fieldName,
        bool    $canEdit,
        int     $tabIndex,
        ?string $value,
        string  $class,
        string  $onBlur = '',
        bool    $readOnly = false
    ): string
    {
        if (!$canEdit) {
            return '<span style="font-weight: bold;" id="' . $fieldName . '">' . Strings::formatCellNumber($value) . '</span>';
        }

        if (self::isMandatory($fieldName)) {
            $class .= ' mandatory ';
        }

        $allowed = self::isVisible($fieldName);

        return '
                <input type="text"
                       class="form-control mask_cellnew ' . $class . '"
                       name="' . $fieldName . '"
                       id="' . $fieldName . '"
                       value="' . Strings::formatCellNumber($value) . '"
                       placeholder="(___) ___ - ____"
                       tabindex="' . $tabIndex . '"
                       onblur="' . $onBlur . '"
                       ' . ($allowed ? '' : 'disabled') . '
                       ' . ($readOnly ? 'readonly' : '') . '
                />
        ';
    }

    public static function percentage(
        string  $fieldName,
        bool    $canEdit,
        int     $tabIndex,
        ?string $value,
        string  $class,
        string  $onBlur = '',
        bool    $readOnly = false,
        int     $decimals = 2,
        bool    $disabled = false
    ): string
    {
        if (!$canEdit) {
            return '<span style="font-weight: bold;" id="' . $fieldName . '">' . Currency::formatDollarAmountWithDecimal($value, $decimals) . '%</span>';
        }

        if (self::isMandatory($fieldName)) {
            $class .= ' mandatory ';
        }

        $allowed = self::isVisible($fieldName);

//        if (!$allowed) {
//            return self::hidden($fieldName, Strings::replaceCommaValues($value));
//        }

        return '
            <div class="input-group">
                <input type="text"
                       class="form-control input-sm ' . $class . '"
                       name="' . $fieldName . '"
                       id="' . $fieldName . '"
                       value="' . Currency::formatDollarAmountWithDecimal($value, $decimals) . '"
                       onblur="' . $onBlur . '"
                       placeholder="0.0"
                       tabindex="' . $tabIndex . '"
                       autocomplete="off"
                       ' . ($allowed && !$disabled ? '' : 'disabled') . '
                       ' . ($readOnly ? 'readonly' : '') . '
                />
                <div class="input-group-append">
                    <span class="input-group-text">
                        %
                    </span>
                </div>
            </div>
        ';
    }

    public static function simplePercentage(
        string  $fieldId,
        string  $fieldName,
        ?string $value = null,
        ?string $class = null,
        ?string $onBlur = '',
        ?bool   $readOnly = false,
        ?string $Append = '%'
    ): string
    {
        $data = self::getFieldData();

        return '
            <div class="input-group">
                <input type="text"
                       class="customFormField form-control input-sm ' . $class . '"
                       name="' . $fieldName . '"
                       id="' . ($fieldId ?: self::fieldIdFromName($fieldName)) . '"
                       value="' . Currency::formatDollarAmountWithDecimal($value, 3) . '"
                       onblur="' . $onBlur . '"
                       placeholder="0.0"
                       autocomplete="off"
                       ' . ($readOnly ? 'readonly' : '') . '
                       ' . $data . '
                />
                <div class="input-group-append">
                    <span class="input-group-text">
                        ' . $Append . '
                    </span>
                </div>
            </div>
        ';
    }

    public static function simpleDecimal(
        string  $fieldId,
        string  $fieldName,
        ?string $value = null,
        ?string $class = null,
        ?string $onBlur = '',
        ?bool   $readOnly = false
    ): string
    {

        return '
                <input type="text"
                       class="form-control input-sm ' . $class . '"
                       name="' . $fieldName . '"
                       id="' . ($fieldId ?: self::fieldIdFromName($fieldName)) . '"
                       value="' . Currency::formatDollarAmountWithDecimal($value) . '"
                       onblur="' . $onBlur . '"
                       placeholder="0.0"
                       autocomplete="off"
                       ' . ($readOnly ? 'readonly' : '') . '
                />
        ';
    }

    public static function simpleFile(
        string  $fieldId,
        string  $fieldName,
        ?string $value = null,
        ?string $class = null,
        ?string $onBlur = '',
        ?bool   $readOnly = false,
        ?bool   $multiple = false
    ): string
    {

        return '
                <input type="file"
                       class="customFormField form-control input-sm fileUploadValidation ' . $class . '"
                       name="' . $fieldName . '"
                       id="' . ($fieldId ?: self::fieldIdFromName($fieldName)) . '"
                       value="' . $value . '"
                       onblur="' . $onBlur . '"
                       ' . ($readOnly ? 'readonly' : '') . '
                       ' . ($multiple ? 'multiple' : '') . '
                />
                
        ' .  ($value ? self::generateFileView($value) : '')
          .  ($value ? self::generateFileDelete($value) : '')
            ;
    }

    public static function generateFileView(string $value = ''): string
    {
      return $value ?  '<a class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass" 
                data-file="' . cypher::myEncryption($value) . '"
                href="/backoffice/api_v2/load_file?file_path='.cypher::myEncryption($value).'" 
                target="_blank"
                 data-toggle="popover" 
                 data-html="true" 
                 data-content="Click to view"><i class="fa fa-eye icon-md"></i></a>' : '';

    }
    public static function generateFileDelete(string $value = ''): string
    {
      return $value ?  '<a class="btn btn-sm btn-danger btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass " 
                data-file="'.cypher::myEncryption($value).'"
                onclick="LMRequest.deleteFile(this);"
                target="_blank"
                 data-toggle="popover" 
                 data-html="true" 
                 data-content="Click to Delete"><i class="fa fa-minus-circle icon-md "></i></a>' : '';

    }

    public static function simpleDays(
        string  $fieldId,
        string  $fieldName,
        ?string $value = null,
        ?string $class = null,
        ?string $onBlur = '',
        ?bool   $readOnly = false
    ): string
    {

        return '
            <div class="input-group">
                <input type="number"
                       class="form-control input-sm ' . $class . '"
                       name="' . $fieldName . '"
                       id="' . ($fieldId ?: self::fieldIdFromName($fieldName)) . '"
                       value="' . Strings::replaceCommaValues($value) . '"
                       onblur="' . $onBlur . '"
                       placeholder="0"
                       autocomplete="off"
                       ' . ($readOnly ? 'readonly' : '') . '
                />
                <div class="input-group-append">
                    <span class="input-group-text">
                        Days
                    </span>
                </div>
            </div>
        ';
    }

    public static function hasChangeLog(string $fieldName): bool
    {
        if (!self::$changeLogColumns || !sizeof(self::$changeLogColumns)) {
            return false;
        }
        return in_array($fieldName, self::$changeLogColumns);
    }

    public static function sectionHeader(
        string $id,
        string $label
    ): string
    {
        return '
            <div class="form-group row  m-0 my-2 px-0 py-2 bg-secondary" id="' . $id . '">
                <div class=" col-md-12">
                    <label><b>' . $label . '</b></label>
                </div>
            </div>
        
        ';
    }

    public static function getFieldLabel(string $fieldName): ?string
    {
        return (self::$permissions[$fieldName]->fieldLabel) ?: '';
    }

    public static function labelWithValue(
        string  $fieldName,
        ?string $value = null,
        ?string $fieldLabel = null,
        ?string $class = null
    ): string
    {
        return self::label2(
            $fieldName,
            $class,
            '',
            '',
            '',
            $fieldLabel,
            $value
        );
    }

    public static function labelPlaceholder(string $fieldName): ?string
    {

        switch (self::$masterFields[$fieldName]->fieldType) {
            case 'string':
            case 'int':
                $placeHolder = 'Please Enter ' . self::$permissions[$fieldName]->fieldLabel;
                break;
            case 'datetime':
            case 'date':
                $placeHolder = 'MM/DD/YYYY';
                break;
            case 'float':
                $placeHolder = '0.00';
                break;
            default:
                $placeHolder = self::$permissions[$fieldName]->fieldLabel;
                break;
        }
        return $placeHolder;
    }

    public static function changeLog(
        ?string $primary_key,
        string  $column,
        string  $object,
        string  $title = null
    ): string
    {
        if (!$primary_key) {
            return '';
        }

        if (self::$isPublicUser) {
            return '';
        }
        return '<span
                data-primary_key="' . cypher::myEncryption($primary_key) . '"
                data-column="' . cypher::myEncryption($column) . '"
                data-object="' . cypher::myEncryption($object) . '"
                data-title="' . htmlentities($title) . '"
                onclick="changeLog.showFor(this);"
                    ><i class="fa fa-clock cursor-pointer"></i></span>';
    }

    public static function label(
        string  $fieldName,
        ?string $class = '',
        ?string $tooltip = '',
        ?string $extra = '',
        ?string $inc = null,
        ?string $fieldLabel = null
    ): string
    {
        return self::label2(
            $fieldName,
            $class,
            $tooltip,
            $extra,
            $inc,
            $fieldLabel
        );
    }

    public static function label2(
        string  $fieldName,
        ?string $class = '',
        ?string $tooltip = '',
        ?string $extra = '',
        ?string $inc = null,
        ?string $fieldLabel = null,
        ?string $value = null
    ): string
    {
        // always show label, hidden with showField function
//        $allowed = self::isVisible($fieldName);
//        if (!$allowed) {
//            return '';
//        }

        $formField = self::$permissions[$fieldName] ?? null;

        if (!$formField) {
            $class .= ' missing_label ';
        }

        if ($tooltip) {
            return '
            <label class="font-weight-bold ' . $class . '" for="' . (($inc != '') ? $fieldName . '_' . $inc : $fieldName) . '">
                <i 
                    class="fa fa-info-circle text-primary"
                    data-toggle="tooltip" 
                    data-trigger="hover" 
                    data-html="true"
                    title="' . htmlentities($tooltip) . '"></i> 
                ' . ($fieldLabel ?: ($formField ? htmlspecialchars_decode($formField->fieldLabel) : '')) . ' ' . $extra . '
            </label>
            ';
        }

        if (self::hasChangeLog($fieldName)) {
            if (!self::$isPublicUser) {
                $extra .= '
                        <span
                            data-lmrid="' . self::$encLMRId . '"
                            data-column="' . $fieldName . '"
                            onclick="changeLog.showForLMRId(this);"
                        ><i class="fa fa-clock"></i></span>            
            ';
            }
        }

        return '
            <label class="font-weight-bold ' . $class . '" for="' . (($inc != '') ? $fieldName . '_' . $inc : $fieldName) . '">
                ' . ($fieldLabel ?: ($formField ? htmlspecialchars_decode($formField->fieldLabel) : '')) . ' ' . $extra . '
            </label>
        ';
    }

    public static function sectionButtonDelete(
        string  $title,
        ?string $class = null,
        ?array  $data = null
    ): string
    {
        $data_html = [];
        foreach ($data as $k => $v) {
            $data_html[] = 'data-' . strtolower($k) . '="' . htmlentities($v) . '"';
        }
        return '
            <span
               ' . implode("\r\n", $data_html) . '
               class=" btn btn-danger btn-xs btn-text-primary btn-hover-danger btn-icon ml-2 tooltipClass cursor-pointer ' . $class . '"
               title="' . $title . '">
                <i class="flaticon2-trash"></i>
            </span>        
        ';
    }

    public static function sectionButtonShowHide(
        bool    $open,
        ?string $title = null,
        ?string $class = null,
        ?array  $data = null
    ): string
    {
        $data_html = [];
        foreach ($data as $k => $v) {
            $data_html[] = 'data-' . strtolower($k) . '="' . htmlentities($v) . '"';
        }
        return '
            <span
               ' . implode("\r\n", $data_html) . '
               class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer ' . $class . '"
               title="' . $title . '">
                <i class="ki ki-arrow-' . ($open ? 'down' : 'up') . ' icon-nm"></i>
            </span>        
        ';
    }

    public static function hidden(
        string  $fieldName,
        ?string $value
    ): string
    {
        return '
            <input 
                type="hidden"
                id="' . $fieldName . '"
                name="' . $fieldName . '"
                value="' . htmlentities($value) . '"
            />
        ';
    }

    public static function sectionButtonAdd(
        ?string $title = null,
        ?string $onclick = null,
        ?string $class = null,
        ?array  $data = null
    ): string
    {
        $data_html = [];
        foreach ($data as $k => $v) {
            $data_html[] = 'data-' . strtolower($k) . '="' . htmlentities($v) . '"';
        }
        return '
            <span
               ' . implode("\r\n", $data_html) . '
               onclick="' . $onclick . '"
               class="btn btn-sm btn-success btn-text-primary  btn-icon ml-2 tooltipClass ' . $class . '"
               title="' . $title . '">
                <i class="icon-md fas fa-plus"></i>
            </span>        
        ';
    }

    public static function toggleSection(
        string $sectionID,
        string $label,
        bool   $canToggle,
        bool   $isOpen,
        string $onclick = ''
    ): string
    {
        $html = '<!-- toggleSection -->
            <h3 class="card-label ' . $label . '">';
        if ($canToggle) {
            $html .= '
                    <span
                       class="cursor-pointer tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass property1DetailsDivIdToggle d-none"
                       data-card-tool="toggle"
                       data-toggle="tooltip" 
                       data-placement="top"
                       title="Click To Open/Close ' . htmlentities(self::getSectionHeading($sectionID)) . '"
                       onclick="' . $onclick . '">
                        <i class="ki ki-arrow-' . ($isOpen ? 'down' : 'up') . ' icon-nm arrowClass"></i>
                    </span>
            ';
        }
        $html .= self::getSectionHeading($sectionID) . '</h3>';

        $tooltip = self::getTooltip($sectionID);
        if ($tooltip) {
            $html .= '
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="' . htmlentities($tooltip) . '"></i>
                   ';
        }

        return $html;
    }

    public static function toggleSectionV2(
        string  $sectionID,
        bool    $canToggle,
        bool    $isOpen,
        ?string $onclick = '',
        ?string $label = '',
        ?string $tooltip = ''
    ): string
    {
        $html = '<!-- toggleSectionV2 -->
        <div class="card-title">
            <h3 class="card-label ' . $sectionID . '-label">' . self::getSectionHeading($sectionID, $label) . '</h3>
        ';

        $tooltip = self::getTooltip($sectionID, $tooltip);
        if ($tooltip) {
            $html .= '
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="' . htmlentities($tooltip) . '"></i>
                   ';
        }

        $html .= '</div>';

        if ($canToggle) {
            $html .= '
            <div class="card-toolbar">
                <span
                    class="cursor-pointer tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                    data-card-tool="toggle"
                    data-toggle="tooltip" 
                    data-placement="top"
                    data-section="' . $sectionID . '"
                    title="Click To Open/Close ' . htmlentities(self::getSectionHeading($sectionID)) . '"
                    onclick="' . $onclick . '">
                        <i class="ki ki-arrow-' . ($isOpen ? 'down' : 'up') . ' icon-nm arrowClass"></i>
                </span>
            </div>
            ';
        }
        return $html;
    }

    public static function Tooltip(string $tooltip): string
    {
        return '
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="' . htmlentities($tooltip) . '"></i>
                   ';
    }

    public static function addSectionV2(
        string $sectionID,
        bool   $canToggle,
        string $class = '',
        string $span = '',
        string $span_title = '',
        string $label = '',
        string $tooltip = ''
    ): string
    {
        $html = '<!-- addSectionV2 -->
        <div class="card-title">
            <h3 class="card-label ' . $sectionID . '-label">' . self::getSectionHeading($sectionID, $label) . '</h3>
        ';

        $tooltip = self::getTooltip($sectionID, $tooltip);
        if ($tooltip) {
            $html .= '
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="' . htmlentities($tooltip) . '"></i>
                   ';
        }

        $html .= '</div>';

        if ($canToggle) {
            $html .= '
            <div class="card-toolbar">
                <span
                class="' . $class . '"
                ' . $span . '
                >
                <i class=" fas fa-plus icon-md tooltipclass"
                title="' . $span_title . '"></i>
                </a>            
            </div>
            ';
        }
        return $html;
    }

    /**
     * @param string $name
     * @param string $inputClass
     * @param string $labelClass
     * @param string $onchange
     * @param string|null $value
     * @param FormField[]|null $additionalValues
     * @return string
     */
    public static function simpleEthnicity(
        string  $name,
        string  $inputClass,
        string  $labelClass,
        string  $onchange,
        ?string $value,
        ?array  $additionalValues = null
    ): string
    {
        $id = self::fieldIdFromName($name);

        ob_start();
        ?>
        <div class="radio-inline">
            <label class="radio radio-solid" for="<?php echo $id; ?>2">
                <input type="radio"
                       class="Hispanic BorChildRadio  BEYes borrowerSelector"
                       name="<?php echo $name; ?>"
                       id="<?php echo $id; ?>2"
                       value="2"
                    <?php echo $value == 2 ? 'checked="checked"' : ''; ?>
                       data-allow="1">
                <span></span> Hispanic or Latino
            </label>
            <div class="col-md-12 my-4 pl-6 " id="Hispanic">
                <label for="bFiEthnicitySub" class=""></label>
                <div class="radio-list">
                    <label class="radio radio-solid"
                           for="FormField_<?php echo $additionalValues['EthnicitySub']->fieldName; ?>1">
                        <input type="radio"
                               name="FormField[<?php echo $additionalValues['EthnicitySub']->fieldName; ?>]"
                               id="FormField_<?php echo $additionalValues['EthnicitySub']->fieldName; ?>1"
                            <?php echo $additionalValues['EthnicitySub']->value == 1 ? 'checked="checked"' : ''; ?>
                               class="borrowerSelector HispanicPrintOrigin BorChildRadio "
                               value="1"><span></span>
                        Mexican
                    </label>
                    <label class="radio radio-solid"
                           for="FormField_<?php echo $additionalValues['EthnicitySub']->fieldName; ?>2">
                        <input type="radio"
                               name="FormField[<?php echo $additionalValues['EthnicitySub']->fieldName; ?>]"
                               id="FormField_<?php echo $additionalValues['EthnicitySub']->fieldName; ?>2"
                            <?php echo $additionalValues['EthnicitySub']->value == 2 ? 'checked="checked"' : ''; ?>
                               class="borrowerSelector HispanicPrintOrigin BorChildRadio"
                               value="2">
                        <span></span>
                        Puerto Rican
                    </label>
                    <label class="radio radio-solid"
                           for="FormField_<?php echo $additionalValues['EthnicitySub']->fieldName; ?>3">
                        <input type="radio"
                               name="FormField[<?php echo $additionalValues['EthnicitySub']->fieldName; ?>]"
                               id="FormField_<?php echo $additionalValues['EthnicitySub']->fieldName; ?>3"
                               class="borrowerSelector HispanicPrintOrigin BorChildRadio"
                            <?php echo $additionalValues['EthnicitySub']->value == 3 ? 'checked="checked"' : ''; ?>
                               value="3"><span></span>
                        Cuban
                    </label>
                    <label class="radio radio-solid"
                           for="FormField_<?php echo $additionalValues['EthnicitySub']->fieldName; ?>4">
                        <input type="radio"
                               name="FormField[<?php echo $additionalValues['EthnicitySub']->fieldName; ?>]"
                               id="FormField_<?php echo $additionalValues['EthnicitySub']->fieldName; ?>4"
                               class="borrowerSelector HispanicPrintOrigin BorChildRadio"
                            <?php echo $additionalValues['EthnicitySub']->value == 4 ? 'checked="checked"' : ''; ?>
                               value="4"><span></span>
                        Other Hispanic or Latino
                    </label>
                </div>
                <div class="col-md-12 pl-6 " id="HispanicPrintOriginDiv">
                    <label class="col-md-12"
                           for="FormField_<?php echo htmlentities($additionalValues['EthnicitySubOther']->fieldName); ?>txt">
                        Print Origin
                    </label>
                    <input type="text"
                           name="FormField[<?php echo htmlentities($additionalValues['EthnicitySubOther']->fieldName); ?>]"
                           id="FormField_<?php echo htmlentities($additionalValues['EthnicitySubOther']->fieldName); ?>txt"
                           class="form-control input-sm"
                           value="<?php echo htmlentities($additionalValues['EthnicitySubOther']->value); ?>">
                    <span class="text-muted">
                            For example: Argentinean, Colombian, Dominican, Nicaraguan,Salvadoran, Spaniard, and so on
                        </span>
                </div>
            </div>

            <label class="radio radio-solid" for="<?php echo $id; ?>1">
                <input type="radio"
                       name="<?php echo $name; ?>"
                       id="<?php echo $id; ?>1"
                    <?php echo $value == 1 ? 'checked="checked"' : ''; ?>
                       value="1"
                       class="Hispanic borrowerSelector  BENo BorChildRadio">
                <span></span>
                Not Hispanic or Latino
            </label>
            <label class="radio radio-solid" for="<?php echo $id; ?>3">
                <input type="radio"
                       name="<?php echo $name; ?>"
                       id="<?php echo $id; ?>3"
                    <?php echo $value == 3 ? 'checked="checked"' : ''; ?>
                       value="3"
                       class="Hispanic borrowerSelector  BENo BorChildRadio"><span></span>
                Not Disclosed
            </label>
        </div>
        <?php

        return ob_get_clean();
    }

    /**
     * @param string $name
     * @param string $inputClass
     * @param string $labelClass
     * @param string $onchange
     * @param string|null $value
     * @param FormField[]|null $additionalValues
     * @return string
     */
    public static function simpleRace(
        string  $name,
        string  $inputClass,
        string  $labelClass,
        string  $onchange,
        ?string $value,
        ?array  $additionalValues = null
    ): string
    {
        $id = self::fieldIdFromName($name);

        ob_start();
        ?>
        <div class="radio-list">
            <label class="radio radio-solid" for="<?php echo $id; ?>1">
                <input type="radio"
                       class="BorChildRadio Race  BRaceAM borrowerSelector"
                       name="<?php echo $name; ?>"
                       id="<?php echo $id; ?>1"
                       value="1"
                    <?php echo $value == 1 ? 'checked="checked"' : ''; ?>
                       data-allow="1"><span></span>
                American Indian or Alaska Native
            </label>
            <label class="radio radio-solid" for="<?php echo $id; ?>2">
                <input type="radio"
                       name="<?php echo $name; ?>"
                       id="<?php echo $id; ?>2"
                       value="2"
                    <?php echo $value == 2 ? 'checked="checked"' : ''; ?>
                       class="BorChildRadio Race borrowerSelector BRaceAS   "
                       data-allow="1"><span></span>
                Asian
            </label>
            <div class="col-md-12 px-6" id="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>Asian">
                <div class="radio-list">
                    <label class="radio radio-solid"
                           for="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>1">
                        <input type="radio"
                               name="FormField[<?php echo $additionalValues['RaceSub']->fieldName; ?>]"
                               id="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>1"
                            <?php echo $additionalValues['RaceSub']->value == 1 ? 'checked="checked"' : ''; ?>
                               class="BorChildRadio borrowerSelector Asian"
                               value="1"><span></span>
                        Asian Indian
                    </label>
                    <label class="radio radio-solid"
                           for="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>2">
                        <input type="radio"
                               name="FormField[<?php echo $additionalValues['RaceSub']->fieldName; ?>]"
                               id="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>2"
                            <?php echo $additionalValues['RaceSub']->value == 2 ? 'checked="checked"' : ''; ?>
                               class="BorChildRadio borrowerSelector Asian"
                               value="2"><span></span>
                        Chinese
                    </label>
                    <label class="radio radio-solid"
                           for="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>3">
                        <input type="radio"
                               name="FormField[<?php echo $additionalValues['RaceSub']->fieldName; ?>]"
                               id="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>3"
                            <?php echo $additionalValues['RaceSub']->value == 3 ? 'checked="checked"' : ''; ?>
                               class="BorChildRadio borrowerSelector Asian"
                               value="3"><span></span>
                        Filipino
                    </label>
                    <label class="radio radio-solid"
                           for="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>4">
                        <input type="radio"
                               name="FormField[<?php echo $additionalValues['RaceSub']->fieldName; ?>]"
                               id="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>4"
                            <?php echo $additionalValues['RaceSub']->value == 4 ? 'checked="checked"' : ''; ?>
                               class="BorChildRadio borrowerSelector Asian"
                               value="4"><span></span>
                        Japanese
                    </label>
                    <label class="radio radio-solid"
                           for="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>5">
                        <input type="radio"
                               name="FormField[<?php echo $additionalValues['RaceSub']->fieldName; ?>]"
                               id="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>5"
                            <?php echo $additionalValues['RaceSub']->value == 5 ? 'checked="checked"' : ''; ?>
                               class="BorChildRadio borrowerSelector Asian"
                               value="5">
                        <span></span>Korean
                    </label>
                    <label class="radio radio-solid"
                           for="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>6">
                        <input type="radio"
                               name="FormField[<?php echo $additionalValues['RaceSub']->fieldName; ?>]"
                               id="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>6"
                            <?php echo $additionalValues['RaceSub']->value == 6 ? 'checked="checked"' : ''; ?>
                               class="BorChildRadio borrowerSelector Asian"
                               value="6"><span></span>
                        Vietnamese
                    </label>
                    <label class="radio radio-solid"
                           for="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>7">
                        <input type="radio"
                               name="FormField[<?php echo $additionalValues['RaceSub']->fieldName; ?>]"
                               id="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>7"
                            <?php echo $additionalValues['RaceSub']->value == 7 ? 'checked="checked"' : ''; ?>
                               class="BorChildRadio borrowerSelector Asian"
                               value="7"><span></span>
                        Other Asian
                    </label>
                    <div class="col-md-12 pl-6" id="AsianDiv">
                        <label class="col-md-12"
                               for="FormField_<?php echo $additionalValues['RaceAsianOther']->fieldName; ?>">
                            Print Race </label>
                        <input type="text"
                               class="form-control input-sm"
                               name="FormField[<?php echo $additionalValues['RaceAsianOther']->fieldName; ?>]"
                               id="FormField_<?php echo $additionalValues['RaceAsianOther']->fieldName; ?>"
                               value="<?php echo htmlentities($additionalValues['RaceAsianOther']->value); ?>">
                        <span class="text-muted">For example: Hmong, Laotian, Thai, Pakistani, Cambodian, and so on.</span>

                    </div>
                </div>
            </div>
            <label class="radio radio-solid"
                   for="<?php echo $id; ?>3">
                <input type="radio"
                       name="<?php echo $name; ?>"
                       id="<?php echo $id; ?>3"
                       value="3"
                    <?php echo $value == 3 ? 'checked="checked"' : ''; ?>
                       class="BorChildRadio Race borrowerSelector BRaceBA   "
                       data-allow="1"><span></span>
                Black or African American
            </label>
            <label class="radio radio-solid"
                   for="<?php echo $id; ?>4">
                <input type="radio"
                       name="<?php echo $name; ?>"
                       id="<?php echo $id; ?>4"
                       value="4"
                    <?php echo $value == 4 ? 'checked="checked"' : ''; ?>
                       class="BorChildRadio Race borrowerSelector BRaceNH   "
                       data-allow="1"><span></span>
                Native Hawaiian or Other Pacific Islander
            </label>
            <div class="col-md-12 px-6 " id="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>Native">
                <div class="radio-list">
                    <label class="radio radio-solid"
                           for="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>8">
                        <input type="radio"
                               name="FormField[<?php echo $additionalValues['RaceSub']->fieldName; ?>]"
                               id="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>8"
                            <?php echo $additionalValues['RaceSub']->value == 8 ? 'checked="checked"' : ''; ?>
                               class="BorChildRadio borrowerSelector Asian NativeHOPI"
                               value="8"><span></span>
                        Native Hawaiian
                    </label>
                    <label class="radio radio-solid"
                           for="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>9">
                        <input type="radio"
                               name="FormField[<?php echo $additionalValues['RaceSub']->fieldName; ?>]"
                               id="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>9"
                            <?php echo $additionalValues['RaceSub']->value == 9 ? 'checked="checked"' : ''; ?>
                               class="BorChildRadio borrowerSelector Asian NativeHOPI"
                               value="9">
                        <span></span> Guamanian or Chamorro
                    </label>
                    <label class="radio radio-solid"
                           for="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>10">
                        <input type="radio"
                               name="FormField[<?php echo $additionalValues['RaceSub']->fieldName; ?>]"
                               id="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>10"
                            <?php echo $additionalValues['RaceSub']->value == 10 ? 'checked="checked"' : ''; ?>
                               class="BorChildRadio borrowerSelector Asian NativeHOPI"
                               value="10"><span></span>
                        Samoan
                    </label>
                    <label class="radio radio-solid"
                           for="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>11">
                        <input type="radio"
                               name="FormField[<?php echo $additionalValues['RaceSub']->fieldName; ?>]"
                               id="FormField_<?php echo $additionalValues['RaceSub']->fieldName; ?>11"
                            <?php echo $additionalValues['RaceSub']->value == 11 ? 'checked="checked"' : ''; ?>
                               class="BorChildRadio borrowerSelector Asian NativeHOPI"
                               value="11" checked=""><span></span>
                        Other Pacific Islander
                    </label>
                    <div class="col-md-12 px-6 " id="<?php echo $additionalValues['RaceSub']->fieldName; ?>Pacific">
                        <label class="col-md-12"
                               for="FormField_<?php echo $additionalValues['RacePacificOther']->fieldName; ?>">
                            Print Race </label>
                        <input type="text"
                               class="form-control input-sm"
                               name="FormField[<?php echo $additionalValues['RacePacificOther']->fieldName; ?>]"
                               id="FormField_<?php echo $additionalValues['RacePacificOther']->fieldName; ?>"
                               value="<?php echo htmlentities($additionalValues['RacePacificOther']->value); ?>">
                        <span class="text-muted">For example: Fijian, Tongan, and so on.</span>
                    </div>
                </div>
            </div>
            <label class="radio radio-solid"
                   for="<?php echo $id; ?>5">
                <input type="radio"
                       name="<?php echo $name; ?>"
                       id="<?php echo $id; ?>5"
                       value="5"
                    <?php echo $value == 5 ? 'checked="checked"' : ''; ?>
                       class="BorChildRadio Race borrowerSelector BRaceW    "
                       data-allow="1"><span></span>
                White
            </label>
            <label class="radio radio-solid"
                   for="<?php echo $id; ?>6">

                <input type="radio"
                       name="<?php echo $name; ?>"
                       id="<?php echo $id; ?>6"
                       value="6"
                    <?php echo $value == 6 ? 'checked="checked"' : ''; ?>
                       class="BorChildRadio Race borrowerSelector BRaceND   "
                       data-allow="1"><span></span>
                Not Disclosed
            </label>
        </div>
        <?php
        return ob_get_clean();
    }

    public static function simpleCheckbox(
        string  $name,
        string  $inputClass,
        string  $labelClass,
        string  $onchange,
        bool    $isChecked,
        ?string $value,
        string  $label,
        ?string $fieldId = null
    ): string
    {
        $id = $fieldId ?: self::fieldIdFromName($name, $value);
        $data = self::getFieldData();


        if ($onchange) {
            $onchange .= ';';
        }
        $onchange .= '$(\'#' . $id . '_hidden\').val($(this).is(\':checked\') ? this.value : \'\');';

        return '
            <input 
                type="hidden" 
                name="' . $name . '" 
                id="' . $id . '_hidden" 
                value="' . ($isChecked ? htmlentities($value) : '') . '"
                />
            <input type="checkbox" 
                    name="' . $name . '_checkbox"
                   class="customFormField ' . $inputClass . '" 
                   id="' . $id . '"
                   onchange="' . $onchange . '"
                   ' . ($isChecked ? 'checked' : '') . '
                   value="' . $value . '"
                   ' . $data . '
                   />
            <label for="' . $id . '" class="' . $labelClass . '">
                ' . $label . '
            </label>        
        ';
    }

    public static function fieldIdFromName(string $fieldName, ?string $value = null): string
    {
        return str_replace(['[', ']'], '_', $fieldName) . (!is_null($value) ? '_' . str_replace(' ', '_', htmlentities($value)) : '');

    }

    public static function simpleSwitch(
        string  $name,
        string  $inputClass,
        string  $labelClass,
        string  $onchange,
        bool    $isChecked,
        ?string $value,
        string  $label,
        ?string $fieldId = null
    ): string
    {
        $id = self::fieldIdFromName($name, $value);

        return '
            <span class="switch switch-icon">
                <label class="font-weight-bold ' . $labelClass . '">
                    <input 
                        class="form-control ' . $inputClass . '" 
                        ' . ($isChecked ? 'checked="checked"' : '') . ' 
                        id="' . ($fieldId ?: $id) . '_switch" 
                        type="checkbox"
                        data-elem="' . ($fieldId ?: $id) . '"
                        data-value="' . htmlentities($value) . '"
                        onchange="FormHelper.toggleSwitch(this); ' . $onchange . '"
                ><input 
                    type="hidden" 
                    name="' . $name . '" 
                    id="' . ($fieldId ?: $id) . '"
                    value="' . htmlentities($value) . '" />
                    <span>' . $label . '</span>
                </label>
            </span>        
        ';
    }

    public static function simpleRadio(
        string  $name,
        string  $inputClass,
        string  $labelClass,
        string  $onchange,
        bool    $isChecked,
        ?string $value,
        string  $label,
        ?string $fieldId = null
    ): string
    {
        $id = self::fieldIdFromName($name, $value);
        if ($fieldId) {
            $fieldId .= '_' . str_replace(' ', '', $value);
        }

        $data = self::getFieldData();

        return '
            <label for="' . ($fieldId ?: $id) . '" class="' . $labelClass . '">
                <input type="radio" 
                       class="customFormField ' . $inputClass . '" 
                       name="' . $name . '"
                       id="' . ($fieldId ?: $id) . '"
                       onchange="' . $onchange . '"
                       ' . ($isChecked ? 'checked' : '') . '
                       value="' . htmlentities($value) . '"
                       ' . $data . '
                   />
                <span>' . $label . '</span>
            </label>        
        ';
    }

    public static function number(
        string  $fieldName,
        int     $canEdit,
        int     $tabIndex,
        ?string $value,
        ?string $onChange = null,
        ?string $class = null,
        ?bool   $readOnly = false,
        string  $appendText = ''
    ): string
    {
        if (!$canEdit) {
            return '<span id="' . $fieldName . '">' . Currency::formatDollarAmountWithDecimal($value) . '</span>';
        }

        $allowed = self::isVisible($fieldName);

        if ($appendText) {
            $appendText = ' <div class="input-group-append">
                                    <span class="input-group-text">' . $appendText . '</span>
                                </div>
                            ';
        }


        return '
            <div class="input-group">
                <input 
                    type="number" 
                    id="' . $fieldName . '" 
                    name="' . $fieldName . '"
                    onchange="' . $onChange . '"
                    class="form-control ' . $class . '" 
                    tabindex="' . $tabIndex . '"
                    autocomplete="off"
                    ' . ($allowed ? '' : 'disabled') . '
                    ' . ($readOnly ? 'readonly' : '') . '
                    value="' . htmlentities($value) . '"
                    />' . $appendText . '
            </div>
        ';
    }

    public static function simpleNumber(
        string  $fieldName,
        int     $canEdit,
        int     $tabIndex,
        ?string $value,
        ?string $onChange = null,
        ?string $class = null,
        ?string $fieldId = null,
        ?string $Prepend = null,
        ?string $Append = null
    ): string
    {
        if (!$canEdit) {
            return '<span id="' . ($fieldId ?: self::fieldIdFromName($fieldName)) . '">' . Currency::formatDollarAmountWithDecimal($value) . '</span>';
        }

        $data = self::getFieldData();

        if ($Append) {
            return '
            <div class="input-group">
                <input 
                    type="number" 
                    id="' . ($fieldId ?: self::fieldIdFromName($fieldName)) . '" 
                    name="' . $fieldName . '"
                    onchange="' . $onChange . '"
                    class="customFormField form-control ' . $class . '" 
                    tabindex="' . $tabIndex . '"
                    autocomplete="off"
                    value="' . htmlentities($value) . '"
                    ' . $data . '
                    />
                <div class="input-group-append">
                    <span class="input-group-text">' . $Append . '</span>
                </div>
            </div>
        ';
        }

        if ($Prepend) {
            return '
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text">' . $Prepend . '</span>
                </div>
                <input 
                    type="number" 
                    id="' . ($fieldId ?: self::fieldIdFromName($fieldName)) . '" 
                    name="' . $fieldName . '"
                    onchange="' . $onChange . '"
                    class="customFormField form-control ' . $class . '" 
                    tabindex="' . $tabIndex . '"
                    autocomplete="off"
                    value="' . htmlentities($value) . '"
                    ' . $data . '
                    />
            </div>
        ';
        }

        return '
            <div class="input-group">
                <input 
                    type="number" 
                    id="' . ($fieldId ?: self::fieldIdFromName($fieldName)) . '" 
                    name="' . $fieldName . '"
                    onchange="' . $onChange . '"
                    class="customFormField form-control ' . $class . '" 
                    tabindex="' . $tabIndex . '"
                    autocomplete="off"
                    value="' . htmlentities($value) . '"
                    ' . $data . '
                    />
            </div>
        ';
    }

    public static function select(
        string  $fieldName,
        ?int    $canEdit,
        int     $tabIndex,
        ?string $value,
        array   $options,
        ?string $onchange = '',
        ?string $class = '',
        ?string $empty = '',
        ?string $dataPlaceholder = '',
        bool    $disabled = false,
        ?string $dataAttribute = null
    ): string
    {
        $fieldName_id = str_replace(['[', ']'], '_', $fieldName);
        $fieldName = stristr($fieldName, '_mirror') !== false ? explode('_', $fieldName)[0] : $fieldName;

        if (!$canEdit) {
            return '<span id="' . $fieldName_id . '" style="font-weight: bold;">' . $options[$value] . '</span>';
        }
        $allowed = self::isVisible($fieldName);

        if (self::isMandatory($fieldName)) {
            $class .= ' mandatory ';
        }
//        if (!$allowed) {
//            return self::hidden($fieldName, $value);
//        }

        $html = '
            <div class="input-group">
                <select 
                    ' . ($dataPlaceholder ? 'data-placeholder ="' . $dataPlaceholder . '" ' : '') . '
                    id="' . $fieldName_id . '" 
                    class="form-control ' . $class . '"
                    tabindex="' . $tabIndex . '"
                    name="' . $fieldName . '"
                    ' . ($allowed && !$disabled ? '' : 'disabled') . '
                    onchange="' . $onchange . '"
                    ' . $dataAttribute . '
                >
                         ';
        if ($empty) {
            $html .= '<option value="">' . $empty . '</option>';
        }

        foreach ($options as $val => $display) {
            $selected = $value == $val ? 'selected' : '';
            $html .= '<option ' . $selected . ' value="' . htmlentities($val) . '">' . $display . '</option>';
        }

        return $html . '</select></div>';
    }

    public static function simpleSelect(
        string  $fieldName,
        int     $canEdit,
        int     $tabIndex,
                $value, // can be string or array
        array   $options,
        ?string $onchange = '',
        ?string $class = '',
        ?string $empty = '',
        ?string $dataPlaceholder = '',
        ?string $fieldId = null,
        ?bool   $multiple = false,
        bool    $disabled = false
    ): string
    {
        $data = self::getFieldData();

        if (!$canEdit) {
            return '<span id="' . ($fieldId ?: self::fieldIdFromName($fieldName)) . '" style="font-weight: bold;">' . $options[$value] . '</span>';
        }

        $html = '
            <div class="input-group">
                <select 
                ' . ($multiple ? 'multiple' : '') . '
                    data-orig="' . htmlentities($value ? (is_array($value) ? implode(',', $value) : $value) : '') . '"
                    ' . ($dataPlaceholder ? 'data-placeholder ="' . $dataPlaceholder . '" ' : '') . '
                    id="' . ($fieldId ?: self::fieldIdFromName($fieldName)) . '" 
                    class="customFormField form-control ' . $class . '"
                    tabindex="' . $tabIndex . '"
                    name="' . $fieldName . '"
                    onchange="' . $onchange . '"
                    ' . (!$disabled ? '' : 'disabled') . '
                    ' . $data . '
                >
                         ';
        if ($empty) {
            $html .= '<option value="">' . $empty . '</option>';
        }

        $inGroup = false;
        $lastGroup = null;

        foreach ($options as $val => $display) {
            $val = explode('::', $val); // required by docStatus, $val[0] is the current id, $val[1] is the original id of the modified option
            if (stristr($val[0], '__label__') !== false) {
                if (!is_null($lastGroup) && $lastGroup !== $val) {
                    $html .= '</optgroup>';
                }
                $html .= '<optgroup label="' . htmlentities($display) . '">';
                $lastGroup = $val[0];
                $inGroup = true;
                continue;
            }
            $selected = null;
            if ($value) {
                if (is_string($value)) {
                    $selected = ($value == $val[0]) || ($value && $value == ($val[1] ?? null)) || $value == $display;
                } else {
                    if (!is_array($value)) {
                        $value = [$value];
                    }
                    $selected = in_array($val[0], $value) || in_array($display, $value) || in_array($val[1] ?? null, $value);
                }
            }
            $html .= '<option ' . ($selected ? 'selected' : '') . ' value="' . htmlentities($val[0]) . '">' . $display . '</option>';
        }

        if ($inGroup) {
            $html .= '</optgroup>';
        }
        return $html . '</select></div>';
    }

    public static function selectMulti(
        string  $fieldName,
        int     $canEdit,
        int     $tabIndex,
        array   $values,
        array   $options,
        ?string $onchange,
        ?string $class,
        ?string $empty = ''
    ): string
    {
        $fieldName_id = str_replace(['[', ']'], '_', $fieldName);


        if (!$canEdit) {
            $selected = [];
            foreach ($values as $value) {
                $selected[] = $options[$value];
            }
            return '<span id="' . $fieldName_id . '" style="font-weight: bold;">' . implode(', ', $selected) . '</span>';
        }

        $allowed = self::isVisible($fieldName);

        //  || (sizeof($values) && self::$displayValue) -- showing fields when there's a value is a whole thing - 5/3/2023

        $html = '
            <div class="input-group">
                <select 
                    multiple
                    id="' . $fieldName_id . '" 
                    class="form-control ' . $class . '"
                    tabindex="' . $tabIndex . '"
                    name="' . $fieldName . '[]"
                    ' . ($allowed ? '' : 'disabled') . '
                    onchange="' . $onchange . '"
                >
                         ';
        if ($empty) {
            $html .= '<option value="">' . $empty . '</option>';
        }

        foreach ($options as $val => $display) {
            $selected = in_array($val, $values) ? 'selected' : '';
            $html .= '<option ' . $selected . ' value="' . htmlentities($val) . '">' . $display . '</option>';
        }

        return $html . '</select></div>';
    }

    public static function radio(
        string  $fieldName,
        int     $canEdit,
        int     $tabIndex,
        ?string $value,
        array   $options,
        ?string $onchange = null,
        ?string $class = null
    ): string
    {
        if (!$canEdit) {
            return '<span id="' . $fieldName . '" style="font-weight: bold;">' . $options[$value] . '</span>';
        }
        if (self::isMandatory($fieldName)) {
            $class .= ' mandatory ';
        }

        $allowed = self::isVisible($fieldName);

//        if (!$allowed) {
//            return self::hidden($fieldName, $value);
//        }

        $html = '
            <div class="radio-inline">
                         ';
        foreach ($options as $val => $display) {
            $selected = $value == $val ? 'checked' : '';
            $valId = str_replace(' ', '', $val);
            $html .= '
            <label class="radio radio-solid" for="' . $fieldName . $valId . '">
                <input type="radio" 
                    id="' . $fieldName . $valId . '" 
                    class="' . $class . '"
                    tabindex="' . $tabIndex . '"
                    name="' . $fieldName . '"
                    ' . ($allowed ? '' : 'disabled') . '
                    onchange="' . $onchange . '"
                    value="' . htmlentities($val) . '"
                    ' . $selected . '
                /><span></span>' . $display . '
            </label>
            ';
        }
        return $html . '</div>';
    }


    /**
     * Get the length of a field.
     *
     * @param string $_columnName
     * @param string|null $_table Optional table name
     * @return int The length of the field
     */
    public static function getFieldLength(string $_columnName, ?string $_table = null): int
    {
        // Check if PC is enabled for maxlength
        if (glCustomJobForProcessingCompany::isPCEnabledForMaxlength(self::$PCID)) {
            return MaxLengthFields::$CV3_MAX_FIELDS_LENGTH[$_columnName] ?? 0;
        }
        // If table is specified, get length from table class
        $class = $_table ? 'models\\lendingwise\\' . $_table : null;
        if ($class) {
            return (new $class())->getPropertyLength($_columnName) ?? 0;
        }

        // Default to 0 if no length is found
        return 0;
    }


    private static ?array $stateCodeArray = null;

    public static function getStates(): ?array
    {
        if (is_null(self::$stateCodeArray)) {
            $stateArray = Arrays::fetchStates();
            foreach ($stateArray as $state) {
                self::$stateCodeArray[trim($state['stateCode'])] = trim($state['stateName']);
            }
        }
        return self::$stateCodeArray;
    }

    public static function simpleCountry(
        string  $fieldName,
        string  $fieldId,
        ?string $value,
        ?string $class = ''
    ): string
    {
        return self::simpleSelect(
            $fieldName,
            true,
            0,
            $value,
            array_flip(glCountryArray::$glCountryArray),
            $class,
            '',
            'Select One...',
            '',
            $fieldId
        );
    }


    public static function simpleStateMulti(
        string  $fieldName,
        string  $fieldId,
        ?array  $value,
        ?string $class = ''
    ): string
    {
        $states = self::getStates();
        return self::simpleSelect(
            $fieldName,
            true,
            0,
            $value,
            $states,
            $class,
            '',
            'Select One...',
            '',
            $fieldId,
            true
        );
    }

    public static function simpleState(
        string  $fieldName,
        string  $fieldId,
        ?string $value,
        ?string $class = ''
    ): string
    {
        $states = self::getStates();
        return self::simpleSelect(
            $fieldName,
            true,
            0,
            $value,
            $states,
            $class,
            '',
            'Select One...',
            '',
            $fieldId
        );
    }

    public static function simpleHeader(
        ?string $text = null,
        ?string $class = ''
    ): string
    {
        $html = '<div
class="' . $class . '"
>';
        if ($text) {
            $html .= $text;
        }
        return $html . '</div>
        ';
    }

    public static function customFormField()
    {
        $params = [
            'id'        => self::$customFieldId,
            'PCID'      => self::$PCID,
            'sectionID' => self::$currentSectionID
        ];
        $table = tblCustomField::Get($params);
        if ($table) {
            switch (self::$fileTab) {
                case'FA':
                    self::$customField_Display_FA = $table->displayFA;
                    break;
                case 'QA':
                    self::$customField_Display_QA = $table->displayQA;
                    break;
                case 'BO':
                    self::$customField_Display_BO = $table->displayBO;
                    break;
            }
        }
    }

    private static function getFieldData(): string
    {
        $data = [];
        if (loanForm::$customFieldId) {
            self::customFormField();
        }
        if (self::$fileLoanPrograms) {
            $data[] = 'data-lp="' . implode('_^_', self::$fileLoanPrograms) . '"';
        }
        if (self::$fileTypes) {
            $data[] = 'data-ft="' . implode('_^_', self::$fileTypes) . '"';
        }
        if (self::$sectionId) {
            $data[] = 'data-si="' . self::$sectionId . '"';
        }
        if (self::$customField_Display_FA) {
            $data[] = 'data-fa="' . self::$customField_Display_FA . '"';
        }
        if (self::$customField_Display_QA) {
            $data[] = 'data-qa="' . self::$customField_Display_QA . '"';
        }
        if (self::$customField_Display_BO) {
            $data[] = 'data-bo="' . self::$customField_Display_BO . '"';
        }

        return implode(' ', $data);
    }
}
