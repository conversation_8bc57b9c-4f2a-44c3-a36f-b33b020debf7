<?php

namespace pages\backoffice\loan\web_form\HMLO\classes;

use models\composite\oBranch\getBranchAndAgentRequiredDocs;
use models\composite\oBranch\getBranchHearAbout;
use models\composite\oBranch\getBranchModules;
use models\composite\oBranch\getBranchServices;
use models\composite\oBranch\getReferralSiteInfo;
use models\composite\oBranch\isBranchActive;
use models\composite\oBranch\savePreferredAgentForBranch;
use models\composite\oBroker\getAgentReferralSiteName;
use models\composite\oBroker\getBrokerInfo;
use models\composite\oBroker\isAgentActive;
use models\composite\oBroker\isPCAgentActive;
use models\composite\oChecklist\getChecklistForPCList;
use models\composite\oFile\getBorrowerNumberOfDeals;
use models\composite\oFile\getFileInfo;
use models\composite\oFile\getFilter;
use models\composite\oFile\getMyFileInfo;
use models\composite\oPC\getAppFormFields;
use models\composite\oPC\getPermissionsToEdit;
use models\composite\oServiceType\getServiceTypes;
use models\constants\gl\glHMLOTAC;
use models\constants\gl\glUserRole;
use models\constants\GpropertyTypeNumbArray;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\Property;
use models\Controllers\loanForm;
use models\cypher;
use models\lendingwise\tblBrokerAssociatedLoanOfficers;
use models\oMicroBuiltAPI;
use models\PageVariables;
use models\Request;
use models\standard\Arrays;
use models\types\strongType;
use models\types\unknown;

class HMLOController extends strongType
{
    public static ?string $shareLink = null;
    public static ?int $LMRId = null;
    public static ?string $wfOpt = null;
    public static ?string $PCID = null;
    public static ?string $borInfoRed = null;
    public static ?string $aud = null;
    public static ?array $FSArray = null;
    public static ?string $titleTxt = null;
    public static ?array $FArray = null;
    public static ?string $brokerNumber = null;
    public static ?string $googleTrackingCodeQA = null;
    public static ?int $LMRResponseId = null;
    public static ?int $executiveId = null;
    public static ?string $UType = null;
    public static ?string $emailOpt = null;
    public static ?string $so = null;
    public static ?string $activeTab = null;

    public static ?string $clientId = null;
    public static ?string $userName = null;
    public static ?string $userGroup = null;
    public static ?string $userNumber = null;

    public static ?string $agentReferralCode = null;
    public static ?string $lmrLogo = null;
    public static ?string $userLogo = null;
    public static ?string $brokerLogo = null;
    public static ?string $fOpt = null;
    public static ?string $branchReferralCode = null;

    public static ?string $processingCompanyId = null;
    public static ?string $tabOpt = null;
    public static ?int $reqCnt = null;
    public static ?int $assignedPCID = null;
    public static ?int $agentNumber = null;

    public static ?int $dummyBrokerId = null;
    public static ?int $loanOfficerId = null;
    public static ?int $primeStatusId = null;
    public static ?int $publicUser = null;
    public static ?string $ft = null;
    public static ?string $fileRecordDate = null;
    public static ?int $allowToEdit = null;
    public static ?int $hideBorrowerInfo = null;
    public static ?int $allowCaptcha = null;
    public static ?int $hsFADisplay = null;
    public static ?int $hsQADisplay = null;
    public static ?string $formCoBroEsignSignature = null;
    public static ?int $isHMLO = null;

    public static ?string $isEsignRequired = null;
    public static ?string $tacField = null;

    public static ?array $stateArray = null;
    public static ?array $glLMRClientTypeData = [];

    public static ?string $fileTab = null;
    public static ?array $fieldsInfo = null;
    public static ?string $schedulePropState = null;
    public static ?string $scheduleStatus = null;
    public static ?string $datePurchased = null;
    public static ?int $tabIndex = null;
    public static ?string $intendedOccupancy = null;
    public static ?string $salesDate = null;
    public static ?float $salesPrice = null;
    public static ?string $anyMortgagesLiens = null;
    public static ?string $mortgageType = null;
    public static ?string $maturityDateSchedule = null;
    public static ?string $anyOtherMortgagesLiens = null;
    public static ?string $typeAnother = null;
    public static ?string $maturityDateAnother = null;
    public static ?int $hideThisField = 0;

    public static function Init()
    {
        $glHMLOTAC = glHMLOTAC::$glHMLOTAC;

// getFileInfo::$skipNotes = true; // don't load notes, they're not used on this page


        if (isset($_REQUEST['bRc'])) {
            if ($_REQUEST['bRc']) {
                if (!is_numeric(cypher::myDecryption(Request::GetClean('bRc')))) {
                    echo '<h3>Incorrect URL Parameters</h3>';
                    exit;
                }
            }
        }
        if (isset($_REQUEST['lid'])) {
            if ($_REQUEST['lid']) {
                if (!is_numeric(cypher::myDecryption(Request::GetClean('lid')))) {
                    echo '<h3>Incorrect URL Parameters</h3>';
                    exit;
                }
            }
        }

        self::$stateArray = Arrays::fetchStates();


        $checkReferralCode = 1;
        $LMRAEUserType = '';
        $incomeInfo = $fileLP = [];
        self::$allowToEdit = 1;
        $referralSiteCode = 1;
        $LMRInfoArray = [];
        $subscribedOption = 1;
        self::$LMRId = 0;
        $showPoweredByTMPLink = 1;
        $PCStatus = 0;
        self::$branchReferralCode = 1;
        self::$agentReferralCode = 1;
        $agentInfoArray = [];
        self::$fOpt = '';
        self::$tabOpt = 'BI';
        $responseId = 0;
        $googleTrackingCode = '';
        self::$googleTrackingCodeQA = '';
        $allowToAddAgent = 0;
        self::$processingCompanyId = 0;
        $externalBroker = 0;
        $processingCompanyInfoArray = [];
        $userRole = '';
        $stateArray = [];
        $servicer1 = '';
        $servicer2 = '';
        $docArray = [];
        $servicer1Text = $originalLender1Text = $servicer2Text = $originalLender2Text = '- Type Name Here -';
        $PCStatusInfo = [];
        $ResponseInfo = [];
        $webformName = '';
        $statusInfoArray = [];
        $fileInfo = [];
        self::$primeStatusId = 0;
        self::$isHMLO = 1;
        $isLOC = 1;
        $addBranchHearAbout = 0;
        self::$publicUser = 1;
        $showLimitedMandatoryField = 1;
        $fileUpdLimit = 5;
        $tempDocInfoArray = [];
        self::$activeTab = 'LI';
        $mandatoryChecklistItemsArray = $fileLMRAdditionalLoanprograms = [];
        $borrowerName = '';
        $LMRInfo = [];

        self::$brokerNumber = 0;
        $mailSent = 0;
        $siteName = '';
        $tempSiteName = '';
        self::$executiveId = 0;
        $sdOpt = 0;
        $agentId = 0;
        $modifyOpt = 0;
        $tabNumb = 0;
        $MLMRAllow = false;
        $editOpt = 0;
        self::$LMRResponseId = 0;
        $qstrOpt = 0;
        $LMRPerson = '';
        $exOpt = 0;
        $agentFName = '';
        $agentLName = '';
        $agentName = '';
        $borrowerNumberOfDeals = [];
        self::$agentNumber = 0;
        self::$loanOfficerId = 0;
        $branchName = '';
        $hardshipType = '';
        $originalPurchasePrice = 0;
        self::$clientId = 0;
        $isEF = 0;
        $allowClientToCreateHMLOFile = '';
        $branchStatus = 0;
        $agentStatus = 0;
        $agentPCStatus = 0;
        $exeStatus = 0;
        $HMLOTAC = $HMLOTACQA = '';
        self::$fileRecordDate = '';
        $leadSource = '';
        $userTimeZone = '';
        $brokerFName = '';
        $brokerLName = '';
        $brokerCompany = '';
        $brokerEmail = '';
        $brokerPhone = '';
        $brokerPhNo1 = '';
        $brokerPhNo2 = '';
        $brokerPhNo3 = '';
        $brokerExt = '';
        $formName = 'loanModForm';
        $paymentTempFormName = 'loanModForm';
        $showAgentList = 0;
        $webFormFOpt = '';
        $checklistInfoArray = [];
        $PCChecklistInfoArray = [];
        $clientType = '';

        $stateArray = Arrays::fetchStates();
        /** Fetch all States **/
        $branchHearAboutInfoArray = [];
        $branchHAInfoArray = [];
        $inputExeArray = [];
        $LMRClientTypeInfo = [];
        $ClientType = '';
        $isMF = '';
        self::$emailOpt = '';
        $fileHMLOChecklistUploadDocs = [];
        $REBroker = '';
        $RELoanofficer = '';
        $BrokerInfoDivDisp = 'display: none;';
        $isBrokerExistsDisp = 'display: none;';
        $fileModuleInfo = [];
        $cliType = '';
        $brancArray = [];
        $defaultPrimaryStatus = 0;
        $oldFPCID = '';
        self::$wfOpt = '';
        self::$PCID = null;
        self::$userGroup = 'Client';
        $processorAssignedCompany = [];
        $selClientId = '';
        $isClientProfile = 0;
        self::$hideBorrowerInfo = 0;
        $theme = $tempClientEmail = '';
        $defaultPrimaryStatusForFA = 0;
        $registerDate = '';
        self::$ft = 'HMLO';
        self::$userNumber = 0;
        $URLPOSTING = 0;

        $isPLO = 0;
        $selClient = '';
        self::$allowCaptcha = 0;
        self::$dummyBrokerId = 0;
        $SecondaryBrokerInfo = [];
        $secondaryBrokerNumber = 0;
        $isCoBorrower = 0;

        if (isset($_REQUEST['bRc'])) {
            self::$branchReferralCode = cypher::myDecryption(Request::GetClean('bRc'));
        }

        if (!self::$branchReferralCode) {
            self::$branchReferralCode = 1;
        }

        if (isset($_REQUEST['aRc'])) {
            self::$agentReferralCode = cypher::myDecryption(Request::GetClean('aRc'));
        }

        if (!self::$agentReferralCode) {
            self::$agentReferralCode = 1;
        }

        if (isset($_REQUEST['fOpt'])) {
            self::$fOpt = cypher::myDecryption(Request::GetClean('fOpt'));
        }

        if (isset($_REQUEST['opt'])) {
            self::$emailOpt = cypher::myDecryption(Request::GetClean('opt'));
        }

        if (isset($_REQUEST['op'])) {
            self::$wfOpt = cypher::myDecryption(Request::GetClean('op'));
        }

        if (isset($_REQUEST['ft'])) {
            self::$ft = Request::GetClean('ft');
            self::$ft = str_replace('\%27A=0', '', self::$ft);
            self::$ft = str_replace("\'A=0", '', self::$ft);
            self::$ft = preg_replace('/[^A-Za-z0-9]/', '', trim(self::$ft));
        }

        if (strpos(self::$wfOpt, 'QA') !== false) {
            self::$wfOpt = 'QA';
        } elseif (strpos(self::$wfOpt, 'FA') !== false) {
            self::$wfOpt = 'FA';
        } else {
            self::$wfOpt = 'QA';
        }

        if ($userRole == glUserRole::USER_ROLE_AGENT) {
            if (isset($_REQUEST['aRc'])) $checkReferralCode = Request::GetClean('aRc');
            header('Location: /alertUser.php');
            exit();
        }
        if (isset($_REQUEST['lid'])) {
            self::$LMRId = cypher::myDecryption(Request::GetClean('lid'));
        }
        if (isset($_REQUEST['tabOpt'])) {
            self::$tabOpt = Request::GetClean('tabOpt');
        } else if (isset($_SESSION['tabOpt'])) {
            self::$tabOpt = trim($_SESSION['tabOpt']);
        }
        if (!self::$tabOpt) {
            self::$tabOpt = 'BI';
        }

        if (self::$wfOpt == 'FA') {
            self::$titleTxt = 'Full Application Form';
            $myOpt = 'FA';
            $reqDoc = 'LV';
            $webFormType = 'LV';
            self::$activeTab = 'LI';
        } else {
            self::$titleTxt = 'Quick Application Form';
            $myOpt = 'QA';
            $reqDoc = 'SV';
            $webFormType = 'SV';
            self::$activeTab = 'QAPP';
        }

        if (self::$tabOpt == '1003') {
            self::$titleTxt = 'Uniform Residential Loan Application';
            self::$activeTab = '1003';
            self::$wfOpt = 'BO';
        }
        self::$shareLink = '';
        self::$borInfoRed = 'no';
        self::$hideThisField = 1; //default show
        self::$UType = '';
        self::$aud = 'no';
        self::$so = 'no';//Access to Uploaded Docs

        if (isset($_REQUEST['sl'])) self::$shareLink = cypher::myDecryption(Request::GetClean('sl'));
        if (isset($_REQUEST['bir'])) self::$borInfoRed = cypher::myDecryption(Request::GetClean('bir'));
        if (isset($_REQUEST['aud'])) self::$aud = cypher::myDecryption(Request::GetClean('aud'));
        if (isset($_REQUEST['UType'])) self::$UType = cypher::myDecryption(Request::GetClean('UType'));
        if (isset($_REQUEST['so'])) self::$so = cypher::myDecryption(Request::GetClean('so'));

        if (isset($_REQUEST['brokerNumber'])) {
            self::$brokerNumber = Request::GetClean('brokerNumber');
            if (self::$brokerNumber > 0) {
                $qstrOpt = 1;
            }
        } elseif (isset($_SESSION['userNumber'])) {
            self::$brokerNumber = trim($_SESSION['userNumber']);
        } elseif (isset($_SESSION['MLMRNo'])) {
            self::$brokerNumber = trim($_SESSION['MLMRNo']);
        }

        if (isset($_REQUEST['bRc'])) {
            self::$branchReferralCode = cypher::myDecryption(Request::GetClean('bRc'));
        }

        if (isset($_REQUEST['aRc'])) {
            self::$agentReferralCode = cypher::myDecryption(Request::GetClean('aRc'));
        }

        if (isset($_SESSION['LMRId'])) {
            self::$LMRId = trim($_SESSION['LMRId']);
        } elseif (isset($_REQUEST['lId'])) {
            self::$LMRId = cypher::myDecryption(Request::GetClean('lId'));
        }

        Property::init(self::$LMRId);
        LMRequest::setLMRId(self::$LMRId);


        if (self::$LMRId > 0) {


            getFileInfo::$publicUser = self::$publicUser;

            if (LMRequest::myFileInfo()->LMRInfo()->activeStatus == 0) {
                $_SESSION['errorMsg'] = 'This file has been deactivated';
                header('Location: /invalidFile.php');
                exit;
            }
        }

        if (self::$LMRId) {
            $getMyFileInfo = getMyFileInfo::getReport(self::$LMRId);
            $borrowerNumberOfDeals = getBorrowerNumberOfDeals::getReport(
                LMRequest::myFileInfo()->LMRInfo()->clientId,
                LMRequest::myFileInfo()->LMRInfo()->FPCID
            );
        }

        if (self::$branchReferralCode == 1 || (self::$fOpt == 'agent' && self::$agentReferralCode == 1)) {
            header('location: /backoffice/subscriptionExpired.php?check=24');
            exit();
        }

        if ((self::$branchReferralCode > 1) || (self::$agentReferralCode > 1)) {



            if (LMRequest::myFileInfo()) {
                $BrokerInfo = LMRequest::myFileInfo()->BrokerInfo();
                $SecondaryBrokerInfo = LMRequest::myFileInfo()->SecondaryBrokerInfo();
                $ResponseInfo = LMRequest::myFileInfo()->ResponseInfo();
                $LMRInfo = LMRequest::myFileInfo()->LMRInfo();

                $LMRClientTypeInfo = LMRequest::myFileInfo()->LMRClientTypeInfo();
                $fileModuleInfo = LMRequest::myFileInfo()->fileModuleInfo();
                $fileHMLOChecklistUploadDocs = LMRequest::myFileInfo()->fileHMLOChecklistUploadDocsNew();
                $fileLMRInternalLoanprograms = LMRequest::myFileInfo()->LMRInternalLoanprograms();
                $fileLMRAdditionalLoanprograms = LMRequest::myFileInfo()->LMRadditionalLoanprograms();
                $fileLMRInternalLoanprograms = array_merge($fileLMRInternalLoanprograms, $fileLMRAdditionalLoanprograms);

                // $filRentRollInfo = LMRequest::myFileInfo()->fileRentRollInfo();

                if (count(LMRequest::myFileInfo()->fileModuleInfo())) {
                    self::$ft = LMRequest::myFileInfo()->fileModuleInfo()[0]->moduleCode ?? '';
                }

                if (LMRequest::myFileInfo()->SBABackground()) {
                    $SBABackground = LMRequest::myFileInfo()->SBABackground();
                }

                if (array_key_exists(self::$LMRId, $LMRClientTypeInfo)) $ClientType = $LMRClientTypeInfo[self::$LMRId][0]['ClientType'];
                self::$agentNumber = LMRequest::myFileInfo()->BrokerInfo()->userNumber;

                self::$LMRResponseId = LMRequest::File()->getTblFileResponse_by_LMRId()->LMRResponseId;

                $borrowerName = ucwords(LMRequest::myFileInfo()->LMRInfo()->borrowerName . ' ' . LMRequest::myFileInfo()->LMRInfo()->borrowerLName);
                self::$fileRecordDate = LMRequest::myFileInfo()->LMRInfo()->recordDate;
                self::$clientId = LMRequest::myFileInfo()->LMRInfo()->clientId;
                $leadSource = LMRequest::myFileInfo()->ResponseInfo()->leadSource;
                $LMRClientTypeInfo = $LMRClientTypeInfo[self::$LMRId];
                $REBroker = LMRequest::myFileInfo()->fileHMLOInfo()->REBroker;
                self::$PCID = LMRequest::myFileInfo()->LMRInfo()->FPCID;
                // $registerDate = LMRequest::myFileInfo()->LMRInfo()->registerDate;
                /** Rehab construction Field hide and show **/
                if ($REBroker == 'Yes') {
                    $BrokerInfoDivDisp = 'display: block;';
                    $isBrokerExistsDisp = 'display: none;';
                } else {
                    $BrokerInfoDivDisp = 'display: none;';
                    $isBrokerExistsDisp = 'display: table-row;';
                }

                $inArray = [];
                $inArray['PCID'] = self::$PCID;
                $inArray['requiredBy'] = 'Borrower';
                $inArray['opt'] = 'reqBy';
                $inArray['moduleType'] = self::$ft;

                if (self::$PCID > 0) {
                    $checklistInfoArray = getChecklistForPCList::getReport($inArray);
                }

                if (count($checklistInfoArray) > 0) $checklistInfoArray = $checklistInfoArray[self::$ft];

                for ($pChk = 0; $pChk < count($checklistInfoArray); $pChk++) {
                    $tempArray1 = [];
                    $tempArray2 = [];
                    $chPos = '';
                    $tempID = '';
                    $tempID = trim($checklistInfoArray[$pChk]['MultiCID']);
                    $MultiSType = '';
                    $MultiSType = trim($checklistInfoArray[$pChk]['MultiSType']);

                    if ($MultiSType != '') {
                        $tempArray1 = explode(',', $MultiSType);
                        $tempArray2 = explode(',', $tempID);
                    }
                    if (count($tempArray1) > 0) {
                        if (in_array($ClientType, $tempArray1)) {
                            $chPos = array_search($ClientType, $tempArray1);
                            $PCChecklistInfoArray[$tempID] = $checklistInfoArray[$pChk];
                            $PCChecklistInfoArray[$tempID]['PCMID'] = $tempArray2[$chPos];
                        }
                    }
                }
            }
            $oldFPCID = self::$PCID;
            if (self::$branchReferralCode > 1) {
                $LMRInfoArray = getReferralSiteInfo::getObjects(self::$branchReferralCode);
            }
            self::$lmrLogo = '';

            if (self::$branchReferralCode > 1) {
                foreach ($LMRInfoArray as $tempArray) {
                    $siteName = $tempArray->company;
                    $tempSiteName = $siteName;
                    self::$executiveId = $tempArray->executiveId;
                    $subscribedOption = trim($tempArray->subscribedOption);
                    $LMRAEUserType = trim($tempArray->userType);
                    $showPoweredByTMPLink = trim($tempArray->showPoweredByTMPLink);
                    $googleTrackingCode = urldecode($tempArray->googleTrackingCode);
                    self::$googleTrackingCodeQA = urldecode($tempArray->googleTrackingCodeQA);
                    $allowToAddAgent = trim($tempArray->allowToAddAgent);
                    self::$processingCompanyId = trim($tempArray->processingCompanyId);
                    $branchName = trim($tempArray->LMRExecutive);
                    $addBranchHearAbout = trim($tempArray->addBranchHearAbout);
                    $HMLOTAC = trim($tempArray->TAC);
                    $HMLOTACQA = trim($tempArray->TACQA);
                    self::$lmrLogo = trim($tempArray->logo);
                    $userTimeZone = trim($tempArray->timeZone);
                    self::$PCID = self::$processingCompanyId;
                    self::$assignedPCID = self::$processingCompanyId;
                    self::$allowCaptcha = $tempArray->allowcaptcha;
                }
                if (in_array(self::$processingCompanyId, CONST_API_ACCESS_PC)) {

                    $oMicro = new oMicroBuiltAPI([
                        'MICROBILT_KEY' => CONST_MICROBILT_KEY,
                        'MICROBILT_SECRET' => CONST_MICROBILT_SECRET,
                        'MICROBILT_TOKEN_URL' => CONST_MICROBILT_TOKEN_URL,
                    ]);

                    $currentIP = ['IP' => $_SERVER['REMOTE_ADDR']];
                    $url = CONST_MICROBILT_IPVERIFY_API;

                    $encodedData = json_encode($currentIP);
                    $param = ['url' => $url, 'data' => $encodedData];

                    $response = $oMicro->postData($param);
                    $loadingCountry = (json_decode($response)->IpInfo->Country);

                    if ($loadingCountry != 'usa' && $_ENV['APP_ENV'] == 'production') {
                        echo 'Unauthorised Access Location';
                        exit;
                    }
                }
                if (!$HMLOTAC || !$HMLOTACQA) {
                    $HMLOTAC = $glHMLOTAC;
                    $HMLOTACQA = $glHMLOTAC;
                }

                if (self::$processingCompanyId > 0) {
                    $processingCompanyInfoArray = LMRequest::myFileInfo()->PCInfo(self::$processingCompanyId);
                }

                if(!$processingCompanyInfoArray->captcha){
                    self::$allowCaptcha = 0;
                }

                self::$hideBorrowerInfo = $processingCompanyInfoArray->hideBorrowerInfo; //echo $REBroker;exit();
                if ($REBroker == 'Yes' && self::$hideBorrowerInfo == 1) {
                    self::$hideBorrowerInfo = 1;
                }

                if (count($LMRInfoArray) > 0) {
                    $branchStatus = isBranchActive::getReport(['executiveId' => self::$executiveId]);
                } else {
                    $subscribedOption = 0;
                }
            }

            if (($subscribedOption == 0) || ($branchStatus == 0)) {
                header('location: /backoffice/subscriptionExpired.php?check=25');
                exit();
            }
            self::$brokerLogo = '';
            if (self::$agentReferralCode > 1) {


                $agArray = ['agentReferralCode' => self::$agentReferralCode, 'PCID' => self::$processingCompanyId];
                $agentInfoArray = getAgentReferralSiteName::getReport($agArray);

                if (count($agentInfoArray) > 0) {
                    self::$brokerLogo = $agentInfoArray['logo'];
                    if ($agentInfoArray['externalBroker'] == 0) {
                        self::$agentNumber = trim($agentInfoArray['userNumber']);
                        self::$brokerNumber = self::$agentNumber;
                        $agentFName = trim($agentInfoArray['firstName']);
                        $agentLName = trim($agentInfoArray['lastName']);
                        $userTimeZone = trim($tempArray['timeZone']);
                        //$HMLOTAC        = trim($agentInfoArray['TAC']);   /** Broker should follow the branch TAC setting -- card #219  **/
                        $brokerAssociateLOArray = [];
                        $brokerAssociateLO = [];
                        if (self::$agentNumber) {
                            $brokerAssociateLO = tblBrokerAssociatedLoanOfficers::getData(self::$agentNumber);
                            if (count($brokerAssociateLO)) {
                                $brokerAssociateLOArray = array_column($brokerAssociateLO, 'loanOfficerId');
                            }
                        }
                        if (count($brokerAssociateLOArray)) {
                            self::$loanOfficerId = $brokerAssociateLOArray[0];
                        }
                        $agentStatus = isAgentActive::getReport(['agentId' => self::$agentNumber]);
                        $agentPCStatus = isPCAgentActive::getReport(['agentId' => self::$agentNumber]);
                        if (self::$fOpt == 'agent') {
                            $exeStatus = isBranchActive::getReport(['executiveId' => self::$executiveId]);
                        }


                        if ($agentStatus == 0 && self::$processingCompanyId > 0) {
                            $agentStatus = savePreferredAgentForBranch::getReport([
                                'agentId' => self::$agentNumber,
                                'branchId' => self::$executiveId,
                                'BPCID' => self::$processingCompanyId
                            ]);
                        }

                    } else {
                        self::$loanOfficerId = trim($agentInfoArray['userNumber']);

                        $agentStatus = isAgentActive::getReport(['agentId' => self::$loanOfficerId]);
                        $agentPCStatus = isPCAgentActive::getReport(['agentId' => self::$loanOfficerId]);
                        if (self::$fOpt == 'agent') {
                            $exeStatus = isBranchActive::getReport(['executiveId' => self::$executiveId]);
                        }


                        if ($agentStatus == 0 && self::$processingCompanyId > 0) {
                            $agentStatus = savePreferredAgentForBranch::getReport([
                                'agentId' => self::$loanOfficerId,
                                'branchId' => self::$executiveId,
                                'BPCID' => self::$processingCompanyId
                            ]);
                        }

                    }


                    if (self::$fOpt == 'agent') {
                        if (($agentStatus == 0) || ($exeStatus == 0) || ($agentPCStatus == 0)) {
                            header('location: /backoffice/subscriptionExpired.php?check=26');
                            exit();
                        }
                    } else {
                        if ($agentStatus == 0) {
                            header('location: /backoffice/subscriptionExpired.php?check=27');
                            exit();
                        }
                    }
                }
            }

            if ($SecondaryBrokerInfo) {
                self::$loanOfficerId = trim($SecondaryBrokerInfo->userNumber);
                $secondaryBrokerNumber = trim($SecondaryBrokerInfo->userNumber);
                $agentStatus = isAgentActive::getReport(['agentId' =>self:: $loanOfficerId]);
                if (self::$fOpt == 'agent') {
                    $exeStatus = isBranchActive::getReport(['executiveId' => self::$executiveId]);
                }


                if ($agentStatus == 0) {
                    $agentStatus = savePreferredAgentForBranch::getReport([
                        'agentId' => self::$loanOfficerId,
                        'branchId' => self::$executiveId,
                        'BPCID' => self::$processingCompanyId,
                    ]);
                }


            }

            //}
        }

        /*
        * Description   : Get the agent terms and condition desc
        * Date          : Nov 15, 2017
        * Developer     : Venky
         */
        if (!$HMLOTAC || !$HMLOTACQA) {
            $HMLOTAC = $glHMLOTAC;
            $HMLOTACQA = $glHMLOTAC;
        }


        $fileCT = self::$ft;
        $ipArray['PCID'] = self::$processingCompanyId;

        $statusInfoArray = getFilter::getReport($ipArray);
        if (count($statusInfoArray) > 0) {
            $PCStatusInfo = $statusInfoArray['PCStatusInfo'];
        }
        if (count($PCStatusInfo) > 0) {
            if (array_key_exists(self::$processingCompanyId, $PCStatusInfo)) {
                $PCStatusInfo = $PCStatusInfo[self::$processingCompanyId];
            }
        }
        if (self::$LMRId > 0) {
            self::$primeStatusId = LMRequest::myFileInfo()->ResponseInfo()->primeStatusId;
        } else {
            for ($j = 0; $j < count($PCStatusInfo); $j++) {
                if (strtolower(trim($PCStatusInfo[$j]['primaryStatus'])) == 'lead' && (trim($PCStatusInfo[$j]['moduleCode']) == self::$ft)) {
                    self::$primeStatusId = trim($PCStatusInfo[$j]['PSID']);
                    break;
                }
            }
            if (self::$primeStatusId == 0) {
                for ($j = 0; $j < count($PCStatusInfo); $j++) {
                    if (strtolower(trim($PCStatusInfo[$j]['displayOrder'])) == 1 && (trim($PCStatusInfo[$j]['moduleCode']) == self::$ft)) {
                        self::$primeStatusId = trim($PCStatusInfo[$j]['PSID']);
                        break;
                    }
                }

            }
        }
        $PCInfoArray = LMRequest::myFileInfo()->PCInfo(self::$processingCompanyId);
        self::$userLogo = $PCInfoArray->procCompLogo;
        $isPLO = $PCInfoArray->isPLO;



        $agentName = ucwords($agentFName . ' ' . $agentLName);

        if (self::$executiveId > 0) {
            $inArray['LMRAEID'] = self::$executiveId;

            $branchHearAboutInfoArray = getBranchHearAbout::getReport($inArray);

            if (count($branchHearAboutInfoArray) > 0) {
                if (array_key_exists(self::$executiveId, $branchHearAboutInfoArray)) {
                    $branchHAInfoArray = $branchHearAboutInfoArray[self::$executiveId];
                }
            }
        }

        /*** Dummy Agent ID For PC 12058 **/
        $dummyBrokerInfo = getBrokerInfo::getReport(['mail' => self::$PCID . '@dummyAgentemail.com']);
        if (isset($dummyBrokerInfo['userNumber'])) {
            self::$dummyBrokerId = $dummyBrokerInfo['userNumber'];
        }
        /***End of Dummy Agent ID For PC 12058 **/


        /* Required Docs Start - https://www.pivotaltracker.com/story/show/********* */
        self::$reqCnt = 0;
        $apCo = '';
        $_sty = $_mTy = '';
        $fileMC = [];

        if (self::$LMRId > 0) {

            /* Get Loan Programs */
            for ($lp = 0; $lp < count($LMRClientTypeInfo ?? []); $lp++) {
                $_sty .= $apCo . $LMRClientTypeInfo[$lp]['ClientType'];
                $apCo = ',';
            }

            /* Get File Type */
            $apCo = '';
            foreach ($fileModuleInfo as $item) {
                $fileMC[] = $item->moduleCode;
                $_mTy .= $apCo . $item->moduleCode;
                $apCo = ',';
            }

            $mcArray = [
                '_pcID' => self::$processingCompanyId,
                '_mTy' => $_mTy,
                '_sty' => $_sty,
                '_as' => 1,
                'wfTy' => $reqDoc,
            ];

            if (self::$fOpt == 'agent' || self::$fOpt == 'branch' && self::$UType != 'CoBorrower') {
                $mcArray['_exID'] = self::$executiveId;
                $displayedAndMandatoryItems = getBranchAndAgentRequiredDocs::getReport($mcArray);
                self::$reqCnt = count($displayedAndMandatoryItems);
            }
        }
        /* Required Docs End */

        /* Custom Form Fields */
        $myPCFieldsInfo = $myPCFieldsInfoArray = $PCFieldsInfo = [];
        if (count($myPCFieldsInfo) > 0) {
            if (array_key_exists('PCFields', $myPCFieldsInfo)) $myPCFieldsInfoArray = $myPCFieldsInfo['PCFields'];
            for ($i = 0; $i < count($myPCFieldsInfoArray); $i++) {
                $fieldName = $myPCFieldsInfoArray[$i]['fieldName'];
                $PCFieldsInfo[$fieldName] = $myPCFieldsInfoArray[$i];
            }
            $mandatoryCls = ' class="mandatory" required="required" ';
        }

        /* Quick App Custom Form Fields */
        if (isset($_REQUEST['view'])) {
            if (cypher::myDecryption(Request::GetClean('view')) == 'wizard') {
                self::$FSArray []= '/assetsNew/css/pages/wizard/wizard-2.css';
            }
        }

        self::$FArray = [
            '/assets/js/models/Dates.js',
            '/assets/js/models/formValue.js',
            '/assets/js/fileCommon.js',
            '/assets/js/3rdParty/jquery-autocomplete/jquery.autocomplete_min.js',
            '/assets/js/3rdParty/jquery-chosen-0.9.8/chosen.jquery.js',
            '/assets/js/HMLOLoanInfo.js',
            '/assets/js/loanCalculation.js',
            '/assets/js/loan_math/per_diem.js',
            '/assets/js/LMRequest.js',
            '/assets/js/QAForm.js',
            '/assets/js/incExp.js',
            '/assets/js/fileDocs.js' ,
            '/backoffice/api_v2/js/address_lookup.js',
            '/assets/js/3rdParty/esignSignature/jSignature.js',
            '/assets/js/3rdParty/esignSignature/plugins/jSignature.CompressorBase30.js',
            '/assets/js/3rdParty/esignSignature/plugins/jSignature.CompressorSVG.js',
            '/assets/js/3rdParty/esignSignature/plugins/jSignature.UndoButton.js',
            '/assets/js/3rdParty/esignSignature/plugins/signhere/jSignature.SignHere.js',
        ];


        $myPCquickAppFieldsInfo = $myPCquickAppFieldsInfoArray = $PCquickAppFieldsInfo = $branchModules = [];


        self::$FSArray []= '/assets/styles/autocomplete.css';
        self::$FSArray []= '/assets/styles/multi_select.css';
        if ($theme == 2) {
            self::$FSArray[] = '/assets/styles/theme2.css';
        }


        $stateArray = Arrays::fetchStates();
        $allStatesArray = [];
        $allStatesArray = $stateArray;
        /** Fetch all States **/

        $ipArray = [
            'PCID' => self::$processingCompanyId,
            'keyNeeded' => 'n',
            'branchID' => self::$executiveId,
            'moduleCode' => self::$ft
        ];
        $servicesRequested = getBranchServices::getReport($ipArray);
        $branchModules = getBranchModules::getReport(['branchID' => self::$executiveId]);

        if (array_key_exists(self::$executiveId, $servicesRequested)) {
            $servicesRequested = $servicesRequested[self::$executiveId];
        }
        $glLMRClientTypeArray = getServiceTypes::getReport(['activeStatus' => '1']);
        self::$glLMRClientTypeData = $glLMRClientTypeArray;
        $aCm = $fileCLP = '';
        for ($ct = 0; $ct < count($servicesRequested); $ct++) {
            $fileCLP .= $aCm . trim($servicesRequested[$ct]['LMRClientType']);
            $aCm = ',';
        }

        /*get file level loan program*/

        for ($ct = 0; $ct < count($LMRClientTypeInfo ?? []); $ct++) {
            $fileLP[] = trim($LMRClientTypeInfo[$ct]['ClientType']);
        }

        if (count($fileLP) < 0 && self::$LMRId > 0) {
            $fileLP[0] = 'TBD';
        } else if ($fileLP[0] == '' && self::$LMRId > 0) {
            $fileLP[0] = 'TBD';
        }

        /**
         * Dynamic Fields.
         */
        $fieldsInfo = $fileTypeArray = [];

        $fileTypeArray = $branchModules[self::$executiveId];

        $fileTab = self::$wfOpt;

        $fieldsInfo = getAppFormFields::getReport([
            'assignedPCID' => self::$PCID,
            'fTArray' => $fileModuleInfo,
            'myOpt' => self::$wfOpt,
            'fileType' => self::$ft,
            'activeTab' => self::$activeTab
        ]);

        PageVariables::setPCID(self::$PCID);
        LMRequest::$PCID = self::$PCID;
        loanForm::init(
            self::$PCID,
            $fileTab,
            Request::GetClean('lId') ?? null,
            self::$LMRId,
            self::$LMRId ? $fileMC : null,
            getFileInfo::$fileLoanPrograms,
            getFileInfo::$fileInternalLoanPrograms,
            getFileInfo::$fileAdditionalLoanPrograms,
            $fieldsInfo
        );


        self::$hsFADisplay = array_key_exists('HS', $fieldsInfo) ? $fieldsInfo['HS']['isHardshipApplies']['FADisplay'] : 0;
        self::$hsQADisplay = array_key_exists('HS', $fieldsInfo) ? $fieldsInfo['HS']['isHardshipApplies']['QADisplay'] : 0;
        self::$isEsignRequired = array_key_exists('TAC', $fieldsInfo) ? $fieldsInfo['TAC']['isEsignRequired']['FADisplay'] : 0;
        self::$tacField = array_key_exists('TAC', $fieldsInfo) ? $fieldsInfo['TAC']['agreeTC']['FADisplay'] : 0;
        self::$FSArray []= '/assets/styles/multi_select.css';

        if ((self::$LMRId > 0 && self::$wfOpt == 'FA') || (self::$LMRId > 0 && self::$wfOpt == 'QA')) {
            // Get PC allowed file status.
            $permissionsToEditFile = getPermissionsToEdit::getReport([
                'PCID' => self::$PCID,
                'userGroup' => 'Client',

                'op' => 'view',
            ]);

            if (isset($permissionsToEditFile[LMRequest::myFileInfo()->ResponseInfo()->primeStatusId])) {
                self::$allowToEdit = 1;
            } else {
                self::$allowToEdit = 0;
            }

            //Share Link Read Only
            if (HMLOController::$shareLink == 'shareLink') {
                self::$allowToEdit = 0;
                $op = 'view';
            }
            //Share Link Read Only - Borrower Info Redacted
            if (self::$borInfoRed == 'yes') {
                self::$hideThisField = 0; //hide the field section
            }
            //Access to Uploaded Docs
            if (self::$aud == 'yes') {
                self::$aud = 1;
            }


        }
        if ($_GET['edit']) {
            if (!cypher::myDecryption($_GET['edit'])) {
                self::$allowToEdit = 0;
            }
        }

        $propertyTypeKeyArray = [];

        if (count(GpropertyTypeNumbArray::$GpropertyTypeNumbArray) > 0) {
            $propertyTypeKeyArray = array_keys(GpropertyTypeNumbArray::$GpropertyTypeNumbArray);
        }

        $GpropertyTypeKeyArray = [];
        $GpropertyTypeKeyArray = $propertyTypeKeyArray;
    }
}